name: ad_sdk
description: "简洁易用的Flutter广告SDK，支持Google Mobile Ads"
version: 1.0.0
homepage:
publish_to: none

environment:
  sdk: ^3.8.1
  flutter: '>=3.3.0'

dependencies:
  flutter:
    sdk: flutter
  google_mobile_ads:
    git:
      url: *************:wujing6688/googlemobileads.git
      ref: feature/v6.0.0-dev

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

# 强制覆盖依赖，让所有聚合包都使用git版本的google_mobile_ads
# dependency_overrides:
#   google_mobile_ads:
#     git:
#       url: *************:wujing6688/googlemobileads.git
#       ref: feature/v6.0.0-dev

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
