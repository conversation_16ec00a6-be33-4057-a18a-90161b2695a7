name: world_tune
description: A global radio station aggregation platform
version: 1.0.0+1
publish_to: none

environment:
  sdk: '>=2.19.2 <3.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  flutter_riverpod: ^2.3.0
  riverpod_annotation: ^2.6.1
  dio: ^5.0.1
  pretty_dio_logger: ^1.3.1
  flutter_secure_storage: ^9.2.4
  shared_preferences: ^2.0.15
  google_fonts: ^4.0.1
  lottie: ^2.2.0
  connectivity_plus: ^6.1.4
  flutter_dotenv: ^5.0.2
  go_router: ^6.0.3
  easy_localization: ^3.0.7+1
  just_audio: ^0.9.44
  just_audio_background: ^0.0.1-beta.8
  audio_session: ^0.1.25
  audio_service: ^0.18.12
  json_annotation: ^4.8.1
  flutter_animate: ^4.5.0
  url_launcher: ^6.2.4
  package_info_plus: ^8.0.2
  path_provider: ^2.1.4
  share_plus: ^10.0.3
  # 广告SDK依赖
  ad_sdk:
    git:
      url: *************:wujing6688/pluginadsdk.git
      ref: develop

  # tools 工具
  flutter_plugin_tools:
    git:
      url: *************:wujing6688/flutterplugintools.git
      ref: dev_0.1.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  json_serializable: ^6.6.0
  go_router_builder: any
  build_runner: ^2.4.7
  flutter_launcher_icons: ^0.13.1
  flutter_gen_runner:
  freezed: ^2.2.1
  riverpod_generator: ^2.3.0
  import_sorter: ^4.6.0
  very_good_analysis: ^4.0.0


## Install derry from https://pub.dev/packages/derry and run derry build, derry watch, derry gen
scripts: derry.yaml

# https://pub.dev/packages/flutter_gen, after new assets run derry gen
flutter_gen:
  output: lib/gen/ # Optional (default: lib/gen/)

  #  integrations:
  #    flutter_svg: true
  #    flare_flutter: true

  assets:
    enabled: true
  fonts:
    enabled: true
  colors:
    enabled: true
    inputs:
      - assets/color/colors.xml
import_sorter:
  comments: false # Optional, defaults to true
  ignored_files: # Optional, defaults to []
    - \/lib\/*
flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/lang/en.json
    - assets/lang/zh.json
    - assets/env/.env.production
    - assets/env/.env.staging
    - assets/env/.env.development
    - assets/env/
    - assets/
    - assets/radio_imgs/
    #- assets/image/

# Flutter Launcher Icons 配置
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/app_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/app_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/app_icon.png"


