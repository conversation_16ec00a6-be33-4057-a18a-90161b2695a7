# =============================================================================
# World Tune Flutter项目 Makefile
# =============================================================================

.PHONY: help setup clean lint format test build dev install

# 默认目标
all: setup lint format test

# =============================================================================
# 🛠️  开发环境相关
# =============================================================================

help: ## 📋 显示所有可用命令
	@echo "World Tune Flutter项目 - 可用命令:"
	@echo "=================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## 🚀 初始化项目环境
	@echo "🚀 初始化项目环境..."
	@flutter doctor
	@flutter pub get
	@flutter packages pub run build_runner build --delete-conflicting-outputs
	@cd ios && pod install && cd ..
	@echo "✅ 项目环境初始化完成"

clean: ## 🧹 清理项目文件
	@echo "🧹 清理项目文件..."
	@rm -rf pubspec.lock
	@flutter clean
	@cd ios && rm -rf Pods/ Podfile.lock && cd ..
	@flutter pub get
	@cd ios && pod install && cd ..
	@echo "✅ 项目清理完成"

deep_clean: ## 🗑️  深度清理（包括Gradle缓存）
	@echo "🗑️  执行深度清理..."
	@rm -rf ~/.gradle/caches/
	@rm -rf build/
	@rm -rf .dart_tool/
	@make clean
	@echo "✅ 深度清理完成"

upgrade: clean ## 📦 升级项目依赖
	@echo "📦 升级依赖包..."
	@flutter pub upgrade
	@flutter packages pub run build_runner build --delete-conflicting-outputs
	@echo "✅ 依赖升级完成"

# =============================================================================
# 🔍 代码质量相关
# =============================================================================

lint: ## 🔍 代码静态分析
	@echo "🔍 执行代码分析..."
	@dart analyze . || (echo "❌ 代码分析发现问题"; exit 1)
	@echo "✅ 代码分析通过"

format: ## 🎨 格式化代码
	@echo "🎨 格式化代码..."
	@dart format lib test
	@flutter pub run import_sorter:main
	@echo "✅ 代码格式化完成"

fix: ## 🔧 自动修复代码问题
	@echo "🔧 自动修复代码问题..."
	@dart fix --apply
	@make format
	@echo "✅ 代码修复完成"

# =============================================================================
# 🧪 测试相关
# =============================================================================

test: ## 🧪 运行单元测试
	@echo "🧪 运行单元测试..."
	@flutter test || (echo "❌ 测试失败"; exit 1)
	@echo "✅ 测试通过"

test_coverage: ## 📊 生成测试覆盖率报告
	@echo "📊 生成测试覆盖率报告..."
	@flutter test --coverage
	@lcov --remove coverage/lcov.info 'lib/*/*.g.dart' 'lib/*/*.freezed.dart' 'lib/gen/*' -o coverage/lcov.info
	@genhtml coverage/lcov.info -o coverage/html
	@echo "✅ 覆盖率报告生成完成: coverage/html/index.html"

test_integration: ## 🔗 运行集成测试
	@echo "🔗 运行集成测试..."
	@flutter test integration_test/
	@echo "✅ 集成测试完成"

# =============================================================================
# 🚀 运行相关 - Android
# =============================================================================

run_android_dev: ## 📱 Android开发环境运行
	@echo "📱 在Android上运行开发版本..."
	@flutter run --flavor development -t lib/main_development.dart

run_android_staging: ## 📱 Android预发环境运行
	@echo "📱 在Android上运行预发版本..."
	@flutter run --flavor staging -t lib/main_staging.dart

run_android_prod: ## 📱 Android生产环境运行
	@echo "📱 在Android上运行生产版本..."
	@flutter run --flavor production -t lib/main_production.dart

# =============================================================================
# 🍎 运行相关 - iOS
# =============================================================================

run_ios_dev: ## 🍎 iOS开发环境运行
	@echo "🍎 在iOS上运行开发版本..."
	@flutter run -d ios --flavor development -t lib/main_development.dart

run_ios_staging: ## 🍎 iOS预发环境运行
	@echo "🍎 在iOS上运行预发版本..."
	@flutter run -d ios --flavor staging -t lib/main_staging.dart

run_ios_prod: ## 🍎 iOS生产环境运行
	@echo "🍎 在iOS上运行生产版本..."
	@flutter run -d ios --flavor production -t lib/main_production.dart

# =============================================================================
# 🌐 运行相关 - 其他平台
# =============================================================================

run_web: ## 🌐 Web开发环境运行
	@echo "🌐 在Chrome上运行Web版本..."
	@flutter run -d chrome --dart-define=ENVIRONMENT=development

run_macos: ## 💻 macOS开发环境运行
	@echo "💻 在macOS上运行开发版本..."
	@flutter run -d macos --flavor development -t lib/main_development.dart

# =============================================================================
# 📦 构建相关 - Android APK
# =============================================================================

build_android_dev: ## 📦 构建Android开发版APK
	@echo "📦 构建Android开发版APK..."
	@flutter build apk --debug --flavor development -t lib/main_development.dart
	@echo "✅ APK构建完成: build/app/outputs/flutter-apk/app-development-debug.apk"

build_android_staging: ## 📦 构建Android预发版APK
	@echo "📦 构建Android预发版APK..."
	@flutter build apk --release --flavor staging -t lib/main_staging.dart
	@echo "✅ APK构建完成: build/app/outputs/flutter-apk/app-staging-release.apk"

build_android_prod: ## 📦 构建Android生产版APK
	@echo "📦 构建Android生产版APK..."
	@flutter build apk --release --flavor production -t lib/main_production.dart
	@echo "✅ APK构建完成: build/app/outputs/flutter-apk/app-production-release.apk"

build_android_bundle: ## 📦 构建Android App Bundle
	@echo "📦 构建Android App Bundle..."
	@flutter build appbundle --release --flavor production -t lib/main_production.dart
	@echo "✅ Bundle构建完成: build/app/outputs/bundle/productionRelease/app-production-release.aab"

# =============================================================================
# 📦 构建相关 - iOS
# =============================================================================

build_ios_dev: ## 📦 构建iOS开发版
	@echo "📦 构建iOS开发版..."
	@flutter build ios --debug --flavor development -t lib/main_development.dart --no-codesign
	@echo "✅ iOS开发版构建完成"

build_ios_staging: ## 📦 构建iOS预发版
	@echo "📦 构建iOS预发版..."
	@flutter build ios --release --flavor staging -t lib/main_staging.dart --no-codesign
	@echo "✅ iOS预发版构建完成"

build_ios_prod: ## 📦 构建iOS生产版
	@echo "📦 构建iOS生产版..."
	@flutter build ios --release --flavor production -t lib/main_production.dart --no-codesign
	@echo "✅ iOS生产版构建完成"

build_ipa: ## 📦 构建iOS IPA包
	@echo "📦 构建iOS IPA包..."
	@flutter build ipa --release --flavor production -t lib/main_production.dart
	@echo "✅ IPA构建完成"

# =============================================================================
# 🌐 构建相关 - Web
# =============================================================================

build_web: ## 🌐 构建Web版本
	@echo "🌐 构建Web版本..."
	@flutter build web --release --dart-define=ENVIRONMENT=production
	@echo "✅ Web版本构建完成: build/web/"

# =============================================================================
# 📱 快速开发命令
# =============================================================================

dev: ## 🚀 快速开发启动（Android）
	@echo "🚀 快速开发启动..."
	@make setup
	@make run_android_dev

ios_dev: ## 🍎 快速iOS开发启动
	@echo "🍎 快速iOS开发启动..."
	@make setup
	@make run_ios_dev

web_dev: ## 🌐 快速Web开发启动
	@echo "🌐 快速Web开发启动..."
	@make setup
	@make run_web

# =============================================================================
# 🔧 工具相关
# =============================================================================

gen: ## 🔄 重新生成代码
	@echo "🔄 重新生成代码..."
	@flutter packages pub run build_runner build --delete-conflicting-outputs
	@echo "✅ 代码生成完成"

watch: ## 👀 监听文件变化并重新生成
	@echo "👀 监听文件变化..."
	@flutter packages pub run build_runner watch --delete-conflicting-outputs

install: ## 📲 安装APK到设备
	@echo "📲 安装APK到连接的设备..."
	@flutter install

devices: ## 📱 显示连接的设备
	@echo "📱 连接的设备列表:"
	@flutter devices

doctor: ## 🏥 检查Flutter环境
	@echo "🏥 检查Flutter环境..."
	@flutter doctor -v

# =============================================================================
# 📊 分析相关
# =============================================================================

size_analysis: ## 📊 分析APK大小
	@echo "📊 分析APK大小..."
	@flutter build apk --analyze-size --flavor production -t lib/main_production.dart

performance: ## ⚡ 性能分析
	@echo "⚡ 生成性能分析版本..."
	@flutter build apk --profile --flavor development -t lib/main_development.dart

# =============================================================================
# 🚀 部署相关
# =============================================================================

deploy_web: build_web ## 🌐 部署Web版本
	@echo "🌐 部署Web版本..."
	@echo "请手动上传 build/web/ 目录到服务器"

release_android: ## 🚀 Android完整发布流程
	@echo "🚀 Android完整发布流程..."
	@make clean
	@make lint
	@make test
	@make build_android_prod
	@make build_android_bundle
	@echo "✅ Android发布包准备完成"

release_ios: ## 🚀 iOS完整发布流程
	@echo "🚀 iOS完整发布流程..."
	@make clean
	@make lint
	@make test
	@make build_ipa
	@echo "✅ iOS发布包准备完成"

release_all: ## 🚀 全平台发布流程
	@echo "🚀 全平台发布流程..."
	@make clean
	@make lint
	@make test
	@make build_android_prod
	@make build_android_bundle
	@make build_ios_prod
	@make build_web
	@echo "✅ 全平台发布包准备完成"

# =============================================================================
# 🏷️  版本管理
# =============================================================================

version: ## 🏷️  显示当前版本
	@echo "🏷️  当前版本信息:"
	@grep version pubspec.yaml

tag: ## 🏷️  创建Git标签
	@echo "🏷️  创建Git标签..."
	@VERSION=$$(grep version pubspec.yaml | cut -d' ' -f2); \
	git tag -a v$$VERSION -m "Release v$$VERSION" && \
	git push origin v$$VERSION

# =============================================================================
# 🧰 维护相关
# =============================================================================

outdated: ## 📋 检查过时的依赖
	@echo "📋 检查过时的依赖..."
	@flutter pub outdated

security: ## 🔒 安全检查
	@echo "🔒 执行安全检查..."
	@flutter pub deps | grep -E "(http|path|git)" || echo "✅ 未发现明显的安全问题"

commit_check: ## ✅ 提交前检查
	@echo "✅ 执行提交前检查..."
	@make lint
	@make format
	@make test
	@echo "✅ 提交前检查完成"

# =============================================================================
# 📖 文档相关
# =============================================================================

docs: ## 📖 生成项目文档
	@echo "📖 生成项目文档..."
	@dartdoc
	@echo "✅ 文档生成完成: doc/api/"

readme: ## 📖 更新README统计
	@echo "📖 更新README统计..."
	@echo "代码行数: $$(find lib -name "*.dart" -exec wc -l {} + | tail -1 | awk '{print $$1}')"
	@echo "测试文件数: $$(find test -name "*_test.dart" | wc -l)"


