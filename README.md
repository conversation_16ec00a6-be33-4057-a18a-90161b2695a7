# AdSDK - Flutter广告SDK

一个功能完整的Flutter广告SDK，支持多种广告类型和自动事件上报。

## 支持的Flutter版本

- **Flutter**: >= 3.0.0
- **Dart**: >= 2.17.0

## 支持的广告类型

- ✅ **Banner广告** - 横幅广告
- ✅ **插屏广告** - 全屏插页广告
- ✅ **激励广告** - 观看视频获得奖励
- ✅ **开屏广告** - 应用启动时展示
- ✅ **原生广告** - 自定义样式的原生内容广告

## 主要特性

- 🚀 **智能缓存机制** - 自动预加载和缓存管理
- 📊 **自动事件上报** - 完整的广告生命周期事件追踪
- 🔄 **备用广告支持** - 主广告失败时自动切换备用广告
- 🎯 **场景化投放** - 支持不同场景的广告投放
- 💰 **价格事件上报** - 支持广告收益数据上报
- 🛡️ **错误处理** - 完善的错误处理和重试机制

## 安装

### 1. 添加依赖

在 `pubspec.yaml` 中添加：

```yaml
dependencies:
  ad_sdk:
    git:
      url: https://github.com/your-repo/ad_sdk.git
      ref: main
```

### 2. 强制覆盖依赖（可选）

如果需要使用特定版本的 Google Mobile Ads，可以在 `pubspec.yaml` 中添加：

```yaml
dependency_overrides:
  google_mobile_ads:
    git:
      url: *************:wujing6688/googlemobileads.git
      ref: feature/v6.0.0-dev
```

### 3. 安装依赖

```bash
flutter pub get
```

## 配置

### Android配置

在 `android/app/src/main/AndroidManifest.xml` 中添加：

```xml
<manifest>
    <application>
        <!-- AdMob App ID -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-xxxxxxxxxxxxxxxx~yyyyyyyyyy"/>
    </application>
</manifest>
```

### iOS配置

在 `ios/Runner/Info.plist` 中添加：

```xml
<key>GADApplicationIdentifier</key>
<string>ca-app-pub-xxxxxxxxxxxxxxxx~yyyyyyyyyy</string>
```

## 初始化

```dart
import 'package:ad_sdk/ad_sdk.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置事件上报回调
  AdSDK.setEventReporter((eventName, parameters) {
    print('📊 广告事件上报: $eventName');
    print('   参数: $parameters');
    // 这里可以接入你的数据分析平台
    // 例如：Firebase Analytics等
  });

  // 设置价格事件上报回调
  AdSDK.setEventPriceReporter((scene, posid, platform, price, currency, paytype) {
    print('💰 价格事件上报:');
    print('   scene: $scene, posid: $posid');
    print('   platform: $platform, price: $price, currency: $currency, paytype: $paytype');
    // 这里可以接入你的收益分析平台
  });

  // 初始化广告SDK
  await AdSDK.initialize(AdConfig(
    ads: {
        // Banner广告
        AdType.banner: AdIds(
        main: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/**********' // iOS Banner广告测试ID
            : 'ca-app-pub-3940256099942544/6300978111', // Android Banner广告测试ID
        backup: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/**********' // iOS Banner广告测试ID
            : 'ca-app-pub-3940256099942544/6300978111', // Android Banner广告测试ID
        ),

        // 插屏广告
        AdType.interstitial: AdIds(
        main: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/4411468910' // iOS插屏广告测试ID
            : 'ca-app-pub-3940256099942544/**********', // Android插屏广告测试ID
        backup: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/4411468910' // iOS插屏广告测试ID
            : 'ca-app-pub-3940256099942544/**********', // Android插屏广告测试ID
        ),

        // 激励广告
        AdType.rewarded: AdIds(
        main: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/1712485313' // iOS激励广告测试ID
            : 'ca-app-pub-3940256099942544/**********', // Android激励广告测试ID
        backup: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/1712485313' // iOS激励广告测试ID
            : 'ca-app-pub-3940256099942544/**********', // Android激励广告测试ID
        ),

        // 开屏广告
        AdType.appOpen: AdIds(
        main: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/5575463023' // iOS开屏广告官方测试ID
            : 'ca-app-pub-3940256099942544/3419835294', // Android开屏广告测试ID
        backup: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/5575463023' // iOS开屏广告官方测试ID
            : 'ca-app-pub-3940256099942544/3419835294', // Android开屏广告测试ID
        ),

        // 原生广告
        AdType.native: AdIds(
        main: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/3986624511' // iOS原生广告测试ID
            : 'ca-app-pub-3940256099942544/**********', // Android原生广告测试ID
        backup: Platform.isIOS
            ? 'ca-app-pub-3940256099942544/3986624511' // iOS原生广告测试ID
            : 'ca-app-pub-3940256099942544/**********', // Android原生广告测试ID
        ),
    },
  );

  runApp(MyApp());
}
```

## 使用指南

### 1. Banner广告

Banner广告是显示在屏幕顶部或底部的横幅广告。

#### 加载Banner广告

```dart
import 'package:google_mobile_ads/google_mobile_ads.dart' as gma;

// 加载主Banner广告
await AdSDK.load(
  AdType.banner,
  bannerSize: gma.AdSize.banner, // 可选：指定Banner尺寸
  pageId: 'home_page',           // 必需：页面标识
  callback: AdCallback(
    onAdLoaded: (source) {
      print('Banner广告加载成功: $source');
    },
    onAdFailedToLoad: (error) {
      print('Banner广告加载失败: $error');
    },
  ),
);

// 加载备用Banner广告
await AdSDK.loadBackup(
  AdType.banner,
  bannerSize: gma.AdSize.banner,
  pageId: 'home_page',
  callback: AdCallback(/* ... */),
);
```

#### 展示Banner广告

```dart
// 获取Banner Widget
Widget bannerWidget = AdSDK.getBannerWidget(
  pageId: 'home_page',
  backup: false, // false=主广告, true=备用广告
);

// 在UI中使用
class HomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 页面内容
          Expanded(child: YourContent()),
          // Banner广告
          AdSDK.getBannerWidget(pageId: 'home_page'),
        ],
      ),
    );
  }
}
```

### 2. 插屏广告

插屏广告是全屏显示的广告，通常在应用的自然过渡点展示。

#### 加载插屏广告

```dart
// 加载主插屏广告
await AdSDK.load(
  AdType.interstitial,
  callback: AdCallback(
    onAdLoaded: (source) {
      print('插屏广告加载成功: $source');
    },
    onAdFailedToLoad: (error) {
      print('插屏广告加载失败: $error');
    },
  ),
);
```

#### 展示插屏广告

```dart
// 展示主插屏广告
await AdSDK.show(
  AdType.interstitial,
  scene: 1001, // 可选：场景ID，用于数据分析
  callback: AdCallback(
    onAdShowed: (source) {
      print('插屏广告开始展示: $source');
    },
    onAdClicked: () {
      print('插屏广告被点击');
    },
    onAdClosed: () {
      print('插屏广告关闭');
      // 用户关闭广告后的逻辑
    },
    onAdFailedToShow: (error) {
      print('插屏广告展示失败: $error');
    },
  ),
);

// 展示备用插屏广告
await AdSDK.showBackup(
  AdType.interstitial,
  scene: 1001,
  callback: AdCallback(/* ... */),
);
```

#### 直接展示（加载+展示）

```dart
// 如果没有预加载，可以直接展示（会先加载再展示）
await AdSDK.show(
  AdType.interstitial,
  scene: 1001,
  callback: AdCallback(
    onAdLoaded: (source) {
      print('插屏广告加载成功，即将展示');
    },
    onAdShowed: (source) {
      print('插屏广告开始展示');
    },
    onAdClosed: () {
      print('插屏广告关闭');
    },
  ),
);
```

### 3. 激励广告

激励广告是用户观看完整视频后可以获得奖励的广告。

#### 加载激励广告

```dart
// 加载主激励广告
await AdSDK.load(
  AdType.rewarded,
  callback: AdCallback(
    onAdLoaded: (source) {
      print('激励广告加载成功: $source');
    },
    onAdFailedToLoad: (error) {
      print('激励广告加载失败: $error');
    },
  ),
);
```

#### 展示激励广告

```dart
// 展示主激励广告
await AdSDK.show(
  AdType.rewarded,
  scene: 2001, // 可选：场景ID，如"获得金币"、"复活"等
  callback: AdCallback(
    onAdShowed: (source) {
      print('激励广告开始展示: $source');
    },
    onAdClicked: () {
      print('激励广告被点击');
    },
    onAdRewarded: (rewardType, rewardAmount) {
      print('用户获得奖励: $rewardType x $rewardAmount');
      // 发放奖励给用户
      _giveRewardToUser(rewardType, rewardAmount);
    },
    onAdClosed: () {
      print('激励广告关闭');
    },
    onAdFailedToShow: (error) {
      print('激励广告展示失败: $error');
    },
  ),
);

void _giveRewardToUser(String rewardType, int rewardAmount) {
  // 实现奖励发放逻辑
  print('发放奖励: $rewardType x $rewardAmount');
  // 例如：增加用户金币、生命值等
}
```

#### 检查广告是否准备好

```dart
// 检查激励广告是否已加载
bool isReady = AdSDK.isReady(AdType.rewarded);
if (isReady) {
  // 显示"观看广告获得奖励"按钮
  showRewardButton();
} else {
  // 预加载广告
  AdSDK.load(AdType.rewarded);
}
```

### 4. 开屏广告

开屏广告在应用启动时展示，通常用于应用冷启动场景。

#### 加载开屏广告

```dart
// 加载主开屏广告
await AdSDK.load(
  AdType.appOpen,
  callback: AdCallback(
    onAdLoaded: (source) {
      print('开屏广告加载成功: $source');
    },
    onAdFailedToLoad: (error) {
      print('开屏广告加载失败: $error');
    },
  ),
);
```

#### 展示开屏广告

```dart
// 在应用启动时展示开屏广告
await AdSDK.show(
  AdType.appOpen,
  scene: 3001, // 可选：场景ID，如"应用启动"
  callback: AdCallback(
    onAdShowed: (source) {
      print('开屏广告开始展示: $source');
    },
    onAdClicked: () {
      print('开屏广告被点击');
    },
    onAdClosed: () {
      print('开屏广告关闭');
      // 进入应用主界面
      _navigateToMainScreen();
    },
    onAdFailedToShow: (error) {
      print('开屏广告展示失败: $error');
      // 直接进入应用主界面
      _navigateToMainScreen();
    },
  ),
);

void _navigateToMainScreen() {
  // 导航到应用主界面
  Navigator.of(context).pushReplacement(
    MaterialPageRoute(builder: (context) => MainScreen()),
  );
}
```

### 5. 原生广告

原生广告可以自定义样式，与应用内容无缝融合。

#### 使用模板样式展示原生广告

```dart
// 使用预定义模板展示原生广告
await AdSDK.bindViewByTemplateStyle(
  AdType.native,
  templateType: 'medium', // 模板类型：small, medium, large
  backup: false,          // 是否使用备用广告
  scene: 4001,           // 可选：场景ID
  callback: AdCallback(
    onAdLoaded: (source) {
      print('原生广告加载成功: $source');
    },
    onAdShowed: (source) {
      print('原生广告开始展示: $source');
    },
    onAdClicked: () {
      print('原生广告被点击');
    },
    onAdClosed: () {
      print('原生广告关闭');
    },
    onAdFailedToLoad: (error) {
      print('原生广告加载失败: $error');
    },
    onAdFailedToShow: (error) {
      print('原生广告展示失败: $error');
    },
  ),
);
```

#### 使用Factory ID展示原生广告

```dart
// 使用自定义Factory ID展示原生广告
await AdSDK.bindViewByFactoryId(
  AdType.native,
  factoryId: 'customNativeAdFactory', // 自定义Factory ID
  backup: false,
  scene: 4002,
  callback: AdCallback(/* ... */),
);
```

## 事件上报

AdSDK会自动上报广告生命周期事件，帮助你分析广告效果。

### 自动上报的事件

#### 1. 广告请求事件 (action: 1)
```dart
{
  "action": 1,
  "placement_id": "ca-app-pub-xxx",
  "ad_mediation": "admob"
}
```

#### 2. 广告返回事件 (action: 2)
```dart
{
  "action": 2,
  "placement_id": "ca-app-pub-xxx",
  "ad_platform": "GADMAdapterGoogleAdMobAds",
  "ad_mediation": "admob",
  "filled_time": 1250  // 填充时间（毫秒）
}
```

#### 3. 广告展示事件 (action: 3)
```dart
{
  "action": 3,
  "placement_id": "ca-app-pub-xxx",
  "ad_platform": "GADMAdapterGoogleAdMobAds",
  "ad_mediation": "admob",
  "scene": 1001  // 场景ID（如果提供）
}
```

#### 4. 广告点击事件 (action: 4)
```dart
{
  "action": 4,
  "placement_id": "ca-app-pub-xxx",
  "ad_platform": "GADMAdapterGoogleAdMobAds",
  "ad_mediation": "admob",
  "scene": 1001
}
```

#### 5. 广告关闭事件 (action: 5)
```dart
{
  "action": 5,
  "placement_id": "ca-app-pub-xxx",
  "ad_platform": "GADMAdapterGoogleAdMobAds",
  "ad_mediation": "admob",
  "scene": 1001
}
```

#### 6. 激励广告奖励事件 (action: 6)
```dart
{
  "action": 6,
  "placement_id": "ca-app-pub-xxx",
  "ad_platform": "GADMAdapterGoogleAdMobAds",
  "ad_mediation": "admob",
  "scene": 2001,
  "reward_type": "coins",
  "reward_amount": 100
}
```

### 价格事件上报

如果配置了价格上报回调，SDK还会上报广告收益数据：

```dart
// 价格事件参数
priceReporter: (scene, posid, platform, price, currency, paytype) {
  // scene: 场景ID
  // posid: 广告id
  // platform: 来源
  // price: 收益金额
  // currency: 货币单位
  // paytype: 精确度
  
}
```

## 高级功能

### 预加载广告

为了提升用户体验，建议在合适的时机预加载广告：

```dart
// 预加载主广告
await AdSDK.preLoad(AdType.interstitial);
await AdSDK.preLoad(AdType.rewarded);
await AdSDK.preLoad(AdType.appOpen);

// 预加载备用广告
await AdSDK.preLoad(AdType.interstitial, backup: true);
await AdSDK.preLoad(AdType.rewarded, backup: true);
```

### 检查广告状态

```dart
// 检查广告是否已准备好
bool isInterstitialReady = AdSDK.isReady(AdType.interstitial);
bool isRewardedReady = AdSDK.isReady(AdType.rewarded);
bool isAppOpenReady = AdSDK.isReady(AdType.appOpen);

// Banner和原生广告不支持缓存检查
```

### 打开广告检查器（调试用）

```dart
// 在调试模式下打开AdMob广告检查器
AdSDK.openAdInspector();
```

## 广告适配器

### 当前支持的广告平台

- ✅ **Google AdMob** - 默认支持
- ✅ **Google Ad Manager** - 默认支持

### 添加聚合广告平台

如果需要添加其他广告平台（如Meta、Pangle、AppLovin等），可以在 `pubspec.yaml` 中添加对应的聚合包：

```yaml
dependencies:
  # Meta广告
  gma_mediation_meta: ^1.4.0

  # Pangle广告
  gma_mediation_pangle: ^3.1.1

  # AppLovin广告
  gma_mediation_applovin: ^2.3.1

  # Mintegral广告
  gma_mediation_mintegral: ^1.2.3

  # LiftoffMonetize广告
  gma_mediation_liftoffmonetize: ^1.2.1
```

### 自定义广告适配器

当前版本使用 `GoogleAdsAdapter` 作为广告适配器。如果需要支持其他广告平台，可以参考 `GoogleAdsAdapter` 的实现方式：

```dart
/// 自定义广告适配器示例
class CustomAdAdapter {
  final Map<String, dynamic> _loadedAds = {};

  /// 初始化广告平台
  Future<void> initialize() async {
    // 初始化自定义广告平台SDK
  }

  /// 加载广告
  Future<AdResult> loadAd(String adId, AdType type, [gma.AdSize? bannerSize]) async {
    // 实现广告加载逻辑
    // 返回 AdResult.success() 或 AdResult.failure()
  }

  /// 展示广告
  Future<void> showAd(String adId, AdCallback? callback, AdSource source) async {
    // 实现广告展示逻辑
    // 调用相应的callback方法
  }

  // ... 实现其他必要方法
}
```

**注意**: 当前版本的AdSDK与GoogleAdsAdapter紧密集成。如需支持其他广告平台，建议：
1. 参考GoogleAdsAdapter的完整实现
2. 确保实现所有必要的方法和回调
3. 在AdManager中替换或扩展适配器使用

## 最佳实践

### 1. 广告加载时机

- **开屏广告**: 应用启动时立即加载
- **插屏广告**: 在关卡结束、页面切换等自然过渡点展示
- **激励广告**: 在用户需要奖励时展示，如复活、获得道具等
- **Banner广告**: 在页面加载完成后展示
- **原生广告**: 在内容列表中自然穿插

### 2. 错误处理

```dart
await AdSDK.show(
  AdType.interstitial,
  callback: AdCallback(
    onAdFailedToShow: (error) {
      // 主广告失败，尝试备用广告
      AdSDK.showBackup(
        AdType.interstitial,
        callback: AdCallback(
          onAdFailedToShow: (backupError) {
            // 备用广告也失败，记录错误
            print('所有广告都失败了: $error, $backupError');
          },
        ),
      );
    },
  ),
);
```

### 3. 性能优化

- 合理使用预加载，避免过度预加载影响性能
- 在应用后台时暂停广告加载
- 及时释放不需要的广告资源

## 常见问题

### Q: 为什么广告不显示？

A: 请检查以下几点：
1. 广告ID是否正确配置
2. 网络连接是否正常
3. 是否在测试设备上添加了测试广告ID
4. AdMob账户是否已激活

### Q: 如何测试广告？

A: 使用Google提供的测试广告ID：
- Banner: `ca-app-pub-3940256099942544/**********`
- 插屏: `ca-app-pub-3940256099942544/**********`
- 激励: `ca-app-pub-3940256099942544/**********`
- 开屏: `ca-app-pub-3940256099942544/**********`
- 原生: `ca-app-pub-3940256099942544/**********`

### Q: 如何处理广告加载失败？

A: 实现完善的错误处理和备用方案：
```dart
AdCallback(
  onAdFailedToLoad: (error) {
    // 记录错误
    print('广告加载失败: $error');
    // 尝试备用广告或其他方案
  },
)
```

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 支持

如果你在使用过程中遇到问题，请：
1. 查看本文档的常见问题部分
2. 在 GitHub 上提交 Issue
3. 联系技术支持

