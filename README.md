# Flutter_Plugin_Tools

#### 介绍
Flutter 工具插件集合，包含 AppsFlyer、Firebase、ATT 等常用功能模块
1. Master分支为稳定分支，Tag版本号：tools_v1.1.0
2. Dev分支为开发分支谨慎使用 
3. 各项目分支从Master分支切自己的项目分支：tools_pdf_v1.1.0 
4. 同一个Apple 账户只切一个项目分支，不用多切


## 📦 安装

在您的 `pubspec.yaml` 中添加：

```yaml
dependencies:
  flutter_plugin_tools:
    git:
      url: https://gitee.com/wujing6688/flutterplugintools.git
      ref: xxxx (可选 - 默认为 master 分支)
```

## 🎯 快速开始

### 0.准备
(如果使用Firebase集成，需要把文件放在这个位置)
![img.png](img.png)

![img_1.png](img_1.png)


### 1. 初始化

```dart
import 'package:flutter/material.dart';
 
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化 FlutterTools
  await FlutterTools.initialize(
    FlutterToolsConfig(
      // AppsFlyer 配置
      appsFlyerConfig: AppsFlyerConfig(
        afDevKey: "your_appsflyer_dev_key",
        iosAppId: "your_ios_app_id",
        showDebug: true,
      ),
      // Firebase 配置
      firebaseConfig: FirebaseConfig.simple(
        projectId: "your_project_id",
        iosApiKey: "your_ios_api_key",
        iosAppId: "your_ios_app_id",
        androidApiKey: "your_android_api_key",
        androidAppId: "your_android_app_id",
        messagingSenderId: "your_messaging_sender_id",
      ),
      // ATT 配置
      attConfig: ATTConfig.delayed(
        delaySeconds: 2,
        onStatusChanged: (status, advertisingId) {
          print("ATT 状态变化: ${ATTManager.getStatusDescription(status)}");
          if (advertisingId != null) {
            print("IDFA: $advertisingId");
          }
        },
        onRequestCompleted: (status, isFirstTime, advertisingId) {
          if (status == TrackingStatus.authorized && advertisingId != null) {
            print("✅ 获得 ATT 权限和 IDFA: $advertisingId");
            // 启用追踪功能
          } else {
            print("⚠️ 未获得 ATT 权限，使用匿名模式");
            // 启用匿名模式
          }
        },
      ),
      // 日志配置 - 开发环境启用控制台日志，生产环境可使用 LogConfig.disabled()
      logConfig: LogConfig.console(),
    ),
  );

  runApp(MyApp());
}
```

### 2. 使用 AppsFlyer

```dart
// 上报自定义事件
await AppFlyerReport.reportCustomEvent("custom_event", {
  "param1": "value1",
  "param2": "value2",
});

// Firebase Analytics 事件
await FirebaseAnalyticsHelper.logCustomEvent(
  name: "button_clicked",
  parameters: {"button_name": "subscribe"},
);

// Firebase Remote Config
String welcomeMessage = FirebaseRemoteConfigHelper.getString(
  "welcome_message",
  defaultValue: "欢迎使用应用!",
);

// ATT 权限检查和请求
TrackingStatus? status = await ATTManager.getCurrentStatus();
if (status == TrackingStatus.notDetermined) {
  await ATTManager.requestPermission();
}
```

## 📚 文档

- [AppsFlyer 模块文档](docs/appsflyer.md)
- [Firebase 模块文档](docs/firebase.md)
- [ATT 模块文档](docs/att.md)

## 🏗️ 项目结构

```
lib/
├── flutter_tools.dart          # 主入口文件
├── config/                     # 配置管理
│   └── flutter_tools_config.dart
├── appsflyer/                  # AppsFlyer 模块
│   ├── appsflyer_manager.dart
│   ├── appflyer_report.dart
│   ├── af_util.dart
│   └── config/
├── firebase/                   # Firebase 模块
├── att/                        # ATT 模块
└── other/                     # 其他工具
    └── log/
``
