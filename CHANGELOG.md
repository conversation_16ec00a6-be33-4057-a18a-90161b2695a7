# Changelog

## [1.1.2] - 2025.08.08

### ✨ 新功能

#### 广告功能增强
- **数据分析与追踪**
  - 集成 AppsFlyer 数据上报系统
  - 增加广告转化数据追踪
  - 集成 Firebase 埋点功能
  - 支持苹果 ATT 隐私框架
- **广告展示优化**
  - 增加底部悬浮 banner 广告位
  - 优化 banner 广告显示策略，解决竞态问题
  - 优化插屏广告展示时机，设置30秒冷却时间

#### 用户体验优化
- **交互体验**
  - 优化震动反馈机制
  - 改进滑动图标动画效果
  - 修改雷达推荐动画和图标
  - 优化底部导航栏样式和点击区域

### 🐛 问题修复
- 修复雷达推荐手势滑动兼容性问题
- 优化国家选择的多语言翻译
- 改进搜索和仓库页面的列表显示模式
- 优化分享功能，调整分享链接格式

### 🔧 性能优化
- 增加播放验证功能，失败时自动获取最新资源
- 优化播放可用性，添加用户提示机制

---

## [Unreleased] - 2025.08.08

### ✨ 新功能

#### 播放器增强
- **锁屏播放器功能**
  - 集成 audio_service 包实现 iOS 锁屏播放控制
  - 支持锁屏界面播放控制和信息同步
  - 优化锁屏播放器图片加载和显示策略
  
- **iOS 灵动岛支持**
  - 新增 iOS 设备灵动岛播放器展示
  - 实时同步播放状态和电台信息

- **播放列表功能**
  - 支持在播放页面左右滑动切换播放列表
  - 播放列表范围包含当前区域的电台
  - 支持播放历史和收藏区域的播放列表

#### 探索功能增强
- **高级筛选系统**
  - 新增多维度组合筛选功能
  - 支持按国家、地区进行筛选
  - 支持标签(tag)筛选
  - 支持关键词搜索
  - 筛选条件支持多选组合

#### 雷达推荐
- **个性化推荐**
  - 新增雷达推荐专区
  - 支持左滑忽略、右滑收藏的手势操作
  - 加入收藏动画特效

#### 收藏管理
- **导入导出功能**
  - 新增收藏列表一键导出功能
  - 支持收藏数据导入功能
  - 添加收藏数据统计功能
  - 在收藏列表页面添加固定悬浮操作按钮

#### 分享功能
- **多样化分享方式**
  - 开发 H5 电台分享页面
  - 支持分享卡片模式
  - 支持分享文本模式
  - 支持复制分享链接

#### 广告集成
- **商业化功能**
  - 集成移动端广告 SDK (pluginadsdk)
  - 实现页面顶部 banner 广告
  - 实现播放器底部 banner 广告

### 🎨 界面优化

- **现代化 UI 改造**
  - 重新设计深色渐变背景效果
  - 新增霓虹风格 Tab 设计
  - 优化渐变动画效果
  - 改进沉浸式播放器界面

### 🔧 性能优化

- **图片加载优化**
  - 实现图片加载超时检测机制
  - 添加备用图片兜底策略
  - 优化网络图片加载性能

- **播放器优化**
  - 优化播放暂停恢复逻辑
  - 改进播放资源缓存机制
  - 增强网络状态监测和自动重连

### 🐛 问题修复

- **播放器修复**
  - 修复 HLS 流媒体播放中断问题
  - 优化 M3U8 格式解析和重连机制
  - 扩展播放器支持的音频格式

- **数据维护**
  - 优化电台数据更新机制
  - 增加数据有效性验证
  - 实现 Radio Browser API 数据同步

### 📝 文档
- 更新 API 集成文档
- 补充功能说明文档
- 添加开发规范文档

---
*更新日期：2025.08.07*