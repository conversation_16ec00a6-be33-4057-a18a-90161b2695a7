# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-08-04

### Added
- 🎉 **初始版本发布**
- ✅ **多种广告类型支持**
  - Banner广告（横幅广告）
  - 插屏广告（全屏插页广告）
  - 激励广告（观看视频获得奖励）
  - 开屏广告（应用启动时展示）
  - 原生广告（自定义样式的原生内容广告）

- 🚀 **智能缓存机制**
  - 自动预加载和缓存管理
  - 广告过期时间控制
  - 缓存状态检查API

- 📊 **完整的事件上报系统**
  - 广告请求事件（action: 1）
  - 广告返回事件（action: 2）- 包含填充时间和平台信息
  - 广告展示事件（action: 3）
  - 广告点击事件（action: 4）
  - 广告关闭事件（action: 5）
  - 激励广告奖励事件（action: 6）

- 💰 **价格事件上报**
  - 支持广告收益数据上报
  - 自定义价格上报回调
  - 场景化收益分析

- 🔄 **备用广告支持**
  - 独立的备用广告配置
  - 完整的备用广告API

- 🎯 **场景化投放**
  - 支持不同场景的广告投放
  - 场景ID参数传递
  - 场景化数据分析

- 🛡️ **完善的错误处理**
  - 详细的错误信息回调

- 🔧 **开发者工具**
  - 广告检查器集成
  - 调试模式支持
  - 详细的日志输出

### Technical Features
- **AdConfig**: 统一的广告配置管理
- **AdCallback**: 完整的广告生命周期回调
- **AdManager**: 核心广告管理器
- **GoogleAdsAdapter**: Google AdMob/Ad Manager适配器
- **AdCache**: 智能广告缓存系统
- **AdEvents**: 标准化事件定义

### API Reference
- `AdSDK.initialize()` - SDK初始化
- `AdSDK.load()` - 加载主广告
- `AdSDK.loadBackup()` - 加载备用广告
- `AdSDK.show()` - 展示主广告
- `AdSDK.showBackup()` - 展示备用广告
- `AdSDK.preLoad()` - 预加载广告
- `AdSDK.isReady()` - 检查广告状态
- `AdSDK.getBannerWidget()` - 获取Banner Widget
- `AdSDK.bindViewByTemplateStyle()` - 原生广告模板绑定
- `AdSDK.bindViewByFactoryId()` - 原生广告Factory绑定
- `AdSDK.openAdInspector()` - 打开广告检查器

### Platform Support
- ✅ **Android**: 支持 Android 5.0+ (API level 21+)
- ✅ **iOS**: 支持 iOS 12.0+
- ✅ **Flutter**: 支持 Flutter 3.0.0+
- ✅ **Dart**: 支持 Dart 2.17.0+

### Dependencies
- `google_mobile_ads: ^5.1.0` - Google Mobile Ads SDK
- `flutter: >=3.0.0` - Flutter框架

### Known Issues
- Banner广告在某些设备上可能需要手动刷新
- 原生广告模板样式在不同平台可能有细微差异

### Migration Guide
这是初始版本，无需迁移。

---

## [Unreleased]

### Planned Features
- 🔄 **更多广告平台支持**
  - Meta Audience Network
  - Pangle (字节跳动)
  - AppLovin MAX
  - Mintegral
  - LiftoffMonetize

- 📈 **增强分析功能**
  - 广告收益报表
  - 用户行为分析
  - A/B测试支持

- 🎨 **UI组件库**
  - 预制广告容器组件
  - 自定义广告样式
  - 响应式布局支持

- 🔧 **开发工具增强**
  - 广告调试面板
  - 实时日志查看
  - 性能监控工具
