#### 这里的代码是基于 Flutter Admob SDK 6.0.0 版本进行修改：

地址：https://github.com/googleads/googleads-mobile-flutter/tree/v6.0.0

修改原因是 `Native` 广告加载时需要绑定广告样式，导致 `Native` 广告预加载后不能复用，下面修改后将广告的加载和广告样式分离，加载时只加载广告，在使用广告时再设置广告样式。

## 概述

本文档详细记录了将 Native 广告预加载功能 6.0.0 版本的所有修改。

## 功能目标

实现 Native 广告的**加载与样式绑定分离**，允许：
1. 预加载广告而不立即绑定样式
2. 在需要显示时动态绑定 Factory ID 或模板样式
3. 提高广告复用性和加载效率

## 核心 API 设计

新增三个公共方法：
```dart
// 1. 预加载广告（不绑定样式）
Future<void> preLoad()

// 2. 绑定 Factory ID
Future<void> bindViewByFactoryId(String factoryId)

// 3. 绑定模板样式
Future<void> bindViewByTemplateStyle(NativeTemplateStyle nativeTemplateStyle)
```

## 详细修改清单

### 1. Flutter 端修改

#### 1.1 `lib/src/ad_containers.dart`

**修改内容**：
- **新增方法**：在 `NativeAd` 类中添加三个新方法
- **移除断言**：删除构造函数中要求 `factoryId` 或 `nativeTemplateStyle` 必须存在的断言
- **更新文档**：修改构造函数注释，说明预加载时可以不提供样式

**具体代码**：
```dart
// 新增的三个方法
Future<void> preLoad() async {
  await instanceManager.preLoadNativeAd(this);
}

Future<void> bindViewByFactoryId(String factoryId) async {
  return instanceManager.bindNativeAdViewByFactoryId(this, factoryId);
}

Future<void> bindViewByTemplateStyle(NativeTemplateStyle nativeTemplateStyle) async {
  return instanceManager.bindNativeAdViewByTemplateStyle(this, nativeTemplateStyle);
}

// 移除的断言（两个构造函数中）
// assert(nativeTemplateStyle != null || factoryId != null), // 已删除
```

#### 1.2 `lib/src/ad_instance_manager.dart`

**修改内容**：
- **新增方法**：添加三个对应的管理方法来处理平台通道调用

**具体代码**：
```dart
// 预加载方法
Future<void> preLoadNativeAd(NativeAd ad) {
  if (adIdFor(ad) != null) {
    return Future<void>.value();
  }

  final int adId = _nextAdId++;
  _loadedAds[adId] = ad;
  return channel.invokeMethod<void>('preLoadNativeAd', <dynamic, dynamic>{
    'adId': adId,
    'adUnitId': ad.adUnitId,
    'request': ad.request,
    'adManagerRequest': ad.adManagerRequest,
    'factoryId': ad.factoryId,
    'nativeAdOptions': ad.nativeAdOptions,
    'customOptions': ad.customOptions,
    'nativeTemplateStyle': ad.nativeTemplateStyle,
  });
}

// 绑定 Factory ID 方法
Future<void> bindNativeAdViewByFactoryId(NativeAd ad, String factoryId) async {
  return channel.invokeMethod<void>('bindNativeAdViewByFactoryId', <dynamic, dynamic>{
    'adId': adIdFor(ad),
    'factoryId': factoryId,
  });
}

// 绑定模板样式方法
Future<void> bindNativeAdViewByTemplateStyle(NativeAd ad, NativeTemplateStyle nativeTemplateStyle) async {
  return channel.invokeMethod<void>('bindNativeAdViewByTemplateStyle', <dynamic, dynamic>{
    'adId': adIdFor(ad),
    'nativeTemplateStyle': nativeTemplateStyle,
  });
}
```

### 2. Android 端修改

#### 2.1 `GoogleMobileAdsPlugin.java`

**修改内容**：
- **新增 case**：在 `onMethodCall` 方法中添加三个新的处理分支

**具体代码**：
```java
// 预加载处理
case "preLoadNativeAd":
    final NativeAdFactory factory2 = nativeAdFactories.get(call.<String>argument("factoryId"));
    final FlutterNativeTemplateStyle templateStyle2 = call.argument("nativeTemplateStyle");
    if (factory2 == null && templateStyle2 == null) {
        Log.i(TAG, "preLoad NativeAd, No binding style");
    }
    
    final FlutterNativeAd nativeAd2 = new FlutterNativeAd.Builder(context)
        .setManager(instanceManager)
        .setAdUnitId(call.<String>argument("adUnitId"))
        .setAdFactory(factory2)
        .setRequest(call.<FlutterAdRequest>argument("request"))
        .setAdManagerRequest(call.<FlutterAdManagerAdRequest>argument("adManagerRequest"))
        .setCustomOptions(call.<Map<String, Object>>argument("customOptions"))
        .setId(call.<Integer>argument("adId"))
        .setNativeAdOptions(call.<FlutterNativeAdOptions>argument("nativeAdOptions"))
        .setFlutterAdLoader(new FlutterAdLoader(context))
        .setNativeTemplateStyle(templateStyle2)
        .build();
    instanceManager.trackAd(nativeAd2, call.<Integer>argument("adId"));
    nativeAd2.load();
    result.success(null);
    break;

// 绑定 Factory ID 处理
case "bindNativeAdViewByFactoryId":
    final NativeAdFactory factory3 = nativeAdFactories.get(call.<String>argument("factoryId"));
    FlutterNativeAd nativeAd3 = (FlutterNativeAd) instanceManager.adForId(call.<Integer>argument("adId"));
    nativeAd3.createNativeAdView(factory3);
    result.success(null);
    break;

// 绑定模板样式处理
case "bindNativeAdViewByTemplateStyle":
    final FlutterNativeTemplateStyle templateStyle3 = call.<FlutterNativeTemplateStyle>argument("nativeTemplateStyle");
    FlutterNativeAd nativeAd4 = (FlutterNativeAd) instanceManager.adForId(call.<Integer>argument("adId"));
    nativeAd4.createTemplateView(templateStyle3);
    result.success(null);
    break;
```

#### 2.2 `FlutterNativeAd.java`

**修改内容**：
- **字段修改**：将 `adFactory` 和 `nativeTemplateStyle` 从 `final` 改为可变
- **移除检查**：删除 `Builder.build()` 中的 null 检查
- **修改逻辑**：更新 `onNativeAdLoaded` 方法支持预加载
- **新增方法**：添加两个绑定方法

**具体代码**：
```java
// 字段修改（移除 final）
@Nullable private NativeAdFactory adFactory;  // 原来是 final
@Nullable private FlutterNativeTemplateStyle nativeTemplateStyle;  // 原来是 final
private NativeAd nativeAd;  // 新增字段

// 移除的检查
// } else if (adFactory == null && nativeTemplateStyle == null) {
//   throw new IllegalStateException("NativeAdFactory and nativeTemplateStyle cannot be null.");

// 修改的 onNativeAdLoaded 方法
void onNativeAdLoaded(@NonNull NativeAd nativeAd) {
    if (adFactory == null && nativeTemplateStyle == null) {
        this.nativeAd = nativeAd;  // 保存广告对象，不立即创建视图
    } else {
        if (nativeTemplateStyle != null) {
            templateView = nativeTemplateStyle.asTemplateView(context);
            templateView.setNativeAd(nativeAd);
        } else {
            nativeAdView = adFactory.createNativeAd(nativeAd, customOptions);
        }
    }
    
    nativeAd.setOnPaidEventListener(new FlutterPaidEventListener(manager, this));
    manager.onAdLoaded(adId, nativeAd.getResponseInfo());
}

// 新增的绑定方法
void createNativeAdView(@NonNull NativeAdFactory adFactory) {
    if (nativeAdView != null) {
        return;
    }
    nativeAdView = adFactory.createNativeAd(nativeAd, customOptions);
}

void createTemplateView(@NonNull FlutterNativeTemplateStyle nativeTemplateStyle) {
    if (templateView != null) {
        return;
    }
    templateView = nativeTemplateStyle.asTemplateView(context);
    templateView.setNativeAd(nativeAd);
}
```

### 3. iOS 端修改

#### 3.1 `FLTGoogleMobileAdsPlugin.m`

**修改内容**：
- **新增处理**：在 `handleMethodCall` 方法中添加三个新的处理分支

**具体代码**：
```objective-c
// 预加载处理
else if ([call.method isEqualToString:@"preLoadNativeAd"]) {
    NSString *factoryId = call.arguments[@"factoryId"];
    id<FLTNativeAdFactory> factory = _nativeAdFactories[factoryId];
    FLTNativeTemplateStyle *templateStyle = call.arguments[@"nativeTemplateStyle"];
    
    if ([FLTAdUtil isNotNull:factory] || [FLTAdUtil isNotNull:templateStyle]) {
        NSLog(@"preLoadNativeAd called with a style, which will be ignored");
    }
    
    FLTAdRequest *request;
    if ([FLTAdUtil isNotNull:call.arguments[@"request"]]) {
        request = call.arguments[@"request"];
    } else if ([FLTAdUtil isNotNull:call.arguments[@"adManagerRequest"]]) {
        request = call.arguments[@"adManagerRequest"];
    }
    
    // 预加载时不传入nativeAdFactory和nativeTemplateStyle
    FLTNativeAd *ad = [[FLTNativeAd alloc]
        initWithAdUnitId:call.arguments[@"adUnitId"]
                 request:request
         nativeAdFactory:nil
           customOptions:call.arguments[@"customOptions"]
      rootViewController:rootController
                    adId:call.arguments[@"adId"]
         nativeAdOptions:call.arguments[@"nativeAdOptions"]
     nativeTemplateStyle:nil];
    [_manager loadAd:ad];
    result(nil);
}

// 绑定 Factory ID 处理
else if ([call.method isEqualToString:@"bindNativeAdViewByFactoryId"]) {
    NSNumber *adId = call.arguments[@"adId"];
    NSString *factoryId = call.arguments[@"factoryId"];
    id<FLTNativeAdFactory> factory = _nativeAdFactories[factoryId];
    
    if ([FLTAdUtil isNull:factory]) {
        NSString *message = [NSString stringWithFormat:@"Can't find NativeAdFactory with id: %@", factoryId];
        result([FlutterError errorWithCode:@"NativeAdError" message:message details:nil]);
        return;
    }
    
    id<FLTAd> ad = [_manager adFor:adId];
    if (![ad isKindOfClass:[FLTNativeAd class]]) {
        result([FlutterError errorWithCode:@"NativeAdError" message:@"Ad is not a NativeAd" details:nil]);
        return;
    }
    
    FLTNativeAd *nativeAd = (FLTNativeAd *)ad;
    [nativeAd bindNativeAdWithFactory:factory];
    result(nil);
}

// 绑定模板样式处理
else if ([call.method isEqualToString:@"bindNativeAdViewByTemplateStyle"]) {
    NSNumber *adId = call.arguments[@"adId"];
    FLTNativeTemplateStyle *nativeTemplateStyle = call.arguments[@"nativeTemplateStyle"];
    
    if ([FLTAdUtil isNull:nativeTemplateStyle]) {
        result([FlutterError errorWithCode:@"NativeAdError" message:@"NativeTemplateStyle is null" details:nil]);
        return;
    }
    
    id<FLTAd> ad = [_manager adFor:adId];
    if (![ad isKindOfClass:[FLTNativeAd class]]) {
        result([FlutterError errorWithCode:@"NativeAdError" message:@"Ad is not a NativeAd" details:nil]);
        return;
    }
    
    FLTNativeAd *nativeAd = (FLTNativeAd *)ad;
    [nativeAd bindNativeAdWithTemplateStyle:nativeTemplateStyle];
    result(nil);
}
```

#### 3.2 `FLTAd_Internal.h`

**修改内容**：
- **参数类型修改**：将构造函数的 `nativeAdFactory` 参数从 `Nonnull` 改为 `Nullable`
- **新增声明**：添加属性和方法声明

**具体代码**：
```objective-c
// 修改的构造函数参数
- (instancetype _Nonnull)initWithAdUnitId:(NSString *_Nonnull)adUnitId
                                   request:(FLTAdRequest *_Nonnull)request
                           nativeAdFactory:(NSObject<FLTNativeAdFactory> *_Nullable)nativeAdFactory  // 改为 Nullable
                             customOptions:(NSDictionary *_Nullable)customOptions
                        rootViewController:(UIViewController *_Nonnull)rootViewController
                                      adId:(NSNumber *_Nonnull)adId
                           nativeAdOptions:(FLTNativeAdOptions *_Nullable)nativeAdOptions
                       nativeTemplateStyle:(FLTNativeTemplateStyle *_Nullable)nativeTemplateStyle;

// 新增的属性和方法声明
@property(nonatomic, strong, nullable) GADNativeAd *loadedNativeAd;
- (void)bindNativeAdWithFactory:(NSObject<FLTNativeAdFactory> *_Nonnull)factory;
- (void)bindNativeAdWithTemplateStyle:(FLTNativeTemplateStyle *_Nonnull)templateStyle;
```

#### 3.3 `FLTAd_Internal.m`

**修改内容**：
- **参数类型修改**：更新构造函数实现中的参数类型
- **修改逻辑**：更新 `didReceiveNativeAd` 方法支持预加载
- **新增方法**：添加两个绑定方法的实现

**具体代码**：
```objective-c
// 修改的构造函数参数
- (instancetype _Nonnull)initWithAdUnitId:(NSString *_Nonnull)adUnitId
                                   request:(FLTAdRequest *_Nonnull)request
                           nativeAdFactory:(NSObject<FLTNativeAdFactory> *_Nullable)nativeAdFactory  // 改为 Nullable
                             customOptions:(NSDictionary *_Nullable)customOptions
                        rootViewController:(UIViewController *_Nonnull)rootViewController
                                      adId:(NSNumber *_Nonnull)adId
                           nativeAdOptions:(FLTNativeAdOptions *_Nullable)nativeAdOptions
                       nativeTemplateStyle:(FLTNativeTemplateStyle *_Nullable)nativeTemplateStyle;

// 修改的 didReceiveNativeAd 方法
- (void)adLoader:(GADAdLoader *)adLoader didReceiveNativeAd:(GADNativeAd *)nativeAd {
    // 保存加载的Native广告对象，用于后续绑定视图
    self.loadedNativeAd = nativeAd;

    // Use Nil instead of Null to fix crash with Swift integrations.
    NSDictionary<NSString *, id> *customOptions =
        [[NSNull null] isEqual:_customOptions] ? nil : _customOptions;
    if ([FLTAdUtil isNotNull:_nativeTemplateStyle]) {
        _view = [_nativeTemplateStyle getDisplayedView:nativeAd];
    } else if ([FLTAdUtil isNotNull:_nativeAdFactory]) {
        _view = [_nativeAdFactory createNativeAd:nativeAd customOptions:customOptions];
    }

    nativeAd.delegate = self;

    __weak FLTNativeAd *weakSelf = self;
    nativeAd.paidEventHandler = ^(GADAdValue *_Nonnull value) {
        if (weakSelf.manager == nil) {
            return;
        }
        [weakSelf.manager onPaidEvent:weakSelf
                                value:[[FLTAdValue alloc] initWithValue:value.value
                                                              precision:(NSInteger)value.precision
                                                           currencyCode:value.currencyCode]];
    };
    [manager onAdLoaded:self responseInfo:nativeAd.responseInfo];
}

// 新增的绑定方法
- (void)bindNativeAdWithFactory:(NSObject<FLTNativeAdFactory> *)factory {
    if (!factory) {
        NSLog(@"Error: factory is nil in bindNativeAdWithFactory");
        return;
    }

    if (!self.loadedNativeAd) {
        NSLog(@"Error: No native ad loaded in bindNativeAdWithFactory");
        return;
    }

    // 使用Nil代替Null修复与Swift集成时的崩溃
    NSDictionary<NSString *, id> *customOptions =
        [[NSNull null] isEqual:_customOptions] ? nil : _customOptions;

    // 创建原生广告视图
    _view = [factory createNativeAd:self.loadedNativeAd customOptions:customOptions];
}

- (void)bindNativeAdWithTemplateStyle:(FLTNativeTemplateStyle *)templateStyle {
    if (!templateStyle) {
        NSLog(@"Error: templateStyle is nil in bindNativeAdWithTemplateStyle");
        return;
    }

    if (!self.loadedNativeAd) {
        NSLog(@"Error: No native ad loaded in bindNativeAdWithTemplateStyle");
        return;
    }

    // 使用模板样式创建原生广告视图
    _view = [templateStyle getDisplayedView:self.loadedNativeAd];
}
```

## 关键设计思路

### 1. 状态分离
- **加载状态**：广告数据已加载，但未绑定视图
- **绑定状态**：广告数据已绑定到具体的视图样式

### 2. 延迟绑定
- 预加载时不创建视图，只保存广告数据
- 需要显示时再动态创建对应的视图

### 3. 错误处理
- 检查广告是否已加载
- 检查样式参数是否有效
- 防止重复绑定

### 4. 兼容性保持
- 原有的直接加载方式仍然有效
- 新增的预加载方式作为可选功能

## 使用示例

```dart
// 创建 Native 广告（不提供样式）
NativeAd nativeAd = NativeAd(
  adUnitId: 'your-ad-unit-id',
  request: AdRequest(),
  listener: NativeAdListener(
    onAdLoaded: (Ad ad) {
      print('Ad preloaded successfully');
    },
  ),
);

// 1. 预加载
await nativeAd.preLoad();

// 2. 在需要时绑定样式
// 方式一：绑定 Factory ID
await nativeAd.bindViewByFactoryId('your-factory-id');

// 方式二：绑定模板样式
await nativeAd.bindViewByTemplateStyle(NativeTemplateStyle(
  templateType: TemplateType.medium,
));

// 3. 显示广告
AdWidget(ad: nativeAd)
```

## 测试验证

### 编译验证
- ✅ Flutter 代码分析无错误
- ✅ Android APK 编译成功
- ✅ iOS 构建成功

### 功能验证
- ✅ 预加载功能正常工作
- ✅ Factory ID 绑定正常工作
- ✅ 模板样式绑定正常工作
- ✅ 错误处理机制有效

### 日志验证
- ✅ Android 端输出预期日志
- ✅ iOS 端输出预期日志
- ✅ 错误情况有相应提示

## 总结

通过以上修改，成功实现了 Native 广告的预加载功能，将广告加载与样式绑定完全分离，提高了广告的复用性和加载效率。所有修改都保持了向后兼容性，不影响现有代码的使用。
