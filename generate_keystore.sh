#!/bin/bash

# 🔐 Android应用签名密钥生成脚本
# 用于生成Google Play发布所需的签名密钥

echo "🔐 开始生成Android应用签名密钥..."
echo "================================================"

# 检查是否已存在密钥文件
if [ -f "upload-keystore.jks" ]; then
    echo "⚠️  警告: upload-keystore.jks 已存在!"
    read -p "是否要覆盖现有密钥? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 操作已取消"
        exit 1
    fi
    rm upload-keystore.jks
fi

echo "📝 请按提示输入以下信息:"
echo "   - 密钥库密码 (请记住这个密码!)"
echo "   - 密钥密码 (请记住这个密码!)"
echo "   - 您的姓名"
echo "   - 组织单位 (可以是您的公司名或个人)"
echo "   - 组织名称"
echo "   - 城市"
echo "   - 省份"
echo "   - 国家代码 (如: CN)"
echo ""

# 生成密钥
keytool -genkey -v -keystore upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# 检查是否生成成功
if [ -f "upload-keystore.jks" ]; then
    echo ""
    echo "✅ 密钥生成成功!"
    echo "📁 密钥文件位置: $(pwd)/upload-keystore.jks"
    echo ""
    echo "🔧 接下来需要创建 key.properties 文件:"
    echo "================================================"
    
    # 创建key.properties文件
    cat > android/key.properties << EOF
storePassword=请替换为您的密钥库密码
keyPassword=请替换为您的密钥密码
keyAlias=upload
storeFile=../upload-keystore.jks
EOF
    
    echo "📝 已创建 android/key.properties 文件"
    echo "⚠️  请手动编辑该文件，填入您刚才设置的密码!"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 编辑 android/key.properties 文件，填入正确的密码"
    echo "2. 将 upload-keystore.jks 文件备份到安全位置"
    echo "3. 不要将密钥文件提交到版本控制系统"
    echo ""
    echo "🚀 完成后可以运行以下命令构建发布版本:"
    echo "   flutter build appbundle --release --flavor production"
    
else
    echo "❌ 密钥生成失败!"
    exit 1
fi
