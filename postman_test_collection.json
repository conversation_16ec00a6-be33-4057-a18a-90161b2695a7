{"info": {"name": "WorldTune Radio API - Failed Tests", "description": "后端接口测试失败的请求集合 - 主要是语言代码过滤问题", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "语言代码过滤失败 - 中文", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "bsg", "value": "HGcUOQkoTRlXKjcIGCw2JUV/I3odJxolHG0YChAtcWxWaVo0DiJRDBJ1WVsK", "description": "加密后的参数: {\"language_codes\":[\"zh\"],\"page\":1,\"limit\":10}"}]}, "url": {"raw": "http://**************:8301/api/v1/radio/list", "protocol": "http", "host": ["192", "168", "66", "248"], "port": "8301", "path": ["api", "v1", "radio", "list"]}, "description": "问题: 返回所有50,257个电台，语言过滤无效\n期望: 只返回中文电台"}}, {"name": "多语言代码过滤失败 - 中英文", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "bsg", "value": "HGcUOQkoTRlXKjcIGCw2JUV/I3odJxpUEioGSSpkcSYGIh16XX4UWlwmBQIDamlkVzg=", "description": "加密后的参数: {\"language_codes\":[\"zh\",\"en\"],\"page\":1,\"limit\":20}"}]}, "url": {"raw": "http://**************:8301/api/v1/radio/list", "protocol": "http", "host": ["192", "168", "66", "248"], "port": "8301", "path": ["api", "v1", "radio", "list"]}, "description": "问题: 返回所有电台，多语言过滤无效\n期望: 只返回中文和英文电台"}}, {"name": "组合过滤失败 - 美国英文电台", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "bsg", "value": "HGcUOQkoTRlXKjcIGCw2JUV/I3oCIRolHG0LBAImJyQeGhE8RXUKSgVjShsWLzZ0XXRUegsmVRFEbVJaQjU=", "description": "加密后的参数: {\"language_codes\":[\"en\"],\"country_id\":225,\"page\":1,\"limit\":15}"}]}, "url": {"raw": "http://**************:8301/api/v1/radio/list", "protocol": "http", "host": ["192", "168", "66", "248"], "port": "8301", "path": ["api", "v1", "radio", "list"]}, "description": "问题: 国家过滤生效，但语言过滤无效。返回美国所有语言的电台\n期望: 只返回美国的英文电台"}}, {"name": "空语言代码数组测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "bsg", "value": "HGcUOQkoTRlXKjcIGCw2JUV/IwVLbUgZVypKUUZkcToOKBEsRXUJSE0=", "description": "加密后的参数: {\"language_codes\":[],\"page\":1,\"limit\":10}"}]}, "url": {"raw": "http://**************:8301/api/v1/radio/list", "protocol": "http", "host": ["192", "168", "66", "248"], "port": "8301", "path": ["api", "v1", "radio", "list"]}, "description": "问题: 应该返回所有电台，但需要确认行为一致性\n期望: 返回所有电台（与不传language_codes行为一致）"}}]}