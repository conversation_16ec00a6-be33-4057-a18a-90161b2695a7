package com.example.verygoodcore.flutter_boilerplate

import android.content.Context
import android.telephony.TelephonyManager
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.util.*

/**
 * 设备国家检测插件
 * 
 * 用于获取Android设备的当前国家代码
 */
class DeviceCountryPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "device_country")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "getCountryCode" -> {
                try {
                    val countryCode = getDeviceCountryCode()
                    result.success(countryCode)
                } catch (e: Exception) {
                    result.error("ERROR", "Failed to get country code: ${e.message}", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }

    /**
     * 获取设备国家代码
     * 
     * 尝试多种方式获取设备的国家代码：
     * 1. 从SIM卡获取
     * 2. 从网络运营商获取
     * 3. 从系统语言环境获取
     * 4. 从时区获取
     */
    private fun getDeviceCountryCode(): String? {
        try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            
            // 方法1: 从SIM卡获取国家代码
            val simCountryCode = getCountryFromSim(telephonyManager)
            if (isValidCountryCode(simCountryCode)) {
                return simCountryCode?.uppercase()
            }
            
            // 方法2: 从网络运营商获取国家代码
            val networkCountryCode = getCountryFromNetwork(telephonyManager)
            if (isValidCountryCode(networkCountryCode)) {
                return networkCountryCode?.uppercase()
            }
            
            // 方法3: 从系统语言环境获取国家代码
            val localeCountryCode = getCountryFromLocale()
            if (isValidCountryCode(localeCountryCode)) {
                return localeCountryCode?.uppercase()
            }
            
            // 方法4: 从时区获取国家代码（最后的备用方案）
            val timezoneCountryCode = getCountryFromTimezone()
            if (isValidCountryCode(timezoneCountryCode)) {
                return timezoneCountryCode?.uppercase()
            }
            
            return null
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * 从SIM卡获取国家代码
     */
    private fun getCountryFromSim(telephonyManager: TelephonyManager): String? {
        return try {
            val simCountryIso = telephonyManager.simCountryIso
            if (simCountryIso.isNotEmpty()) {
                simCountryIso
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 从网络运营商获取国家代码
     */
    private fun getCountryFromNetwork(telephonyManager: TelephonyManager): String? {
        return try {
            val networkCountryIso = telephonyManager.networkCountryIso
            if (networkCountryIso.isNotEmpty()) {
                networkCountryIso
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 从系统语言环境获取国家代码
     */
    private fun getCountryFromLocale(): String? {
        return try {
            val locale = Locale.getDefault()
            val countryCode = locale.country
            if (countryCode.isNotEmpty()) {
                countryCode
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 从时区获取国家代码（备用方案）
     */
    private fun getCountryFromTimezone(): String? {
        return try {
            val timeZone = TimeZone.getDefault()
            val timeZoneId = timeZone.id
            
            // 简单的时区到国家代码映射
            val timezoneCountryMap = mapOf(
                "Asia/Shanghai" to "CN",
                "Asia/Hong_Kong" to "HK",
                "Asia/Taipei" to "TW",
                "Asia/Tokyo" to "JP",
                "Asia/Seoul" to "KR",
                "America/New_York" to "US",
                "America/Los_Angeles" to "US",
                "America/Chicago" to "US",
                "America/Denver" to "US",
                "Europe/London" to "GB",
                "Europe/Paris" to "FR",
                "Europe/Berlin" to "DE",
                "Europe/Rome" to "IT",
                "Europe/Madrid" to "ES",
                "Europe/Amsterdam" to "NL",
                "Europe/Stockholm" to "SE",
                "Europe/Oslo" to "NO",
                "Europe/Copenhagen" to "DK",
                "Europe/Helsinki" to "FI",
                "Europe/Zurich" to "CH",
                "Europe/Vienna" to "AT",
                "Europe/Brussels" to "BE",
                "Europe/Warsaw" to "PL",
                "Europe/Prague" to "CZ",
                "Europe/Budapest" to "HU",
                "Europe/Athens" to "GR",
                "Europe/Lisbon" to "PT",
                "Europe/Dublin" to "IE",
                "Australia/Sydney" to "AU",
                "Australia/Melbourne" to "AU",
                "Pacific/Auckland" to "NZ",
                "Asia/Singapore" to "SG",
                "Asia/Bangkok" to "TH",
                "Asia/Kuala_Lumpur" to "MY",
                "Asia/Jakarta" to "ID",
                "Asia/Manila" to "PH",
                "Asia/Ho_Chi_Minh" to "VN",
                "Asia/Kolkata" to "IN",
                "Asia/Dubai" to "AE",
                "Asia/Riyadh" to "SA",
                "Asia/Tehran" to "IR",
                "Asia/Baghdad" to "IQ",
                "Asia/Karachi" to "PK",
                "Asia/Dhaka" to "BD",
                "Africa/Cairo" to "EG",
                "Africa/Johannesburg" to "ZA",
                "Africa/Lagos" to "NG",
                "Africa/Nairobi" to "KE",
                "America/Sao_Paulo" to "BR",
                "America/Argentina/Buenos_Aires" to "AR",
                "America/Santiago" to "CL",
                "America/Bogota" to "CO",
                "America/Lima" to "PE",
                "America/Caracas" to "VE",
                "America/Mexico_City" to "MX",
                "America/Toronto" to "CA",
                "America/Vancouver" to "CA"
            )
            
            timezoneCountryMap[timeZoneId]
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 验证国家代码格式
     */
    private fun isValidCountryCode(countryCode: String?): Boolean {
        return countryCode != null && 
               countryCode.isNotEmpty() && 
               countryCode.length == 2 && 
               countryCode.matches(Regex("[A-Za-z]{2}"))
    }
}
