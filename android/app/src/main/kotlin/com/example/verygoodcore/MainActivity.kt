package com.example.verygoodcore.flutter_boilerplate

import com.ryanheise.audioservice.AudioServiceActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity: AudioServiceActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // 注册设备国家检测插件
        flutterEngine.plugins.add(DeviceCountryPlugin())
    }
}
