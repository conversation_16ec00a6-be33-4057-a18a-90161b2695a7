<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 生产环境API域名配置 -->
    <domain-config>
        <domain includeSubdomains="true">api.knowvidapp.com</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>
    
    <!-- 开发环境配置：允许所有HTTP连接 -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- 本地地址 (包含just_audio代理服务器需要的localhost和127.0.0.1) -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>

        <!-- 您的后端服务器 -->
        <domain includeSubdomains="true">************</domain>

        <!-- 电台服务器域名 -->
        <domain includeSubdomains="true">181.fm</domain>
        <domain includeSubdomains="true">www.181.fm</domain>
        <domain includeSubdomains="true">listen.181fm.com</domain>
        <domain includeSubdomains="true">pulseedm.cdnstream1.com</domain>
        <domain includeSubdomains="true">cdnstream1.com</domain>
        
        <!-- 常见局域网地址段 -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********00</domain>
        <domain includeSubdomains="true">***********01</domain>
        <domain includeSubdomains="true">***********02</domain>
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">***********04</domain>
        <domain includeSubdomains="true">***********05</domain>
        
        <domain includeSubdomains="true">192.168.0.1</domain>
        <domain includeSubdomains="true">192.168.0.100</domain>
        <domain includeSubdomains="true">192.168.0.101</domain>
        <domain includeSubdomains="true">192.168.0.102</domain>
        <domain includeSubdomains="true">192.168.0.103</domain>
        <domain includeSubdomains="true">192.168.0.104</domain>
        <domain includeSubdomains="true">192.168.0.105</domain>
        
        <!-- 10.x.x.x段 -->
        <domain includeSubdomains="true">10.0.0.1</domain>
        <domain includeSubdomains="true">10.1.1.1</domain>
    </domain-config>
    
    <!-- 基础配置：允许HTTP流量（仅开发环境使用） -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config> 