import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../shared/providers/startup_cache_provider.dart';

// 导入服务初始化状态Provider
import '../../../app.dart' show servicesInitializedProvider;

/// 启动页面 - 简洁白色设计
class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _progressController;
  
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _textOpacityAnimation;
  late Animation<Offset> _textSlideAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    // Logo动画控制器
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // 文字动画控制器
    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 进度动画控制器
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Logo缩放动画
    _logoScaleAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Logo透明度动画
    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    // 文字透明度动画
    _textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeIn,
    ));

    // 文字滑入动画
    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutQuart,
    ));

    // 进度动画
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _startSplashSequence() async {
    // 启动Logo动画
    unawaited(_logoController.forward());

    // 延迟启动文字动画
    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      unawaited(_textController.forward());
    }

    // 延迟启动进度动画
    await Future.delayed(const Duration(milliseconds: 200));
    if (mounted) {
      unawaited(_progressController.forward());
    }

    // 等待至少3秒的用户体验时间，确保用户能看到协议链接
    await Future.delayed(const Duration(milliseconds: 3000));

    // 等待服务初始化完成
    await _waitForServicesInitialization();

    if (mounted) {
      context.go('/');
    }
  }

  /// 等待服务初始化完成
  Future<void> _waitForServicesInitialization() async {
    // 检查服务是否已经初始化完成
    if (ref.read(servicesInitializedProvider)) {
      return;
    }

    // 轮询检查服务初始化状态，最多等待5秒
    const checkInterval = Duration(milliseconds: 100);
    const maxWaitTime = Duration(seconds: 5);
    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      if (ref.read(servicesInitializedProvider)) {
        return;
      }
      await Future.delayed(checkInterval);
    }

    print('⚠️ 服务初始化超时，强制跳转');
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  /// 构建动态渐变背景（与主页保持一致）
  LinearGradient _buildDynamicGradient() {
    return const LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color(0xFF0F0F23), // 深蓝黑
        Color(0xFF1A1A2E), // 深紫蓝
        Color(0xFF16213E), // 深蓝
        Color(0xFF0F3460), // 中蓝
      ],
      stops: [0.0, 0.3, 0.7, 1.0],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: _buildDynamicGradient(),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 主要内容区域
              Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo区域
                    _buildLogo(),
                    const SizedBox(height: 40),

                    // 应用名称
                    _buildAppName(),
                    const SizedBox(height: 12),

                    // 标语
                    _buildTagline(),
                    const SizedBox(height: 80),

                    // 加载进度
                    _buildLoadingIndicator(),
                  ],
                ),
              ),
            ),

            // 底部协议链接区域
            _buildBottomPolicyLinks(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建Logo
  Widget _buildLogo() {
    return AnimatedBuilder(
      animation: _logoController,
      builder: (context, child) {
        return Transform.scale(
          scale: _logoScaleAnimation.value,
          child: Opacity(
            opacity: _logoOpacityAnimation.value,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(50),
                child: Image.asset(
                  'assets/app_icon.png',
                  width: 100,
                  height: 100,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建应用名称
  Widget _buildAppName() {
    return AnimatedBuilder(
      animation: _textController,
      builder: (context, child) {
        return SlideTransition(
          position: _textSlideAnimation,
          child: Opacity(
            opacity: _textOpacityAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF00FFFF).withOpacity(0.5),
                    blurRadius: 20,
                    offset: const Offset(0, 0),
                    spreadRadius: 5,
                  ),
                  BoxShadow(
                    color: const Color(0xFF00FFFF).withOpacity(0.3),
                    blurRadius: 40,
                    offset: const Offset(0, 0),
                    spreadRadius: 10,
                  ),
                ],
              ),
              child: ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [
                    Color(0xFF00FFFF), // 霓虹青色
                    Color(0xFFFFFFFF), // 白色
                    Color(0xFF00FFFF), // 霓虹青色
                  ],
                  stops: [0.0, 0.5, 1.0],
                ).createShader(bounds),
                child: Text(
                  'WorldTune',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 2.0,
                    fontSize: 36,
                    shadows: [
                      Shadow(
                        color: const Color(0xFF00FFFF).withOpacity(0.8),
                        blurRadius: 10,
                        offset: const Offset(0, 0),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建标语
  Widget _buildTagline() {
    return AnimatedBuilder(
      animation: _textController,
      builder: (context, child) {
        return SlideTransition(
          position: _textSlideAnimation,
          child: Opacity(
            opacity: _textOpacityAnimation.value * 0.8,
            child: Text(
              'app_tagline'.tr(),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white.withOpacity(0.8),
                letterSpacing: 0.8,
                fontSize: 16,
                shadows: [
                  Shadow(
                    color: const Color(0xFF00FFFF).withOpacity(0.3),
                    blurRadius: 5,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return AnimatedBuilder(
      animation: _progressController,
      builder: (context, child) {
        return Column(
          children: [
            // 霓虹风格进度条
            Container(
              width: 120,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(2),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 0.5,
                ),
              ),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  width: 120 * _progressAnimation.value,
                  height: 4,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFF00FFFF), // 霓虹青色
                        Color(0xFFFFFFFF), // 白色
                        Color(0xFF00FFFF), // 霓虹青色
                      ],
                      stops: [0.0, 0.5, 1.0],
                    ),
                    borderRadius: BorderRadius.circular(2),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF00FFFF).withOpacity(0.5),
                        blurRadius: 8,
                        offset: const Offset(0, 0),
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),

            // 加载文字 - 霓虹风格
            Text(
              'loading_text'.tr(),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.white.withOpacity(0.7),
                letterSpacing: 1.0,
                shadows: [
                  Shadow(
                    color: const Color(0xFF00FFFF).withOpacity(0.3),
                    blurRadius: 3,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建底部协议链接区域
  Widget _buildBottomPolicyLinks() {
    return AnimatedBuilder(
      animation: _logoController,
      builder: (context, child) {
        return Opacity(
          opacity: _logoOpacityAnimation.value * 0.9,
          child: Container(
            padding: const EdgeInsets.only(bottom: 24, left: 24, right: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 协议链接行
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildPolicyLink(
                      text: 'privacy_policy'.tr(),
                      onTap: () => _openPolicyUrl('https://sites.google.com/view/worldtune-privacypolicy'),
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 12),
                      width: 1,
                      height: 12,
                      color: Colors.white.withOpacity(0.3),
                    ),
                    _buildPolicyLink(
                      text: 'terms_of_service'.tr(),
                      onTap: () => _openPolicyUrl('https://sites.google.com/view/worldtune-terms-of-user'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // 版权信息
                Text(
                  '© 2025 WorldTune. ${'all_rights_reserved'.tr()}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建单个协议链接
  Widget _buildPolicyLink({
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: const Color(0xFF00FFFF).withOpacity(0.3),
            width: 1.0,
          ),
          color: Colors.white.withOpacity(0.05),
        ),
        child: Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: const Color(0xFF00FFFF),
            fontSize: 11,
            fontWeight: FontWeight.w500,
            decoration: TextDecoration.underline,
            decorationColor: const Color(0xFF00FFFF).withOpacity(0.6),
          ),
        ),
      ),
    );
  }

  /// 打开协议链接
  Future<void> _openPolicyUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        debugPrint('无法打开链接: $url');
      }
    } catch (e) {
      debugPrint('打开链接时出错: $e');
    }
  }
}
