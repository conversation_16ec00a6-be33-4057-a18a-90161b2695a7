# SplashPage - 启动页面组件

现代化的应用启动页面，提供优雅的加载体验和协议链接访问。

## 📋 功能概述

### 核心功能
- **动画启动序列**: 精心设计的Logo、文字和进度动画
- **自动跳转**: 3秒后自动跳转到主页面
- **协议链接**: 底部提供用户协议和隐私协议的快速访问
- **国际化支持**: 完全支持中英文切换
- **响应式设计**: 适配不同屏幕尺寸

### 动画效果
- **Logo动画**: 弹性缩放和透明度渐变
- **文字动画**: 滑入效果和透明度渐变
- **进度动画**: 平滑的进度条加载效果
- **协议链接**: 跟随文字动画的透明度变化

## 🎨 设计特性

### 视觉设计
- **简洁白色背景**: 干净现代的视觉风格
- **渐变Logo阴影**: 主题色渐变的阴影效果
- **渐变文字**: ShaderMask实现的渐变文字效果
- **精致协议链接**: 带边框的小巧链接按钮
- **版权信息**: 底部显示版权声明

### 布局结构
- **居中主内容**: Logo、标题、标语、进度条垂直居中
- **底部协议区**: 协议链接和版权信息固定在底部
- **合理间距**: 各元素间距经过精心调整

## 🔧 技术实现

### 依赖项
```yaml
dependencies:
  flutter: sdk
  flutter_riverpod: ^2.5.1
  go_router: ^14.2.7
  easy_localization: ^3.0.7+1
  url_launcher: ^6.2.4
```

### 动画控制器
- **_logoController**: 控制Logo的缩放和透明度动画
- **_textController**: 控制文字的滑入和透明度动画
- **_progressController**: 控制进度条的加载动画

### 时序安排
1. **0ms**: Logo动画开始
2. **300ms**: 文字动画开始
3. **500ms**: 进度动画开始
4. **3000ms**: 自动跳转到主页

## 📱 协议链接功能

### 链接配置
- **隐私协议**: https://sites.google.com/view/worldtune-privacypolicy
- **用户协议**: https://sites.google.com/view/worldtune-terms-of-user

### 设计特点
- **小巧按钮**: 带边框的紧凑设计
- **分隔线**: 两个链接间的视觉分隔
- **外部打开**: 自动在外部浏览器中打开
- **错误处理**: 链接打开失败的安全处理

## 🌍 国际化支持

### 语言文件配置
```json
// assets/lang/zh.json
{
  "app_tagline": "发现世界的声音",
  "loading_text": "正在加载...",
  "privacy_policy": "隐私协议",
  "terms_of_service": "用户协议",
  "all_rights_reserved": "保留所有权利"
}

// assets/lang/en.json
{
  "app_tagline": "Discover the World's Sound",
  "loading_text": "Loading...",
  "privacy_policy": "Privacy Policy",
  "terms_of_service": "Terms of Service",
  "all_rights_reserved": "All rights reserved"
}
```

## 🚀 使用方法

### 路由配置
```dart
// 在app_router.dart中配置
GoRoute(
  path: '/splash',
  name: 'splash',
  builder: (context, state) => const SplashPage(),
),
```

### 自动跳转
```dart
// 启动页面会自动跳转到主页
context.go('/');
```

## 🔒 错误处理

### 链接打开失败
```dart
try {
  final uri = Uri.parse(url);
  if (await canLaunchUrl(uri)) {
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  } else {
    debugPrint('无法打开链接: $url');
  }
} catch (e) {
  debugPrint('打开链接时出错: $e');
}
```

## 🚀 最新更新

### v2.0.0 (2025-01-18)
- ✅ 添加底部协议链接功能
- ✅ 延长启动时间到3秒
- ✅ 优化协议链接的视觉设计
- ✅ 添加版权信息显示
- ✅ 完善错误处理机制
- ✅ 支持外部链接打开

### v1.0.0 (初始版本)
- ✅ 基础启动页面功能
- ✅ 动画效果实现
- ✅ 自动跳转功能
- ✅ 国际化支持

## 📁 相关文件
- `lib/src/features/splash/splash_page.dart` - 主组件文件
- `lib/src/shared/router/app_router.dart` - 路由配置
- `assets/lang/zh.json` - 中文语言文件
- `assets/lang/en.json` - 英文语言文件

## 🔮 未来扩展

### 可能的功能扩展
- 添加启动页面主题切换
- 支持自定义启动时长
- 添加网络状态检查
- 支持启动页面广告展示
- 添加启动页面配置选项
