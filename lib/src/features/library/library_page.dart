import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../shared/models/models.dart';
import '../../shared/services/storage_service.dart';
import '../../shared/services/audio_service.dart';
import '../../shared/widgets/import_favorites_dialog.dart';
import '../../shared/utils/station_image_builder.dart';
import '../../shared/utils/floating_elements_helper.dart';

/// 资料库页面 - 收藏和历史记录
class LibraryPage extends ConsumerStatefulWidget {
  const LibraryPage({super.key});

  @override
  ConsumerState<LibraryPage> createState() => _LibraryPageState();
}

class _LibraryPageState extends ConsumerState<LibraryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 构建动态渐变背景（与主页保持一致）
  LinearGradient _buildDynamicGradient() {
    return const LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color(0xFF0F0F23), // 深蓝黑
        Color(0xFF1A1A2E), // 深紫蓝
        Color(0xFF16213E), // 深蓝
        Color(0xFF0F3460), // 中蓝
      ],
      stops: [0.0, 0.3, 0.7, 1.0],
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: _buildDynamicGradient(),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 自定义Tab栏设计 - 霓虹风格
              Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF1A1A2E).withOpacity(0.8),
                      const Color(0xFF16213E).withOpacity(0.6),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF00FFFF).withOpacity(0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF00FFFF).withOpacity(0.1),
                      blurRadius: 15,
                      offset: const Offset(0, 0),
                      spreadRadius: 2,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Tab栏
                    TabBar(
                controller: _tabController,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF00FFFF).withOpacity(0.8),
                      const Color(0xFF00FFFF).withOpacity(0.6),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF00FFFF).withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white.withOpacity(0.6),
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
                dividerColor: Colors.transparent,
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.favorite_rounded, size: 20),
                        const SizedBox(width: 8),
                        Text('favorites'.tr()),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.history_rounded, size: 20),
                        const SizedBox(width: 8),
                        Text('recent'.tr()),
                      ],
                    ),
                  ),
                ],
                    ),
                  ],
                ),
              ),

            // Tab内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildFavoritesTab(),
                  _buildRecentTab(),
                ],
              ),
            ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFavoritesTab() {
    final favoritesAsync = ref.watch(favoriteStationsProvider);

    return favoritesAsync.when(
      data: (favorites) {
        if (favorites.isEmpty) {
          return Column(
            children: [
              Expanded(
                child: _buildEmptyState(
                  icon: Icons.favorite_border_rounded,
                  title: 'no_favorites'.tr(),
                  subtitle: 'favorites_hint'.tr(),
                ),
              ),
              _buildActionButtons(),
            ],
          );
        }
        return Column(
          children: [
            Expanded(
              child: _buildStationsList(favorites, isRecentTab: false),
            ),
            _buildActionButtons(),
          ],
        );
      },
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildEmptyState(
        icon: Icons.error_outline_rounded,
        title: 'error'.tr(),
        subtitle: 'load_favorites_failed'.tr(),
        actionText: 'retry'.tr(),
        onAction: () {
          ref.invalidate(favoriteStationsProvider);
        },
      ),
    );
  }

  Widget _buildRecentTab() {
    final recentAsync = ref.watch(recentStationsProvider);

    return recentAsync.when(
      data: (recent) {
        // 过滤掉没有有效URL的电台
        final playableStations = recent.where((station) => station.url.isNotEmpty).toList();
        
        if (playableStations.isEmpty) {
          return _buildEmptyState(
            icon: Icons.radio_rounded,
            title: 'no_recent_plays'.tr(),
            subtitle: 'recent_hint'.tr(),
          );
        }
        return _buildStationsList(playableStations, isRecentTab: true);
      },
      loading: () => _buildLoadingState(),
      error: (error, stack) => _buildEmptyState(
        icon: Icons.error_outline_rounded,
        title: 'error'.tr(),
        subtitle: 'load_recent_failed'.tr(),
        actionText: 'retry'.tr(),
        onAction: () {
          ref.read(recentStationsProvider.notifier).refresh();
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'loading'.tr(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    String? actionText,
    VoidCallback? onAction,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标容器
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 64,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            
            // 标题
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            
            // 副标题
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            // 操作按钮
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: 32),
              FilledButton.tonal(
                onPressed: onAction,
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(actionText),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStationsList(List<StationSimple> stations, {required bool isRecentTab}) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Consumer(
      builder: (context, ref, child) {
        // 计算动态底部边距
        final floatingHeight = FloatingElementsHelper.getFloatingElementsHeight(context, ref);
        
        return ListView.builder(
          padding: EdgeInsets.fromLTRB(16, 8, 16, floatingHeight + 8),
          itemCount: stations.length,
          itemBuilder: (context, index) {
        final station = stations[index];
        
        return AnimatedContainer(
          duration: Duration(milliseconds: 200 + (index * 50)),
          curve: Curves.easeOutBack,
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Material(
              color: colorScheme.surface,
              elevation: 1,
              shadowColor: colorScheme.shadow.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () => isRecentTab 
                    ? _playHistoryStation(station) 
                    : _playFavoriteStation(station),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // 电台图标
                      _buildStationAvatar(station, colorScheme),
                      const SizedBox(width: 16),
                      
                      // 电台信息
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              station.name,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: colorScheme.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on_outlined,
                                  size: 14,
                                  color: colorScheme.onSurfaceVariant,
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    station.country.isNotEmpty ? station.country : 'unknown'.tr(),
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                            
                            // 最近播放时间（仅在最近播放标签显示）
                            if (isRecentTab && station.lastPlayed != null) ...[
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.schedule_rounded,
                                    size: 14,
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _formatPlayTime(station.lastPlayed!),
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                      
                      // 操作按钮
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 收藏按钮
                          _buildFavoriteButton(station, colorScheme),
                          const SizedBox(width: 8),
                          
                          // 播放按钮
                          Container(
                            decoration: BoxDecoration(
                              color: colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              onPressed: () => isRecentTab 
                                  ? _playHistoryStation(station) 
                                  : _playFavoriteStation(station),
                              icon: Icon(
                                Icons.play_arrow_rounded,
                                color: colorScheme.onPrimary,
                              ),
                              padding: const EdgeInsets.all(8),
                              constraints: const BoxConstraints(
                                minWidth: 40,
                                minHeight: 40,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
      },
    );
  }

  Widget _buildStationAvatar(StationSimple station, ColorScheme colorScheme) {
    return StationImageBuilder.buildStationImage(
      station: station,
      width: 56,
      height: 56,
      fit: BoxFit.cover,
      borderRadius: BorderRadius.circular(12),
    );
  }

  /// 构建导入导出按钮
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            icon: Icons.share_rounded,
            label: 'export_favorites'.tr(),
            onTap: _exportFavorites,
          ),
          _buildActionButton(
            icon: Icons.download_rounded,
            label: 'import_favorites'.tr(),
            onTap: _importFavorites,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteButton(StationSimple station, ColorScheme colorScheme) {
    return FutureBuilder<bool>(
      future: ref.read(storageServiceProvider).isFavorite(station.id),
      builder: (context, snapshot) {
        final isFavorite = snapshot.data ?? false;
        
        return IconButton(
          onPressed: () => _toggleFavorite(station, isFavorite),
          icon: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: Icon(
              isFavorite ? Icons.favorite_rounded : Icons.favorite_border_rounded,
              key: ValueKey(isFavorite),
              color: isFavorite ? Colors.red[400] : colorScheme.onSurfaceVariant,
            ),
          ),
          padding: const EdgeInsets.all(8),
          constraints: const BoxConstraints(
            minWidth: 40,
            minHeight: 40,
          ),
        );
      },
    );
  }

  String _formatPlayTime(DateTime playTime) {
    final now = DateTime.now();
    final difference = now.difference(playTime);
    
    if (difference.inMinutes < 1) {
      return 'just_now'.tr();
    } else if (difference.inHours < 1) {
      return '${'minutes_ago'.tr().replaceAll('{count}', difference.inMinutes.toString())}';
    } else if (difference.inDays < 1) {
      return '${'hours_ago'.tr().replaceAll('{count}', difference.inHours.toString())}';
    } else if (difference.inDays < 7) {
      return '${'days_ago'.tr().replaceAll('{count}', difference.inDays.toString())}';
    } else {
      return DateFormat('MM/dd HH:mm').format(playTime);
    }
  }

  Future<void> _playStation(StationSimple station) async {
    final audioService = ref.read(audioServiceProvider);
    await audioService.playStation(station);
  }

  /// 播放收藏列表中的电台（直接播放版）
  /// 
  /// 功能实现: 直接播放电台，播放失败时自动验证和处理
  /// 优化策略: 先播放，失败时才验证，避免不必要的延迟
  /// 实现方案: 直接构建播放列表并播放，AudioService处理失败验证
  /// 影响范围: library_page.dart
  /// 实现日期: 2025-01-27
  Future<void> _playFavoriteStation(StationSimple station) async {
    try {
      // 🔧 清理雷达推荐上下文，确保使用正确的播放模式
      ref.read(radarRecommendationContextProvider.notifier).state = null;
      
      // 直接构建播放列表并播放，让AudioService处理失败情况
      await _buildAndPlayFavoritesList(station);
    } catch (e) {
      print('❌ 播放收藏电台失败: $e');
      // 降级处理：直接播放原电台
      await _playStation(station);
    }
  }

  /// 构建并播放收藏列表
  /// 
  /// 功能实现: 获取最新收藏列表，构建播放上下文并播放
  /// 实现方案: 重新获取收藏列表（可能已更新），查找当前电台位置
  /// 影响范围: library_page.dart
  /// 实现日期: 2025-01-27
  Future<void> _buildAndPlayFavoritesList(StationSimple station) async {
    try {
      // 重新获取收藏列表（可能在验证过程中有更新）
      final favorites = await ref.read(favoriteStationsProvider.future);
      
      if (favorites.isEmpty) {
        await _playStation(station);
        return;
      }

      final currentIndex = favorites.indexWhere((s) => s.id == station.id);
      if (currentIndex == -1) {
        await _playStation(station);
        return;
      }

      final playlistContext = PlaylistContext(
        sourceType: PlaylistSourceType.favorites,
        stations: favorites,
        currentIndex: currentIndex,
        sourceTitle: 'favorites'.tr(),
      );

      final audioService = ref.read(audioServiceProvider);
      await audioService.playStationWithPlaylist(
        station: station,
        playlistContext: playlistContext,
      );
    } catch (e) {
      print('❌ 构建收藏播放列表失败: $e');
      await _playStation(station);
    }
  }

  /// 播放历史列表中的电台（直接播放版）
  /// 
  /// 功能实现: 直接播放电台，播放失败时自动验证和处理
  /// 优化策略: 先播放，失败时才验证，避免不必要的延迟
  /// 实现方案: 直接构建播放列表并播放，AudioService处理失败验证
  /// 影响范围: library_page.dart
  /// 实现日期: 2025-01-27
  Future<void> _playHistoryStation(StationSimple station) async {
    try {
      // 🔧 清理雷达推荐上下文，确保使用正确的播放模式
      ref.read(radarRecommendationContextProvider.notifier).state = null;
      
      // 直接构建播放列表并播放，让AudioService处理失败情况
      await _buildAndPlayHistoryList(station);
    } catch (e) {
      print('❌ 播放历史电台失败: $e');
      // 降级处理：直接播放原电台
      await _playStation(station);
    }
  }

  /// 构建并播放历史列表
  /// 
  /// 功能实现: 获取最新历史列表，构建播放上下文并播放
  /// 实现方案: 重新获取历史列表（可能已更新），查找当前电台位置
  /// 影响范围: library_page.dart
  /// 实现日期: 2025-01-27
  Future<void> _buildAndPlayHistoryList(StationSimple station) async {
    try {
      // 重新获取历史列表（可能在验证过程中有更新）
      final historyList = await ref.read(playHistoryProvider.future);
      
      if (historyList.isEmpty) {
        await _playStation(station);
        return;
      }

      // 将PlayHistory转换为StationSimple列表
      final stations = historyList.map((h) => StationSimple(
        id: h.stationId,
        name: h.stationName,
        url: h.stationUrl ?? '',
        country: h.stationCountry ?? '',
        favicon: h.stationFavicon ?? '',
        language: h.stationLanguage ?? '',
      )).toList();

      final currentIndex = stations.indexWhere((s) => s.id == station.id);
      if (currentIndex == -1) {
        await _playStation(station);
        return;
      }

      final playlistContext = PlaylistContext(
        sourceType: PlaylistSourceType.playHistory,
        stations: stations,
        currentIndex: currentIndex,
        sourceTitle: 'play_history'.tr(),
      );

      final audioService = ref.read(audioServiceProvider);
      await audioService.playStationWithPlaylist(
        station: station,
        playlistContext: playlistContext,
      );
    } catch (e) {
      print('❌ 构建历史播放列表失败: $e');
      await _playStation(station);
    }
  }

  Future<void> _toggleFavorite(StationSimple station, bool currentIsFavorite) async {
    final storageService = ref.read(storageServiceProvider);
    
    if (currentIsFavorite) {
      await storageService.removeFromFavorites(station.id);
    } else {
      await storageService.addToFavorites(station);
    }
    
    // 刷新收藏列表
    ref.invalidate(favoriteStationsProvider);
    setState(() {}); // 刷新UI
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color(0xFF00FFFF).withOpacity(0.3),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              colors: [
                const Color(0xFF1A1A2E).withOpacity(0.6),
                const Color(0xFF16213E).withOpacity(0.4),
              ],
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18,
                color: const Color(0xFF00FFFF),
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 导出收藏列表
  Future<void> _exportFavorites() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      final shareText = await storageService.exportFavorites();
      
      if (shareText == null) {
        _showMessage('no_favorites_to_export'.tr());
        return;
      }

      // 复制到剪贴板
      await Clipboard.setData(ClipboardData(text: shareText));
      _showMessage('export_success'.tr());
    } catch (e) {
      _showMessage('export_failed'.tr());
    }
  }

  /// 显示导入对话框
  Future<void> _importFavorites() async {
    await showDialog<void>(
      context: context,
      builder: (context) => const ImportFavoritesDialog(),
    );
  }

  /// 显示消息提示
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        backgroundColor: const Color(0xFF1A1A2E),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示简短的状态提示
  /// 
  /// 功能实现: 显示简短的1.5秒消息提示，用于电台失效等快速反馈
  /// 实现方案: 使用较短的显示时间和简洁样式
  /// 影响范围: library_page.dart - 用户反馈
  /// 实现日期: 2025-01-27
  void _showBriefMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.orange.shade300,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              message,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.orange.shade600.withOpacity(0.9),
        duration: const Duration(milliseconds: 1500), // 更短的显示时间
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: const EdgeInsets.only(
          bottom: 100,
          left: 16,
          right: 16,
        ),
      ),
    );
  }
}
