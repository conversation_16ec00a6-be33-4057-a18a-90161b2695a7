import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../shared/models/models.dart';
import '../../shared/providers/radio_providers.dart';
import '../../shared/providers/country_provider.dart';
import '../../shared/providers/state_provider.dart';
import '../../shared/services/audio_service.dart';
import '../../shared/utils/station_image_builder.dart';
import '../../shared/widgets/state_selector.dart';
import '../../shared/utils/floating_elements_helper.dart';

/// 探索页面 - 搜索和发现电台
class ExplorePage extends ConsumerStatefulWidget {
  const ExplorePage({super.key});

  @override
  ConsumerState<ExplorePage> createState() => _ExplorePageState();
}

class _ExplorePageState extends ConsumerState<ExplorePage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  
  // 搜索和筛选状态
  String _searchQuery = '';
  final Set<String> _selectedTagIds = {};
  String _sortBy = 'clickcount'; // 默认按点击量排序
  bool _showFilters = false;
  bool _showTagSelection = false; // 控制tag选择区域的展开/折叠
  
  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // 缓存数据
  List<RadioTag>? _cachedTags;
  
  // 控制器
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // 🔧 添加 StateSelector 的强制刷新机制
  Key _stateSelectorKey = UniqueKey();
  
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadInitialData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 加载标签数据
      ref.refresh(radioTagsProvider);
      
      // 确保热门电台数据被加载
      final popularState = ref.read(popularRadiosProvider);
      final selectedCountry = ref.read(selectedCountryProvider);
      final selectedState = ref.read(selectedStateProvider);
      
      if (popularState.stations.isEmpty && !popularState.isLoading) {
        ref.read(popularRadiosProvider.notifier).loadPopularRadios(
          countryId: selectedCountry?.id,
          stateName: selectedState?.stateName,
          refresh: true,
        );
      }
    });
  }

  void _performSearch() {
    final selectedCountry = ref.read(selectedCountryProvider);
    final selectedState = ref.read(selectedStateProvider);

    print('🔍🔍🔍 _performSearch 开始执行 - 累加式筛选');
    print('🔍 === 当前筛选条件分析 ===');
    print('🔍 1. 国家筛选: ${selectedCountry?.name} (ID: ${selectedCountry?.id}) ${selectedCountry != null ? '✅' : '❌'}');
    print('🔍 2. 地区筛选: ${selectedState?.stateName ?? "null"} ${selectedState != null ? '✅' : '❌ (全部地区)'}');
    print('🔍 3. 标签筛选: $_selectedTagIds ${_selectedTagIds.isNotEmpty ? '✅' : '❌ (全部标签)'}');
    print('🔍 4. 关键词搜索: "${_searchQuery.trim()}" ${_searchQuery.trim().isNotEmpty ? '✅' : '❌ (无关键词)'}');
    print('🔍 5. 排序方式: $_sortBy');

    // 如果没有选择国家，不执行搜索（因为需要在某个国家范围内搜索）
    if (selectedCountry == null) {
      print('🔍 ❌ 没有选择国家，无法执行搜索');
      return;
    }
    
    // 将选中的tag ID字符串转换为整数列表
    final tagIntIds = _selectedTagIds
        .map((id) => int.tryParse(id))
        .where((id) => id != null)
        .cast<int>()
        .toList();
    
    print('🔍 转换后的标签IDs: $tagIntIds');
    print('🔍 === 最终API调用参数 ===');
    print('🔍   - keyword: "${_searchQuery.trim()}" ${_searchQuery.trim().isEmpty ? '(空-不限制关键词)' : '(筛选关键词)'}');
    print('🔍   - countryId: ${selectedCountry?.id} (必须-国家范围)');
    print('🔍   - state: "${selectedState?.stateName}" ${selectedState == null ? '(null-全部地区)' : '(指定地区)'}');
    print('🔍   - tagIds: $tagIntIds ${tagIntIds.isEmpty ? '(空-全部标签)' : '(指定标签)'}');
    print('🔍   - sortBy: $_sortBy');
    print('🔍   - refresh: true');
    print('🔍 ⚠️ 累加式筛选：国家(必须) + 地区(可选) + 标签(可选) + 关键词(可选)');
    
    // 使用修正后的搜索方法
    ref.read(searchRadiosProvider.notifier).searchRadios(
      _searchQuery.trim(),
      countryId: selectedCountry?.id,
      state: selectedState?.stateName, // null值会被正确传递，表示不筛选省州
      tagIds: tagIntIds.isNotEmpty ? tagIntIds : null,
      sortBy: _sortBy,
      refresh: true,
    );
  }

  void _clearSearch() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
    });
  }

  /// 调试方法：验证地区选择状态
  void _debugStateSelection() {
    final selectedState = ref.read(selectedStateProvider);
    final stateSelectionState = ref.read(stateSelectionProvider);
    final selectedCountry = ref.read(selectedCountryProvider);

    print('🔍 === 地区选择状态调试 ===');
    print('🔍 selectedStateProvider: ${selectedState?.stateName ?? 'null'}');
    print('🔍 stateSelectionState.selectedState: ${stateSelectionState.selectedState?.stateName ?? 'null'}');
    print('🔍 selectedCountry: ${selectedCountry?.name}');
    print('🔍 availableStates count: ${stateSelectionState.availableStates.length}');
    print('🔍 ========================');
  }

  /// 清除所有筛选条件 - 重新实现版本
  void _clearAllFilters() async {
    print('🧹🧹🧹 开始清除所有筛选条件 - 重新实现版本');
    print('🧹 清除前状态 - 搜索词: "${_searchQuery}", 标签: $_selectedTagIds');
    print('🧹 清除前地区状态: ${ref.read(selectedStateProvider)?.stateName ?? 'null'}');

    // 调试：清除前的状态
    _debugStateSelection();

    // 第一步：清除本地状态
    setState(() {
      _selectedTagIds.clear();
      _showTagSelection = false;
      _searchQuery = '';
      _searchController.clear();
    });
    print('🧹 ✅ 本地状态已清除');

    // 第二步：清除地区选择 - 使用更可靠的方法
    print('🧹 开始清除地区选择：调用 selectState(null)');
    try {
      await ref.read(stateSelectionProvider.notifier).selectState(null);
      print('🧹 ✅ selectState(null) 调用完成');

      // 立即验证状态是否真的更新了
      await _verifyStateCleared();

    } catch (e) {
      print('🧹 ❌ selectState(null) 调用失败: $e');
    }

    // 第三步：强制刷新所有相关的 UI 组件
    if (mounted) {
      setState(() {
        // 强制触发 ExplorePage 的 UI 重建
        // 🔧 强制刷新 StateSelector 组件
        _stateSelectorKey = UniqueKey();
      });
    }

    // 第四步：使用多重回调确保状态同步
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _finalizeStateClear();
    });

    // 额外的延迟回调，确保状态完全同步
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _finalizeStateClear();
      }
    });
  }

  /// 验证状态是否真的被清除了
  Future<void> _verifyStateCleared() async {
    print('🧹 🔍 开始验证状态清除...');

    // 等待一个 frame 确保状态更新
    await Future.delayed(const Duration(milliseconds: 50));

    final currentState = ref.read(selectedStateProvider);
    final stateSelectionState = ref.read(stateSelectionProvider);

    print('🧹 🔍 状态验证结果：');
    print('🧹   - selectedStateProvider: ${currentState?.stateName ?? 'null'}');
    print('🧹   - stateSelectionProvider.selectedState: ${stateSelectionState.selectedState?.stateName ?? 'null'}');

    if (currentState != null || stateSelectionState.selectedState != null) {
      print('🧹 ⚠️ 状态清除可能失败，尝试强制清除...');

      // 强制清除状态
      try {
        await ref.read(stateSelectionProvider.notifier).selectState(null);
        await Future.delayed(const Duration(milliseconds: 50));

        final retryState = ref.read(selectedStateProvider);
        print('🧹 🔍 强制清除后的状态: ${retryState?.stateName ?? 'null'}');
      } catch (e) {
        print('🧹 ❌ 强制清除失败: $e');
      }
    } else {
      print('🧹 ✅ 状态清除验证成功');
    }
  }

  /// 完成状态清除的最终步骤
  void _finalizeStateClear() {
    final finalState = ref.read(selectedStateProvider);
    print('🧹 🏁 最终状态验证：');
    print('🧹   - finalState: ${finalState?.stateName ?? 'null'}');

    // 调试：清除后的状态
    _debugStateSelection();

    print('🧹 🏁 即将调用 _refreshDataAfterStateChange() 来刷新数据');
    _refreshDataAfterStateChange();
  }

  /// 省州选择变化后刷新数据
  void _refreshDataAfterStateChange() {
    final selectedCountry = ref.read(selectedCountryProvider);
    final selectedState = ref.read(selectedStateProvider);

    print('🔄 省州选择变化，刷新数据 - 国家: ${selectedCountry?.name}, 省州: ${selectedState?.stateName ?? "全部地区"}');
    print('🔄 当前筛选条件 - 搜索词: "${_searchQuery}", 标签: $_selectedTagIds, 地区: ${selectedState?.stateName ?? "null"}');

    // 修复：只要选择了国家，就执行搜索（累加式筛选逻辑）
    // 这样可以支持以下场景：
    // 1. 只选国家 → 搜索该国家所有电台
    // 2. 国家+标签 → 搜索该国家指定标签的电台
    // 3. 国家+地区 → 搜索该国家指定地区的电台
    // 4. 国家+标签+地区 → 搜索该国家指定地区指定标签的电台
    // 5. 国家+标签+地区+关键词 → 在上述基础上再加关键词筛选
    if (selectedCountry != null) {
      print('🔄 选择了国家，执行累加式搜索');
      _performSearch();
    } else {
      print('🔄 没有选择国家，显示默认内容');
      // 只有在没有选择国家时，才显示默认内容（可以是热门电台或空状态）
      // 这里可以根据需要调整默认行为
    }
  }

  /// 构建动态渐变背景（与主页保持一致）
  LinearGradient _buildDynamicGradient() {
    return const LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color(0xFF0F0F23), // 深蓝黑
        Color(0xFF1A1A2E), // 深紫蓝
        Color(0xFF16213E), // 深蓝
        Color(0xFF0F3460), // 中蓝
      ],
      stops: [0.0, 0.3, 0.7, 1.0],
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    // 监听标签数据
    ref.listen(radioTagsProvider, (previous, next) {
      next.whenData((tags) {
        if (_cachedTags == null) {
          setState(() {
            _cachedTags = tags;
          });
        }
      });
    });

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: _buildDynamicGradient(),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: RefreshIndicator(
          onRefresh: () async {
            final selectedCountry = ref.read(selectedCountryProvider);
            final selectedState = ref.read(selectedStateProvider);
            ref.refresh(radioTagsProvider);
            await ref.read(popularRadiosProvider.notifier).loadPopularRadios(
              countryId: selectedCountry?.id,
              stateName: selectedState?.stateName,
              refresh: true,
            );
          },
          child: CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics(),
            ),
            slivers: [
              // 筛选区域（可折叠）
              SliverToBoxAdapter(
                child: _buildFilterArea(),
              ),
              
              // 搜索栏
              SliverToBoxAdapter(
                child: _buildSearchBar(),
              ),
              
              // 搜索结果或热门内容
              SliverToBoxAdapter(
                child: _buildContent(),
              ),
              
              // 底部安全区域
              SliverToBoxAdapter(
                child: Consumer(
                  builder: (context, ref, child) {
                    // 使用FloatingElementsHelper计算动态高度
                    final floatingHeight = FloatingElementsHelper.getFloatingElementsHeight(context, ref);
                    return SizedBox(height: floatingHeight);
                  },
                ),
              ),
            ],
          ),
          ),
        ),
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索栏 - 霓虹风格
          Container(
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF1A1A2E).withOpacity(0.8),
                  const Color(0xFF16213E).withOpacity(0.7),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: const Color(0xFF00FFFF).withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF00FFFF).withOpacity(0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 0),
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                // 实时搜索延迟 - 支持累加式筛选
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (_searchQuery == value) {
                    // 无论搜索词是否为空，都触发搜索以支持累加式筛选
                    // 空搜索词 + 其他筛选条件 = 在其他条件基础上显示所有结果
                    _refreshDataAfterStateChange();
                  }
                });
              },
              decoration: InputDecoration(
                hintText: _showFilters
                    ? 'search_with_filters'.tr()
                    : 'search_globally'.tr(),
                hintStyle: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontSize: 16,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: const Color(0xFF00FFFF).withOpacity(0.8),
                  size: 22,
                ),
                suffixIcon: _searchQuery.isNotEmpty || _selectedTagIds.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: Colors.white.withOpacity(0.7),
                        ),
                        onPressed: _clearSearch,
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ),
          
          // 搜索统计
          if (_searchQuery.isNotEmpty || _selectedTagIds.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(top: 12),
              child: _buildSearchStats(),
            ),
        ],
      ),
    );
  }

  /// 构建搜索统计
  Widget _buildSearchStats() {
    final searchState = ref.watch(searchRadiosProvider);
    
    if (searchState.isLoading) {
      return Text(
        'searching'.tr(),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      );
    }
    
    if (searchState.error != null) {
      return Text(
        'search failed'.tr(),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.error,
        ),
      );
    }
    
    return Text(
      'stations',
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }

  /// 构建筛选区域
  Widget _buildFilterArea() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 60, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 筛选区域头部（折叠/展开控制）- 霓虹风格
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF1A1A2E).withOpacity(0.6),
                  const Color(0xFF16213E).withOpacity(0.4),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF00FFFF).withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  Icons.tune,
                  color: const Color(0xFF00FFFF),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'filters'.tr(),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                // 有任何筛选条件时显示清除按钮（标签、地区、搜索词）
                if (_selectedTagIds.isNotEmpty ||
                    ref.watch(selectedStateProvider) != null ||
                    _searchQuery.isNotEmpty)
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.red.withOpacity(0.2),
                          Colors.red.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.red.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: TextButton.icon(
                      onPressed: _clearAllFilters,
                      icon: const Icon(Icons.clear_all, size: 16),
                      label: Text('clear_all'.tr()),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red[300],
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        minimumSize: Size.zero,
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                // 折叠/展开按钮 - 霓虹风格
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _showFilters
                          ? [
                              const Color(0xFF00FFFF).withOpacity(0.3),
                              const Color(0xFF00FFFF).withOpacity(0.1),
                            ]
                          : [
                              Colors.white.withOpacity(0.1),
                              Colors.white.withOpacity(0.05),
                            ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _showFilters
                          ? const Color(0xFF00FFFF).withOpacity(0.5)
                          : Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                    boxShadow: _showFilters ? [
                      BoxShadow(
                        color: const Color(0xFF00FFFF).withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 0),
                        spreadRadius: 2,
                      ),
                    ] : null,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _showFilters = !_showFilters;
                          if (!_showFilters) {
                            _showTagSelection = false; // 折叠时也关闭tag选择
                          }
                        });
                      },
                      borderRadius: BorderRadius.circular(20),
                      child: Container(
                        width: 40,
                        height: 40,
                        alignment: Alignment.center,
                        child: AnimatedRotation(
                          turns: _showFilters ? 0.5 : 0,
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            Icons.expand_more,
                            color: _showFilters
                                ? const Color(0xFF00FFFF)
                                : Colors.white.withOpacity(0.7),
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 筛选内容（可折叠）
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _showFilters ? null : 0,
            child: _showFilters ? _buildFilterContent() : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  /// 构建筛选内容
  Widget _buildFilterContent() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tag选择按钮
          _buildTagSelectionButton(),
          
          // Tag选择区域（内联展开）
          if (_showTagSelection) ...[
            const SizedBox(height: 16),
            _buildTagSelectionArea(),
          ],
          
          const SizedBox(height: 20),
          
          // 地区选择
          _buildLocationSelection(),
          
          const SizedBox(height: 20),
          
          // 排序选项
          _buildSortSelection(),
        ],
      ),
    );
  }

  /// 构建Tag选择按钮
  Widget _buildTagSelectionButton() {
    return Row(
      children: [
        Icon(
          Icons.music_note_rounded,
          color: Theme.of(context).colorScheme.primary,
          size: 18,
        ),
        const SizedBox(width: 8),
        Text(
          'music_genres'.tr(),
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        if (_selectedTagIds.isNotEmpty) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              '${_selectedTagIds.length}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
        const Spacer(),
        Material(
          color: _showTagSelection 
              ? Theme.of(context).colorScheme.primaryContainer
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16),
          child: InkWell(
            onTap: () {
              setState(() {
                _showTagSelection = !_showTagSelection;
              });
            },
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _showTagSelection ? 'collapse'.tr() : 'select_tags'.tr(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _showTagSelection 
                          ? Theme.of(context).colorScheme.onPrimaryContainer
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  AnimatedRotation(
                    turns: _showTagSelection ? 0.5 : 0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.expand_more,
                      size: 16,
                      color: _showTagSelection 
                          ? Theme.of(context).colorScheme.onPrimaryContainer
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建Tag选择区域
  Widget _buildTagSelectionArea() {
    if (_cachedTags == null) {
      return const SizedBox(
        height: 40,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    // 按照 sort_order 从高到低排序，然后取前15个
    final sortedTags = List<RadioTag>.from(_cachedTags!)
      ..sort((a, b) => b.sortOrder.compareTo(a.sortOrder));
    final displayTags = sortedTags.take(15).toList();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 8,
        runSpacing: 10,
        alignment: WrapAlignment.start,
        children: displayTags.map((tag) {
          final isSelected = _selectedTagIds.contains(tag.id.toString());

          return AnimatedContainer(
            duration: const Duration(milliseconds: 250),
            curve: Curves.easeInOut,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      _selectedTagIds.remove(tag.id.toString());
                    } else {
                      _selectedTagIds.add(tag.id.toString());
                    }
                  });
                  // 立即触发搜索
                  Future.delayed(const Duration(milliseconds: 100), () {
                    _refreshDataAfterStateChange();
                  });
                },
                borderRadius: BorderRadius.circular(22),
                splashColor: isSelected
                    ? Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.1)
                    : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                highlightColor: isSelected
                    ? Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.05)
                    : Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 250),
                  curve: Curves.easeInOut,
                  padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.primary,
                              Theme.of(context).colorScheme.primary.withValues(alpha: 0.9),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.8),
                              Theme.of(context).colorScheme.surfaceContainer.withValues(alpha: 0.6),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                    borderRadius: BorderRadius.circular(22),
                    border: Border.all(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                          : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                      width: isSelected ? 1.2 : 1,
                    ),
                    boxShadow: isSelected ? [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.15),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ] : [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.03),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isSelected) ...[
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.check_rounded,
                            size: 11,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const SizedBox(width: 6),
                      ],
                      Flexible(
                        child: Text(
                          tag.name,
                          style: TextStyle(
                            fontSize: 12.5,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                            letterSpacing: 0.2,
                            height: 1.2,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 构建地区选择
  Widget _buildLocationSelection() {
    final selectedCountry = ref.watch(selectedCountryProvider);
    final selectedState = ref.watch(selectedStateProvider);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.location_on_rounded,
              color: Theme.of(context).colorScheme.primary,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              'location_filter'.tr(),
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (selectedState != null) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  selectedState.stateName,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
        
        const SizedBox(height: 12),
        
        // 当前国家信息
        if (selectedCountry != null)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.public_rounded,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'country'.tr(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      selectedCountry.name,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // 省州选择器
                StateSelector(
                  key: _stateSelectorKey, // 🔧 添加 key 以支持强制刷新
                  isInSearchArea: false,
                  width: double.infinity,
                  onStateSelected: (selectedState) {
                    // 立即触发搜索
                    Future.delayed(const Duration(milliseconds: 100), () {
                      _refreshDataAfterStateChange();
                    });
                  },
                ),
              ],
            ),
          )
        else
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.errorContainer.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.error.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'select_country_first'.tr(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// 构建排序选择
  Widget _buildSortSelection() {
    final sortOptions = [
      {'key': 'clickcount', 'label': 'popularity'.tr(), 'icon': Icons.trending_up},
      {'key': 'votes', 'label': 'most_voted'.tr(), 'icon': Icons.thumb_up},
      {'key': 'name', 'label': 'name'.tr(), 'icon': Icons.sort_by_alpha},
      {'key': 'lastcheckok', 'label': 'newest'.tr(), 'icon': Icons.new_releases},
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.sort_rounded,
              color: Theme.of(context).colorScheme.primary,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              'sort_by'.tr(),
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: sortOptions.map((option) {
            final isSelected = _sortBy == option['key'];
            
            return Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _sortBy = option['key'] as String;
                  });
                  // 立即触发搜索
                  Future.delayed(const Duration(milliseconds: 100), () {
                    _refreshDataAfterStateChange();
                  });
                },
                borderRadius: BorderRadius.circular(20),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primaryContainer
                        : Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                      width: isSelected ? 1.5 : 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        option['icon'] as IconData,
                        size: 14,
                        color: isSelected
                            ? Theme.of(context).colorScheme.onPrimaryContainer
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        option['label'] as String,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isSelected
                              ? Theme.of(context).colorScheme.onPrimaryContainer
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }





  /// 构建主要内容区域
  Widget _buildContent() {
    final selectedState = ref.watch(selectedStateProvider);
    final hasSearch = _searchQuery.isNotEmpty || _selectedTagIds.isNotEmpty || selectedState != null;
    
    if (hasSearch) {
      return _buildSearchResults();
    } else {
      return _buildDiscoverContent();
    }
  }

  /// 构建搜索结果
  Widget _buildSearchResults() {
    final searchState = ref.watch(searchRadiosProvider);
    final selectedCountry = ref.watch(selectedCountryProvider);
    final selectedState = ref.watch(selectedStateProvider);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索结果标题栏
          _buildSearchResultsHeader(selectedCountry, selectedState, searchState.stations.length),
          
          const SizedBox(height: 16),
          
          // 搜索结果内容
          _buildSearchResultsContent(searchState),
        ],
      ),
    );
  }

  /// 构建搜索结果标题栏
  Widget _buildSearchResultsHeader(Country? selectedCountry, CountryState? selectedState, int resultCount) {
    List<String> filterInfo = [];
    
    if (_searchQuery.isNotEmpty) {
      filterInfo.add('"${_searchQuery.trim()}"');
    }
    if (selectedCountry != null) {
      filterInfo.add(selectedCountry.name);
    }
    if (selectedState != null) {
      filterInfo.add(selectedState.stateName);
    } else if (selectedCountry != null) {
      filterInfo.add('all_states'.tr());
    }
    if (_selectedTagIds.isNotEmpty) {
      filterInfo.add('${_selectedTagIds.length} ${'music_genres'.tr()}');
    }
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.filter_list_rounded,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'search_results'.tr(),
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Colors.white, // 霓虹青色，在深色背景下清晰可见
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (filterInfo.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    filterInfo.join(' • '),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white, // 白色半透明，在深色背景下清晰
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '$resultCount',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索结果内容
  Widget _buildSearchResultsContent(RadioListState searchState) {
    if (searchState.isLoading) {
      return const SizedBox(
        height: 200,
        child: Center(child: CircularProgressIndicator()),
      );
    }
    
    if (searchState.error != null) {
      return Container(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'search_failed'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white, // 白色标题，在深色背景下清晰
              ),
            ),
            const SizedBox(height: 8),
            Text(
              searchState.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withOpacity(0.7), // 白色半透明，在深色背景下清晰
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    if (searchState.stations.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Icon(
              Icons.search_off,
              color: Colors.white.withOpacity(0.6), // 白色半透明图标，在深色背景下清晰
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'no_results'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white, // 白色标题，在深色背景下清晰
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'try_different_keywords'.tr(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withOpacity(0.7), // 白色半透明，在深色背景下清晰
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    return _buildStationsList(searchState.stations);
  }

  /// 构建发现内容
  Widget _buildDiscoverContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 热门电台（移除重复的热门标签部分，因为筛选区域已有tag选择功能）
        _buildPopularStations(),
      ],
    );
  }





  /// 构建热门电台
  Widget _buildPopularStations() {
    final popularState = ref.watch(popularRadiosProvider);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'popular_stations'.tr(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (popularState.isLoading)
            const SizedBox(
              height: 200,
              child: Center(child: CircularProgressIndicator()),
            )
          else if (popularState.error != null)
            SizedBox(
              height: 200,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Theme.of(context).colorScheme.error,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text('load_failed'.tr()),
                  ],
                ),
              ),
            )
          else if (popularState.stations.isEmpty)
            SizedBox(
              height: 200,
              child: Center(child: Text('no_data'.tr())),
            )
          else
            _buildStationsList(popularState.stations.take(20).toList()),
        ],
      ),
    );
  }

  /// 构建电台列表
  Widget _buildStationsList(List<StationSimple> stations) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: stations.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final station = stations[index];
        return _buildStationListItem(station);
      },
    );
  }

  /// 构建电台列表项（修复版 - 支持播放列表上下文）
  /// 
  /// 修复问题: 
  /// 1. 清理雷达推荐上下文，确保使用正确的播放模式
  /// 2. 添加播放列表上下文，支持轮播图模式的上下首切换
  /// 影响范围: explore_page.dart
  /// 实现日期: 2025-01-27
  Widget _buildStationListItem(StationSimple station) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () async {
          // 🔧 清理雷达推荐上下文，确保探索页面使用传统轮播图模式
          ref.read(radarRecommendationContextProvider.notifier).state = null;
          
          await _playStationWithContext(station);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(
                alpha: 0.2,
              ),
            ),
          ),
          child: Row(
            children: [
              // 电台图标
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: StationImageBuilder.buildStationImage(
                    station: station,
                    width: 56,
                    height: 56,
                    fit: BoxFit.cover,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // 电台信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      station.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    if (station.country.isNotEmpty)
                      Row(
                        children: [
                          Icon(
                            Icons.public,
                            size: 14,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              station.country,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    
                    if (station.tags.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          station.tags.take(3).join(' • '),
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall
                              ?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              
              // 播放按钮
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.play_arrow,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 播放电台并设置播放列表上下文（修复版）
  /// 
  /// 功能实现: 根据当前数据源（搜索结果或热门电台）构建正确的播放列表上下文
  /// 修复问题: 确保explore页面播放时支持轮播图模式的上下首切换
  /// 影响范围: explore_page.dart
  /// 实现日期: 2025-01-27
  Future<void> _playStationWithContext(StationSimple station) async {
    try {
      final selectedState = ref.read(selectedStateProvider);
      final hasSearch = _searchQuery.isNotEmpty || _selectedTagIds.isNotEmpty || selectedState != null;
      
      List<StationSimple> stations;
      PlaylistSourceType sourceType;
      String sourceTitle;
      
      if (hasSearch) {
        // 搜索结果模式
        final searchState = ref.read(searchRadiosProvider);
        stations = searchState.stations;
        sourceType = PlaylistSourceType.categoryGrid; // 搜索结果使用分类网格类型
        
        // 构建搜索条件描述作为源标题
        List<String> searchParts = [];
        if (_searchQuery.isNotEmpty) {
          searchParts.add('"${_searchQuery.trim()}"');
        }
        final selectedCountry = ref.read(selectedCountryProvider);
        if (selectedCountry != null) {
          searchParts.add(selectedCountry.name);
        }
        if (selectedState != null) {
          searchParts.add(selectedState.stateName);
        }
        if (_selectedTagIds.isNotEmpty) {
          searchParts.add('${_selectedTagIds.length} ${'music_genres'.tr()}');
        }
        
        sourceTitle = searchParts.isNotEmpty 
            ? 'search_results'.tr() + ': ' + searchParts.join(' • ')
            : 'search_results'.tr();
      } else {
        // 热门电台模式
        final popularState = ref.read(popularRadiosProvider);
        stations = popularState.stations.take(20).toList(); // 只取前20个，与显示逻辑一致
        sourceType = PlaylistSourceType.hotRecommendations; // 热门电台使用热门推荐类型
        sourceTitle = 'popular_stations'.tr();
      }
      
      // 检查电台列表是否为空
      if (stations.isEmpty) {
        print('❌ 无法播放：电台列表为空');
        return;
      }
      
      // 找到当前电台在列表中的索引
      final currentIndex = stations.indexWhere((s) => s.id == station.id);
      if (currentIndex == -1) {
        print('❌ 电台不在当前列表中，单独播放: ${station.name}');
        // 如果电台不在当前列表中，单独播放（不设置播放列表）
        final audioService = ref.read(audioServiceProvider);
        await audioService.playStation(station);
        return;
      }
      
      // 构建播放列表上下文
      final playlistContext = PlaylistContext(
        sourceType: sourceType,
        stations: stations,
        currentIndex: currentIndex,
        sourceTitle: sourceTitle,
      );
      
      print('🎵 探索页播放电台: ${station.name}');
      print('📋 播放列表: $sourceTitle (${currentIndex + 1}/${stations.length})');
      
      // 使用播放列表播放
      final audioService = ref.read(audioServiceProvider);
      await audioService.playStationWithPlaylist(
        station: station,
        playlistContext: playlistContext,
      );
    } catch (e) {
      print('❌ 播放电台失败: $e');
      // 发生错误时，尝试单独播放
      final audioService = ref.read(audioServiceProvider);
      await audioService.playStation(station);
    }
  }
}