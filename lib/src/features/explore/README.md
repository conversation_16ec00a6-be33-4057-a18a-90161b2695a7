# Explore Page 功能模块说明

## 概述
探索页面（ExplorePage）是用户搜索和发现电台的主要入口，提供了搜索、筛选和浏览热门内容等功能。

## 主要功能

### 1. 搜索功能
- 支持关键词搜索电台
- 实时搜索（500ms延迟）
- 支持多种排序方式（人气、投票、名称、最新）

### 2. 标签筛选系统
- **排序优化**：按照 `sort_order` 字段排序，显示最重要的标签
- **数量限制**：只显示前15个标签，避免界面过于拥挤
- **双重展示**：
  - 筛选面板：紧凑型标签选择，支持多选
  - 热门标签：卡片式展示，点击直接搜索

### 3. 标签UI设计特点

#### 筛选面板标签
- 现代化胶囊式设计（22px圆角）
- 渐变背景效果，增强视觉层次
- 选中状态显示圆形勾选图标
- 选中时主色调渐变背景，未选中时表面容器渐变
- 流畅动画过渡效果（250ms，easeInOut曲线）
- 增强的阴影效果和边框设计
- 支持文本溢出省略显示

#### 热门标签卡片
- 三色渐变背景设计（主容器色→次容器色）
- 圆形音符图标装饰，带渐变背景
- 多层阴影效果增加立体感和深度
- 入场动画效果（TweenAnimationBuilder）
- 缩放和透明度动画（300ms）
- 增强的点击反馈效果
- 支持文本溢出省略显示

### 4. 响应式布局
- 自适应换行的Wrap布局
- 优化间距：水平8-10px，垂直10-12px
- 圆角容器包裹（16-20px圆角），增加视觉层次
- 增强的容器阴影和边框效果

## 技术实现

### 标签排序逻辑
```dart
// 按照 sort_order 从高到低排序，然后取前12个
final sortedTags = List<RadioTag>.from(_cachedTags!)
  ..sort((a, b) => b.sortOrder.compareTo(a.sortOrder));
final displayTags = sortedTags.take(12).toList();
```

### 样式系统
- 使用Material 3设计规范
- 主题色自适应（支持深色模式）
- withValues() 替代已废弃的 withOpacity()

## 数据流程

1. **初始化**：从radioTagsProvider获取标签数据
2. **排序**：按sort_order从高到低排列（与数据库一致）
3. **限制**：取前12个标签
4. **展示**：分别在筛选面板和热门标签区域显示
5. **交互**：选择标签触发搜索

## 最近更新 (2024-01-16)

### ✨ 重大UI升级
- 全新现代化标签设计系统
- 三色渐变背景和多层阴影效果
- 流畅的入场动画和交互反馈
- 按 `sort_order` 从高到低排序，限制显示12个标签
- 支持文本溢出省略处理

### 🎨 视觉设计改进
- **筛选面板标签**：22px圆角胶囊设计，渐变背景
- **热门标签卡片**：20px圆角，三色渐变，音符图标装饰
- **动画系统**：TweenAnimationBuilder入场动画，250-300ms过渡
- **阴影系统**：多层阴影增加立体感和深度
- **交互反馈**：增强的点击波纹和高亮效果

### 🚀 技术优化
- 使用withValues()替代已废弃的withOpacity()
- 优化动画性能和流畅度
- 改进响应式布局和自适应设计
- 增强错误处理和加载状态显示

## 使用方式

### 搜索电台
1. 输入关键词进行实时搜索
2. 点击筛选按钮展开高级筛选
3. 选择音乐类型标签
4. 选择排序方式
5. 点击"应用筛选"查看结果

### 浏览热门内容
1. 滚动查看热门标签
2. 点击标签卡片快速搜索该类型电台
3. 浏览热门电台列表

## 文件结构
```
lib/src/features/explore/
├── explore_page.dart       # 主页面文件
└── README.md              # 本文档
```

## 相关依赖
- `flutter_riverpod`: 状态管理
- `easy_localization`: 国际化
- `go_router`: 路由导航 