import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart' as gma;
import '../models/ad_type.dart';
import '../models/ad_config.dart';
import '../models/ad_callback.dart';
import '../models/ad_result.dart';
import 'cache_manager.dart';
import '../adapters/google_ads_adapter.dart';

/// 广告管理器
///
/// 负责协调广告的加载、展示和缓存管理
/// 这是SDK的核心组件，处理所有业务逻辑
class AdManager {
  /// 广告配置
  final AdConfig _config;

  /// 广告缓存过期时间配置
  static const Map<AdType, Duration> _cacheExpirationDurations = {
    AdType.appOpen: Duration(hours: 4),      // 开屏广告：4小时
    AdType.interstitial: Duration(hours: 1), // 插屏广告：1小时
    AdType.rewarded: Duration(hours: 4),     // 激励广告：4小时
    AdType.native: Duration(hours: 1),       // 原生广告：1小时
    AdType.banner: Duration(minutes: 30),    // Banner广告：30分钟（用于缓存平台信息）
  };

  /// 缓存管理器
  final CacheManager _cache = CacheManager();

  /// Google广告适配器
  late final GoogleAdsAdapter _adapter;

  /// Banner广告管理
  /// Key: pageId_placement_backup, Value: Banner广告对象
  final Map<String, gma.BannerAd> _bannerAds = {};

  /// Banner广告回调管理
  /// Key: pageId_placement_backup, Value: 回调信息
  final Map<String, _BannerCallbackInfo> _bannerCallbacks = {};

  /// 是否已初始化
  bool _initialized = false;

  /// 事件上报函数（可选）
  Function(String, Map<String, dynamic>)? _eventReporter;

  /// 创建广告管理器
  ///
  /// [config] 广告配置
  AdManager(this._config);

  /// 初始化管理器
  ///
  /// 初始化底层的广告适配器
  Future<void> initialize() async {
    if (_initialized) {
      return;
    }

    _adapter = GoogleAdsAdapter();
    await _adapter.initialize();

    // 如果启用了广告检查器且在调试模式下，则打开广告检查器
    if (_config.enableAdInspector && kDebugMode) {
      _openAdInspector();
    }

    _initialized = true;
  }

  /// 设置事件上报器
  void setEventReporter(Function(String, Map<String, dynamic>)? reporter) {
    _eventReporter = reporter;
  }

  /// 获取缓存广告的平台信息
  ///
  /// [adId] 广告ID
  /// 返回缓存中广告的平台信息，如果没有缓存则返回null
  String? getCachedAdPlatform(String adId) {
    // 首先检查通用缓存
    final cachedResult = _cache.get(adId);
    if (cachedResult?.adPlatform != null) {
      return cachedResult!.adPlatform;
    }

    // 对于Banner广告，检查_bannerAds中的平台信息
    return _adapter.getBannerAdPlatform(adId);
  }

  /// 打开广告检查器
  ///
  /// 仅在调试模式下有效，用于检查广告配置和状态
  void _openAdInspector() {
    gma.MobileAds.instance.openAdInspector((error) {
      if (error != null) {
        if (kDebugMode) {
          print('广告检查器打开失败: $error');
        }
      } else {
        if (kDebugMode) {
          print('广告检查器已打开');
        }
      }
    });
  }

  /// 加载广告到缓存
  ///
  /// [type] 广告类型
  /// [backup] 是否为备用广告，默认为false
  /// [callback] 回调函数
  /// [bannerSize] Banner广告尺寸（仅Banner广告有效）
  /// [pageId] 页面标识（仅Banner广告需要）
  Future<void> load(
    AdType type, {
    bool backup = false,
    AdCallback? callback,
    gma.AdSize? bannerSize,
    String? pageId,
  }) async {
    _ensureInitialized();

    try {
      // 获取广告ID
      final adId = _getAdId(type, backup);
      if (adId.isEmpty) {
        final error = '未找到${backup ? '备用' : '主'}广告ID: $type';
        callback?.onAdFailedToLoad?.call(error);
        return;
      }

      // Banner广告特殊处理：需要pageId来管理多个Banner
      if (type == AdType.banner) {
        if (pageId == null) {
          callback?.onAdFailedToLoad?.call('Banner广告需要提供pageId参数');
          return;
        }
        await _loadBannerAd(adId, backup, pageId, bannerSize, callback);
        return;
      }

      // 检查是否已缓存且有效（仅非Banner广告）
      if (type == AdType.native) {
        // 原生广告使用专门的缓存检查
        if (_isNativeCacheValid(adId)) {
          callback?.onAdLoaded?.call(AdSource.cache);
          return;
        }
      } else {
        // 其他广告类型使用通用缓存检查
        if (_isCacheValid(adId)) {
          callback?.onAdLoaded?.call(AdSource.cache);
          return;
        }
      }

      // 实时加载广告
      final result = await _adapter.loadAd(adId, type, bannerSize);
      if (result.success) {
        // 创建带过期时间的结果
        final resultWithExpiration = AdResult.success(
          source: result.source!,
          adPlatform: result.adPlatform,
          expiresAt: _getExpirationTime(type),
        );

        // 缓存新的广告结果
        _cache.cache(adId, resultWithExpiration);
        callback?.onAdLoaded?.call(AdSource.realtime);
      } else {
        callback?.onAdFailedToLoad?.call(result.error ?? '加载失败');
      }
    } catch (e) {
      callback?.onAdFailedToLoad?.call('加载异常: $e');
    }
  }

  /// 展示广告
  ///
  /// 优先使用缓存，如果缓存中没有则实时加载
  ///
  /// [type] 广告类型
  /// [backup] 是否为备用广告，默认为false
  /// [callback] 回调函数
  /// [bannerSize] Banner广告尺寸（仅Banner广告有效）
  /// [pageId] 页面标识（仅Banner广告需要）
  Future<void> show(
    AdType type, {
    bool backup = false,
    AdCallback? callback,
    gma.AdSize? bannerSize,
    String? pageId,
  }) async {
    _ensureInitialized();

    try {
      // 获取广告ID
      final adId = _getAdId(type, backup);
      if (adId.isEmpty) {
        final error = '未找到${backup ? '备用' : '主'}广告ID: $type';
        callback?.onAdFailedToShow?.call(error);
        return;
      }

      // Banner广告特殊处理：需要pageId来管理多个Banner
      if (type == AdType.banner) {
        if (pageId == null) {
          callback?.onAdFailedToShow?.call('Banner广告需要提供pageId参数');
          return;
        }
        await _showBannerAd(pageId, backup, callback);
        return;
      }

      // 检查缓存是否有效（仅非Banner广告）
      if (_isCacheValid(adId)) {
        await _showCachedAd(adId, type, callback);
        return;
      }

      // 实时加载并展示
      await _loadAndShowAd(adId, type, callback, bannerSize);
    } catch (e) {
      callback?.onAdFailedToShow?.call('展示异常: $e');
    }
  }

  /// 检查广告是否已准备好
  ///
  /// [type] 广告类型
  /// [backup] 是否为备用广告
  bool isReady(AdType type, bool backup) {
    if (!_initialized) {
      return false;
    }

    // Banner广告不支持缓存，总是返回false
    if (type == AdType.banner) {
      return false;
    }

    final adId = _getAdId(type, backup);
    if (adId.isEmpty) {
      return false;
    }

    // 原生广告使用专门的缓存检查
    if (type == AdType.native) {
      return _isNativeCacheValid(adId);
    }

    // 其他广告类型使用通用缓存检查
    return _isCacheValid(adId);
  }

  /// 加载Banner广告（只加载，不展示）
  ///
  /// [adId] 广告ID
  /// [backup] 是否为备用广告
  /// [pageId] 页面标识
  /// [bannerSize] Banner尺寸
  /// [callback] 回调函数
  Future<void> _loadBannerAd(
    String adId,
    bool backup,
    String pageId,
    gma.AdSize? bannerSize,
    AdCallback? callback,
  ) async {
    final key = _getBannerKey(pageId, backup);

    // 如果已经存在，先释放
    _bannerAds[key]?.dispose();

    try {
      final bannerAd = gma.BannerAd(
        adUnitId: adId,
        size: bannerSize ?? gma.AdSize.banner,
        request: const gma.AdRequest(),
        listener: gma.BannerAdListener(
          onAdLoaded: (ad) {
            _bannerAds[key] = ad as gma.BannerAd;
            // 同时存储到_loadedAds中，以便getBannerAdPlatform能够找到
            _adapter.storeBannerAd(adId, ad);
            // 只触发加载成功回调，不触发展示回调
            callback?.onAdLoaded?.call(AdSource.realtime);
          },
          onAdFailedToLoad: (ad, error) {
            ad.dispose();
            callback?.onAdFailedToLoad?.call(error.message);
          },
          // 注意：这些回调只有在Banner真正显示时才会触发
          onAdImpression: (ad) {
            // 从_bannerCallbacks中获取回调信息
            final callbackInfo = _bannerCallbacks[key];
            if (callbackInfo != null) {
              // 只触发曝光回调，不触发展示回调（展示回调在show方法中已经触发）
              callbackInfo.callback?.onAdImpression?.call(callbackInfo.source);
            }
          },
          onAdClicked: (ad) {
            final callbackInfo = _bannerCallbacks[key];
            callbackInfo?.callback?.onAdClicked?.call(callbackInfo.source);
          },
          onAdClosed: (ad) {
            final callbackInfo = _bannerCallbacks[key];
            callbackInfo?.callback?.onAdDismissed?.call(callbackInfo.source);
          },
        ),
      );

      // 只加载，不显示
      await bannerAd.load();
    } catch (e) {
      callback?.onAdFailedToLoad?.call('Banner广告加载失败: $e');
    }
  }

  /// 展示Banner广告
  ///
  /// [pageId] 页面标识
  /// [backup] 是否为备用广告
  /// [callback] 回调函数
  Future<void> _showBannerAd(
    String pageId,
    bool backup,
    AdCallback? callback,
  ) async {
    final key = _getBannerKey(pageId, backup);
    final bannerAd = _bannerAds[key];

    if (bannerAd == null) {
      callback?.onAdFailedToShow?.call('Banner广告未加载，请先调用load方法');
      return;
    }

    // 存储回调信息，用于后续的点击、关闭等事件
    _bannerCallbacks[key] = _BannerCallbackInfo(callback, AdSource.realtime);

    // 立即触发展示回调
    callback?.onAdShowed?.call(AdSource.realtime);

    // Banner广告的展示是通过getBannerWidget方法实现的
  }

  /// 获取Banner广告Widget
  ///
  /// [pageId] 页面标识
  /// [backup] 是否为备用广告
  Widget getBannerWidget(String pageId, bool backup) {
    final key = _getBannerKey(pageId, backup);
    final bannerAd = _bannerAds[key];

    if (bannerAd == null) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      width: bannerAd.size.width.toDouble(),
      height: bannerAd.size.height.toDouble(),
      child: gma.AdWidget(ad: bannerAd),
    );
  }

  /// 释放Banner广告
  ///
  /// [pageId] 页面标识
  /// [backup] 是否为备用广告
  void disposeBanner(String pageId, bool backup) {
    final key = _getBannerKey(pageId, backup);
    final bannerAd = _bannerAds[key];
    if (bannerAd != null) {
      bannerAd.dispose();
      _bannerAds.remove(key);
      _bannerCallbacks.remove(key);
    }
  }

  /// 生成Banner广告的Key
  ///
  /// [pageId] 页面标识
  /// [backup] 是否为备用广告
  String _getBannerKey(String pageId, bool backup) {
    return '${pageId}_banner_${backup ? 'backup' : 'main'}';
  }

  /// 获取广告类型的过期时间
  ///
  /// [type] 广告类型
  DateTime? _getExpirationTime(AdType type) {
    final duration = _cacheExpirationDurations[type];
    if (duration == null) return null;
    return DateTime.now().add(duration);
  }

  /// 检查缓存是否有效（未过期）
  ///
  /// [adId] 广告ID
  bool _isCacheValid(String adId) {
    final cachedResult = _cache.get(adId);
    if (cachedResult == null) return false;

    // 检查是否过期
    if (cachedResult.isExpired) {
      // 过期了，移除缓存
      _cache.remove(adId);
      return false;
    }

    return cachedResult.isValid;
  }

  /// 检查原生广告缓存是否有效
  ///
  /// [adId] 广告ID
  bool _isNativeCacheValid(String adId) {
    // 检查适配器中是否有可用的未使用原生广告
    return _adapter.hasUnusedNativeAd(adId);
  }

  /// 清理所有缓存
  void dispose() {
    _cache.clear();

    // 清理所有Banner广告
    for (final bannerAd in _bannerAds.values) {
      bannerAd.dispose();
    }
    _bannerAds.clear();
    _bannerCallbacks.clear();

    if (_initialized) {
      _adapter.dispose();
      _initialized = false;
    }
  }

  /// 展示缓存的广告
  ///
  /// [adId] 广告ID
  /// [type] 广告类型
  /// [callback] 回调函数
  Future<void> _showCachedAd(String adId, AdType type, AdCallback? callback) async {
    final cachedResult = _cache.get(adId);
    if (cachedResult == null) {
      callback?.onAdFailedToShow?.call('缓存广告不存在');
      return;
    }

    // 包装回调，添加自动缓存逻辑
    final wrappedCallback = _wrapCallbackWithAutoCache(callback, type, adId);

    // 先触发加载成功回调（来源是缓存）
    wrappedCallback?.onAdLoaded?.call(AdSource.cache);

    // 展示广告（onAdShowed回调会在adapter中触发）
    await _adapter.showAd(adId, wrappedCallback, AdSource.cache);

    // 展示后移除缓存（Banner广告除外，Banner广告可以重复使用）
    if (type != AdType.banner) {
      _cache.remove(adId);
    }
  }

  /// 实时加载并展示广告
  ///
  /// [adId] 广告ID
  /// [type] 广告类型
  /// [callback] 回调函数
  /// [bannerSize] Banner广告尺寸
  Future<void> _loadAndShowAd(
    String adId,
    AdType type,
    AdCallback? callback, [
    gma.AdSize? bannerSize,
  ]) async {
    // 实时加载
    final result = await _adapter.loadAd(adId, type, bannerSize);
    if (!result.success) {
      callback?.onAdFailedToLoad?.call(result.error ?? '加载失败');
      callback?.onAdFailedToShow?.call(result.error ?? '加载失败');
      return;
    }

    // 缓存加载结果（包含平台信息）
    final resultWithExpiration = AdResult.success(
      source: result.source!,
      adPlatform: result.adPlatform,
      expiresAt: _getExpirationTime(type),
    );
    _cache.cache(adId, resultWithExpiration);

    // 包装回调，添加自动缓存逻辑
    final wrappedCallback = _wrapCallbackWithAutoCache(callback, type, adId);

    // 先触发加载成功回调（来源是实时）
    wrappedCallback?.onAdLoaded?.call(AdSource.realtime);

    // 立即展示
    await _adapter.showAd(adId, wrappedCallback, AdSource.realtime);
  }

  /// 获取广告ID
  ///
  /// [type] 广告类型
  /// [backup] 是否为备用广告
  String getAdId(AdType type, bool backup) {
    return backup
        ? _config.getBackupAdId(type)
        : _config.getMainAdId(type);
  }

  /// 内部获取广告ID（保持向后兼容）
  String _getAdId(AdType type, bool backup) {
    return getAdId(type, backup);
  }

  /// 包装回调，添加自动缓存逻辑
  ///
  /// [callback] 原始回调
  /// [type] 广告类型
  /// [adId] 广告ID
  AdCallback? _wrapCallbackWithAutoCache(AdCallback? callback, AdType type, String adId) {
    // Banner广告不支持缓存，直接返回原始回调
    if (type == AdType.banner || !_config.autoCache) {
      return callback;
    }

    return AdCallback(
      onAdLoaded: callback?.onAdLoaded,
      onAdFailedToLoad: callback?.onAdFailedToLoad,
      onAdShowed: (source) {
        callback?.onAdShowed?.call(source);
        // 展示成功后自动预加载下一条
        _triggerAutoPreload(type, adId);
      },
      onAdImpression: callback?.onAdImpression,
      onAdClicked: callback?.onAdClicked,
      onAdDismissed: callback?.onAdDismissed,
      onAdFailedToShow: callback?.onAdFailedToShow,
      onUserEarnedReward: callback?.onUserEarnedReward,
      onPaidEvent: callback?.onPaidEvent,
    );
  }

  /// 触发自动预加载
  ///
  /// [type] 广告类型
  /// [currentAdId] 当前广告ID
  void _triggerAutoPreload(AdType type, String currentAdId) {
    // 在后台异步预加载，不阻塞当前流程
    Future.delayed(Duration.zero, () async {
      try {
        // 清除当前缓存
        _cache.remove(currentAdId);

        // 上报自动预加载请求事件
        _eventReporter?.call('ad_sdk_base', {
          'action': 1, // 广告请求
          'placement_id': currentAdId,
          'ad_mediation': 'admob',
        });

        final startTime = DateTime.now();

        // 重新加载同一个广告ID（实现预加载下一条）
        final result = await _adapter.loadAd(currentAdId, type);
        if (result.success) {
          // 上报自动预加载返回事件
          final filledTime = DateTime.now().difference(startTime).inMilliseconds;
          _eventReporter?.call('ad_sdk_base', {
            'action': 2, // 广告返回
            'placement_id': currentAdId,
            'ad_platform': result.adPlatform ?? 'unknown',
            'ad_mediation': 'admob',
            'filled_time': filledTime,
          });

          // 创建带过期时间的结果
          final resultWithExpiration = AdResult.success(
            source: result.source!,
            adPlatform: result.adPlatform,
            expiresAt: _getExpirationTime(type),
          );
          _cache.cache(currentAdId, resultWithExpiration);
        } else {
          // 上报自动预加载失败事件
          _eventReporter?.call('ad_sdk_base', {
            'action': 5, // 广告加载失败
            'placement_id': currentAdId,
            'ad_platform': 'unknown', // 失败时无法获取平台信息
            'ad_mediation': 'admob',
            'fall_reason': result.error ?? 'Unknown error',
          });
        }
      } catch (e) {
        // 上报自动预加载失败事件
        _eventReporter?.call('ad_sdk_base', {
          'action': 5, // 广告加载失败
          'placement_id': currentAdId,
          'ad_platform': 'unknown', // 异常时无法获取平台信息
          'ad_mediation': 'admob',
          'fall_reason': e.toString(),
        });

        // 自动预加载失败不影响主流程，只记录日志
      }
    });
  }



  /// 绑定Template样式并展示原生广告
  ///
  /// [templateType] 模板类型
  /// [backup] 是否为备用广告
  /// [callback] 回调函数
  Future<void> bindViewByTemplateStyle(
    String templateType,
    bool backup,
    AdCallback? callback,
  ) async {
    _ensureInitialized();

    try {
      // 获取广告ID
      final adId = _getAdId(AdType.native, backup);
      if (adId.isEmpty) {
        final error = '未找到${backup ? '备用' : '主'}原生广告ID';
        callback?.onAdFailedToShow?.call(error);
        return;
      }

      // 检查是否有缓存，没有缓存时上报请求事件
      final hasCache = _isNativeCacheValid(adId);
      if (!hasCache) {
        // 上报广告请求事件
        _eventReporter?.call('ad_sdk_base', {
          'action': 1, // 广告请求
          'placement_id': adId,
          'ad_mediation': 'admob',
        });
      }

      // 绑定Template样式并展示
      final result = await _adapter.bindViewByTemplateStyle(adId, templateType, callback: callback);
      if (!result.success) {
        callback?.onAdFailedToLoad?.call(result.error ?? '绑定Template样式失败');
        callback?.onAdFailedToShow?.call(result.error ?? '绑定Template样式失败');
        return;
      }

      // 如果是实时加载，更新缓存中的平台信息
      if (!hasCache && result.source == AdSource.realtime && result.adPlatform != null) {
        final resultWithExpiration = AdResult.success(
          source: result.source!,
          adPlatform: result.adPlatform,
          expiresAt: _getExpirationTime(AdType.native),
        );
        _cache.cache(adId, resultWithExpiration);
      }

      // 触发回调（AdCallback.withReporting会处理事件上报）
      callback?.onAdLoaded?.call(result.source ?? AdSource.realtime);

      callback?.onAdShowed?.call(result.source ?? AdSource.realtime);

      // 如果开启了自动预加载，补充一条到缓存
      _triggerNativeAutoPreload(adId);
    } catch (e) {
      callback?.onAdFailedToShow?.call('绑定Template样式异常: $e');
    }
  }

  /// 绑定Factory样式并展示原生广告
  ///
  /// [factoryId] Factory ID
  /// [backup] 是否为备用广告
  /// [callback] 回调函数
  Future<void> bindViewByFactoryId(
    String factoryId,
    bool backup,
    AdCallback? callback,
  ) async {
    _ensureInitialized();

    try {
      // 获取广告ID
      final adId = _getAdId(AdType.native, backup);
      if (adId.isEmpty) {
        final error = '未找到${backup ? '备用' : '主'}原生广告ID';
        callback?.onAdFailedToShow?.call(error);
        return;
      }

      // 检查是否有缓存，没有缓存时上报请求事件
      final hasCache = _isNativeCacheValid(adId);
      if (!hasCache) {
        // 上报广告请求事件
        _eventReporter?.call('ad_sdk_base', {
          'action': 1, // 广告请求
          'placement_id': adId,
          'ad_mediation': 'admob',
        });
      }

      // 绑定Factory样式并展示
      final result = await _adapter.bindViewByFactoryId(adId, factoryId, callback: callback);
      if (!result.success) {
        callback?.onAdFailedToLoad?.call(result.error ?? '绑定Factory样式失败');
        callback?.onAdFailedToShow?.call(result.error ?? '绑定Factory样式失败');
        return;
      }

      // 如果是实时加载，更新缓存中的平台信息
      if (!hasCache && result.source == AdSource.realtime && result.adPlatform != null) {
        final resultWithExpiration = AdResult.success(
          source: result.source!,
          adPlatform: result.adPlatform,
          expiresAt: _getExpirationTime(AdType.native),
        );
        _cache.cache(adId, resultWithExpiration);
      }

      // 触发回调（AdCallback.withReporting会处理事件上报）
      callback?.onAdLoaded?.call(result.source ?? AdSource.realtime);

      callback?.onAdShowed?.call(result.source ?? AdSource.realtime);

      // 如果开启了自动预加载，补充一条到缓存
      _triggerNativeAutoPreload(adId);
    } catch (e) {
      callback?.onAdFailedToShow?.call('绑定Factory样式异常: $e');
    }
  }

  /// 获取原生广告Widget
  ///
  /// [backup] 是否为备用广告
  Widget getNativeWidget(bool backup) {
    _ensureInitialized();

    final adId = _getAdId(AdType.native, backup);
    if (adId.isEmpty) {
      return const SizedBox(
        height: 200,
        child: Center(
          child: Text('未找到原生广告配置'),
        ),
      );
    }

    return _adapter.getNativeAdWidget(adId);
  }



  /// 触发原生广告自动预加载
  void _triggerNativeAutoPreload(String adId) {
    // 在后台异步预加载，不阻塞当前流程
    Future.delayed(Duration.zero, () async {
      try {
        // 上报自动预加载请求事件
        _eventReporter?.call('ad_sdk_base', {
          'action': 1, // 广告请求
          'placement_id': adId,
          'ad_mediation': 'admob',
        });

        final startTime = DateTime.now();

        // 通过loadAd方法预加载原生广告
        final result = await _adapter.loadAd(adId, AdType.native);
        if (result.success) {
          // 上报自动预加载返回事件
          final filledTime = DateTime.now().difference(startTime).inMilliseconds;
          _eventReporter?.call('ad_sdk_base', {
            'action': 2, // 广告返回
            'placement_id': adId,
            'ad_platform': result.adPlatform ?? 'unknown',
            'ad_mediation': 'admob',
            'filled_time': filledTime,
          });
        } else {
          // 上报自动预加载失败事件
          _eventReporter?.call('ad_sdk_base', {
            'action': 5, // 广告加载失败
            'placement_id': adId,
            'ad_platform': 'unknown',
            'ad_mediation': 'admob',
            'fall_reason': result.error ?? 'Unknown error',
          });
        }
      } catch (e) {
        // 上报自动预加载失败事件
        _eventReporter?.call('ad_sdk_base', {
          'action': 5, // 广告加载失败
          'placement_id': adId,
          'ad_platform': 'unknown',
          'ad_mediation': 'admob',
          'fall_reason': e.toString(),
        });

        // 自动预加载失败不影响主流程，只记录日志
      }
    });
  }

  /// 确保管理器已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('AdManager未初始化');
    }
  }
}

/// Banner广告回调信息
class _BannerCallbackInfo {
  final AdCallback? callback;
  final AdSource source;

  _BannerCallbackInfo(this.callback, this.source);
}
