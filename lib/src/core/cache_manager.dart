import '../models/ad_result.dart';

/// 缓存管理器
/// 
/// 负责管理已加载的广告缓存
/// 使用广告ID作为缓存Key，简单高效
class CacheManager {
  /// 缓存存储
  /// Key: 广告ID，Value: 广告结果
  final Map<String, AdResult> _cache = {};

  /// 缓存广告
  /// 
  /// [adId] 广告ID
  /// [result] 广告结果
  void cache(String adId, AdResult result) {
    if (adId.isEmpty) {
      throw ArgumentError('广告ID不能为空');
    }

    if (!result.success) {
      throw ArgumentError('只能缓存成功的广告结果');
    }

    _cache[adId] = result;
  }

  /// 获取缓存的广告
  /// 
  /// [adId] 广告ID
  /// 
  /// 返回缓存的广告结果，如果不存在则返回null
  AdResult? get(String adId) {
    if (adId.isEmpty) {
      return null;
    }

    return _cache[adId];
  }

  /// 检查广告是否已缓存
  /// 
  /// [adId] 广告ID
  bool has(String adId) {
    if (adId.isEmpty) {
      return false;
    }

    return _cache.containsKey(adId);
  }

  /// 移除缓存的广告
  /// 
  /// [adId] 广告ID
  /// 
  /// 返回被移除的广告结果，如果不存在则返回null
  AdResult? remove(String adId) {
    if (adId.isEmpty) {
      return null;
    }

    return _cache.remove(adId);
  }

  /// 清理所有缓存
  void clear() {
    _cache.clear();
  }

  /// 获取缓存大小
  int get size => _cache.length;

  /// 检查缓存是否为空
  bool get isEmpty => _cache.isEmpty;

  /// 检查缓存是否不为空
  bool get isNotEmpty => _cache.isNotEmpty;

  /// 获取所有缓存的广告ID
  List<String> get cachedAdIds => _cache.keys.toList();

  /// 获取缓存统计信息
  Map<String, dynamic> get stats => {
        'totalCached': size,
        'cachedAdIds': cachedAdIds,
      };

  @override
  String toString() {
    return 'CacheManager(size: $size, cached: $cachedAdIds)';
  }
}
