import 'dart:async';
import 'dart:collection';
import 'package:flutter/widgets.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart' as gma;
import '../models/ad_type.dart';
import '../models/ad_callback.dart';
import '../models/ad_result.dart';


/// Google广告适配器
///
/// 负责与Google Mobile Ads SDK进行交互
/// 将Google SDK的原生API适配为我们的统一接口
class GoogleAdsAdapter {
  /// 已加载的广告存储
  /// Key: 广告ID，Value: 广告对象
  final Map<String, dynamic> _loadedAds = {};

  /// 原生广告缓存队列（预加载的广告，未绑定样式）
  /// Key: 广告ID，Value: 预加载的原生广告队列
  final Map<String, Queue<gma.NativeAd>> _nativeAdCache = {};

  /// 已使用的原生广告标记
  /// Key: 广告对象的hashCode，Value: 是否已使用
  final Map<int, bool> _usedNativeAds = {};

  /// 当前展示的原生广告
  /// Key: 广告位ID，Value: 当前展示的广告对象
  final Map<String, gma.NativeAd> _currentNativeAds = {};


  /// Banner广告的回调存储
  /// Key: 广告ID，Value: 回调和来源信息
  final Map<String, _BannerCallbackInfo> _bannerCallbacks = {};

  /// 原生广告的回调存储
  /// Key: 广告ID，Value: 回调和来源信息
  final Map<String, _BannerCallbackInfo> _nativeCallbacks = {};

  /// 原生广告对象到广告ID的映射
  /// Key: 广告对象的hashCode，Value: 广告ID
  final Map<int, String> _nativeAdToIdMap = {};

  /// 是否已初始化
  bool _initialized = false;

  /// 初始化适配器
  ///
  /// 初始化Google Mobile Ads SDK
  Future<void> initialize() async {
    if (_initialized) {
      return;
    }

    await gma.MobileAds.instance.initialize();
    _initialized = true;
  }

  /// 加载广告
  ///
  /// [adId] 广告ID
  /// [type] 广告类型
  /// [bannerSize] Banner广告尺寸（仅Banner广告有效）
  ///
  /// 返回加载结果
  Future<AdResult> loadAd(String adId, AdType type, [gma.AdSize? bannerSize]) async {
    _ensureInitialized();

    try {
      switch (type) {
        case AdType.banner:
          return await _loadBannerAd(adId, bannerSize ?? gma.AdSize.banner);
        case AdType.interstitial:
          return await _loadInterstitialAd(adId);
        case AdType.rewarded:
          return await _loadRewardedAd(adId);
        case AdType.appOpen:
          return await _loadAppOpenAd(adId);
        case AdType.native:
          return await _preLoadNativeAd(adId);
      }
    } catch (e) {
      return AdResult.failure(error: '加载${type.displayName}失败: $e');
    }
  }

  /// 加载Banner广告并设置回调
  ///
  /// [adId] 广告ID
  /// [size] Banner尺寸
  /// [callback] 回调函数
  /// [source] 广告来源
  Future<AdResult> loadBannerAdWithCallback(
    String adId,
    gma.AdSize size,
    AdCallback? callback,
    AdSource source,
  ) async {
    _ensureInitialized();

    try {
      // 存储回调信息
      _bannerCallbacks[adId] = _BannerCallbackInfo(callback, source);

      final completer = Completer<AdResult>();

      final bannerAd = gma.BannerAd(
        adUnitId: adId,
        size: size,
        request: const gma.AdRequest(),
        listener: gma.BannerAdListener(
          onAdLoaded: (ad) {
            _loadedAds[adId] = ad;
            // 获取广告平台信息
            final adPlatform = ad.responseInfo?.mediationAdapterClassName ?? "unknown";
            completer.complete(AdResult.success(source: source, adPlatform: adPlatform));
            // 触发加载成功回调
            callback?.onAdLoaded?.call(source);
            // Banner广告加载成功，但还未显示，等待Widget显示时再触发onAdShowed
          },
          onAdFailedToLoad: (ad, error) {
            ad.dispose();
            _bannerCallbacks.remove(adId);
            completer.complete(AdResult.failure(error: error.message));
            callback?.onAdFailedToLoad?.call(error.message);
          },
          onAdImpression: (ad) {
            // 第一次曝光时触发展示回调
            final callbackInfo = _bannerCallbacks[adId];
            if (callbackInfo != null) {
              callbackInfo.callback?.onAdShowed?.call(callbackInfo.source);
              callbackInfo.callback?.onAdImpression?.call(callbackInfo.source);
            }
          },
          onAdClicked: (ad) {
            final callbackInfo = _bannerCallbacks[adId];
            callbackInfo?.callback?.onAdClicked?.call(callbackInfo.source);
          },
          onAdOpened: (ad) {
            // Banner广告打开时的处理
          },
          onAdClosed: (ad) {
            final callbackInfo = _bannerCallbacks[adId];
            callbackInfo?.callback?.onAdDismissed?.call(callbackInfo.source);
          },
          onPaidEvent: (ad, valueMicros, precision, currencyCode) {
            final callbackInfo = _bannerCallbacks[adId];
            if (callbackInfo != null) {
              final paidEventData = PaidEventData(
                valueMicros: valueMicros,
                currencyCode: currencyCode,
                precision: precision.toString(),
                precisionType: precision, // 传递原始枚举
              );
              callbackInfo.callback?.onPaidEvent?.call(paidEventData, callbackInfo.source);
            }
          },
        ),
      );

      await bannerAd.load();
      return completer.future;
    } catch (e) {
      _bannerCallbacks.remove(adId);
      return AdResult.failure(error: '加载Banner广告失败: $e');
    }
  }

  /// 展示广告
  ///
  /// [adId] 广告ID
  /// [callback] 回调函数
  /// [source] 广告来源
  Future<void> showAd(
    String adId,
    AdCallback? callback,
    AdSource source,
  ) async {
    _ensureInitialized();

    final ad = _loadedAds[adId];
    if (ad == null) {
      callback?.onAdFailedToShow?.call('广告未加载或已过期');
      return;
    }

    try {
      // 根据广告类型展示
      if (ad is gma.InterstitialAd) {
        await _showInterstitialAd(ad, callback, source);
      } else if (ad is gma.RewardedAd) {
        await _showRewardedAd(ad, callback, source);
      } else if (ad is gma.AppOpenAd) {
        await _showAppOpenAd(ad, callback, source);
      } else if (ad is gma.NativeAd) {
        await _showNativeAd(ad, callback, source);
      } else if (ad is gma.BannerAd) {
        // Banner广告已经在加载时设置了完整的回调，这里不需要额外处理
        // 因为Banner广告加载后自动显示，回调已经在loadBannerAdWithCallback中触发
      } else {
        callback?.onAdFailedToShow?.call('不支持的广告类型');
      }
    } catch (e) {
      callback?.onAdFailedToShow?.call('展示广告失败: $e');
    } finally {
      // 展示后移除广告对象（Banner广告除外）
      if (ad is! gma.BannerAd) {
        _loadedAds.remove(adId);
      }
    }
  }

  /// 检查广告是否已加载
  ///
  /// [adId] 广告ID
  bool isAdLoaded(String adId) {
    return _loadedAds.containsKey(adId);
  }

  /// 获取Banner广告对象
  ///
  /// [adId] 广告ID
  gma.BannerAd? getBannerAd(String adId) {
    final ad = _loadedAds[adId];
    return ad is gma.BannerAd ? ad : null;
  }

  /// 获取Banner广告的平台信息
  ///
  /// [adId] 广告ID
  /// 返回Banner广告的平台信息，如果没有广告则返回null
  String? getBannerAdPlatform(String adId) {
    final bannerAd = getBannerAd(adId);
    return bannerAd?.responseInfo?.mediationAdapterClassName;
  }

  /// 存储Banner广告到_loadedAds中
  ///
  /// [adId] 广告ID
  /// [bannerAd] Banner广告对象
  void storeBannerAd(String adId, gma.BannerAd bannerAd) {
    _loadedAds[adId] = bannerAd;
  }

  /// 清理所有广告
  void dispose() {
    // 清理已加载的广告
    for (final ad in _loadedAds.values) {
      if (ad is gma.Ad) {
        ad.dispose();
      }
    }
    _loadedAds.clear();



    // 清理原生广告缓存
    for (final queue in _nativeAdCache.values) {
      for (final ad in queue) {
        ad.dispose();
      }
      queue.clear();
    }
    _nativeAdCache.clear();

    // 清理已使用标记
    _usedNativeAds.clear();

    // 清理当前展示的广告
    _currentNativeAds.clear();

    // 清理Banner广告回调
    _bannerCallbacks.clear();

    // 清理原生广告回调
    _nativeCallbacks.clear();

    // 清理原生广告映射关系
    _nativeAdToIdMap.clear();

    _initialized = false;
  }

  /// 加载Banner广告
  Future<AdResult> _loadBannerAd(String adId, gma.AdSize size) async {
    final completer = Completer<AdResult>();

    final bannerAd = gma.BannerAd(
      adUnitId: adId,
      size: size,
      request: const gma.AdRequest(),
      listener: gma.BannerAdListener(
        onAdLoaded: (ad) {
          _loadedAds[adId] = ad;
          // 获取广告平台信息
          final adPlatform = ad.responseInfo?.mediationAdapterClassName ?? "unknown";
          completer.complete(AdResult.success(source: AdSource.realtime, adPlatform: adPlatform));
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
          completer.complete(AdResult.failure(error: error.message));
        },
      ),
    );

    await bannerAd.load();
    return completer.future;
  }

  /// 加载插屏广告
  Future<AdResult> _loadInterstitialAd(String adId) async {
    final completer = Completer<AdResult>();

    await gma.InterstitialAd.load(
      adUnitId: adId,
      request: const gma.AdRequest(),
      adLoadCallback: gma.InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _loadedAds[adId] = ad;
          // 获取广告平台信息
          final adPlatform = ad.responseInfo?.mediationAdapterClassName ?? "unknown";
          completer.complete(AdResult.success(source: AdSource.realtime, adPlatform: adPlatform));
        },
        onAdFailedToLoad: (error) {
          completer.complete(AdResult.failure(error: error.message));
        },
      ),
    );

    return completer.future;
  }

  /// 加载激励广告
  Future<AdResult> _loadRewardedAd(String adId) async {
    final completer = Completer<AdResult>();

    await gma.RewardedAd.load(
      adUnitId: adId,
      request: const gma.AdRequest(),
      rewardedAdLoadCallback: gma.RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _loadedAds[adId] = ad;
          // 获取广告平台信息
          final adPlatform = ad.responseInfo?.mediationAdapterClassName ?? "unknown";
          completer.complete(AdResult.success(source: AdSource.realtime, adPlatform: adPlatform));
        },
        onAdFailedToLoad: (error) {
          completer.complete(AdResult.failure(error: error.message));
        },
      ),
    );

    return completer.future;
  }

  /// 加载开屏广告
  Future<AdResult> _loadAppOpenAd(String adId) async {
    final completer = Completer<AdResult>();

    await gma.AppOpenAd.load(
      adUnitId: adId,
      request: const gma.AdRequest(),
      adLoadCallback: gma.AppOpenAdLoadCallback(
        onAdLoaded: (ad) {
          _loadedAds[adId] = ad;
          // 获取广告平台信息
          final adPlatform = ad.responseInfo?.mediationAdapterClassName ?? "unknown";
          completer.complete(AdResult.success(source: AdSource.realtime, adPlatform: adPlatform));
        },
        onAdFailedToLoad: (error) {
          completer.complete(AdResult.failure(error: error.message));
        },
      ),
    );

    return completer.future;
  }







  /// 获取已加载的广告对象
  dynamic getLoadedAd(String adId) {
    return _loadedAds[adId];
  }

  /// 移除已加载的广告对象
  void removeLoadedAd(String adId) {
    _loadedAds.remove(adId);
  }

  /// 展示插屏广告
  Future<void> _showInterstitialAd(
    gma.InterstitialAd ad,
    AdCallback? callback,
    AdSource source,
  ) async {
    ad.fullScreenContentCallback = gma.FullScreenContentCallback(
      onAdShowedFullScreenContent: (ad) {
        callback?.onAdShowed?.call(source);
      },
      onAdImpression: (ad) {
        callback?.onAdImpression?.call(source);
        // 设置收益事件监听器
        (ad as dynamic).onPaidEvent = (ad, valueMicros, precision, currencyCode) {
          final paidEventData = PaidEventData(
            valueMicros: valueMicros,
            currencyCode: currencyCode,
            precision: precision.toString(),
            precisionType: precision, // 传递原始枚举
          );
          callback?.onPaidEvent?.call(paidEventData, source);
        };
      },
      onAdDismissedFullScreenContent: (ad) {
        callback?.onAdDismissed?.call(source);
        ad.dispose();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        callback?.onAdFailedToShow?.call(error.message);
        ad.dispose();
      },
      onAdClicked: (ad) {
        callback?.onAdClicked?.call(source);
      },
    );

    await ad.show();
  }

  /// 展示激励广告
  Future<void> _showRewardedAd(
    gma.RewardedAd ad,
    AdCallback? callback,
    AdSource source,
  ) async {
    ad.fullScreenContentCallback = gma.FullScreenContentCallback(
      onAdShowedFullScreenContent: (ad) {
        callback?.onAdShowed?.call(source);
      },
      onAdImpression: (ad) {
        callback?.onAdImpression?.call(source);
        // 设置收益事件监听器
        (ad as dynamic).onPaidEvent = (ad, valueMicros, precision, currencyCode) {
          final paidEventData = PaidEventData(
            valueMicros: valueMicros,
            currencyCode: currencyCode,
            precision: precision.toString(),
            precisionType: precision, // 传递原始枚举
          );
          callback?.onPaidEvent?.call(paidEventData, source);
        };
      },
      onAdDismissedFullScreenContent: (ad) {
        callback?.onAdDismissed?.call(source);
        ad.dispose();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        callback?.onAdFailedToShow?.call(error.message);
        ad.dispose();
      },
      onAdClicked: (ad) {
        callback?.onAdClicked?.call(source);
      },
    );

    await ad.show(onUserEarnedReward: (ad, reward) {
      callback?.onUserEarnedReward?.call(
        RewardData(type: reward.type, amount: reward.amount.toInt()),
        source,
      );
    });
  }

  /// 展示开屏广告
  Future<void> _showAppOpenAd(
    gma.AppOpenAd ad,
    AdCallback? callback,
    AdSource source,
  ) async {
    ad.fullScreenContentCallback = gma.FullScreenContentCallback(
      onAdShowedFullScreenContent: (ad) {
        callback?.onAdShowed?.call(source);
      },
      onAdImpression: (ad) {
        callback?.onAdImpression?.call(source);
        // 设置收益事件监听器
        (ad as dynamic).onPaidEvent = (ad, valueMicros, precision, currencyCode) {
          final paidEventData = PaidEventData(
            valueMicros: valueMicros,
            currencyCode: currencyCode,
            precision: precision.toString(),
            precisionType: precision, // 传递原始枚举
          );
          callback?.onPaidEvent?.call(paidEventData, source);
        };
      },
      onAdDismissedFullScreenContent: (ad) {
        callback?.onAdDismissed?.call(source);
        ad.dispose();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        callback?.onAdFailedToShow?.call(error.message);
        ad.dispose();
      },
      onAdClicked: (ad) {
        callback?.onAdClicked?.call(source);
      },
    );

    await ad.show();
  }

  /// 展示原生广告
  Future<void> _showNativeAd(
    gma.NativeAd ad,
    AdCallback? callback,
    AdSource source,
  ) async {
    // 原生广告不需要调用show方法，因为它是通过Widget显示的
    // 这里只需要触发展示回调
    callback?.onAdShowed?.call(source);

    // 原生广告的曝光和点击回调已经在加载时设置
    // 这里不需要额外处理
  }





  /// 预加载原生广告（不绑定样式）
  Future<AdResult> _preLoadNativeAd(String adId) async {
    final completer = Completer<AdResult>();

    try {
      // 创建原生广告，使用修改后的SDK的preLoad方法
      final nativeAd = gma.NativeAd(
        adUnitId: adId,
        request: const gma.AdRequest(),
        // 预加载时不提供factoryId和nativeTemplateStyle
        factoryId: null,
        nativeTemplateStyle: null,
        listener: gma.NativeAdListener(
          onAdLoaded: (ad) {
            final nativeAd = ad as gma.NativeAd;
            // 建立广告对象到广告ID的映射
            _nativeAdToIdMap[nativeAd.hashCode] = adId;
            // 添加到缓存队列
            _nativeAdCache.putIfAbsent(adId, () => Queue<gma.NativeAd>());
            _nativeAdCache[adId]!.add(nativeAd);
            // 获取广告平台信息
            final adPlatform = nativeAd.responseInfo?.mediationAdapterClassName ?? "unknown";
            completer.complete(AdResult.success(source: AdSource.realtime, adPlatform: adPlatform));
          },
          onAdFailedToLoad: (ad, error) {
            ad.dispose();
            completer.complete(AdResult.failure(error: '预加载原生广告失败: ${error.message}'));
          },
          onAdClicked: (ad) {
            // 通过广告对象查找对应的回调
            _handleNativeAdClicked(ad as gma.NativeAd);
          },
          onAdImpression: (ad) {
            // 通过广告对象查找对应的回调
            _handleNativeAdImpression(ad as gma.NativeAd);
          },
          onPaidEvent: (ad, valueMicros, precision, currencyCode) {
            // 处理原生广告收益事件
            _handleNativeAdPaidEventWithParams(ad as gma.NativeAd, valueMicros, precision, currencyCode);
          },
        ),
      );

      // 调用修改后SDK的preLoad方法
      await nativeAd.preLoad();
    } catch (e) {
      completer.complete(AdResult.failure(error: '预加载原生广告异常: $e'));
    }

    return completer.future;
  }

  /// 绑定Template样式并展示原生广告
  Future<AdResult> bindViewByTemplateStyle(String adId, String templateType, {AdCallback? callback}) async {
    final completer = Completer<AdResult>();

    try {
      gma.NativeAd? nativeAd;
      AdSource source = AdSource.realtime;

      // 检查缓存中是否有可用的未使用广告
      final cache = _nativeAdCache[adId];
      if (cache != null && cache.isNotEmpty) {
        // 查找第一个未使用的广告
        for (final ad in cache) {
          if (!(_usedNativeAds[ad.hashCode] ?? false)) {
            nativeAd = ad;
            source = AdSource.cache;
            break;
          }
        }
      }

      if (nativeAd != null) {
        // 存储回调信息
        if (callback != null) {
          _nativeCallbacks[adId] = _BannerCallbackInfo(callback, source);
        }

        // 使用缓存的广告，绑定Template样式
        await nativeAd.bindViewByTemplateStyle(_getTemplateStyle(templateType));
        // 标记为已使用
        _usedNativeAds[nativeAd.hashCode] = true;
        // 存储为当前展示的广告
        _currentNativeAds[adId] = nativeAd;
        // 对于缓存广告，使用保存的平台信息，如果没有则使用unknown
        final adPlatform = nativeAd.responseInfo?.mediationAdapterClassName ?? "unknown";
        completer.complete(AdResult.success(source: source, adPlatform: adPlatform));
      } else {
        // 缓存中没有可用广告，实时加载并绑定样式
        nativeAd = gma.NativeAd(
          adUnitId: adId,
          request: const gma.AdRequest(),
          factoryId: null,
          nativeTemplateStyle: _getTemplateStyle(templateType),
          listener: gma.NativeAdListener(
            onAdLoaded: (ad) {
              final nativeAd = ad as gma.NativeAd;
              // 建立广告对象到广告ID的映射
              _nativeAdToIdMap[nativeAd.hashCode] = adId;
              // 存储回调信息
              if (callback != null) {
                _nativeCallbacks[adId] = _BannerCallbackInfo(callback, AdSource.realtime);
              }
              // 存储为当前展示的广告
              _currentNativeAds[adId] = nativeAd;
              // 获取广告平台信息
              final adPlatform = nativeAd.responseInfo?.mediationAdapterClassName ?? "unknown";
              completer.complete(AdResult.success(source: AdSource.realtime, adPlatform: adPlatform));
            },
            onAdFailedToLoad: (ad, error) {
              ad.dispose();
              completer.complete(AdResult.failure(error: '加载原生广告失败: ${error.message}'));
            },
            onAdClicked: (ad) {
              _handleNativeAdClicked(ad as gma.NativeAd);
            },
            onAdImpression: (ad) {
              _handleNativeAdImpression(ad as gma.NativeAd);
            },
            onPaidEvent: (ad, valueMicros, precision, currencyCode) {
              _handleNativeAdPaidEventWithParams(ad as gma.NativeAd, valueMicros, precision, currencyCode);
            },
          ),
        );

        await nativeAd.load();
      }
    } catch (e) {
      completer.complete(AdResult.failure(error: '绑定Template样式异常: $e'));
    }

    return completer.future;
  }

  /// 绑定Factory样式并展示原生广告
  Future<AdResult> bindViewByFactoryId(String adId, String factoryId, {AdCallback? callback}) async {
    final completer = Completer<AdResult>();

    try {
      gma.NativeAd? nativeAd;
      AdSource source = AdSource.realtime;

      // 检查缓存中是否有可用的未使用广告
      final cache = _nativeAdCache[adId];
      if (cache != null && cache.isNotEmpty) {
        // 查找第一个未使用的广告
        for (final ad in cache) {
          if (!(_usedNativeAds[ad.hashCode] ?? false)) {
            nativeAd = ad;
            source = AdSource.cache;
            break;
          }
        }
      }

      if (nativeAd != null) {
        // 存储回调信息
        if (callback != null) {
          _nativeCallbacks[adId] = _BannerCallbackInfo(callback, source);
        }

        // 使用缓存的广告，绑定Factory样式
        await nativeAd.bindViewByFactoryId(factoryId);
        // 标记为已使用
        _usedNativeAds[nativeAd.hashCode] = true;
        // 存储为当前展示的广告
        _currentNativeAds[adId] = nativeAd;
        // 对于缓存广告，使用保存的平台信息，如果没有则使用unknown
        final adPlatform = nativeAd.responseInfo?.mediationAdapterClassName ?? "unknown";
        completer.complete(AdResult.success(source: source, adPlatform: adPlatform));
      } else {
        // 缓存中没有可用广告，实时加载并绑定样式
        nativeAd = gma.NativeAd(
          adUnitId: adId,
          request: const gma.AdRequest(),
          factoryId: factoryId,
          nativeTemplateStyle: null,
          listener: gma.NativeAdListener(
            onAdLoaded: (ad) {
              final nativeAd = ad as gma.NativeAd;
              // 建立广告对象到广告ID的映射
              _nativeAdToIdMap[nativeAd.hashCode] = adId;
              // 存储回调信息
              if (callback != null) {
                _nativeCallbacks[adId] = _BannerCallbackInfo(callback, AdSource.realtime);
              }
              // 存储为当前展示的广告
              _currentNativeAds[adId] = nativeAd;
              // 获取广告平台信息
              final adPlatform = nativeAd.responseInfo?.mediationAdapterClassName ?? "unknown";
              completer.complete(AdResult.success(source: AdSource.realtime, adPlatform: adPlatform));
            },
            onAdFailedToLoad: (ad, error) {
              ad.dispose();
              completer.complete(AdResult.failure(error: '加载原生广告失败: ${error.message}'));
            },
            onAdClicked: (ad) {
              _handleNativeAdClicked(ad as gma.NativeAd);
            },
            onAdImpression: (ad) {
              _handleNativeAdImpression(ad as gma.NativeAd);
            },
            onPaidEvent: (ad, valueMicros, precision, currencyCode) {
              _handleNativeAdPaidEventWithParams(ad as gma.NativeAd, valueMicros, precision, currencyCode);
            },
          ),
        );

        await nativeAd.load();
      }
    } catch (e) {
      completer.complete(AdResult.failure(error: '绑定Factory样式异常: $e'));
    }

    return completer.future;
  }

  /// 获取原生广告Widget
  Widget getNativeAdWidget(String adId) {
    // 从当前展示的广告中获取
    final ad = _currentNativeAds[adId];
    if (ad != null) {
      return gma.AdWidget(
        key: ValueKey('native_ad_${adId}_${ad.hashCode}'),
        ad: ad,
      );
    }

    return const SizedBox(
      height: 200,
      child: Center(
        child: Text('原生广告未加载'),
      ),
    );
  }

  /// 清理已使用的原生广告（内存优化）
  void cleanupUsedNativeAds(String adId) {
    final cache = _nativeAdCache[adId];
    if (cache != null) {
      // 移除已使用的广告
      cache.removeWhere((ad) => _usedNativeAds[ad.hashCode] ?? false);

      // 清理对应的使用标记
      final toRemove = <int>[];
      for (final hashCode in _usedNativeAds.keys) {
        bool found = false;
        for (final queue in _nativeAdCache.values) {
          if (queue.any((ad) => ad.hashCode == hashCode)) {
            found = true;
            break;
          }
        }
        if (!found) {
          toRemove.add(hashCode);
        }
      }
      for (final hashCode in toRemove) {
        _usedNativeAds.remove(hashCode);
      }
    }
  }

  /// 检查是否有可用的未使用原生广告
  bool hasUnusedNativeAd(String adId) {
    final cache = _nativeAdCache[adId];
    if (cache == null || cache.isEmpty) {
      return false;
    }

    // 检查是否有未使用的广告
    for (final ad in cache) {
      if (!(_usedNativeAds[ad.hashCode] ?? false)) {
        return true;
      }
    }
    return false;
  }

  /// 处理原生广告点击事件
  void _handleNativeAdClicked(gma.NativeAd ad) {
    final adId = _nativeAdToIdMap[ad.hashCode];
    if (adId != null) {
      final callbackInfo = _nativeCallbacks[adId];
      callbackInfo?.callback?.onAdClicked?.call(callbackInfo.source);
    }
  }

  /// 处理原生广告impression事件
  void _handleNativeAdImpression(gma.NativeAd ad) {
    final adId = _nativeAdToIdMap[ad.hashCode];
    if (adId != null) {
      final callbackInfo = _nativeCallbacks[adId];
      callbackInfo?.callback?.onAdImpression?.call(callbackInfo.source);
    }
  }

  /// 处理原生广告收益事件（新API格式）
  void _handleNativeAdPaidEventWithParams(gma.NativeAd ad, double valueMicros, dynamic precision, String currencyCode) {
    final adId = _nativeAdToIdMap[ad.hashCode];
    if (adId != null) {
      final callbackInfo = _nativeCallbacks[adId];
      final paidEventData = PaidEventData(
        valueMicros: valueMicros,
        currencyCode: currencyCode,
        precision: precision.toString(), // 转换为字符串
        precisionType: precision, // 传递原始枚举
      );
      callbackInfo?.callback?.onPaidEvent?.call(paidEventData, callbackInfo.source);
    }
  }



  /// 获取模板样式
  gma.NativeTemplateStyle _getTemplateStyle(String templateType) {
    switch (templateType.toLowerCase()) {
      case 'small':
        return gma.NativeTemplateStyle(templateType: gma.TemplateType.small);
      case 'medium':
        return gma.NativeTemplateStyle(templateType: gma.TemplateType.medium);
      default:
        return gma.NativeTemplateStyle(templateType: gma.TemplateType.medium);
    }
  }

  /// 确保适配器已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('GoogleAdsAdapter未初始化');
    }
  }
}

/// Banner广告回调信息
class _BannerCallbackInfo {
  final AdCallback? callback;
  final AdSource source;

  _BannerCallbackInfo(this.callback, this.source);
}
