/// 广告来源枚举
/// 
/// 标识广告的来源，用于区分缓存广告和实时加载的广告
enum AdSource {
  /// 缓存广告 - 从预加载的缓存中获取的广告
  cache,

  /// 实时广告 - 实时加载的广告
  realtime,
}

/// 广告来源扩展方法
extension AdSourceExtension on AdSource {
  /// 获取广告来源的显示名称
  String get displayName {
    switch (this) {
      case AdSource.cache:
        return '缓存';
      case AdSource.realtime:
        return '实时';
    }
  }

  /// 获取广告来源的英文名称
  String get name {
    switch (this) {
      case AdSource.cache:
        return 'cache';
      case AdSource.realtime:
        return 'realtime';
    }
  }
}

/// 广告结果类
///
/// 表示广告操作的结果
class AdResult {
  /// 操作是否成功
  final bool success;

  /// 错误信息（仅在失败时有值）
  final String? error;

  /// 广告来源（仅在成功时有值）
  final AdSource? source;

  /// 广告平台信息（仅在成功时有值）
  final String? adPlatform;

  /// 时间戳
  final DateTime timestamp;

  /// 过期时间
  final DateTime? expiresAt;

  /// 创建广告结果
  ///
  /// [success] 是否成功
  /// [error] 错误信息
  /// [source] 广告来源
  /// [adPlatform] 广告平台信息
  /// [timestamp] 时间戳，默认为当前时间
  /// [expiresAt] 过期时间
  AdResult({
    required this.success,
    this.error,
    this.source,
    this.adPlatform,
    DateTime? timestamp,
    this.expiresAt,
  }) : timestamp = timestamp ?? DateTime.now();

  /// 创建成功结果
  ///
  /// [source] 广告来源
  /// [adPlatform] 广告平台信息
  /// [expiresAt] 过期时间
  factory AdResult.success({
    required AdSource source,
    String? adPlatform,
    DateTime? expiresAt,
  }) {
    return AdResult(
      success: true,
      source: source,
      adPlatform: adPlatform,
      expiresAt: expiresAt,
    );
  }

  /// 创建失败结果
  ///
  /// [error] 错误信息
  factory AdResult.failure({required String error}) {
    return AdResult(
      success: false,
      error: error,
    );
  }

  /// 检查广告是否已过期
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 检查广告是否有效（成功且未过期）
  bool get isValid {
    return success && !isExpired;
  }

  @override
  String toString() {
    if (success) {
      return 'AdResult.success(source: $source, timestamp: $timestamp)';
    } else {
      return 'AdResult.failure(error: $error, timestamp: $timestamp)';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdResult &&
        other.success == success &&
        other.error == error &&
        other.source == source;
  }

  @override
  int get hashCode => success.hashCode ^ error.hashCode ^ source.hashCode;
}

/// 奖励数据类（激励广告专用）
/// 
/// 包含用户观看激励广告后获得的奖励信息
class RewardData {
  /// 奖励类型（如：coins, points等）
  final String type;

  /// 奖励数量
  final int amount;

  /// 创建奖励数据
  /// 
  /// [type] 奖励类型
  /// [amount] 奖励数量
  const RewardData({
    required this.type,
    required this.amount,
  });

  @override
  String toString() {
    return 'RewardData(type: $type, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RewardData && other.type == type && other.amount == amount;
  }

  @override
  int get hashCode => type.hashCode ^ amount.hashCode;
}

/// 收益事件数据类
///
/// 包含广告产生的收益信息
class PaidEventData {
  /// 收益金额（微分单位，需要除以1,000,000得到实际金额）
  final double valueMicros;

  /// 货币代码（如：USD, CNY等）
  final String currencyCode;

  /// 精度级别（字符串格式，向后兼容）
  final String precision;

  /// 精度级别（枚举格式，推荐使用）
  final dynamic precisionType;

  /// 创建收益事件数据
  ///
  /// [valueMicros] 收益金额（微分单位）
  /// [currencyCode] 货币代码
  /// [precision] 精度级别（字符串格式）
  /// [precisionType] 精度级别（枚举格式，可选）
  const PaidEventData({
    required this.valueMicros,
    required this.currencyCode,
    required this.precision,
    this.precisionType,
  });

  /// 获取实际收益金额
  double get actualValue => valueMicros / 1000000.0;

  @override
  String toString() {
    return 'PaidEventData(value: $actualValue $currencyCode, precision: $precision)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaidEventData &&
        other.valueMicros == valueMicros &&
        other.currencyCode == currencyCode &&
        other.precision == precision;
  }

  @override
  int get hashCode =>
      valueMicros.hashCode ^ currencyCode.hashCode ^ precision.hashCode;
}
