import 'package:ad_sdk/ad_sdk.dart';

/// 广告请求
class AdRequest {
  /// 广告类型
  final AdType adType;

  /// 广告位ID
  final String placementId;

  /// 实际使用的广告ID
  final String adId;

  /// 是否使用备用ID
  final bool useBackup;

  /// 请求时间戳
  final DateTime timestamp;

  /// 自定义参数
  final Map<String, dynamic>? customParams;

  /// Banner广告尺寸（仅对Banner广告有效）
  final String? bannerSize;

  const AdRequest({
    required this.adType,
    required this.placementId,
    required this.adId,
    this.useBackup = false,
    required this.timestamp,
    this.customParams,
    this.bannerSize,
  });
  
  /// 创建广告请求
  factory AdRequest.create({
    required AdType adType,
    required String placementId,
    required String adId,
    bool useBackup = false,
    Map<String, dynamic>? customParams,
    String? bannerSize,
  }) {
    return AdRequest(
      adType: adType,
      placementId: placementId,
      adId: adId,
      useBackup: useBackup,
      timestamp: DateTime.now(),
      customParams: customParams,
      bannerSize: bannerSize,
    );
  }
  
  @override
  String toString() {
    return 'AdRequest{adType: $adType, placementId: $placementId, adId: $adId, useBackup: $useBackup, timestamp: $timestamp, customParams: $customParams, bannerSize: $bannerSize}';
  }
}

/// 广告展示选项
class AdShowOptions {
  /// 展示回调
  final AdShowCallback? callback;
  
  /// 自定义参数
  final Map<String, dynamic>? customParams;
  
  const AdShowOptions({
    this.callback,
    this.customParams,
  });
  
  @override
  String toString() {
    return 'AdShowOptions{callback: $callback, customParams: $customParams}';
  }
}

/// 展示来源枚举
enum AdShowSource {
  cache,    // 使用缓存的广告
  realtime, // 实时请求广告
}

/// 收益事件数据
class PaidEventData {
  /// 收益金额（微分单位）
  final double valueMicros;

  /// 精度
  final String precision;

  /// 货币代码
  final String currencyCode;

  const PaidEventData({
    required this.valueMicros,
    required this.precision,
    required this.currencyCode,
  });

  @override
  String toString() {
    return 'PaidEventData{valueMicros: $valueMicros, precision: $precision, currencyCode: $currencyCode}';
  }
}

/// 奖励数据
class RewardData {
  /// 奖励类型
  final String type;

  /// 奖励数量
  final int amount;

  const RewardData({
    required this.type,
    required this.amount,
  });

  @override
  String toString() {
    return 'RewardData{type: $type, amount: $amount}';
  }
}

/// 详细广告回调
class AdDetailedCallback {
  /// 广告加载成功
  final VoidCallback? onAdLoaded;

  /// 广告加载失败
  final Function(String error)? onAdFailedToLoad;

  /// 广告开始展示
  final Function(AdSource source)? onAdShowed;

  /// 广告曝光
  final Function(AdSource source)? onAdImpression;

  /// 广告关闭
  final Function(AdSource source)? onAdDismissed;

  /// 广告展示失败
  final Function(String error)? onAdFailedToShow;

  /// 广告点击
  final Function(AdSource source)? onAdClicked;

  /// 用户获得奖励（激励广告专用）
  final Function(RewardData reward, AdSource source)? onUserEarnedReward;

  /// 收益事件
  final Function(PaidEventData data, AdSource source)? onPaidEvent;

  const AdDetailedCallback({
    this.onAdLoaded,
    this.onAdFailedToLoad,
    this.onAdShowed,
    this.onAdImpression,
    this.onAdDismissed,
    this.onAdFailedToShow,
    this.onAdClicked,
    this.onUserEarnedReward,
    this.onPaidEvent,
  });
}

/// 广告展示回调（保持向后兼容）
typedef AdShowCallback = void Function(AdShowResult result);

/// 广告展示结果（保持向后兼容）
class AdShowResult {
  /// 是否成功展示
  final bool success;

  /// 广告ID
  final String? adId;

  /// 错误信息
  final String? error;

  /// 广告来源
  final AdSource source;

  /// 时间戳
  final DateTime timestamp;

  const AdShowResult({
    required this.success,
    this.adId,
    this.error,
    required this.source,
    required this.timestamp,
  });

  /// 创建成功结果
  factory AdShowResult.success({
    required String adId,
    required AdSource source,
  }) {
    return AdShowResult(
      success: true,
      adId: adId,
      source: source,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory AdShowResult.failure({
    required String error,
    required AdSource source,
  }) {
    return AdShowResult(
      success: false,
      error: error,
      source: source,
      timestamp: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'AdShowResult{success: $success, adId: $adId, error: $error, source: $source, timestamp: $timestamp}';
  }
}

