import 'ad_type.dart';

/// 广告配置类
///
/// 包含所有广告类型的配置信息
class AdConfig {
  /// 广告配置映射
  /// Key: 广告类型，Value: 广告ID配置
  final Map<AdType, AdIds> ads;

  /// 是否启用自动缓存，默认为true
  /// 启用后，广告展示成功后会自动预加载下一条
  final bool autoCache;

  /// 是否启用广告检查器，默认为false
  /// 启用后，初始化完成后会自动打开广告检查器
  /// 注意：仅在调试模式下有效，生产环境会被忽略
  final bool enableAdInspector;

  /// 创建广告配置
  ///
  /// [ads] 广告配置映射
  /// [autoCache] 是否启用自动缓存，默认为true
  /// [enableAdInspector] 是否启用广告检查器，默认为false
  const AdConfig({
    required this.ads,
    this.autoCache = true,
    this.enableAdInspector = false,
  });

  /// 获取主广告ID
  ///
  /// [type] 广告类型
  ///
  /// 返回对应的主广告ID，如果不存在则返回空字符串
  String getMainAdId(AdType type) {
    return ads[type]?.main ?? '';
  }

  /// 获取备用广告ID
  ///
  /// [type] 广告类型
  ///
  /// 返回对应的备用广告ID，如果不存在则返回空字符串
  String getBackupAdId(AdType type) {
    return ads[type]?.backup ?? '';
  }

  /// 检查广告类型是否存在
  ///
  /// [type] 广告类型
  bool hasAdType(AdType type) {
    return ads.containsKey(type);
  }

  /// 检查指定广告类型是否有有效配置
  ///
  /// [type] 广告类型
  bool hasValidAds(AdType type) {
    return ads[type]?.hasValidAds ?? false;
  }
}

/// 广告位配置类
/// 
/// 包含一个广告位下所有广告类型的配置
class AdPlacement {
  /// 广告类型配置映射
  /// Key: 广告类型，Value: 广告ID配置
  final Map<AdType, AdIds> ads;

  /// 创建广告位配置
  /// 
  /// [ads] 广告类型配置映射
  const AdPlacement({
    required this.ads,
  });

  /// 获取主广告ID
  /// 
  /// [type] 广告类型
  String getMainAdId(AdType type) {
    return ads[type]?.main ?? '';
  }

  /// 获取备用广告ID
  /// 
  /// [type] 广告类型
  String getBackupAdId(AdType type) {
    return ads[type]?.backup ?? '';
  }

  /// 检查是否配置了指定广告类型
  /// 
  /// [type] 广告类型
  bool hasAdType(AdType type) {
    return ads.containsKey(type);
  }

  /// 获取所有配置的广告类型
  List<AdType> get supportedAdTypes {
    return ads.keys.toList();
  }
}

/// 广告ID配置类
/// 
/// 包含主广告和备用广告的ID
class AdIds {
  /// 主广告ID
  final String main;

  /// 备用广告ID
  final String backup;

  /// 创建广告ID配置
  /// 
  /// [main] 主广告ID
  /// [backup] 备用广告ID
  const AdIds({
    required this.main,
    required this.backup,
  });

  /// 检查主广告ID是否有效
  bool get hasValidMain => main.isNotEmpty;

  /// 检查备用广告ID是否有效
  bool get hasValidBackup => backup.isNotEmpty;

  /// 检查是否有有效的广告ID
  bool get hasValidAds => hasValidMain || hasValidBackup;

  @override
  String toString() {
    return 'AdIds(main: $main, backup: $backup)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdIds && other.main == main && other.backup == backup;
  }

  @override
  int get hashCode => main.hashCode ^ backup.hashCode;
}
