/// 广告类型枚举
/// 
/// 定义了SDK支持的所有广告类型
enum AdType {
  /// Banner广告 - 横幅广告，通常显示在屏幕顶部或底部
  banner,

  /// 插屏广告 - 全屏广告，在应用的自然过渡点显示
  interstitial,

  /// 激励广告 - 用户观看完整广告后获得奖励
  rewarded,

  /// 开屏广告 - 应用启动时显示的全屏广告
  appOpen,

  /// 原生广告 - 与应用内容融合的广告
  native,
}

/// 广告类型扩展方法
extension AdTypeExtension on AdType {
  /// 获取广告类型的显示名称
  String get displayName {
    switch (this) {
      case AdType.banner:
        return 'Banner广告';
      case AdType.interstitial:
        return '插屏广告';
      case AdType.rewarded:
        return '激励广告';
      case AdType.appOpen:
        return '开屏广告';
      case AdType.native:
        return '原生广告';
    }
  }

  /// 获取广告类型的英文名称
  String get name {
    switch (this) {
      case AdType.banner:
        return 'banner';
      case AdType.interstitial:
        return 'interstitial';
      case AdType.rewarded:
        return 'rewarded';
      case AdType.appOpen:
        return 'appOpen';
      case AdType.native:
        return 'native';
    }
  }
}
