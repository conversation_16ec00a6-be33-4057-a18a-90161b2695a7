import 'ad_type.dart';

/// 广告事件定义
class AdEvents {
  /// 广告SDK底层数据上报事件名
  static const String eventNameAdSdk = "ad_sdk_base";
  
  /// 事件动作类型
  static const String buildAdSdkAction = "action";
  static const int buildAdSdkAction1 = 1; // 广告请求
  static const int buildAdSdkAction2 = 2; // 广告返回
  static const int buildAdSdkAction3 = 3; // 广告展示
  static const int buildAdSdkAction4 = 4; // 广告点击
  static const int buildAdSdkAction5 = 5; // 广告加载失败
  static const int buildAdSdkAction6 = 6; // 广告展示失败
  static const int buildAdSdkAction8 = 8; // banner展示回调
  
  /// 事件参数
  static const String buildAdSdkPlacementId = "placement_id"; // 广告位ID
  static const String buildAdSdkFilledTime = "filled_time"; // 广告填充时间
  static const String buildAdSdkFallReason = "fall_reason"; // 广告填充失败原因
  static const String buildAdSdkAdPlatform = "ad_platform"; // 广告源
  static const String buildAdSdkAdMediation = "ad_mediation"; // 广告中介
  
  /// 广告类型字符串映射
  static String getAdTypeString(AdType adType) {
    switch (adType) {
      case AdType.banner:
        return 'banner';
      case AdType.interstitial:
        return 'interstitial';
      case AdType.rewarded:
        return 'rewarded';
      case AdType.appOpen:
        return 'app_open';
      case AdType.native:
        return 'native';
    }
  }
  
  /// 创建广告请求事件参数
  static Map<String, dynamic> createAdRequestParams({
    required String placementId,
  }) {
    return {
      buildAdSdkAction: buildAdSdkAction1,
      buildAdSdkPlacementId: placementId,
      buildAdSdkAdMediation: 'admob',
    };
  }
  
  /// 创建广告返回事件参数
  static Map<String, dynamic> createAdReturnParams({
    required String placementId,
    required String adPlatform,
    int filledTime = 0,
  }) {
    return {
      buildAdSdkAction: buildAdSdkAction2,
      buildAdSdkPlacementId: placementId,
      buildAdSdkAdPlatform: adPlatform,
      buildAdSdkAdMediation: 'admob',
      buildAdSdkFilledTime: filledTime,
    };
  }
  
  /// 创建广告展示事件参数
  static Map<String, dynamic> createAdShowParams({
    required String placementId,
    required String adPlatform,
  }) {
    return {
      buildAdSdkAction: buildAdSdkAction3,
      buildAdSdkPlacementId: placementId,
      buildAdSdkAdPlatform: adPlatform,
      buildAdSdkAdMediation: 'admob',
    };
  }

  /// 创建广告点击事件参数
  static Map<String, dynamic> createAdClickParams({
    required String placementId,
    required String adPlatform,
  }) {
    return {
      buildAdSdkAction: buildAdSdkAction4,
      buildAdSdkPlacementId: placementId,
      buildAdSdkAdPlatform: adPlatform,
      buildAdSdkAdMediation: 'admob',
    };
  }

  /// 创建广告加载失败事件参数
  static Map<String, dynamic> createAdLoadFailParams({
    required String placementId,
    required String adPlatform,
    required String fallReason,
  }) {
    return {
      buildAdSdkAction: buildAdSdkAction5,
      buildAdSdkPlacementId: placementId,
      buildAdSdkAdPlatform: adPlatform,
      buildAdSdkAdMediation: 'admob',
      buildAdSdkFallReason: fallReason,
    };
  }

  /// 创建广告展示失败事件参数
  static Map<String, dynamic> createAdShowFailParams({
    required String placementId,
    required String adPlatform,
    required String fallReason,
  }) {
    return {
      buildAdSdkAction: buildAdSdkAction6,
      buildAdSdkPlacementId: placementId,
      buildAdSdkAdPlatform: adPlatform,
      buildAdSdkAdMediation: 'admob',
      buildAdSdkFallReason: fallReason,
    };
  }

  /// 创建Banner展示回调事件参数
  static Map<String, dynamic> createBannerShowParams({
    required String placementId,
    required String adPlatform,
  }) {
    return {
      buildAdSdkAction: buildAdSdkAction8,
      buildAdSdkPlacementId: placementId,
      buildAdSdkAdPlatform: adPlatform,
      buildAdSdkAdMediation: 'admob',
    };
  }
}
