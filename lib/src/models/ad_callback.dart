import 'package:google_mobile_ads/google_mobile_ads.dart' as gma;
import 'ad_result.dart';
import 'ad_events.dart';

/// 广告回调类
/// 
/// 提供广告生命周期的所有回调事件
/// 基于Google Mobile Ads SDK的原生回调设计
class AdCallback {
  /// 广告加载成功回调
  ///
  /// 当广告成功加载到缓存时触发
  /// [source] 广告来源（缓存或实时）
  final Function(AdSource source)? onAdLoaded;

  /// 广告加载失败回调
  /// 
  /// 当广告加载失败时触发
  /// [error] 错误信息
  final Function(String error)? onAdFailedToLoad;

  /// 广告开始展示回调
  /// 
  /// 当广告开始显示时触发
  /// [source] 广告来源（缓存或实时）
  final Function(AdSource source)? onAdShowed;

  /// 广告曝光回调
  /// 
  /// 当广告被用户看到时触发（对应Google SDK的onAdImpression）
  /// [source] 广告来源（缓存或实时）
  final Function(AdSource source)? onAdImpression;

  /// 广告点击回调
  /// 
  /// 当用户点击广告时触发
  /// [source] 广告来源（缓存或实时）
  final Function(AdSource source)? onAdClicked;

  /// 广告关闭回调
  /// 
  /// 当广告被关闭时触发（对应Google SDK的onAdDismissed）
  /// [source] 广告来源（缓存或实时）
  final Function(AdSource source)? onAdDismissed;

  /// 广告展示失败回调
  /// 
  /// 当广告展示失败时触发
  /// [error] 错误信息
  final Function(String error)? onAdFailedToShow;

  /// 用户获得奖励回调（仅激励广告）
  /// 
  /// 当用户完整观看激励广告并获得奖励时触发
  /// [reward] 奖励数据
  /// [source] 广告来源（缓存或实时）
  final Function(RewardData reward, AdSource source)? onUserEarnedReward;

  /// 广告收益回调
  /// 
  /// 当广告产生收益时触发（对应Google SDK的onPaidEvent）
  /// [data] 收益数据
  /// [source] 广告来源（缓存或实时）
  final Function(PaidEventData data, AdSource source)? onPaidEvent;

  /// 创建广告回调
  /// 
  /// 所有回调都是可选的，只需要设置需要的回调即可
  const AdCallback({
    this.onAdLoaded,
    this.onAdFailedToLoad,
    this.onAdShowed,
    this.onAdImpression,
    this.onAdClicked,
    this.onAdDismissed,
    this.onAdFailedToShow,
    this.onUserEarnedReward,
    this.onPaidEvent,
  });

  /// 创建一个简单的回调，只包含成功和失败
  ///
  /// [onSuccess] 成功回调
  /// [onFailure] 失败回调
  factory AdCallback.simple({
    VoidCallback? onSuccess,
    Function(String error)? onFailure,
  }) {
    return AdCallback(
      onAdLoaded: onSuccess != null ? (_) => onSuccess() : null,
      onAdFailedToLoad: onFailure,
      onAdShowed: onSuccess != null ? (_) => onSuccess() : null,
      onAdFailedToShow: onFailure,
    );
  }

  /// 创建带事件上报的回调包装器
  ///
  /// [placementId] 广告位ID（实际的广告ID）
  /// [eventReporter] 事件上报函数
  /// [originalCallback] 原始回调
  /// [startTime] 广告加载开始时间
  /// [isBanner] 是否为Banner广告
  /// [scene] 广告场景参数（可选）
  /// [platform] 广告平台信息（可选）
  /// [platformGetter] 获取最新平台信息的函数（可选）
  /// [priceReporter] 价格事件上报函数（可选）
  factory AdCallback.withReporting({
    required String placementId,
    required Function(String, Map<String, dynamic>) eventReporter,
    AdCallback? originalCallback,
    DateTime? startTime,
    bool isBanner = false,
    int? scene, // 广告场景参数
    String? platform, // 广告平台信息
    String? Function(String)? platformGetter, // 获取最新平台信息的函数
    Function(int, String, String, double, String, int)? priceReporter, // 价格事件上报函数
  }) {
    return AdCallback(
      onAdLoaded: (source) {
        // 底层埋点：只有实时加载的广告才上报，缓存广告不上报
        if (source == AdSource.realtime) {
          // 计算填充时间（毫秒）
          final filledTime = startTime != null
            ? DateTime.now().difference(startTime).inMilliseconds
            : 0;

          // 重新获取最新的平台信息（可能在加载过程中已更新）
          final latestPlatform = platformGetter?.call(placementId) ?? platform ?? 'unknown';

          // 上报广告返回事件
          eventReporter(AdEvents.eventNameAdSdk, AdEvents.createAdReturnParams(
            placementId: placementId,
            adPlatform: latestPlatform, // 使用最新的平台信息
            filledTime: filledTime,
          ));
        }

        // 业务层回调：无论缓存还是实时都要触发
        originalCallback?.onAdLoaded?.call(source);
      },
      onAdFailedToLoad: (error) {
        // 底层埋点：加载失败总是上报（因为肯定是实时请求失败）
        eventReporter(AdEvents.eventNameAdSdk, AdEvents.createAdLoadFailParams(
          placementId: placementId,
          adPlatform: 'unknown', // 加载失败时无法获取平台信息
          fallReason: error,
        ));

        // 业务层回调
        originalCallback?.onAdFailedToLoad?.call(error);
      },
      onAdShowed: (source) {
        // 底层埋点：根据广告类型选择不同的事件
        if (isBanner) {
          // Banner广告使用Banner展示回调事件 (action: 8)
          eventReporter(AdEvents.eventNameAdSdk, AdEvents.createBannerShowParams(
            placementId: placementId,
            adPlatform: platform ?? 'unknown', // 使用传入的平台信息
          ));
        } else {
          // 其他广告使用普通展示事件 (action: 3)
          eventReporter(AdEvents.eventNameAdSdk, AdEvents.createAdShowParams(
            placementId: placementId,
            adPlatform: platform ?? 'unknown', // 使用传入的平台信息
          ));
        }

        // 业务层回调
        originalCallback?.onAdShowed?.call(source);
      },
      onAdFailedToShow: (error) {
        // 底层埋点：展示失败总是上报
        eventReporter(AdEvents.eventNameAdSdk, AdEvents.createAdShowFailParams(
          placementId: placementId,
          adPlatform: 'unknown', // 展示失败时无法获取平台信息
          fallReason: error,
        ));

        // 业务层回调
        originalCallback?.onAdFailedToShow?.call(error);
      },
      onAdClicked: (source) {
        // 底层埋点：点击事件总是上报
        eventReporter(AdEvents.eventNameAdSdk, AdEvents.createAdClickParams(
          placementId: placementId,
          adPlatform: platform ?? 'unknown', // 使用传入的平台信息
        ));

        // 业务层回调
        originalCallback?.onAdClicked?.call(source);
      },
      onAdImpression: originalCallback?.onAdImpression,
      onAdDismissed: originalCallback?.onAdDismissed,
      onUserEarnedReward: originalCallback?.onUserEarnedReward,
      onPaidEvent: (data, source) {
        // 价格事件上报
        if (priceReporter != null && scene != null) {
          // 优先使用枚举类型的精确度，如果没有则使用字符串
          final precisionValue = data.precisionType != null
            ? _getPrecisionTypeFromEnum(data.precisionType as gma.PrecisionType)
            : _getPrecisionType(data.precision);

          priceReporter(
            scene,                                    // 场景
            placementId,                             // 广告ID
            platform ?? 'unknown',                   // 平台来源（传入的平台信息）
            data.valueMicros / 1000000.0,            // 价格（转换为美元）
            data.currencyCode,                       // 货币
            precisionValue,                          // 精确度
          );
        }

        // 业务层回调
        originalCallback?.onPaidEvent?.call(data, source);
      },
    );
  }

  /// 获取精确度类型对应的整数值
  ///
  /// [precision] 精确度枚举
  /// 返回对应的整数值：0=未知, 1=预估, 2=发布者提供, 3=精准
  static int _getPrecisionTypeFromEnum(gma.PrecisionType precision) {
    switch (precision) {
      case gma.PrecisionType.estimated:
        return 1; // 预估
      case gma.PrecisionType.publisherProvided:
        return 2; // 发布者提供
      case gma.PrecisionType.precise:
        return 3; // 精准
      case gma.PrecisionType.unknown:
        return 0; // 未知
    }
  }

  /// 从字符串获取精确度类型对应的整数值（向后兼容）
  ///
  /// [precision] 精确度字符串
  /// 返回对应的整数值：0=未知, 1=预估, 2=发布者提供, 3=精准
  static int _getPrecisionType(String precision) {
    switch (precision.toLowerCase()) {
      case 'estimated':
        return 1; // 预估
      case 'publisher_provided':
        return 2; // 发布者提供
      case 'precise':
        return 3; // 精准
      default:
        return 0; // 未知
    }
  }

  /// 检查是否有任何回调被设置
  bool get hasAnyCallback {
    return onAdLoaded != null ||
        onAdFailedToLoad != null ||
        onAdShowed != null ||
        onAdImpression != null ||
        onAdClicked != null ||
        onAdDismissed != null ||
        onAdFailedToShow != null ||
        onUserEarnedReward != null ||
        onPaidEvent != null;
  }

  /// 检查是否有加载相关的回调
  bool get hasLoadCallback {
    return onAdLoaded != null || onAdFailedToLoad != null;
  }

  /// 检查是否有展示相关的回调
  bool get hasShowCallback {
    return onAdShowed != null ||
        onAdImpression != null ||
        onAdClicked != null ||
        onAdDismissed != null ||
        onAdFailedToShow != null;
  }
}

/// 简化的回调类型定义
typedef VoidCallback = void Function();
