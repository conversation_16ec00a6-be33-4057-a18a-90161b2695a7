import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';

/// 启动缓存服务
/// 负责缓存关键数据以实现快速启动
class StartupCacheService {
  static const String _homeDataKey = 'startup_cache_home_data';
  static const String _categoriesKey = 'startup_cache_categories';
  static const String _selectedCountryKey = 'startup_cache_selected_country';
  static const String _popularStationsKey = 'startup_cache_popular_stations';
  static const String _mostClickedStationsKey = 'startup_cache_most_clicked_stations';
  static const String _highQualityStationsKey = 'startup_cache_high_quality_stations';
  static const String _latestStationsKey = 'startup_cache_latest_stations';
  static const String _cacheTimestampKey = 'startup_cache_timestamp';
  static const String _cacheVersionKey = 'startup_cache_version';
  static const String _selectedCategoryKey = 'startup_cache_selected_category';
  
  // 缓存版本，用于处理数据结构变更
  static const int _currentCacheVersion = 2;
  
  // 缓存有效期：24小时
  static const Duration _cacheValidDuration = Duration(hours: 24);
  
  late final SharedPreferences _prefs;
  bool _initialized = false;

  /// 初始化缓存服务
  Future<void> initialize() async {
    if (_initialized) return;
    
    _prefs = await SharedPreferences.getInstance();
    _initialized = true;
    
    // 检查缓存版本，如果版本不匹配则清空缓存
    final cachedVersion = _prefs.getInt(_cacheVersionKey) ?? 0;
    if (cachedVersion != _currentCacheVersion) {
      print('🗑️ 缓存版本不匹配，清空启动缓存');
      await clearAllCache();
      await _prefs.setInt(_cacheVersionKey, _currentCacheVersion);
    }
  }

  /// 检查缓存是否有效
  bool isCacheValid() {
    if (!_initialized) {
      print('🔍 缓存有效性检查: 服务未初始化');
      return false;
    }

    final timestamp = _prefs.getInt(_cacheTimestampKey);
    if (timestamp == null) {
      print('🔍 缓存有效性检查: 没有时间戳');
      return false;
    }

    final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();
    final diff = now.difference(cacheTime);
    final isValid = diff < _cacheValidDuration;

    print('🔍 缓存有效性检查:');
    print('   - 缓存时间: $cacheTime');
    print('   - 当前时间: $now');
    print('   - 时间差: ${diff.inMinutes}分钟');
    print('   - 有效期: ${_cacheValidDuration.inHours}小时');
    print('   - 是否有效: $isValid');

    return isValid;
  }

  /// 缓存首页数据
  Future<void> cacheHomeData({
    required List<StationSimple> homeStations,
    required List<StationSimple> popularStations,
    required List<StationSimple> mostClickedStations,
    required List<StationSimple> highQualityStations,
    required List<StationSimple> latestStations,
    required List<RadioCategory> categories,
    required Country selectedCountry,
    int? selectedCategoryId, // 新增：当前选中的分类ID
  }) async {
    if (!_initialized) await initialize();
    
    try {
      // 缓存首页电台数据
      final homeStationsJson = homeStations.map((s) => s.toJson()).toList();
      await _prefs.setString(_homeDataKey, jsonEncode(homeStationsJson));

      // 缓存热门电台数据
      final popularStationsJson = popularStations.map((s) => s.toJson()).toList();
      await _prefs.setString(_popularStationsKey, jsonEncode(popularStationsJson));

      // 缓存点击最高电台数据
      final mostClickedStationsJson = mostClickedStations.map((s) => s.toJson()).toList();
      await _prefs.setString(_mostClickedStationsKey, jsonEncode(mostClickedStationsJson));

      // 缓存高质量电台数据
      final highQualityStationsJson = highQualityStations.map((s) => s.toJson()).toList();
      await _prefs.setString(_highQualityStationsKey, jsonEncode(highQualityStationsJson));

      // 缓存最新电台数据
      final latestStationsJson = latestStations.map((s) => s.toJson()).toList();
      await _prefs.setString(_latestStationsKey, jsonEncode(latestStationsJson));

      // 缓存分类数据
      final categoriesJson = categories.map((c) => c.toJson()).toList();
      await _prefs.setString(_categoriesKey, jsonEncode(categoriesJson));

      // 缓存选择的国家
      await _prefs.setString(_selectedCountryKey, jsonEncode(selectedCountry.toJson()));

      // 缓存选中的分类ID
      if (selectedCategoryId != null) {
        await _prefs.setInt(_selectedCategoryKey, selectedCategoryId);
      } else {
        await _prefs.remove(_selectedCategoryKey);
      }

      // 更新缓存时间戳
      await _prefs.setInt(_cacheTimestampKey, DateTime.now().millisecondsSinceEpoch);

      print('✅ 启动缓存已保存');
      print('   - 首页电台: ${homeStations.length}个');
      print('   - 热门推荐: ${popularStations.length}个');
      print('   - 点击最高: ${mostClickedStations.length}个');
      print('   - 高质量: ${highQualityStations.length}个');
      print('   - 最新上架: ${latestStations.length}个');
    } catch (e) {
      print('❌ 保存启动缓存失败: $e');
    }
  }

  /// 获取缓存的首页数据
  Future<StartupCacheData?> getCachedHomeData() async {
    print('🔍 开始获取缓存的首页数据...');

    if (!_initialized) {
      print('🔍 缓存服务未初始化，正在初始化...');
      await initialize();
    }

    if (!isCacheValid()) {
      print('⚠️ 启动缓存已过期或无效');
      return null;
    }
    
    try {
      // 获取首页电台数据
      final homeDataString = _prefs.getString(_homeDataKey);
      final popularDataString = _prefs.getString(_popularStationsKey);
      final mostClickedDataString = _prefs.getString(_mostClickedStationsKey);
      final highQualityDataString = _prefs.getString(_highQualityStationsKey);
      final latestDataString = _prefs.getString(_latestStationsKey);
      final categoriesString = _prefs.getString(_categoriesKey);
      final countryString = _prefs.getString(_selectedCountryKey);

      if (homeDataString == null || popularDataString == null ||
          mostClickedDataString == null || highQualityDataString == null ||
          latestDataString == null || categoriesString == null || countryString == null) {
        print('⚠️ 启动缓存数据不完整');
        return null;
      }
      
      // 解析数据
      final homeStationsJson = jsonDecode(homeDataString) as List;
      final popularStationsJson = jsonDecode(popularDataString) as List;
      final mostClickedStationsJson = jsonDecode(mostClickedDataString) as List;
      final highQualityStationsJson = jsonDecode(highQualityDataString) as List;
      final latestStationsJson = jsonDecode(latestDataString) as List;
      final categoriesJson = jsonDecode(categoriesString) as List;
      final countryJson = jsonDecode(countryString) as Map<String, dynamic>;

      final homeStations = homeStationsJson
          .map((json) => StationSimple.fromJson(json as Map<String, dynamic>))
          .toList();

      final popularStations = popularStationsJson
          .map((json) => StationSimple.fromJson(json as Map<String, dynamic>))
          .toList();

      final mostClickedStations = mostClickedStationsJson
          .map((json) => StationSimple.fromJson(json as Map<String, dynamic>))
          .toList();

      final highQualityStations = highQualityStationsJson
          .map((json) => StationSimple.fromJson(json as Map<String, dynamic>))
          .toList();

      final latestStations = latestStationsJson
          .map((json) => StationSimple.fromJson(json as Map<String, dynamic>))
          .toList();
      
      final categories = categoriesJson
          .map((json) => RadioCategory.fromJson(json as Map<String, dynamic>))
          .toList();
      
      final selectedCountry = Country.fromJson(countryJson);
      
      // 获取选中的分类ID（可选，兼容旧缓存）
      final selectedCategoryId = _prefs.getInt(_selectedCategoryKey);
      
      // print('✅ 成功加载启动缓存数据');
      // print('   - 首页电台: ${homeStations.length}个');
      // print('   - 热门推荐: ${popularStations.length}个');
      // print('   - 点击最高: ${mostClickedStations.length}个');
      // print('   - 高质量: ${highQualityStations.length}个');
      // print('   - 最新上架: ${latestStations.length}个');
      // print('   - 分类: ${categories.length}个');
      // print('   - 国家: ${selectedCountry.name}');
      // print('   - 缓存分类ID: $selectedCategoryId');

      return StartupCacheData(
        homeStations: homeStations,
        popularStations: popularStations,
        mostClickedStations: mostClickedStations,
        highQualityStations: highQualityStations,
        latestStations: latestStations,
        categories: categories,
        selectedCountry: selectedCountry,
        selectedCategoryId: selectedCategoryId,
      );
    } catch (e) {
      print('❌ 解析启动缓存数据失败: $e');
      return null;
    }
  }

  /// 清空所有缓存
  Future<void> clearAllCache() async {
    if (!_initialized) await initialize();
    
    await Future.wait([
      _prefs.remove(_homeDataKey),
      _prefs.remove(_popularStationsKey),
      _prefs.remove(_mostClickedStationsKey),
      _prefs.remove(_highQualityStationsKey),
      _prefs.remove(_latestStationsKey),
      _prefs.remove(_categoriesKey),
      _prefs.remove(_selectedCountryKey),
      _prefs.remove(_selectedCategoryKey),
      _prefs.remove(_cacheTimestampKey),
    ]);
    
    print('🗑️ 启动缓存已清空');
  }

  /// 获取缓存时间信息
  String getCacheInfo() {
    if (!_initialized) return '缓存服务未初始化';
    
    final timestamp = _prefs.getInt(_cacheTimestampKey);
    if (timestamp == null) return '无缓存数据';
    
    final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();
    final diff = now.difference(cacheTime);
    
    if (diff.inHours > 0) {
      return '缓存时间: ${diff.inHours}小时前';
    } else if (diff.inMinutes > 0) {
      return '缓存时间: ${diff.inMinutes}分钟前';
    } else {
      return '缓存时间: ${diff.inSeconds}秒前';
    }
  }
}

/// 启动缓存数据模型
class StartupCacheData {
  final List<StationSimple> homeStations;
  final List<StationSimple> popularStations;
  final List<StationSimple> mostClickedStations;
  final List<StationSimple> highQualityStations;
  final List<StationSimple> latestStations;
  final List<RadioCategory> categories;
  final Country selectedCountry;
  final int? selectedCategoryId; // 新增：保存缓存时选中的分类ID

  const StartupCacheData({
    required this.homeStations,
    required this.popularStations,
    required this.mostClickedStations,
    required this.highQualityStations,
    required this.latestStations,
    required this.categories,
    required this.selectedCountry,
    this.selectedCategoryId, // 可选参数，兼容旧缓存
  });
  
  /// 检查缓存是否对应当前分类
  bool isValidForCategory(int? categoryId) {
    return selectedCategoryId == categoryId;
  }
}
