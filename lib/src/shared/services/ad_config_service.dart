import 'dart:io';
import 'package:ad_sdk/ad_sdk.dart';

/// 广告配置服务
/// 
/// 功能实现: 统一管理广告SDK的初始化配置和相关服务
/// 实现方案: 将广告相关配置从app.dart中抽离，提供统一的初始化接口
/// 影响范围: ad_config_service.dart, app.dart
/// 实现日期: 2025-01-27
class AdConfigService {
  static bool _isInitialized = false;
  
  /// 检查广告SDK是否已初始化
  static bool get isInitialized => _isInitialized;
  
  /// 初始化广告SDK
  /// 
  /// 包含所有广告类型的配置：Banner、插屏、激励、开屏、原生广告
  static Future<void> initialize() async {
    if (_isInitialized) {
      print('🎯 广告SDK已经初始化，跳过重复初始化');
      return;
    }
    
    try {
      print('🎯 开始初始化广告SDK...');

      await AdSDK.initialize(AdConfig(
        ads: _buildAdConfigurations(),
        autoCache: true, // 自动缓存广告，提升显示速度
        enableAdInspector: false, // 生产环境设为false
      ),);

      _isInitialized = true;
      print('✅ 广告SDK初始化成功');
    } catch (e) {
      print('❌ 广告SDK初始化失败: $e');
      // 广告SDK初始化失败不阻塞应用启动
      rethrow;
    }
  }
  
  /// 构建广告配置Map
  /// 
  /// 返回包含所有广告类型配置的Map
  static Map<AdType, AdIds> _buildAdConfigurations() {
    return {
      // Banner广告配置
      AdType.banner: AdIds(
        main: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/2934735716' // iOS Banner广告测试ID
          : 'ca-app-pub-3940256099942544/6300978111', // Android Banner广告测试ID
        backup: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/2934735716' // iOS Banner广告测试ID
          : 'ca-app-pub-3940256099942544/6300978111', // Android Banner广告测试ID
      ),

      // 插屏广告配置
      AdType.interstitial: AdIds(
        main: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/4411468910' // iOS插屏广告测试ID
          : 'ca-app-pub-3940256099942544/1033173712', // Android插屏广告测试ID
        backup: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/4411468910' // iOS插屏广告测试ID
          : 'ca-app-pub-3940256099942544/1033173712', // Android插屏广告测试ID
      ),

      // 激励广告配置
      AdType.rewarded: AdIds(
        main: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/1712485313' // iOS激励广告测试ID
          : 'ca-app-pub-3940256099942544/5224354917', // Android激励广告测试ID
        backup: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/1712485313' // iOS激励广告测试ID
          : 'ca-app-pub-3940256099942544/5224354917', // Android激励广告测试ID
      ),

      // 开屏广告配置
      AdType.appOpen: AdIds(
        main: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/5575463023' // iOS开屏广告官方测试ID
          : 'ca-app-pub-3940256099942544/3419835294', // Android开屏广告测试ID
        backup: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/5575463023' // iOS开屏广告官方测试ID
          : 'ca-app-pub-3940256099942544/3419835294', // Android开屏广告测试ID
      ),

      // 原生广告配置
      AdType.native: AdIds(
        main: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/3986624511' // iOS原生广告测试ID
          : 'ca-app-pub-3940256099942544/2247696110', // Android原生广告测试ID
        backup: Platform.isIOS
          ? 'ca-app-pub-3940256099942544/3986624511' // iOS原生广告测试ID
          : 'ca-app-pub-3940256099942544/2247696110', // Android原生广告测试ID
      ),
    };
  }
  
  /// 获取广告ID配置信息（用于调试）
  static Map<String, String> getAdIdInfo() {
    final platform = Platform.isIOS ? 'iOS' : 'Android';
    final configs = _buildAdConfigurations();
    
    return {
      'platform': platform,
      'banner_main': configs[AdType.banner]?.main ?? 'N/A',
      'banner_backup': configs[AdType.banner]?.backup ?? 'N/A',
      'interstitial_main': configs[AdType.interstitial]?.main ?? 'N/A',
      'interstitial_backup': configs[AdType.interstitial]?.backup ?? 'N/A',
      'rewarded_main': configs[AdType.rewarded]?.main ?? 'N/A',
      'rewarded_backup': configs[AdType.rewarded]?.backup ?? 'N/A',
      'app_open_main': configs[AdType.appOpen]?.main ?? 'N/A',
      'app_open_backup': configs[AdType.appOpen]?.backup ?? 'N/A',
      'native_main': configs[AdType.native]?.main ?? 'N/A',
      'native_backup': configs[AdType.native]?.backup ?? 'N/A',
    };
  }
  
  /// 打印广告配置信息（用于调试）
  static void printAdConfigInfo() {
    final info = getAdIdInfo();
    print('📊 广告配置信息:');
    print('   - 平台: ${info['platform']}');
    print('   - Banner主要: ${info['banner_main']}');
    print('   - Banner备用: ${info['banner_backup']}');
    print('   - 插屏主要: ${info['interstitial_main']}');
    print('   - 插屏备用: ${info['interstitial_backup']}');
    print('   - 激励主要: ${info['rewarded_main']}');
    print('   - 激励备用: ${info['rewarded_backup']}');
    print('   - 开屏主要: ${info['app_open_main']}');
    print('   - 开屏备用: ${info['app_open_backup']}');
    print('   - 原生主要: ${info['native_main']}');
    print('   - 原生备用: ${info['native_backup']}');
  }
  
  /// 重置初始化状态（用于测试）
  static void resetInitializationState() {
    _isInitialized = false;
    print('🔄 广告SDK初始化状态已重置');
  }
}
