/// 📱 广告分析服务 - 对标demo中的AdInitializationService
/// 负责广告SDK事件回调处理和事件上报（不负责广告SDK初始化）
/// 注意：需要在AdConfigService.initialize()完成后调用setupAdCallbacks()
import 'package:ad_sdk/ad_sdk.dart';
import 'package:flutter/material.dart';
import 'package:world_tune/src/shared/services/analytics_service.dart';
import 'package:world_tune/src/shared/services/attribution_service.dart';

/// 广告分析服务类
class AdAnalyticsService {
  static bool _isInitialized = false;
  
  /// 设置广告事件回调（在广告SDK初始化之后调用）
  static Future<void> setupAdCallbacks() async {
    if (_isInitialized) {
      debugPrint('⚠️ [AdAnalytics] 广告回调已设置，跳过重复设置');
      return;
    }
    
    debugPrint('🚀 [AdAnalytics] 开始设置广告事件回调...');

    // 设置事件上报回调 - 对标demo
    AdSDK.setEventReporter((eventName, parameters) {
      debugPrint('📊 [AdAnalytics] 广告事件上报: $eventName');
      debugPrint('   参数: $parameters');
      
      // 使用我们的AnalyticsService替代demo中的ReportService。此处不等待，并发执行
      AnalyticsService.instance.logEvent(
        name: eventName, 
        parameters: parameters.cast<String, Object>()
      );
    });

    // 设置价格事件上报回调 - 对标demo，这是核心功能！
    AdSDK.setEventPriceReporter((scene, posid, platform, price, currency, paytype) {
      debugPrint('💰 [AdAnalytics] 价格事件上报:');
      debugPrint('   scene: $scene, posid: $posid');
      debugPrint('   platform: $platform, price: $price, currency: $currency, paytype: $paytype');
      
      // 1. Firebase Analytics事件上报 - 对标demo中的ReportService.instance.logEvent
      AnalyticsService.instance.logEvent(
        name: "ad_scene_price",
        parameters: {
          "scene": scene,
          "ad_posid": posid,
          "ad_platform": platform,
          "ad_price": price,
          "ad_currency": currency,
          "ad_paytype": paytype,
          "ad_mediation": "admob",
          // 注意：这里移除了lifetime，按照用户要求
        }
      );
      
      // 2. AppsFlyer收入事件上报 - 对标demo中的AttributionManager.getSharedInstance().logEvent
      AttributionService.getSharedInstance().logEvent("ad_impression_revenue", {
        "currencyCode": currency,
        "af_revenue": price,
      });
    });
    
    _isInitialized = true;
    debugPrint('✅ [AdAnalytics] 广告事件回调设置完成');
  }
  
  /// 手动上报广告事件
  static Future<void> logAdEvent(String eventName, Map<String, dynamic> parameters) async {
    debugPrint('📊 [AdAnalytics] 手动广告事件: $eventName');
    await AnalyticsService.instance.logEvent(
      name: eventName,
      parameters: parameters.cast<String, Object>(),
    );
  }
  
  /// 手动上报广告收入事件
  static Future<void> logAdRevenue({
    required double price,
    required String currency,
    String? scene,
    String? platform,
  }) async {
    debugPrint('💰 [AdAnalytics] 手动收入事件: $price $currency');
    
    // Firebase Analytics
    if (scene != null && platform != null) {
      await AnalyticsService.instance.logEvent(
        name: "ad_revenue_manual",
        parameters: {
          "scene": scene,
          "ad_platform": platform,
          "ad_price": price,
          "ad_currency": currency,
        },
      );
    }
    
    // AppsFlyer
    await AttributionService.getSharedInstance().logEvent("ad_impression_revenue", {
      "currencyCode": currency,
      "af_revenue": price,
    });
  }
  
  /// 检查初始化状态
  static bool get isInitialized => _isInitialized;
  
  /// 获取服务状态
  static Map<String, String> getStatus() {
    return {
      "ad_callbacks": _isInitialized ? "✅ 已设置" : "❌ 未设置",
      "analytics": AnalyticsService.instance.statusDescription,
      "attribution": AttributionService.getSharedInstance().statusDescription,
    };
  }
  
  /// 打印服务状态
  static void printStatus() {
    debugPrint('📋 [AdAnalytics] ===== 广告分析服务状态 =====');
    final status = getStatus();
    status.forEach((key, value) {
      debugPrint('   $key: $value');
    });
    debugPrint('📋 [AdAnalytics] =====================================');
  }
}
