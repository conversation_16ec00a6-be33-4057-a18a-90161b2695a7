/// 🚀 SDK初始化服务
/// 统一管理 Firebase、A<PERSON><PERSON><PERSON><PERSON>、ATT 等第三方SDK的初始化
import 'dart:io';

import 'package:flutter_plugin_tools/flutter_plugin_tools.dart';
import 'package:flutter_plugin_tools/config/flutter_tools_config.dart';
import 'package:flutter_plugin_tools/firebase/config/firebase_config.dart';
import 'package:flutter_plugin_tools/appsflyer/config/appsFlyer_config.dart';
import 'package:flutter_plugin_tools/att/config/att_config.dart';
import 'package:flutter_plugin_tools/att/att_manager.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';

class SDKInitializationService {
  static bool _isFirebaseInitialized = false;
  static bool _isAppsFlyerInitialized = false;
  static bool _isATTInitialized = false;
  
  /// 🚀 初始化所有SDK - 独立模块，互不影响
  static Future<void> initialize({
    String? appsFlyerDevKey = "YOUR_APPSFLYER_DEV_KEY",  // 默认占位符
    String? appsFlyerIOSAppId = "YOUR_IOS_APP_STORE_ID", // 默认占位符
  }) async {
    print('🔧 开始初始化所有SDK模块...');
    
    try {
      // 🔥 预处理Firebase配置
      FirebaseConfig? firebaseConfig;
      try {
        firebaseConfig = _createFirebaseConfig();
        print('✅ [Firebase] 配置创建成功');
        _isFirebaseInitialized = true;
      } catch (e) {
        print('❌ [Firebase] 配置创建失败: $e');
        _isFirebaseInitialized = false;
      }
      
      // 📊 预处理AppsFlyer配置
      AppsFlyerConfig? appsFlyerConfig;
      try {
        if (appsFlyerDevKey != null && appsFlyerIOSAppId != null && 
            !appsFlyerDevKey.contains('YOUR_') && !appsFlyerIOSAppId.contains('YOUR_')) {
          
          // 验证App ID格式
          if (RegExp(r'^\d+$').hasMatch(appsFlyerIOSAppId)) {
            appsFlyerConfig = _createAppsFlyerConfig(appsFlyerDevKey, appsFlyerIOSAppId);
            print('✅ [AppsFlyer] 配置创建成功');
            _isAppsFlyerInitialized = true;
          } else {
            print('❌ [AppsFlyer] App ID格式错误: $appsFlyerIOSAppId');
            _isAppsFlyerInitialized = false;
          }
        } else {
          print('⚠️ [AppsFlyer] 使用占位符配置，跳过初始化（这是正常的）');
          _isAppsFlyerInitialized = false;
        }
      } catch (e) {
        print('❌ [AppsFlyer] 配置创建失败: $e');
        _isAppsFlyerInitialized = false;
      }
      
      // 🍎 预处理ATT配置
      ATTConfig? attConfig;
      try {
        if (Platform.isIOS) {
          attConfig = _createATTConfig();
          print('✅ [ATT] 配置创建成功');
          _isATTInitialized = true;
        } else {
          print('⚠️ [ATT] 非iOS平台，跳过初始化');
          _isATTInitialized = false;
        }
      } catch (e) {
        print('❌ [ATT] 配置创建失败: $e');
        _isATTInitialized = false;
      }
      
      // 🚀 一次性初始化所有可用的模块
      await FlutterPluginTools.initialize(
        FlutterPluginToolsConfig(
          firebaseConfig: firebaseConfig,
          appsFlyerConfig: appsFlyerConfig,
          attConfig: attConfig,
          logConfig: LogConfig.console(defaultTag: "WorldTune"),
        ),
      );
      
      print('🎉 FlutterPluginTools 主框架初始化完成！');
      
      // 🍎 如果ATT初始化成功，检查状态
      if (_isATTInitialized) {
        await _checkATTStatus();
      }
      
    } catch (e, stackTrace) {
      print('❌ FlutterPluginTools 初始化过程中发生错误: $e');
      print('📍 堆栈信息: $stackTrace');
      
      // 即使主框架初始化失败，也要分析各模块状态
      print('🔄 正在分析各模块状态...');
    }
    
    // 输出初始化结果摘要
    _printInitializationSummary();
  }
  
  /// 📋 输出初始化结果摘要
  static void _printInitializationSummary() {
    print('');
    print('📋 ============ SDK初始化结果摘要 ============');
    print('🔥 Firebase:   ${_isFirebaseInitialized ? "✅ 已启用" : "❌ 失败"}');
    print('📊 AppsFlyer:  ${_isAppsFlyerInitialized ? "✅ 已启用" : "⚠️  未配置/失败"}');
    print('🍎 ATT:        ${_isATTInitialized ? "✅ 已启用" : "⚠️  未启用/失败"}');
    print('=========================================');
    
    // 给出使用建议
    if (_isFirebaseInitialized) {
      print('💡 Firebase Analytics/Crashlytics/RemoteConfig 可正常使用');
    }
    if (_isAppsFlyerInitialized) {
      print('💡 AppsFlyer 事件追踪可正常使用');
    } else {
      print('💡 要启用AppsFlyer，请提供真实的DevKey和AppID');
    }
    if (_isATTInitialized) {
      print('💡 ATT权限管理可正常使用');
    }
    print('');
  }
  
  /// 🔥 创建Firebase配置
  static FirebaseConfig _createFirebaseConfig() {
    return FirebaseConfig.simple(
      projectId: "wt-dev-16cdc",
      iosApiKey: "AIzaSyCxDc8nqMFFYTLPW823THHrR3SGsDI3KJ4",
      iosAppId: "1:923233263680:ios:c938cb93997177f16cffa5",
      messagingSenderId: "923233263680",
      storageBucket: "wt-dev-16cdc.firebasestorage.app",
      // Android配置 - 需要添加Android应用到Firebase项目
      androidApiKey: "YOUR_ANDROID_API_KEY", // 🚨 需要从Firebase控制台获取
      androidAppId: "YOUR_ANDROID_APP_ID",   // 🚨 需要从Firebase控制台获取
      // 功能开关
      enableAnalytics: true,
      enableCrashlytics: true,
      enableRemoteConfig: true,
      // Remote Config 默认值
      remoteConfigDefaults: {
        "welcome_message": "Welcome to WorldTune!",
        "feature_new_ui": false,
        "max_favorite_stations": 100,
        "ad_refresh_interval": 30,
      },
    );
  }
  
  /// 📊 创建AppsFlyer配置
  static AppsFlyerConfig? _createAppsFlyerConfig(String? devKey, String? iosAppId) {
    if (devKey == null || iosAppId == null) {
      print('⚠️ AppsFlyer配置不完整，跳过初始化');
      return null;
    }
    
    // 验证App ID格式（数字格式）
    if (!RegExp(r'^\d+$').hasMatch(iosAppId)) {
      print('❌ AppsFlyer iOS App ID格式错误，应为纯数字格式: $iosAppId');
      return null;
    }
    
    return AppsFlyerConfig(
      afDevKey: devKey,
      iosAppId: iosAppId,
      showDebug: true, // 开发环境显示调试信息
      timeToWaitForATTUserAuthorization: 60,  // 等待ATT授权60毫秒
    );
  }
  
  /// 🍎 创建ATT配置
  static ATTConfig _createATTConfig() {
    return ATTConfig.delayed(
      delaySeconds: 3, // 延迟3秒请求，让用户先熟悉应用
      onStatusChanged: (status, advertisingId) {
        print("📱 [ATT回调] 状态变化: ${ATTManager.getStatusDescription(status)}");
        print("📝 [ATT回调] 详细状态: $status");
        if (advertisingId != null) {
          print("🆔 [ATT回调] IDFA获取成功: $advertisingId");
        } else {
          print("🚫 [ATT回调] IDFA为空");
        }
      },
      onRequestCompleted: (status, isFirstTime, advertisingId) {
        print("✅ [ATT回调] 权限请求完成: ${ATTManager.getStatusDescription(status)}");
        print("📝 [ATT回调] 详细状态: $status");
        print("🔄 [ATT回调] 是否首次请求: $isFirstTime");
        print("🆔 [ATT回调] IDFA: ${advertisingId ?? 'null'}");
        
        if (status == TrackingStatus.authorized && advertisingId != null) {
          print("🎉 [ATT回调] 用户同意追踪权限，IDFA: $advertisingId");
          // 启用个性化功能
          _enablePersonalizedFeatures(advertisingId);
        } else {
          print("⚠️ [ATT回调] 用户拒绝追踪权限或权限受限，启用匿名模式");
          print("📊 [ATT回调] 状态分析: ${_getATTStatusAnalysis(status)}");
          // 启用匿名模式
          _enableAnonymousMode();
        }
      },
    );
  }
  
  /// 🎯 启用个性化功能（用户同意ATT权限时）
  static void _enablePersonalizedFeatures(String idfa) {
    print('🎯 启用个性化功能...');
    // TODO: 配置个性化广告
    // TODO: 启用详细的用户行为分析
    // TODO: 配置AppsFlyer使用IDFA进行精确归因
  }

  /// 🕶️ 启用匿名模式（用户拒绝ATT权限时）
  static void _enableAnonymousMode() {
    print('🕶️ 启用匿名模式...');
    // TODO: 禁用个性化广告
    // TODO: 启用匿名分析模式
    // TODO: 配置AppsFlyer匿名模式
  }

  /// 📊 获取ATT状态分析
  static String _getATTStatusAnalysis(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.authorized:
        return "用户明确同意追踪";
      case TrackingStatus.denied:
        return "用户明确拒绝追踪";
      case TrackingStatus.notDetermined:
        return "尚未询问用户（首次使用）";
      case TrackingStatus.restricted:
        return "系统限制追踪（家长控制等）";
      default:
        return "未知状态: $status";
    }
  }

  /// 🔍 检查ATT状态（调试用）
  static Future<void> _checkATTStatus() async {
    try {
      print('🔍 开始检查ATT状态...');
      
      // 检查ATT管理器是否已初始化
      if (!ATTManager.isInitialized) {
        print('❌ ATTManager未初始化');
        return;
      }
      
      // 获取当前状态
      final status = await ATTManager.getCurrentStatus();
      print('📱 当前ATT状态: ${ATTManager.getStatusDescription(status!)}');
      
      // 检查是否可以请求权限
      final canRequest = await ATTManager.canRequestPermission();
      print('🤔 是否可以请求权限: $canRequest');
      
      // 检查是否已授权
      final isAuthorized = await ATTManager.isTrackingAuthorized();
      print('✅ 是否已授权追踪: $isAuthorized');
      
      // 尝试获取IDFA
      final idfa = await ATTManager.getAdvertisingIdentifier();
      if (idfa != null) {
        print('🆔 IDFA: $idfa');
      } else {
        print('🚫 无法获取IDFA（可能未授权）');
      }
      
      // 模拟器特殊说明
      print('📝 注意：ATT权限弹窗在iOS模拟器上可能不会显示');
      print('📱 请在真机iOS 14.5+设备上测试ATT功能');
      
      // 如果状态是未确定，可以尝试手动请求（仅用于测试）
      if (status == TrackingStatus.notDetermined) {
        print('🔄 检测到未确定状态，将在5秒后手动请求权限（测试用）');
        Future.delayed(Duration(seconds: 5), () async {
          try {
            print('🚀 手动请求ATT权限...');
            final newStatus = await ATTManager.requestPermission();
            print('📱 手动请求结果: ${ATTManager.getStatusDescription(newStatus!)}');
          } catch (e) {
            print('❌ 手动请求ATT权限失败: $e');
          }
        });
      }
      
    } catch (e, stackTrace) {
      print('❌ ATT状态检查失败: $e');
      print('📍 错误堆栈: $stackTrace');
    }
  }
  
  /// 📊 检查各模块初始化状态
  static bool get isFirebaseInitialized => _isFirebaseInitialized;
  static bool get isAppsFlyerInitialized => _isAppsFlyerInitialized;
  static bool get isATTInitialized => _isATTInitialized;
  
  /// 🔍 检查是否有任何模块初始化成功
  static bool get hasAnyModuleInitialized => 
    _isFirebaseInitialized || _isAppsFlyerInitialized || _isATTInitialized;
}
