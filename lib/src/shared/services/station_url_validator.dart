import 'dart:async';
import 'dart:io';
import 'package:just_audio/just_audio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/models.dart';
import 'radio_api_service.dart';
import '../utils/logger.dart';

/// 电台URL验证结果
enum ValidationStatus { 
  success,      // URL有效，可直接播放
  updated,      // URL已更新，使用新URL
  unavailable,  // 电台不可用，应移除
  notFound      // 电台已从API删除
}

/// 电台验证结果
class StationValidationResult {
  final ValidationStatus status;
  final StationSimple? station;
  final String? errorMessage;

  const StationValidationResult._({
    required this.status,
    this.station,
    this.errorMessage,
  });

  factory StationValidationResult.success(StationSimple station) {
    return StationValidationResult._(
      status: ValidationStatus.success,
      station: station,
    );
  }

  factory StationValidationResult.updated(StationSimple station) {
    return StationValidationResult._(
      status: ValidationStatus.updated,
      station: station,
    );
  }

  factory StationValidationResult.unavailable([String? message]) {
    return StationValidationResult._(
      status: ValidationStatus.unavailable,
      errorMessage: message ?? 'Station unavailable',
    );
  }

  factory StationValidationResult.notFound([String? message]) {
    return StationValidationResult._(
      status: ValidationStatus.notFound,
      errorMessage: message ?? 'Station not found',
    );
  }

  bool get isSuccess => status == ValidationStatus.success;
  bool get isUpdated => status == ValidationStatus.updated;
  bool get isPlayable => status == ValidationStatus.success || status == ValidationStatus.updated;
  bool get shouldRemove => status == ValidationStatus.unavailable || status == ValidationStatus.notFound;
}

/// 电台URL验证服务
/// 
/// 功能实现: 检测电台URL有效性，自动更新失效URL，标记不可用电台
/// 实现方案: 播放测试 + API重新获取 + 结果缓存
/// 影响范围: 全局电台播放验证
/// 实现日期: 2025-01-27
class StationUrlValidator {
  static final Logger _logger = Logger();
  static final RadioApiService _apiService = RadioApiService();
  
  /// 验证结果缓存 - 避免短期内重复验证
  static final Map<String, _CachedResult> _cache = {};
  static const Duration _cacheExpiry = Duration(minutes: 10);

  /// 验证并更新电台URL
  /// 
  /// [station] - 要验证的电台
  /// [useCache] - 是否使用缓存结果（默认true）
  /// 
  /// 返回验证结果，包含最新的电台信息或错误状态
  static Future<StationValidationResult> validateAndUpdate(
    StationSimple station, {
    bool useCache = true,
  }) async {
    try {
      // 检查缓存
      if (useCache && _isCacheValid(station.id)) {
        final cached = _cache[station.id]!;
        print('📋 Using cached validation result for: ${station.name}');
        return cached.result;
      }

      print('🔍 Validating station: ${station.name}');
      print('🔗 Current URL: ${station.url}');

      // 1. 快速测试当前URL可播放性
      final currentUrlValid = await _testPlayability(station.url);
      if (currentUrlValid) {
        print('✅ Current URL is valid');
        final result = StationValidationResult.success(station);
        _cacheResult(station.id, result);
        return result;
      }

      print('❌ Current URL failed, fetching latest info from API');

      // 2. 从API重新获取最新电台信息
      try {
        final latestInfo = await _apiService.getRadioDetail(station.id);
        final updatedStation = StationSimple.fromRadioDetailResponse(latestInfo);
        
        print('📡 Got updated station info');
        print('🔗 New URL: ${updatedStation.url}');

        // 3. 测试更新后的URL
        final updatedUrlValid = await _testPlayability(updatedStation.url);
        if (updatedUrlValid) {
          print('✅ Updated URL is valid');
          final result = StationValidationResult.updated(updatedStation);
          _cacheResult(station.id, result);
          return result;
        }

        print('❌ Updated URL is also invalid');
        final result = StationValidationResult.unavailable('Station stream unavailable');
        _cacheResult(station.id, result);
        return result;

      } catch (apiError) {
        print('❌ API fetch failed: $apiError');
        
        // API获取失败可能意味着电台已被删除
        final result = StationValidationResult.notFound('Station removed from database');
        _cacheResult(station.id, result);
        return result;
      }

    } catch (e) {
      _logger.log(ProviderContainer(), 'Station validation error: $e');
      // 验证过程异常，返回原电台（降级处理）
      return StationValidationResult.success(station);
    }
  }

  /// 测试URL可播放性
  /// 
  /// [url] - 要测试的音频URL
  /// 
  /// 返回URL是否可以正常播放（3秒超时）
  static Future<bool> _testPlayability(String url) async {
    if (url.isEmpty) return false;

    AudioPlayer? testPlayer;
    try {
      testPlayer = AudioPlayer();
      
      // 设置3秒超时
      await testPlayer.setUrl(url).timeout(
        const Duration(seconds: 3),
        onTimeout: () {
          print('⏱️ URL test timeout: $url');
          return null;
        },
      );

      // 检查播放器状态
      final duration = testPlayer.duration;
      final isReady = duration != null || testPlayer.processingState == ProcessingState.ready;
      
      print('🎵 URL test result: $isReady for $url');
      return isReady;

    } on TimeoutException {
      print('⏱️ URL test timeout: $url');
      return false;
    } on SocketException {
      print('🌐 Network error testing URL: $url');
      return false;
    } catch (e) {
      print('❌ URL test error: $e for $url');
      return false;
    } finally {
      try {
        await testPlayer?.dispose();
      } catch (e) {
        // 忽略清理错误
      }
    }
  }

  /// 检查缓存是否有效
  static bool _isCacheValid(String stationId) {
    final cached = _cache[stationId];
    if (cached == null) return false;
    
    final isExpired = DateTime.now().difference(cached.timestamp) > _cacheExpiry;
    if (isExpired) {
      _cache.remove(stationId);
      return false;
    }
    
    return true;
  }

  /// 缓存验证结果
  static void _cacheResult(String stationId, StationValidationResult result) {
    _cache[stationId] = _CachedResult(
      result: result,
      timestamp: DateTime.now(),
    );
    
    // 清理过期缓存
    _cleanExpiredCache();
  }

  /// 清理过期缓存
  static void _cleanExpiredCache() {
    final now = DateTime.now();
    _cache.removeWhere((key, cached) {
      return now.difference(cached.timestamp) > _cacheExpiry;
    });
  }

  /// 清除指定电台的缓存
  static void clearCache(String stationId) {
    _cache.remove(stationId);
  }

  /// 清除所有缓存
  static void clearAllCache() {
    _cache.clear();
  }
}

/// 缓存结果内部类
class _CachedResult {
  final StationValidationResult result;
  final DateTime timestamp;

  _CachedResult({
    required this.result,
    required this.timestamp,
  });
}

/// 播放结果
class PlayResult {
  final bool isSuccess;
  final StationSimple? station;
  final String? errorMessage;

  const PlayResult._({
    required this.isSuccess,
    this.station,
    this.errorMessage,
  });

  factory PlayResult.success(StationSimple station) {
    return PlayResult._(
      isSuccess: true,
      station: station,
    );
  }

  factory PlayResult.failure(String message) {
    return PlayResult._(
      isSuccess: false,
      errorMessage: message,
    );
  }
}
