/// 📊 分析服务 - 对标demo中的ReportService
/// 负责Firebase Analytics和其他分析平台的事件上报
import 'package:flutter/material.dart';
import 'package:flutter_plugin_tools/firebase/firebase_analytics_helper.dart';
import 'package:world_tune/src/shared/services/sdk_initialization_service.dart';

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._();
  
  /// 获取单例实例 - 对标demo中的ReportService.instance
  static AnalyticsService get instance => _instance;
  
  AnalyticsService._();
  
  /// 上报事件 - 对标demo中的logEvent方法
  /// [name] 事件名称，如 "ad_scene_price"
  /// [parameters] 事件参数，如 {"scene": "interstitial", "ad_price": 0.01}
  Future<void> logEvent({
    required String name, 
    required Map<String, Object> parameters
  }) async {
    try {
      debugPrint('📊 [AnalyticsService] 事件上报: $name');
      debugPrint('   参数: $parameters');
      
      // Firebase Analytics上报（如果已初始化）
      if (SDKInitializationService.isFirebaseInitialized) {
        await FirebaseAnalyticsHelper.logCustomEvent(
          name: name,
          parameters: parameters,
        );
        debugPrint('✅ [AnalyticsService] Firebase事件上报成功: $name');
      } else {
        debugPrint('⚠️ [AnalyticsService] Firebase未初始化，跳过事件上报: $name');
      }
      
    } catch (e) {
      debugPrint('❌ [AnalyticsService] 事件上报失败: $e');
      debugPrint('   事件: $name, 参数: $parameters');
    }
  }
  
  /// 专门用于广告相关事件上报
  void logAdEvent({
    required String eventName,
    required String scene,
    required String adPlatform,
    required double adPrice,
    required String adCurrency,
    required String adPayType,
    String? adPositionId,
  }) {
    logEvent(
      name: eventName,
      parameters: {
        "scene": scene,
        "ad_platform": adPlatform,
        "ad_price": adPrice,
        "ad_currency": adCurrency,
        "ad_paytype": adPayType,
        "ad_mediation": "admob",
        if (adPositionId != null) "ad_posid": adPositionId,
      },
    );
  }
  
  /// 其他常用事件上报方法
  
  /// 用户行为事件
  void logUserAction(String action, {Map<String, Object>? parameters}) {
    logEvent(
      name: "user_action",
      parameters: {
        "action": action,
        ...?parameters,
      },
    );
  }
  
  /// 应用性能事件
  void logPerformanceEvent(String eventName, Duration duration) {
    logEvent(
      name: eventName,
      parameters: {
        "duration_ms": duration.inMilliseconds,
      },
    );
  }
  
  /// 电台播放事件
  void logStationEvent(String action, {
    String? stationName,
    String? stationCountry,
    String? stationUrl,
  }) {
    logEvent(
      name: "station_$action",
      parameters: {
        if (stationName != null) "station_name": stationName,
        if (stationCountry != null) "station_country": stationCountry,
        if (stationUrl != null) "station_url": stationUrl,
      },
    );
  }
  
  /// 错误事件上报
  void logError(String errorType, String errorMessage, {
    String? stackTrace,
    Map<String, Object>? additionalData,
  }) {
    logEvent(
      name: "app_error",
      parameters: {
        "error_type": errorType,
        "error_message": errorMessage,
        if (stackTrace != null) "stack_trace": stackTrace,
        ...?additionalData,
      },
    );
  }
  
  /// 检查服务可用性
  bool get isAvailable => SDKInitializationService.isFirebaseInitialized;
  
  /// 获取服务状态描述
  String get statusDescription {
    if (isAvailable) {
      return "✅ Firebase Analytics服务可用";
    } else {
      return "⚠️ Firebase未初始化，请检查配置";
    }
  }
}
