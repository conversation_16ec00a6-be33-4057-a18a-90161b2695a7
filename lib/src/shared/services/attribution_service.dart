/// 🎯 归因服务 - 对标demo中的AttributionManager
/// 负责AppsFlyer事件上报，特别是广告收入事件
import 'package:flutter/material.dart';
import 'package:flutter_plugin_tools/appsflyer/appsflyer_manager.dart';
import 'package:world_tune/src/shared/services/sdk_initialization_service.dart';

class AttributionService {
  static final AttributionService _instance = AttributionService._();
  
  /// 获取单例实例 - 对标demo中的getSharedInstance()
  static AttributionService getSharedInstance() => _instance;
  
  AttributionService._();
  
  /// 上报事件到AppsFlyer - 对标demo中的logEvent方法
  /// [eventName] 事件名称，如 "ad_impression_revenue"
  /// [values] 事件参数，如 {"currencyCode": "USD", "af_revenue": 0.01}
  Future<void> logEvent(String eventName, Map<String, dynamic> values) async {
    try {
      // 检查AppsFlyer是否已初始化
      if (!SDKInitializationService.isAppsFlyerInitialized) {
        debugPrint('⚠️ [AttributionService] AppsFlyer未初始化，跳过事件上报: $eventName');
        return;
      }
      
      debugPrint('📊 [AttributionService] AF事件上报: $eventName');
      debugPrint('   参数: $values');
      
      // 使用AppsFlyerManager进行AF事件上报
      await AppsFlyerManager.logEvent(eventName, values);
      
      debugPrint('✅ [AttributionService] AF事件上报成功: $eventName');
      
    } catch (e) {
      debugPrint('❌ [AttributionService] AF事件上报失败: $e');
      debugPrint('   事件: $eventName, 参数: $values');
    }
  }
  
  /// 专门用于广告收入事件上报 - 简化接口
  /// [revenue] 收入金额，如 0.01
  /// [currency] 货币代码，如 "USD"
  void logAdRevenue(double revenue, String currency) {
    logEvent("ad_impression_revenue", {
      "currencyCode": currency,
      "af_revenue": revenue,
    });
  }
  
  /// 其他常用的广告事件上报方法
  
  /// 广告展示事件
  void logAdImpression({
    required String adPlatform,
    required String adType,
    String? adUnitId,
  }) {
    logEvent("ad_impression", {
      "ad_platform": adPlatform,
      "ad_type": adType,
      if (adUnitId != null) "ad_unit_id": adUnitId,
    });
  }
  
  /// 广告点击事件
  void logAdClick({
    required String adPlatform,
    required String adType,
    String? adUnitId,
  }) {
    logEvent("ad_click", {
      "ad_platform": adPlatform,
      "ad_type": adType,
      if (adUnitId != null) "ad_unit_id": adUnitId,
    });
  }
  
  /// 检查服务可用性
  bool get isAvailable => SDKInitializationService.isAppsFlyerInitialized;
  
  /// 获取服务状态描述
  String get statusDescription {
    if (isAvailable) {
      return "✅ AppsFlyer归因服务可用";
    } else {
      return "⚠️ AppsFlyer未初始化，请检查配置";
    }
  }
}
