import '../models/radio_models.dart';
import '../config/app_config.dart';
import 'base_api_service.dart';

/// 电台API服务类
/// 提供电台相关的所有API接口调用
class RadioApiService extends BaseApiService {
  /// 单例实例
  static final RadioApiService _instance = RadioApiService._internal();

  /// 工厂构造函数
  factory RadioApiService() => _instance;

  /// 私有构造函数
  RadioApiService._internal();

  /// 缓存的标签信息，用于日志记录
  final Map<int, String> _tagCache = {};

  /// 获取电台列表
  /// 
  /// [request] 请求参数
  /// 
  /// 返回电台列表响应数据
  Future<RadioListResponse> getRadioList(RadioListRequest request) async {
    try {
      final requestJson = request.toJson();
      
      // print('🔥🔥🔥 getRadioList 最终HTTP请求');
      // print('🔥 URL: /api/v1/radio/list');
      // print('🔥 请求体JSON: $requestJson');
      
      final responseData = await postEncrypted(
        '/api/v1/radio/list',
        requestJson,
      );
      
      final result = RadioListResponse.fromJson(responseData);
      
      print('🔥 HTTP响应结果: 总记录数=${result.page.total}, 本页数据量=${result.list.length}');
      if (result.list.isNotEmpty) {
        print('🔥 第一个电台: ${result.list.first.name} (ID: ${result.list.first.stationUuid})');
      }
      
      return result;
    } catch (e) {
      print('🔥 ❌ HTTP请求失败: $e');
      throw ApiException('获取电台列表失败: $e');
    }
  }

  /// 获取电台详情
  /// 
  /// [stationUuid] 电台UUID（更新为使用UUID而非ID）
  /// 
  /// 返回电台详情信息
  Future<RadioDetailResponse> getRadioDetail(String stationUuid) async {
    try {
      final responseData = await postEncrypted(
        '/api/v1/radio/detail',
        {'station_uuid': stationUuid},
      );
      
      return RadioDetailResponse.fromJson(responseData);
    } catch (e) {
      throw ApiException('获取电台详情失败: $e');
    }
  }

  /// 获取电台分类标签
  ///
  /// [isShow] 展示状态筛选（可选）
  ///
  /// 返回标签列表
  Future<List<RadioTag>> getRadioTags({
    int? isShow,
  }) async {
    try {
      final params = <String, dynamic>{};
      if (isShow != null) params['is_show'] = isShow;

      final responseData = await postEncrypted(
        '/api/v1/radio/tags',
        params,
      );

      final response = RadioTagListResponse.fromJson(responseData);

      // 更新标签缓存，用于日志记录
      for (final tag in response.list) {
        _tagCache[tag.id] = tag.name;
      }

      return response.list;
    } catch (e) {
      throw ApiException('获取电台分类失败: $e');
    }
  }

  /// 获取国家列表
  /// 
  /// [keyword] 搜索关键字（可选）
  /// [isShow] 展示状态筛选（可选）
  /// 
  /// 返回国家列表
  Future<List<Country>> getCountryList({
    String? keyword,
    int? isShow,
  }) async {
    try {
      final params = <String, dynamic>{};
      if (keyword != null && keyword.isNotEmpty) params['keyword'] = keyword;
      if (isShow != null) params['is_show'] = isShow;
      
      final responseData = await postEncrypted(
        '/api/v1/country/list',
        params,
      );
      
      final countryList = responseData['list'] as List<dynamic>;
      return countryList
          .map((json) => Country.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw ApiException('获取国家列表失败: $e');
    }
  }

  /// 根据国家ID获取省/州列表
  /// 
  /// [countryId] 国家ID（必需）
  /// 
  /// 返回省州列表响应
  Future<CountryStatesResponse> getCountryStates({
    required int countryId,
  }) async {
    try {
      // print('🌍 开始请求省州列表 - 国家ID: $countryId');
      final requestParams = {'country_id': countryId};
      // print('📤 请求参数: $requestParams');
      
      final responseData = await postEncrypted(
        '/api/v1/country/states',
        requestParams,
      );
      
      // print('📥 API响应数据: $responseData');
      // print('📥 响应数据类型: ${responseData.runtimeType}');
      
      if (responseData is Map<String, dynamic>) {
        final response = CountryStatesResponse.fromJson(responseData);
        // print('✅ 解析成功 - 省州数量: ${response.states.length}');
        // print('🗺️ 省州列表预览: ${response.states.take(3).map((s) => '${s.stateName}(${s.stationCount})').join(', ')}');
        return response;
      } else {
        throw ApiException('响应数据格式错误: ${responseData.runtimeType}');
      }
    } catch (e) {
      print('❌ 获取省州列表失败 - 国家ID: $countryId, 错误: $e');
      if (e is ApiException) {
        throw e;
      } else {
        throw ApiException('获取省州列表失败: $e');
      }
    }
  }

  /// 搜索电台
  /// 
  /// [keyword] 搜索关键字
  /// [page] 页码
  /// [pageSize] 每页数量
  /// [countryId] 国家ID筛选（可选）
  /// [state] 省/州名称筛选（可选，配合countryId使用）
  /// [languageCodes] 语言代码列表筛选（可选，暂时无效）
  /// [tagIds] 标签ID列表筛选（可选）
  /// [sortBy] 排序字段（可选）
  /// [sortOrder] 排序方式（可选）
  /// 
  /// 返回搜索结果
  Future<RadioListResponse> searchRadios({
    required String keyword,
    int page = 1,
    int pageSize = 20,
    int? countryId,
    String? state,
    List<String>? languageCodes,
    List<int>? tagIds,
    String? sortBy,
    String? sortOrder,
  }) async {
    print('🌐🌐🌐 API Service searchRadios 被调用');
    print('🌐 最终API请求参数:');
    print('🌐   - keyword: "$keyword"');
    print('🌐   - page: $page');
    print('🌐   - pageSize: $pageSize');
    print('🌐   - countryId: $countryId');
    print('🌐   - state: "$state" ${state == null ? '(全部地区)' : '(指定地区)'}');
    print('🌐   - languageCodes: $languageCodes');
    print('🌐   - tagIds: $tagIds');
    print('🌐   - sortBy: ${sortBy ?? 'votes'}');
    print('🌐   - sortOrder: ${sortOrder ?? 'desc'}');
    print('🌐 ⚠️ 关键：state参数=${state == null ? 'null(将查询所有地区)' : '"$state"(将查询指定地区)'}');
    
    final request = RadioListRequest(
      page: page,
      pageSize: pageSize,
      keyword: keyword,
      countryId: countryId,
      state: state,
      languageCodes: languageCodes,
      tagIds: tagIds,
      sortBy: sortBy ?? 'votes',
      sortOrder: sortOrder ?? 'desc',
    );
    
    print('🌐 构建的 RadioListRequest: $request');
    
    final result = await getRadioList(request);
    
    print('🌐 API响应结果: 总数=${result.page.total}, 当前页数据=${result.list.length}');
    
    return result;
  }

  /// 获取热门电台
  /// 
  /// [page] 页码
  /// [pageSize] 每页数量
  /// [countryId] 国家ID筛选（可选）
  /// [state] 省/州名称筛选（可选，配合countryId使用）
  /// [tagIds] 标签ID列表筛选（可选）
  /// 
  /// 返回热门电台列表
  Future<RadioListResponse> getPopularRadios({
    int page = 1,
    int pageSize = 20,
    int? countryId,
    String? state,
    List<int>? tagIds,
  }) async {
    final request = RadioListRequest(
      page: page,
      pageSize: pageSize,
      countryId: countryId,
      state: state,
      tagIds: tagIds,
      sortBy: 'votes',
      sortOrder: 'desc',
    );
    
    return getRadioList(request);
  }

  /// 获取最新电台
  /// 
  /// [page] 页码
  /// [pageSize] 每页数量
  /// [countryId] 国家ID筛选（可选）
  /// [tagIds] 标签ID列表筛选（可选）
  /// 
  /// 返回最新电台列表
  Future<RadioListResponse> getLatestRadios({
    int page = 1,
    int pageSize = 20,
    int? countryId,
    List<int>? tagIds,
  }) async {
    final request = RadioListRequest(
      page: page,
      pageSize: pageSize,
      countryId: countryId,
      tagIds: tagIds,
      sortBy: 'created_at',
      sortOrder: 'desc',
    );
    
    return getRadioList(request);
  }

  /// 按分类获取电台（使用标签ID筛选，已修复）
  ///
  /// [tagIds] 标签ID列表
  /// [page] 页码
  /// [pageSize] 每页数量
  /// [countryId] 国家ID筛选（可选）
  /// [sortBy] 排序字段
  /// [sortOrder] 排序方式
  ///
  /// 返回分类电台列表
  Future<RadioListResponse> getRadiosByCategory({
    required List<int> tagIds,
    int page = 1,
    int pageSize = 20,
    int? countryId,
    String sortBy = 'votes',
    String sortOrder = 'desc',
  }) async {
    final request = RadioListRequest(
      page: page,
      pageSize: pageSize,
      tagIds: tagIds,
      countryId: countryId,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );

    // 检查是否是pop分类请求，如果是则记录日志
    final isPopRequest = _isPopCategoryRequest(tagIds);
    if (isPopRequest) {
      print('🎤 [POP分类请求] 请求参数 - tagIds: $tagIds, '
          'countryId: $countryId, page: $page, pageSize: $pageSize');
      print('🎤 [POP分类请求] 配置的最大分页大小: ${AppConfig.maxPageSize}');
    }

    final response = await getRadioList(request);

    // 如果是pop分类请求，记录返回的电台数量和分页信息
    if (isPopRequest) {
      print('🎤 [POP分类响应] 返回电台数量: ${response.list.length}');
      print('🎤 [POP分类响应] 分页信息 - 当前页: ${response.page.page}, '
          '每页大小: ${response.page.pageSize}, 总数: ${response.page.total}');
      print('🎤 [POP分类响应] 是否有下一页: ${response.page.hasNextPage}');
    }

    return response;
  }

  /// 检查是否是pop分类请求
  bool _isPopCategoryRequest(List<int> tagIds) {
    for (final tagId in tagIds) {
      final tagName = _tagCache[tagId];
      if (tagName != null && tagName.toLowerCase() == 'pop') {
        return true;
      }
    }
    return false;
  }

  /// 按国家获取电台（使用国家ID筛选，已修复）
  /// 
  /// [countryId] 国家ID
  /// [page] 页码
  /// [pageSize] 每页数量
  /// [state] 省/州名称筛选（可选，配合countryId使用）
  /// [sortBy] 排序字段
  /// [sortOrder] 排序方式
  /// [tagIds] 标签ID列表筛选（可选）
  /// 
  /// 返回国家电台列表
  Future<RadioListResponse> getRadiosByCountry({
    required int countryId,
    int page = 1,
    int pageSize = 20,
    String? state,
    String sortBy = 'votes',
    String sortOrder = 'desc',
    List<int>? tagIds,
  }) async {
    final request = RadioListRequest(
      page: page,
      pageSize: pageSize,
      countryId: countryId,
      state: state,
      tagIds: tagIds,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
    
    return getRadioList(request);
  }

  /// 按语言获取电台（使用关键字搜索）
  /// 
  /// [languageNames] 语言名称关键字列表
  /// [page] 页码
  /// [pageSize] 每页数量
  /// [countryName] 国家名称筛选（可选）
  /// [sortBy] 排序字段
  /// [sortOrder] 排序方式
  /// 
  /// 返回语言电台列表
  Future<RadioListResponse> getRadiosByLanguage({
    required List<String> languageNames,
    int page = 1,
    int pageSize = 20,
    String? countryName,
    String sortBy = 'votes',
    String sortOrder = 'desc',
  }) async {
    // 暂时使用关键字搜索替代语言筛选
    final keyword = languageNames.join(' ');
    final searchKeyword = countryName != null ? '$keyword $countryName' : keyword;
    
    final request = RadioListRequest(
      page: page,
      pageSize: pageSize,
      keyword: searchKeyword,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
    
    return getRadioList(request);
  }
}
