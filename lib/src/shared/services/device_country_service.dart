import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';

/// 设备国家检测服务
///
/// 用于检测用户设备的当前国家/地区设置
class DeviceCountryService {
  
  /// 获取设备当前国家代码
  /// 
  /// 返回ISO 3166-1 alpha-2格式的国家代码（如：CN, US, JP等）
  /// 如果无法获取，返回null
  static Future<String?> getDeviceCountryCode() async {
    try {
      // 方法1: 尝试从系统语言环境获取国家代码
      final countryCode = await _getCountryFromLocale();
      if (countryCode != null && countryCode.isNotEmpty) {
        print('🌍 从系统语言环境获取到国家代码: $countryCode');
        return countryCode.toUpperCase();
      }

      // 方法2: 尝试从平台特定方法获取
      final platformCountryCode = await _getCountryFromPlatform();
      if (platformCountryCode != null && platformCountryCode.isNotEmpty) {
        print('🌍 从平台特定方法获取到国家代码: $platformCountryCode');
        return platformCountryCode.toUpperCase();
      }

      print('⚠️ 无法获取设备国家代码，将使用默认值');
      return null;
    } catch (e) {
      print('❌ 获取设备国家代码时出错: $e');
      return null;
    }
  }
  
  /// 从系统语言环境获取国家代码
  static Future<String?> _getCountryFromLocale() async {
    try {
      // 获取系统默认语言环境
      final locale = ui.PlatformDispatcher.instance.locale;
      final countryCode = locale.countryCode;
      
      if (countryCode != null && countryCode.length == 2) {
        print('🌍 系统语言环境: ${locale.languageCode}_${locale.countryCode}');
        return countryCode;
      }

      // 如果主要语言环境没有国家代码，尝试获取所有支持的语言环境
      final locales = ui.PlatformDispatcher.instance.locales;
      for (final supportedLocale in locales) {
        final supportedCountryCode = supportedLocale.countryCode;
        if (supportedCountryCode != null && supportedCountryCode.length == 2) {
          print('🌍 从支持的语言环境获取: ${supportedLocale.languageCode}_${supportedLocale.countryCode}');
          return supportedCountryCode;
        }
      }

      return null;
    } catch (e) {
      print('❌ 从语言环境获取国家代码失败: $e');
      return null;
    }
  }
  
  /// 从平台特定方法获取国家代码
  static Future<String?> _getCountryFromPlatform() async {
    try {
      if (Platform.isAndroid) {
        return await _getCountryFromAndroid();
      } else if (Platform.isIOS) {
        return await _getCountryFromIOS();
      }
      return null;
    } catch (e) {
      print('❌ 从平台特定方法获取国家代码失败: $e');
      return null;
    }
  }
  
  /// 从Android系统获取国家代码
  static Future<String?> _getCountryFromAndroid() async {
    try {
      const platform = MethodChannel('device_country');
      final countryCode = await platform.invokeMethod<String>('getCountryCode');
      return countryCode;
    } catch (e) {
      print('⚠️ Android平台方法调用失败，尝试其他方式: $e');

      // 备用方案：从环境变量获取
      try {
        final country = Platform.environment['COUNTRY'];
        if (country != null && country.length == 2) {
          return country;
        }
      } catch (e) {
        print('⚠️ 从环境变量获取国家代码失败: $e');
      }

      return null;
    }
  }
  
  /// 从iOS系统获取国家代码
  static Future<String?> _getCountryFromIOS() async {
    try {
      const platform = MethodChannel('device_country');
      final countryCode = await platform.invokeMethod<String>('getCountryCode');
      return countryCode;
    } catch (e) {
      print('⚠️ iOS平台方法调用失败: $e');
      return null;
    }
  }
  
  /// 获取设备国家的显示名称（英文）
  /// 
  /// 根据国家代码返回对应的英文国家名称
  /// 这个映射用于匹配API返回的国家列表
  static String? getCountryNameByCode(String countryCode) {
    final code = countryCode.toUpperCase();
    
    // 常见国家代码到英文名称的映射
    const countryMap = {
      'CN': 'China',
      'US': 'United States',
      'JP': 'Japan',
      'KR': 'South Korea',
      'GB': 'United Kingdom',
      'DE': 'Germany',
      'FR': 'France',
      'IT': 'Italy',
      'ES': 'Spain',
      'CA': 'Canada',
      'AU': 'Australia',
      'IN': 'India',
      'BR': 'Brazil',
      'RU': 'Russia',
      'MX': 'Mexico',
      'NL': 'Netherlands',
      'SE': 'Sweden',
      'NO': 'Norway',
      'DK': 'Denmark',
      'FI': 'Finland',
      'CH': 'Switzerland',
      'AT': 'Austria',
      'BE': 'Belgium',
      'PL': 'Poland',
      'CZ': 'Czech Republic',
      'HU': 'Hungary',
      'GR': 'Greece',
      'PT': 'Portugal',
      'IE': 'Ireland',
      'NZ': 'New Zealand',
      'SG': 'Singapore',
      'HK': 'Hong Kong',
      'TW': 'Taiwan',
      'TH': 'Thailand',
      'MY': 'Malaysia',
      'ID': 'Indonesia',
      'PH': 'Philippines',
      'VN': 'Vietnam',
      'EG': 'Egypt',
      'ZA': 'South Africa',
      'NG': 'Nigeria',
      'KE': 'Kenya',
      'AR': 'Argentina',
      'CL': 'Chile',
      'CO': 'Colombia',
      'PE': 'Peru',
      'VE': 'Venezuela',
      'TR': 'Turkey',
      'SA': 'Saudi Arabia',
      'AE': 'United Arab Emirates',
      'IL': 'Israel',
      'IR': 'Iran',
      'IQ': 'Iraq',
      'PK': 'Pakistan',
      'BD': 'Bangladesh',
      'LK': 'Sri Lanka',
      'MM': 'Myanmar',
      'KH': 'Cambodia',
      'LA': 'Laos',
      'NP': 'Nepal',
      'BT': 'Bhutan',
      'MV': 'Maldives',
      'AF': 'Afghanistan',
      'UZ': 'Uzbekistan',
      'KZ': 'Kazakhstan',
      'KG': 'Kyrgyzstan',
      'TJ': 'Tajikistan',
      'TM': 'Turkmenistan',
      'MN': 'Mongolia',
      'UA': 'Ukraine',
      'BY': 'Belarus',
      'MD': 'Moldova',
      'RO': 'Romania',
      'BG': 'Bulgaria',
      'RS': 'Serbia',
      'HR': 'Croatia',
      'SI': 'Slovenia',
      'SK': 'Slovakia',
      'BA': 'Bosnia and Herzegovina',
      'ME': 'Montenegro',
      'MK': 'North Macedonia',
      'AL': 'Albania',
      'XK': 'Kosovo',
      'LT': 'Lithuania',
      'LV': 'Latvia',
      'EE': 'Estonia',
      'IS': 'Iceland',
      'MT': 'Malta',
      'CY': 'Cyprus',
      'LU': 'Luxembourg',
      'LI': 'Liechtenstein',
      'MC': 'Monaco',
      'SM': 'San Marino',
      'VA': 'Vatican City',
      'AD': 'Andorra',
    };
    
    final countryName = countryMap[code];
    if (countryName != null) {
      print('🌍 国家代码 $code 对应的英文名称: $countryName');
    } else {
      print('⚠️ 未找到国家代码 $code 对应的英文名称');
    }

    return countryName;
  }
  
  /// 验证国家代码格式
  static bool isValidCountryCode(String? countryCode) {
    if (countryCode == null || countryCode.isEmpty) {
      return false;
    }
    
    // ISO 3166-1 alpha-2 格式：2个大写字母
    final regex = RegExp(r'^[A-Z]{2}$');
    return regex.hasMatch(countryCode.toUpperCase());
  }
}
