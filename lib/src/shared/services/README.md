# 服务层 (Services Layer)

服务层提供应用的核心业务逻辑和数据处理功能，包括音频播放、本地存储和模拟数据服务。

## 📋 服务概述

### AudioService - 音频播放服务
负责音频播放的核心逻辑，基于just_audio包实现。

#### 主要功能
- **电台播放**: 支持网络流媒体播放
- **播放列表支持**: 自动解析PLS、M3U、M3U8播放列表文件
- **播放控制**: 播放/暂停/停止控制
- **状态管理**: 播放状态实时监听
- **音量控制**: 音量调节功能
- **错误处理**: 播放错误捕获和处理
- **详细日志**: 完整的播放过程日志记录
- **错误分析**: 智能错误原因分析和诊断

#### 核心方法
```dart
Future<void> initialize([StorageService? storageService])
Future<void> playStation(StationSimple station)
Future<void> pause()
Future<void> resume()
Future<void> stop()
Future<void> setVolume(double volume)
```

#### 状态流
- `playbackStream`: 播放状态变化流
- `currentStation`: 当前播放电台
- `currentState`: 当前播放状态

#### 日志功能
AudioService现在包含详细的日志记录功能，帮助诊断播放问题：

##### 播放过程日志
- 电台信息记录（名称、URL、国家）
- 平台检测（Android/iOS）
- URL解析和验证
- 音频源设置耗时统计
- 播放状态变化追踪

##### 错误分析功能
智能分析播放失败的具体原因：
- **网络连接问题**: 超时、连接失败等
- **HTTP错误**: 404、403、500等状态码
- **SSL/TLS问题**: 证书验证失败
- **音频格式问题**: 不支持的编码格式
- **播放列表问题**: PLS/M3U文件解析失败
- **Android网络安全策略**: HTTP连接被阻止
- **URL格式错误**: 无效的链接格式
- **端口问题**: 无效的端口号

##### 平台特定分析
- **Android**: 网络安全配置检查
- **iOS**: App Transport Security检查

#### 播放列表支持
AudioService现在支持自动解析播放列表文件：

##### 支持的格式
- **PLS格式**: `.pls` 文件，常用于网络电台
- **M3U格式**: `.m3u` 文件，通用播放列表格式
- **M3U8格式**: `.m3u8` 文件，扩展M3U格式

##### 解析过程
1. **自动检测**: 根据URL扩展名或内容自动识别播放列表类型
2. **下载解析**: 下载播放列表文件并解析内容
3. **提取流URL**: 从播放列表中提取实际的音频流地址
4. **播放流媒体**: 使用解析出的URL进行播放

##### 示例
```
原始URL: http://www.181.fm/stream/pls/181-kickincountry.pls
解析后URL: http://listen.181fm.com/181-kickincountry_128k.mp3
```

#### 调试使用方法
当遇到播放问题时，查看控制台日志：

1. **播放开始日志**:
   ```
   🎵 AudioService: 开始播放电台
   📻 电台名称: [电台名]
   🔗 电台URL: [URL]
   📱 当前平台: Android/iOS
   ```

2. **URL解析日志**:
   ```
   🔍 AudioService: URL解析结果:
      - 协议: http/https
      - 主机: [域名]
      - 端口: [端口号]
      - 路径: [路径]
   ```

3. **播放列表解析日志**:
   ```
   📋 AudioService: 检测到播放列表文件，开始解析...
   📋 AudioService: 播放列表内容长度: [长度]
   📋 AudioService: 解析PLS格式播放列表
   📋 AudioService: 找到PLS流URL: [实际URL]
   ✅ AudioService: 播放列表解析成功
   ```

4. **错误分析日志**:
   ```
   ❌ AudioService: 播放电台时发生错误
   🔍 错误详情: [具体错误]
   🔬 错误分析: [分析结果]
   ```

通过这些日志可以快速定位播放失败的具体原因。

#### 常见问题解决

##### 播放列表文件问题
如果看到以下错误：
```
UnrecognizedInputFormatException: None of the available extractors could read the stream
```
通常表示URL指向的是播放列表文件而不是直接的音频流。AudioService会自动检测并解析这类文件。

##### 网络安全策略问题
如果在Android上看到网络相关错误，检查 `network_security_config.xml` 是否允许目标域名的HTTP连接。

### StorageService - 本地存储服务
负责应用数据的本地持久化存储，基于SharedPreferences实现。

#### 主要功能
- **收藏管理**: 电台收藏的增删查改
- **播放历史**: 播放记录的存储和管理
- **用户偏好**: 用户设置的存储
- **数据序列化**: JSON格式的数据序列化

#### 核心方法
```dart
Future<void> initialize()
Future<List<StationSimple>> getFavoriteStations()
Future<void> addToFavorites(StationSimple station)
Future<void> removeFromFavorites(String stationId)
Future<List<PlayHistory>> getPlayHistory()
Future<void> addToHistory(StationSimple station)
```

#### 存储键值
- `favorite_stations`: 收藏电台列表
- `play_history`: 播放历史记录
- `user_preferences`: 用户偏好设置

### MockDataService - 模拟数据服务
提供示例电台数据，用于开发和测试阶段。

#### 主要功能
- **示例数据**: 提供真实的电台数据示例
- **分类筛选**: 按分类获取电台数据
- **数据转换**: RadioStation到StationSimple的转换

#### 核心方法
```dart
static List<RadioStation> getSampleStations()
static List<StationSimple> getSampleSimpleStations()
static List<StationSimple> getStationsByCategory(RadioCategory category)
static List<StationCategory> getAllCategories()
```

## 🔄 Riverpod提供者

### 音频相关Provider
```dart
final audioServiceProvider = Provider<AudioService>
final currentPlaybackProvider = StreamProvider<CurrentPlayback>
final currentStationProvider = Provider<StationSimple?>
```

### 存储相关Provider
```dart
final storageServiceProvider = Provider<StorageService>
final favoriteStationsProvider = FutureProvider<List<StationSimple>>
final playHistoryProvider = FutureProvider<List<PlayHistory>>
final recentStationsProvider = FutureProvider<List<StationSimple>>
final userPreferencesProvider = FutureProvider<UserPreferences>
```

## 📊 数据流架构

### 播放流程
1. 用户选择电台 → UI组件
2. 调用AudioService.playStation() → 音频服务
3. 添加到播放历史 → 存储服务
4. 更新播放状态 → 状态流
5. UI自动更新 → Provider监听

### 收藏流程
1. 用户点击收藏 → UI组件
2. 调用StorageService.addToFavorites() → 存储服务
3. 更新本地存储 → SharedPreferences
4. 刷新Provider → favoriteStationsProvider
5. UI自动更新 → Consumer监听

## 🚀 性能优化

### 音频服务优化
- **单例模式**: 确保全局唯一的音频服务实例
- **流监听**: 高效的状态变化监听
- **资源管理**: 及时释放音频资源
- **错误恢复**: 播放错误的自动恢复机制

### 存储服务优化
- **异步操作**: 所有存储操作使用异步方法
- **数据缓存**: 减少重复的存储读取
- **批量操作**: 支持批量数据处理
- **错误处理**: 存储失败的降级处理

### 数据服务优化
- **静态方法**: 无状态的静态数据提供
- **懒加载**: 按需加载数据
- **内存管理**: 避免大量数据常驻内存

## 🔧 配置和初始化

### 服务初始化顺序
1. **StorageService**: 首先初始化存储服务
2. **AudioService**: 传入存储服务实例进行初始化
3. **Provider注册**: 在应用启动时注册所有Provider

### 初始化代码
```dart
Future<void> _initializeServices(WidgetRef ref) async {
  final storageService = ref.read(storageServiceProvider);
  await storageService.initialize();
  
  final audioService = ref.read(audioServiceProvider);
  await audioService.initialize(storageService);
}
```

## 🔮 未来扩展

### 计划功能
- [ ] 网络API服务集成
- [ ] 缓存服务实现
- [ ] 推荐算法服务
- [ ] 用户账户服务
- [ ] 同步服务

### 技术改进
- [ ] 数据库存储（SQLite）
- [ ] 网络状态监听
- [ ] 离线数据支持
- [ ] 数据加密存储
- [ ] 服务降级策略
