import '../models/models.dart';

/// 模拟数据服务，提供示例电台数据
class MockDataService {
  static const List<Map<String, dynamic>> _sampleStationsData = [
    {
      'changeuuid': '7fc40bca-aac4-4a31-8dd8-209669a162ec',
      'stationuuid': '63151721-3f42-4515-89e1-51b4ff35aad5',
      'serveruuid': '18b788f4-4693-4d67-af55-c78e5c1d2d3e',
      'name': '黑龙江都市·女性广播',
      'url': 'https://lhttp-hw.qtfm.cn/live/4968/64k.mp3',
      'url_resolved': 'https://lhttp-hw.qtfm.cn/live/4968/64k.mp3',
      'homepage': 'https://www.hljtv.com/',
      'favicon': 'https://pic.qtfm.cn/2014/0330/20140330112742795.jpg',
      'tags': 'lifestyle',
      'country': 'China',
      'countrycode': 'CN',
      'iso_3166_2': '',
      'state': '',
      'language': 'chinese',
      'languagecodes': 'zh',
      'votes': 29,
      'lastchangetime': '2025-06-13 00:54:54',
      'lastchangetime_iso8601': '2025-06-13T00:54:54Z',
      'codec': 'MP3',
      'bitrate': 0,
      'hls': 0,
      'lastcheckok': 1,
      'lastchecktime': '2025-07-07 03:50:14',
      'lastchecktime_iso8601': '2025-07-07T03:50:14Z',
      'lastcheckoktime': '2025-07-07 03:50:14',
      'lastcheckoktime_iso8601': '2025-07-07T03:50:14Z',
      'lastlocalchecktime': '2025-07-06 22:26:44',
      'lastlocalchecktime_iso8601': '2025-07-06T22:26:44Z',
      'clicktimestamp': '2025-07-05 14:32:30',
      'clicktimestamp_iso8601': '2025-07-05T14:32:30Z',
      'clickcount': 29,
      'clicktrend': -2,
      'ssl_error': 0,
      'geo_lat': null,
      'geo_long': null,
      'geo_distance': null,
      'has_extended_info': false
    },
    {
      'changeuuid': '4f278108-e406-461f-8fb9-83418f091c29',
      'stationuuid': 'bac7634f-dd95-46c0-b176-3961f1742511',
      'serveruuid': 'c13505ab-efd4-4aa9-a68c-22ce6d89e14c',
      'name': '黑龙江都市女性广播',
      'url': 'http://stream3.hljtv.com/hljrd97/sd/live.m3u8',
      'url_resolved': 'http://stream3.hljtv.com/hljrd97/sd/live.m3u8',
      'homepage': 'http://www.hljtv.com/',
      'favicon': 'http://www.hljtv.com/hljtv.ico',
      'tags': 'literature',
      'country': 'China',
      'countrycode': 'CN',
      'iso_3166_2': '',
      'state': 'Amur River',
      'language': 'chinese',
      'languagecodes': 'zh',
      'votes': 113,
      'lastchangetime': '2025-06-13 07:02:48',
      'lastchangetime_iso8601': '2025-06-13T07:02:48Z',
      'codec': 'UNKNOWN',
      'bitrate': 0,
      'hls': 1,
      'lastcheckok': 1,
      'lastchecktime': '2025-07-07 06:04:36',
      'lastchecktime_iso8601': '2025-07-07T06:04:36Z',
      'lastcheckoktime': '2025-07-07 06:04:36',
      'lastcheckoktime_iso8601': '2025-07-07T06:04:36Z',
      'lastlocalchecktime': '2025-07-07 06:04:36',
      'lastlocalchecktime_iso8601': '2025-07-07T06:04:36Z',
      'clicktimestamp': '2025-07-01 00:21:22',
      'clicktimestamp_iso8601': '2025-07-01T00:21:22Z',
      'clickcount': 10,
      'clicktrend': 0,
      'ssl_error': 0,
      'geo_lat': null,
      'geo_long': null,
      'geo_distance': null,
      'has_extended_info': false
    },
    {
      'changeuuid': 'a004d4be-294d-4a9d-8ee2-2cb61265b79a',
      'stationuuid': '4559582e-6dcb-4e3c-b20f-bbd9d87a66f8',
      'serveruuid': '2c8e32ce-33be-42ce-bc28-fb58047273ca',
      'name': '黑龙江音乐广播',
      'url': 'https://lhttp.qtfm.cn/live/4969/64k.mp3',
      'url_resolved': 'https://lhttp.qtfm.cn/live/4969/64k.mp3',
      'homepage': 'http://www.hljtv.com/',
      'favicon': '',
      'tags': '95.8,95.8 fm,entertainment,music',
      'country': 'China',
      'countrycode': 'CN',
      'iso_3166_2': '',
      'state': 'Amur River',
      'language': 'chinese',
      'languagecodes': 'zh',
      'votes': 63,
      'lastchangetime': '2025-06-13 06:34:06',
      'lastchangetime_iso8601': '2025-06-13T06:34:06Z',
      'codec': 'MP3',
      'bitrate': 0,
      'hls': 0,
      'lastcheckok': 1,
      'lastchecktime': '2025-07-06 21:33:46',
      'lastchecktime_iso8601': '2025-07-06T21:33:46Z',
      'lastcheckoktime': '2025-07-06 21:33:46',
      'lastcheckoktime_iso8601': '2025-07-06T21:33:46Z',
      'lastlocalchecktime': '2025-07-06 15:12:22',
      'lastlocalchecktime_iso8601': '2025-07-06T15:12:22Z',
      'clicktimestamp': '2025-07-06 11:47:57',
      'clicktimestamp_iso8601': '2025-07-06T11:47:57Z',
      'clickcount': 12,
      'clicktrend': 1,
      'ssl_error': 0,
      'geo_lat': null,
      'geo_long': null,
      'geo_distance': null,
      'has_extended_info': false
    },
  ];

  /// 获取所有示例电台数据
  @Deprecated('Use RadioApiService instead')
  static List<LegacyRadioStation> getSampleStations() {
    return _sampleStationsData
        .map((data) => LegacyRadioStation.fromJson(data))
        .toList();
  }

  /// 获取简化的电台数据
  static List<StationSimple> getSampleSimpleStations() {
    return _sampleStationsData
        .map((data) => StationSimple.fromSampleData(data))
        .toList();
  }

  /// 根据分类获取电台
  static List<StationSimple> getStationsByCategory(RadioCategory category) {
    final allStations = getSampleSimpleStations();
    return allStations.where((station) {
      return station.tags.any((tag) => 
        tag.toLowerCase().contains(category.name.toLowerCase()));
    }).toList();
  }

  /// 获取所有分类及其电台
  static List<StationCategory> getAllCategories() {
    final mainCategories = RadioCategory.getMainCategories();
    return mainCategories.map((category) {
      final stations = getStationsByCategory(category);
      return StationCategory(
        id: category.id.toString(),
        name: category.name,
        displayName: category.displayName,
        stations: stations,
        totalCount: stations.length,
      );
    }).toList();
  }
}
