import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:audio_service/audio_service.dart' as audio_svc;
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:world_tune/src/shared/models/models.dart';
import 'package:world_tune/src/shared/services/audio_service.dart' as app_audio;
import 'package:world_tune/src/shared/utils/image_url_validator.dart';

/// WorldTune 音频处理器 - 实现锁屏播放器功能
/// 
/// 功能实现: 集成系统原生的锁屏媒体控制界面
/// 实现方案: 基于audio_service包，连接现有AudioService逻辑
/// 平台支持: 优先支持iOS，后续扩展Android
/// 实现日期: 2025-01-27
class WorldTuneAudioHandler extends audio_svc.BaseAudioHandler with audio_svc.SeekHandler, audio_svc.QueueHandler {
  final app_audio.AudioService _audioService;
  StreamSubscription? _playbackSubscription;
  StreamSubscription? _stationSubscription;
  StreamSubscription? _playlistSubscription;
  
  /// BUG修复: 缓存应用图标的file URI，避免重复转换
  /// 用于锁屏播放器的兜底图标显示
  static String? _cachedAppIconFileUri;

  WorldTuneAudioHandler(this._audioService) {
    print('🎵 WorldTuneAudioHandler: 初始化锁屏播放器');
    _setupListeners();
    // BUG修复: 预加载应用图标，确保锁屏播放器有兜底图标
    _preloadAppIcon();
  }
  
  /// 预加载应用图标
  /// 
  /// 功能实现: 在初始化时异步加载应用图标，确保后续使用时图标已准备好
  /// 实现方案: 静默加载，不阻塞初始化流程
  /// BUG修复: 2025-01-27 - 提前准备兜底图标，避免首次使用时延迟
  void _preloadAppIcon() async {
    try {
      await _loadAppIconAsFile();
      print('📱 WorldTuneAudioHandler: 应用图标预加载完成');
    } catch (e) {
      print('⚠️ WorldTuneAudioHandler: 应用图标预加载失败 - $e');
    }
  }

  /// 设置监听器，同步状态
  void _setupListeners() {
    // 监听播放状态变化
    _playbackSubscription = _audioService.playbackStream.listen((playback) {
      // 只在状态真正改变时输出日志
      if (_lastLoggedState != playback.state) {
        // print('🔄 WorldTuneAudioHandler: 播放状态更新 - ${playback.state.name}');
        _lastLoggedState = playback.state;
      }
      _updatePlaybackState(playback.state);
    });

    print('✅ WorldTuneAudioHandler: 监听器设置完成');
  }

  // 添加状态跟踪变量
  PlaybackState? _lastLoggedState;

  /// 播放控制 - 连接到现有AudioService
  @override
  Future<void> play() async {
    print('▶️ WorldTuneAudioHandler: 锁屏播放请求');
    try {
      await _audioService.resume();
      print('✅ WorldTuneAudioHandler: 播放成功');
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 播放失败 - $e');
    }
  }

  @override
  Future<void> pause() async {
    print('⏸️ WorldTuneAudioHandler: 锁屏暂停请求');
    try {
      await _audioService.pause();
      print('✅ WorldTuneAudioHandler: 暂停成功');
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 暂停失败 - $e');
    }
  }

  @override
  Future<void> stop() async {
    print('⏹️ WorldTuneAudioHandler: 锁屏停止请求');
    try {
      await _audioService.stop();
      print('✅ WorldTuneAudioHandler: 停止成功');
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 停止失败 - $e');
    }
  }

  @override
  Future<void> skipToNext() async {
    print('⏭️ WorldTuneAudioHandler: 锁屏下一首请求');
    
    // 边界检查：确保有播放列表且可以播放下一首
    if (!_audioService.canPlayNext) {
      print('❌ WorldTuneAudioHandler: 无法播放下一首 - 已是最后一首或无播放列表');
      return;
    }
    
    try {
      final success = await _audioService.playNext();
      if (success) {
        print('✅ WorldTuneAudioHandler: 下一首播放成功');
      } else {
        print('❌ WorldTuneAudioHandler: 下一首播放失败 - 逻辑限制');
      }
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 下一首播放异常 - $e');
    }
  }

  @override
  Future<void> skipToPrevious() async {
    print('⏮️ WorldTuneAudioHandler: 锁屏上一首请求');
    
    // 边界检查：确保有播放列表且可以播放上一首
    if (!_audioService.canPlayPrevious) {
      print('❌ WorldTuneAudioHandler: 无法播放上一首 - 已是第一首或无播放列表');
      return;
    }
    
    try {
      final success = await _audioService.playPrevious();
      if (success) {
        print('✅ WorldTuneAudioHandler: 上一首播放成功');
      } else {
        print('❌ WorldTuneAudioHandler: 上一首播放失败 - 逻辑限制');
      }
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 上一首播放异常 - $e');
    }
  }

  /// 更新锁屏媒体信息为加载状态
  /// 
  /// 功能实现: 在切换电台时立即显示加载提示，提升用户体验
  /// 实现方案: 构建带有加载提示的MediaItem对象
  /// 边界处理: 安全处理空值和异常URL，避免网络超时错误
  /// 实现日期: 2025-01-27
  /// BUG修复: 2025-01-27 - 使用本地图标避免网络超时
  void updateLockScreenMediaItemLoading(StationSimple station) {
    // print('⏳ WorldTuneAudioHandler: 更新媒体信息为加载状态 - ${station.name}');
    
    try {
      // BUG修复: 加载状态使用本地图标，避免网络超时错误
      // 修复策略: 在加载过程中不使用网络图标，减少超时风险
      // 影响范围: worldtune_audio_handler.dart 加载状态图标显示
      // 修复日期: 2025-01-27
      Uri? artUri = _getSafeArtUri(station.favicon, useNetworkForLoading: false);

      final mediaItem = audio_svc.MediaItem(
        id: station.id,
        title: 'loading... ${station.name}',
        artist: 'WorldTune Radio',
        album: station.country.isNotEmpty ? station.country : 'Radio Station',
        artUri: artUri,
        duration: null, // 电台流无固定时长
        extras: {
          'stationId': station.id,
          'stationUrl': station.url,
          'stationCountry': station.country,
          'isLoading': true,
        },
      );

      this.mediaItem.add(mediaItem);
      // print('✅ WorldTuneAudioHandler: 加载状态媒体信息更新完成');
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 更新加载状态媒体信息失败 - $e');
    }
  }

  /// 更新锁屏媒体信息
  /// 
  /// 功能实现: 将当前播放的电台信息同步到锁屏界面
  /// 实现方案: 构建MediaItem对象，包含电台基本信息
  /// 边界处理: 安全处理空值和异常URL，智能处理网络超时
  /// BUG修复: 2025-01-27 - 添加网络图标超时保护
  void updateLockScreenMediaItem(StationSimple station) {
    // print('📻 WorldTuneAudioHandler: 更新媒体信息 - ${station.name}');
    
    try {
      // BUG修复: 使用安全的图标获取方法，避免网络超时
      Uri? artUri = _getSafeArtUri(station.favicon, useNetworkForLoading: true);

      final mediaItem = audio_svc.MediaItem(
        id: station.id,
        title: station.name,
        artist: 'WorldTune Radio',
        album: station.country.isNotEmpty ? station.country : 'Radio Station',
        artUri: artUri,
        duration: null, // 电台流无固定时长
        extras: {
          'stationId': station.id,
          'stationUrl': station.url,
          'stationCountry': station.country,
        },
      );

      this.mediaItem.add(mediaItem);
      // print('✅ WorldTuneAudioHandler: 媒体信息更新完成');
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 更新媒体信息失败 - $e');
    }
  }

  /// 更新播放状态到锁屏界面
  /// 
  /// 功能实现: 同步播放状态并动态控制锁屏按钮显示
  /// 实现方案: 根据播放列表状态智能显示控制按钮
  /// 边界处理: 无播放列表时隐藏切换按钮
  void _updatePlaybackState(PlaybackState audioServiceState) {
    // 减少冗余日志，只在状态变化或出错时输出
    
    try {
      List<audio_svc.MediaControl> controls = [];
      
      // 边界检查：根据播放列表状态动态添加控制按钮
      final canPlayPrev = _audioService.canPlayPrevious;
      final canPlayNext = _audioService.canPlayNext;
      
      if (canPlayPrev) {
        controls.add(audio_svc.MediaControl.skipToPrevious);
      }
      
      // 播放/暂停按钮（必选）
      if (audioServiceState == PlaybackState.playing) {
        controls.add(audio_svc.MediaControl.pause);
      } else {
        controls.add(audio_svc.MediaControl.play);
      }
      
      if (canPlayNext) {
        controls.add(audio_svc.MediaControl.skipToNext);
      }

      // 构建紧凑视图动作索引（iOS锁屏界面显示的主要按钮）
      final compactActionIndices = _buildCompactActionIndices(controls);
      
      final newPlaybackState = audio_svc.PlaybackState(
        controls: controls,
        systemActions: const {audio_svc.MediaAction.seek},
        androidCompactActionIndices: compactActionIndices,
        processingState: _mapAudioProcessingState(audioServiceState),
        playing: audioServiceState == PlaybackState.playing,
        updatePosition: Duration.zero,
        speed: 1.0,
      );

      playbackState.add(newPlaybackState);
      
      // 只在调试模式下输出详细信息
      // print('✅ WorldTuneAudioHandler: 播放状态更新完成 - ${controls.length}个控制按钮');
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 更新播放状态失败 - $e');
    }
  }

  /// 构建紧凑视图动作索引
  /// 
  /// 功能实现: 为iOS锁屏界面选择最重要的3个控制按钮
  /// 实现方案: 优先级排序（上一首、播放/暂停、下一首）
  /// 边界处理: 确保不超过3个按钮限制
  List<int> _buildCompactActionIndices(List<audio_svc.MediaControl> controls) {
    List<int> indices = [];
    
    for (int i = 0; i < controls.length && indices.length < 3; i++) {
      final control = controls[i];
      
      // 按重要性优先添加到紧凑视图
      if (control == audio_svc.MediaControl.skipToPrevious ||
          control == audio_svc.MediaControl.play ||
          control == audio_svc.MediaControl.pause ||
          control == audio_svc.MediaControl.skipToNext) {
        indices.add(i);
      }
    }
    
    return indices;
  }

  /// 映射音频处理状态
  /// 
  /// 功能实现: 将内部播放状态转换为audio_service状态
  /// 实现方案: 状态枚举映射
  /// 边界处理: 覆盖所有可能的状态值
  audio_svc.AudioProcessingState _mapAudioProcessingState(PlaybackState state) {
    switch (state) {
      case PlaybackState.stopped:
        return audio_svc.AudioProcessingState.idle;
      case PlaybackState.loading:
        return audio_svc.AudioProcessingState.loading;
      case PlaybackState.playing:
        return audio_svc.AudioProcessingState.ready;
      case PlaybackState.paused:
        return audio_svc.AudioProcessingState.ready;
      case PlaybackState.error:
        return audio_svc.AudioProcessingState.error;
    }
  }

  /// 释放资源
  @override
  Future<void> onTaskRemoved() async {
    print('🗑️ WorldTuneAudioHandler: 任务被移除，释放资源');
    await _cleanup();
    await super.onTaskRemoved();
  }

  /// 清理资源
  Future<void> _cleanup() async {
    print('🧹 WorldTuneAudioHandler: 清理监听器');
    await _playbackSubscription?.cancel();
    await _stationSubscription?.cancel();
    await _playlistSubscription?.cancel();
  }

  /// BUG修复: 缓存失败的图标URL，避免重复请求
  /// BUG修复: 统一使用ImageUrlValidator处理图片URL验证
  /// 这些静态变量已迁移到ImageUrlValidator中统一管理
  /// 实现日期: 2025-01-27
  
  /// BUG修复: 应用图标路径常量，用于锁屏播放器兜底图标
  /// 2025-01-27 - 使用临时文件方案解决MediaItem协议限制
  static const String _appIconPath = 'assets/app_icon.png';
  
  /// 加载应用图标并保存为临时文件
  /// 
  /// 功能实现: 将Flutter asset图标保存为临时文件，返回file URI
  /// 实现方案: 使用rootBundle加载图标，写入临时目录，返回file URI
  /// 缓存策略: 保存后缓存文件URI，避免重复写入
  /// BUG修复: 2025-01-27 - 解决锁屏播放器不支持data URI的问题
  static Future<String?> _loadAppIconAsFile() async {
    // 如果已缓存，直接返回
    if (_cachedAppIconFileUri != null) {
      return _cachedAppIconFileUri;
    }
    
    try {
      print('🔄 WorldTuneAudioHandler: 开始加载应用图标...');
      
      // 从assets加载图标数据
      final ByteData data = await rootBundle.load(_appIconPath);
      final Uint8List bytes = data.buffer.asUint8List();
      
      // 获取临时目录
      final Directory tempDir = await getTemporaryDirectory();
      final String filePath = '${tempDir.path}/worldtune_app_icon.png';
      
      // 写入临时文件
      final File iconFile = File(filePath);
      await iconFile.writeAsBytes(bytes);
      
      // 创建file URI
      final String fileUri = iconFile.uri.toString();
      
      // 缓存结果
      _cachedAppIconFileUri = fileUri;
      
      print('✅ WorldTuneAudioHandler: 应用图标文件保存完成，路径: $filePath');
      print('🔗 WorldTuneAudioHandler: 文件URI: $fileUri');
      return fileUri;
      
    } catch (e) {
      print('❌ WorldTuneAudioHandler: 应用图标文件保存失败 - $e');
      return null;
    }
  }

  /// 获取安全的本地图标URI
  /// 
  /// 功能实现: 提供应用图标作为网络图标的兜底方案
  /// 实现方案: 使用预加载的临时文件URI，确保锁屏播放器有美观的图标显示
  /// 返回值: 应用图标的file URI，如果未加载则返回null让系统使用默认图标
  /// BUG修复: 2025-01-27 - 使用自定义应用图标替代系统默认图标
  Uri? _getSafeLocalIconUri() {
    // 如果应用图标已预加载，使用自定义图标
    if (_cachedAppIconFileUri != null) {
      // print('📱 WorldTuneAudioHandler: 使用自定义应用图标文件');
      return Uri.parse(_cachedAppIconFileUri!);
    }
    
    // 如果图标未加载，返回null让系统使用默认图标
    print('⚠️ WorldTuneAudioHandler: 应用图标文件未加载，使用系统默认图标');
    return null;
  }

  /// 安全获取电台图标URL
  /// 
  /// 功能实现: 智能处理网络图标，避免重复请求失败的URL
  /// 实现方案: 检查失败缓存，定期清理过期记录，失败时使用自定义应用图标兜底
  /// 边界处理: 加载状态可选择不使用网络图标
  /// BUG修复: 2025-01-27 - 网络图标失败时使用自定义应用图标，提升视觉体验
  Uri? _getSafeArtUri(String iconUrl, {required bool useNetworkForLoading}) {
    // 如果URL为空，使用应用图标兜底
    if (iconUrl.isEmpty) {
      print('📱 WorldTuneAudioHandler: URL为空，使用应用图标兜底');
      return _getSafeLocalIconUri();
    }
    
    // 如果是加载状态且选择不使用网络图标，使用应用图标兜底
    if (!useNetworkForLoading) {
      print('⏳ WorldTuneAudioHandler: 加载状态使用应用图标兜底');
      return _getSafeLocalIconUri();
    }
    
    // 使用统一的ImageUrlValidator进行检查
    if (ImageUrlValidator.isInFailedCache(iconUrl)) {
      print('⚠️ WorldTuneAudioHandler: URL在失败缓存中，使用应用图标兜底 - $iconUrl');
      return _getSafeLocalIconUri();
    }
    
    if (ImageUrlValidator.hasKnownIssues(iconUrl)) {
      print('⚠️ WorldTuneAudioHandler: URL有已知问题(异常端口/问题域名)，使用应用图标兜底 - $iconUrl');
      ImageUrlValidator.addFailedUrl(iconUrl); // 加入统一失败缓存
      return _getSafeLocalIconUri();
    }
    
    // 尝试解析URL
    try {
      final uri = Uri.parse(iconUrl);
      print('🖼️ WorldTuneAudioHandler: 使用网络图标 - $iconUrl');
      return uri;
    } catch (e) {
      print('⚠️ WorldTuneAudioHandler: 图标URL解析失败，使用应用图标兜底 - $iconUrl, 错误: $e');
      ImageUrlValidator.addFailedUrl(iconUrl);
      return _getSafeLocalIconUri();
    }
  }
  
  /// 手动添加失败的图标URL到黑名单
  /// 
  /// 功能实现: 当发现某个URL频繁出错时，可以手动加入黑名单
  /// 使用场景: 调试时发现新的问题域名
  /// 
  /// 注意: 现在统一使用ImageUrlValidator.addFailedUrl()方法
  static void addFailedIconUrl(String iconUrl) {
    ImageUrlValidator.addFailedUrl(iconUrl);
    print('⚠️ WorldTuneAudioHandler: 手动添加失败URL到统一黑名单 - $iconUrl');
  }

  /// 释放资源
  void dispose() {
    print('🗑️ WorldTuneAudioHandler: 释放资源');
    _cleanup();
  }
}