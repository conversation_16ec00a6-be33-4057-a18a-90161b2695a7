import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';
import '../utils/logger.dart';
import 'radio_api_service.dart';
import 'station_url_validator.dart';

/// 导入收藏结果模型
class ImportResult {
  final int successCount;
  final int duplicateCount;
  final int failedCount;
  final List<String> failedIds;
  final List<StationSimple> importedStations;

  ImportResult({
    required this.successCount,
    required this.duplicateCount,
    required this.failedCount,
    required this.failedIds,
    required this.importedStations,
  });
}

/// 存储服务 - 使用SharedPreferences管理本地数据
class StorageService {
  static const String _favoritesKey = 'favorite_stations';
  static const String _historyKey = 'play_history';
  static const String _preferencesKey = 'user_preferences';
  static const String _stationSettingsKey = 'station_settings';

  final SharedPreferences _prefs;
  final Logger _logger = Logger();

  StorageService(this._prefs);

  /// 初始化存储服务
  static Future<StorageService> create() async {
    final prefs = await SharedPreferences.getInstance();
    return StorageService(prefs);
  }

  /// 获取收藏的电台列表
  Future<List<StationSimple>?> getFavorites() async {
    try {
      final String? data = _prefs.getString(_favoritesKey);
      if (data == null) return null;
      
      final List<dynamic> decoded = jsonDecode(data) as List<dynamic>;
      return decoded.map((item) => StationSimple.fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      _logger.log(ProviderContainer(), 'Error reading favorites: $e');
      return null;
    }
  }

  /// 保存收藏电台列表
  Future<bool> saveFavorites(List<StationSimple> favorites) async {
    try {
      final List<Map<String, dynamic>> jsonList = favorites.map((station) => station.toJson()).toList();
      final String data = jsonEncode(jsonList);
      return await _prefs.setString(_favoritesKey, data);
    } catch (e) {
      _logger.log(ProviderContainer(), 'Error saving favorites: $e');
      return false;
    }
  }

  /// 添加电台到收藏
  Future<void> addToFavorites(StationSimple station) async {
    final favorites = await getFavorites() ?? [];
    
    // 检查是否已经收藏
    if (favorites.any((s) => s.id == station.id)) {
      return;
    }

    favorites.add(station);
    await saveFavorites(favorites);
  }

  /// 从收藏中移除电台
  Future<void> removeFromFavorites(String stationId) async {
    final favorites = await getFavorites() ?? [];
    favorites.removeWhere((station) => station.id == stationId);
    await saveFavorites(favorites);
  }

  /// 检查电台是否已收藏
  Future<bool> isFavorite(String stationId) async {
    final favorites = await getFavorites() ?? [];
    return favorites.any((station) => station.id == stationId);
  }

  /// 获取播放历史
  Future<List<PlayHistory>?> getPlayHistory() async {
    try {
      final String? data = _prefs.getString(_historyKey);
      if (data == null) return null;
      
      final List<dynamic> decoded = jsonDecode(data) as List<dynamic>;
      return decoded.map((item) => PlayHistory.fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      _logger.log(ProviderContainer(), 'Error reading play history: $e');
      return null;
    }
  }

  /// 保存播放历史
  Future<bool> savePlayHistory(List<PlayHistory> history) async {
    try {
      final List<Map<String, dynamic>> jsonList = history.map((item) => item.toJson()).toList();
      final String data = jsonEncode(jsonList);
      return await _prefs.setString(_historyKey, data);
    } catch (e) {
      _logger.log(ProviderContainer(), 'Error saving play history: $e');
      return false;
    }
  }

  /// 添加播放记录
  Future<void> addToPlayHistory(StationSimple station) async {
    print('StorageService: Adding to play history - ${station.name}');
    final history = await getPlayHistory() ?? [];
    print('StorageService: Current history count: ${history.length}');
    
    // 移除已存在的记录
    history.removeWhere((h) => h.stationId == station.id);
    
    // 添加新记录到开头，包含更多电台信息
    history.insert(0, PlayHistory(
      stationId: station.id,
      stationName: station.name,
      playedAt: DateTime.now(),
      stationUrl: station.url,
      stationCountry: station.country,
      stationFavicon: station.favicon,
      stationLanguage: station.language,
    ));

    // 限制历史记录数量（最多50条）
    if (history.length > 50) {
      history.removeRange(50, history.length);
    }

    final success = await savePlayHistory(history);
    print('StorageService: Save play history result: $success, new count: ${history.length}');
  }

  /// 清空播放历史
  Future<void> clearPlayHistory() async {
    await _prefs.remove(_historyKey);
  }

  /// 获取用户偏好设置
  Future<UserPreferences> getUserPreferences() async {
    final jsonString = _prefs.getString(_preferencesKey);
    if (jsonString == null) {
      return const UserPreferences();
    }

    try {
      final Map<String, dynamic> json = jsonDecode(jsonString) as Map<String, dynamic>;
      return UserPreferences.fromJson(json);
    } catch (e) {
      return const UserPreferences();
    }
  }

  /// 保存用户偏好设置
  Future<void> saveUserPreferences(UserPreferences preferences) async {
    final jsonString = jsonEncode(preferences.toJson());
    await _prefs.setString(_preferencesKey, jsonString);
  }

  /// 获取最近播放的电台
  Future<List<StationSimple>> getRecentStations() async {
    print('StorageService: Getting recent stations');
    final history = await getPlayHistory() ?? [];
    print('StorageService: Found ${history.length} history items');
    
    final recentStations = <StationSimple>[];
    for (final historyItem in history.take(20)) {
      // 从播放历史构建完整的电台信息
      final station = StationSimple(
        id: historyItem.stationId,
        name: historyItem.stationName,
        url: historyItem.stationUrl ?? '',
        country: historyItem.stationCountry ?? '',
        favicon: historyItem.stationFavicon ?? '',
        language: historyItem.stationLanguage ?? '',
        lastPlayed: historyItem.playedAt,
      );
      recentStations.add(station);
    }
    
    print('StorageService: Returning ${recentStations.length} recent stations');
    return recentStations;
  }

  /// 获取电台设置
  Future<Map<String, dynamic>?> getStationSettings(String stationId) async {
    try {
      final String? data = _prefs.getString('${_stationSettingsKey}_$stationId');
      if (data == null) return null;
      
      return jsonDecode(data) as Map<String, dynamic>;
    } catch (e) {
      _logger.log(ProviderContainer(), 'Error reading station settings: $e');
      return null;
    }
  }

  /// 导出收藏列表为分享文本
  /// 
  /// 返回包含电台ID和用户友好文本的分享字符串
  Future<String?> exportFavorites() async {
    try {
      final favorites = await getFavorites() ?? [];
      
      if (favorites.isEmpty) {
        return null;
      }

      // 提取电台ID列表
      final stationIds = favorites.map((station) => station.id).join(',');
      
      // 生成分享文本
      final shareText = '''I've shared ${favorites.length} favorite radio stations with you!

Copy the following content to WorldTune app's "Library > Import Favorites" to import:

WorldTune_Favorites:v1:$stationIds

Enjoy these amazing radio stations! 📻''';

      return shareText;
    } catch (e) {
      _logger.log(ProviderContainer(), 'Error exporting favorites: $e');
      return null;
    }
  }



  /// 解析导入文本格式
  /// 
  /// 支持格式: WorldTune_Favorites:v1:station_id1,station_id2,station_id3
  /// 返回电台ID列表，如果格式错误返回null
  List<String>? parseImportText(String text) {
    try {
      // 移除前后空白字符
      text = text.trim();
      
      // 查找数据行（包含"WorldTune_Favorites:v1:"的行）
      final lines = text.split('\n');
      String? dataLine;
      
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.startsWith('WorldTune_Favorites:v1:')) {
          dataLine = trimmedLine;
          break;
        }
      }
      
      if (dataLine == null) {
        _logger.log(ProviderContainer(), 'Import text format error: No data line found');
        return null;
      }
      
      // 提取数据部分
      final parts = dataLine.split(':');
      if (parts.length < 3) {
        _logger.log(ProviderContainer(), 'Import text format error: Invalid data format');
        return null;
      }
      
      // 验证格式标识
      if (parts[0] != 'WorldTune_Favorites' || parts[1] != 'v1') {
        _logger.log(ProviderContainer(), 'Import text format error: Invalid header');
        return null;
      }
      
      // 提取并解析电台ID
      final stationIdsText = parts.sublist(2).join(':'); // 处理ID中可能包含":"的情况
      if (stationIdsText.isEmpty) {
        _logger.log(ProviderContainer(), 'Import text format error: No station IDs found');
        return null;
      }
      
      final stationIds = stationIdsText
          .split(',')
          .map((id) => id.trim())
          .where((id) => id.isNotEmpty)
          .toList();
      
      _logger.log(ProviderContainer(), 'Parsed ${stationIds.length} station IDs from import text');
      return stationIds;
    } catch (e) {
      _logger.log(ProviderContainer(), 'Error parsing import text: $e');
      return null;
    }
  }

  /// 从电台ID列表导入收藏
  /// 
  /// [stationIds] 要导入的电台ID列表
  /// [onProgress] 进度回调函数，参数为(当前进度, 总数, 当前处理的电台ID)
  /// 
  /// 返回导入结果统计
  Future<ImportResult> importFavorites(
    List<String> stationIds, {
    Function(int current, int total, String stationId)? onProgress,
  }) async {
    if (stationIds.isEmpty) {
      return ImportResult(
        successCount: 0,
        duplicateCount: 0,
        failedCount: 0,
        failedIds: [],
        importedStations: [],
      );
    }

    try {
      // 获取现有收藏列表
      final existingFavorites = await getFavorites() ?? [];
      final existingIds = existingFavorites.map((s) => s.id).toSet();
      
      // 使用电台API服务单例
      final radioApiService = RadioApiService();

      int currentIndex = 0;
      final newStations = <StationSimple>[];
      final failedIds = <String>[];
      int duplicateCount = 0;

      // 分批处理，控制并发数量
      const batchSize = 5;
      for (int i = 0; i < stationIds.length; i += batchSize) {
        final batch = stationIds.skip(i).take(batchSize).toList();
        final futures = batch.map((stationId) async {
          currentIndex++;
          onProgress?.call(currentIndex, stationIds.length, stationId);

          try {
            // 检查是否已存在
            if (existingIds.contains(stationId)) {
              return {'type': 'duplicate', 'stationId': stationId};
            }

            // 获取电台详情
            final response = await radioApiService.getRadioDetail(stationId);
            final station = StationSimple.fromRadioDetailResponse(response);
            
            return {'type': 'success', 'station': station};
          } catch (e) {
            _logger.log(ProviderContainer(), 'Failed to import station $stationId: $e');
            return {'type': 'failed', 'stationId': stationId};
          }
        });

        // 等待当前批次完成
        final batchResults = await Future.wait(futures);
        
        // 处理批次结果
        for (final batchResult in batchResults) {
          switch (batchResult['type']) {
            case 'success':
              final station = batchResult['station'] as StationSimple;
              newStations.add(station);
              break;
            case 'duplicate':
              duplicateCount++;
              break;
            case 'failed':
              failedIds.add(batchResult['stationId'] as String);
              break;
          }
        }
      }

      // 保存新收藏的电台
      if (newStations.isNotEmpty) {
        final allFavorites = [...existingFavorites, ...newStations];
        await saveFavorites(allFavorites);
      }

      final result = ImportResult(
        successCount: newStations.length,
        duplicateCount: duplicateCount,
        failedCount: failedIds.length,
        failedIds: failedIds,
        importedStations: newStations,
      );

      _logger.log(ProviderContainer(), 
        'Import completed: ${result.successCount} success, ${result.duplicateCount} duplicate, ${result.failedCount} failed');
      
      return result;
    } catch (e) {
      _logger.log(ProviderContainer(), 'Error importing favorites: $e');
      return ImportResult(
        successCount: 0,
        duplicateCount: 0,
        failedCount: 0,
        failedIds: [],
        importedStations: [],
      );
    }
  }




}

/// 最近播放电台状态管理
class RecentStationsNotifier extends StateNotifier<AsyncValue<List<StationSimple>>> {
  final StorageService _storageService;

  RecentStationsNotifier(this._storageService) : super(const AsyncValue.loading()) {
    loadRecentStations();
  }

  /// 加载最近播放电台
  Future<void> loadRecentStations() async {
    try {
      state = const AsyncValue.loading();
      final stations = await _storageService.getRecentStations();
      state = AsyncValue.data(stations);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 刷新最近播放列表
  Future<void> refresh() async {
    await loadRecentStations();
  }
}

/// 存储服务提供者
final storageServiceProvider = Provider<StorageService>((ref) {
  throw UnimplementedError('StorageService must be overridden in main()');
});

/// 收藏电台提供者
final favoriteStationsProvider = FutureProvider<List<StationSimple>>((ref) async {
  final storageService = ref.watch(storageServiceProvider);
  return await storageService.getFavorites() ?? [];
});

/// 播放历史提供者
final playHistoryProvider = FutureProvider<List<PlayHistory>>((ref) async {
  final storageService = ref.watch(storageServiceProvider);
  return await storageService.getPlayHistory() ?? [];
});

/// 用户偏好设置提供者
final userPreferencesProvider = FutureProvider<UserPreferences>((ref) async {
  final storageService = ref.watch(storageServiceProvider);
  return await storageService.getUserPreferences();
});

/// 最近播放电台提供者 - 改为StateNotifierProvider支持手动刷新
final recentStationsProvider = StateNotifierProvider<RecentStationsNotifier, AsyncValue<List<StationSimple>>>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return RecentStationsNotifier(storageService);
});
