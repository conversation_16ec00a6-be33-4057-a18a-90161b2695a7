import 'dart:async';
import 'package:ad_sdk/ad_sdk.dart';

/// 插屏广告服务
/// 
/// 功能实现: 统一管理插屏广告的加载、显示、冷却时间等逻辑
/// 实现方案: 单例服务，提供预加载、显示、冷却管理等功能
/// 影响范围: interstitial_ad_service.dart, home_page.dart
/// 实现日期: 2025-01-27
class InterstitialAdService {
  static final InterstitialAdService _instance = InterstitialAdService._internal();
  factory InterstitialAdService() => _instance;
  InterstitialAdService._internal();
  
  // 冷却时间：1分钟
  static const Duration cooldownDuration = Duration(seconds: 30);
  
  // 上次显示广告的时间
  DateTime? _lastShowTime;
  
  /// 检查是否在冷却期内
  bool get isInCooldown {
    if (_lastShowTime == null) return false;
    return DateTime.now().difference(_lastShowTime!) < cooldownDuration;
  }
  
  /// 预加载插屏广告
  Future<void> preloadAd() async {
    try {
      print('🎯 开始预加载插屏广告...');
      await AdSDK.load(AdType.interstitial);
      print('🎯 插屏广告预加载完成');
    } catch (e) {
      print('❌ 插屏广告预加载失败: $e');
    }
  }
  
  /// 显示插屏广告（如果可用且不在冷却期）
  /// 
  /// [onPlayDirectly] 如果不显示广告，直接播放的回调
  /// [onAdShown] 广告开始显示时的回调
  /// [onAdClosed] 广告关闭时的回调
  void showAdIfAvailable({
    required VoidCallback onPlayDirectly,
    VoidCallback? onAdShown,
    VoidCallback? onAdClosed,
  }) {
    // 检查冷却时间
    if (isInCooldown) {
      final remainingTime = cooldownDuration - DateTime.now().difference(_lastShowTime!);
      print('🎯 插屏广告在冷却期内，剩余时间: ${remainingTime.inSeconds}秒');
      onPlayDirectly();
      return;
    }
    
    // 检查广告是否ready，按照你提供的核心逻辑
    if (AdSDK.isReady(AdType.interstitial)) {
      // 广告ready，显示广告
      print('🎯 插屏广告已准备好，开始显示');
      _lastShowTime = DateTime.now(); // 记录显示时间
      onAdShown?.call();
      
      AdSDK.show(
        AdType.interstitial,
        callback: AdCallback(
          onAdShowed: (source) {
            print('🎯 插屏广告显示成功: $source');
          },
          onAdDismissed: (source) {
            print('🎯 插屏广告关闭');
            onAdClosed?.call();
            onPlayDirectly(); // 广告关闭后播放电台
            // 预加载下一个广告
            preloadAd();
          },
          onAdFailedToShow: (error) {
            print('❌ 插屏广告显示失败: $error');
            onAdClosed?.call();
            onPlayDirectly(); // 广告失败直接播放电台
            // 尝试重新加载
            preloadAd();
          },
        ),
      );
    } else {
      // 广告未ready，加载广告但直接播放电台
      print('🎯 插屏广告未准备好，直接播放电台');
      AdSDK.load(AdType.interstitial); // 为下次播放预加载
      onPlayDirectly();
    }
  }
  
  /// 重置冷却时间（用于测试）
  void resetCooldown() {
    _lastShowTime = null;
    print('🔄 插屏广告冷却时间已重置');
  }
  
  /// 获取服务状态信息（用于调试）
  Map<String, dynamic> getStatusInfo() {
    return {
      'isInCooldown': isInCooldown,
      'isAdReady': AdSDK.isReady(AdType.interstitial),
      'lastShowTime': _lastShowTime?.toIso8601String(),
      'cooldownRemaining': isInCooldown 
          ? (cooldownDuration - DateTime.now().difference(_lastShowTime!)).inSeconds
          : 0,
    };
  }
  
  /// 打印状态信息（用于调试）
  void printStatusInfo() {
    final info = getStatusInfo();
    print('📊 插屏广告服务状态:');
    print('   - 冷却期内: ${info['isInCooldown']}');
    print('   - 广告已准备: ${info['isAdReady']}');
    print('   - 上次显示: ${info['lastShowTime'] ?? 'Never'}');
    print('   - 冷却剩余: ${info['cooldownRemaining']}秒');
  }
}