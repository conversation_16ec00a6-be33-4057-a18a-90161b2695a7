import 'dart:io';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// 应用配置管理类
/// 统一管理环境变量和配置参数
class AppConfig {
  /// 私有构造函数，防止实例化
  AppConfig._();

  /// 初始化配置
  /// 现在环境配置由各个main*.dart文件直接加载，这个方法主要用于兼容性
  static Future<void> initialize() async {
    // 检查是否已经有环境配置被加载
    if (dotenv.env.isEmpty) {
      print('⚠️ 未检测到环境配置，使用默认配置');
      return;
    }
    
    print('✅ 检测到已加载的环境配置');
    
    // 打印所有加载的环境变量（用于调试）
    print('📋 当前环境变量:');
    dotenv.env.forEach((key, value) {
      // 隐藏敏感信息
      final displayValue = key.toLowerCase().contains('key') || key.toLowerCase().contains('secret')
          ? '***${value.length > 4 ? value.substring(value.length - 4) : '***'}'
          : value;
      print('  $key: $displayValue');
    });
  }

  /// 是否为生产环境
  static bool get isProduction {
    return dotenv.env['IS_PRODUCTION'] == '1';
  }

  /// 是否为开发环境
  static bool get isDevelopment => !isProduction;

  /// 检测是否为Android模拟器
  static bool get isAndroidEmulator {
    if (!Platform.isAndroid) return false;
    
    // 多种方式检测Android模拟器
    final isEmulator = 
        // 方式1: 检查系统属性
        Platform.environment['ANDROID_EMULATOR']?.isNotEmpty == true ||
        // 方式2: 检查设备品牌和型号
        _isEmulatorByDeviceInfo();
    
    return isEmulator;
  }

  /// 通过设备信息检测是否为模拟器
  static bool _isEmulatorByDeviceInfo() {
    try {
      // 这些环境变量在模拟器中通常存在
      final brand = Platform.environment['ANDROID_BRAND']?.toLowerCase() ?? '';
      final model = Platform.environment['ANDROID_MODEL']?.toLowerCase() ?? '';
      final product = Platform.environment['ANDROID_PRODUCT']?.toLowerCase() ?? '';
      final device = Platform.environment['ANDROID_DEVICE']?.toLowerCase() ?? '';
      
      // 常见的模拟器标识符
      final emulatorIndicators = [
        'emulator', 'simulator', 'sdk', 'google_sdk', 'android-x86', 
        'genymotion', 'vbox', 'goldfish', 'ranchu'
      ];
      
      return emulatorIndicators.any((indicator) =>
          brand.contains(indicator) ||
          model.contains(indicator) ||
          product.contains(indicator) ||
          device.contains(indicator)
      );
    } catch (e) {
      // 如果无法获取设备信息，假设是物理设备
      return false;
    }
  }

  /// API 基础地址 - 智能适配不同设备环境
  static String get apiBaseUrl {
    final configuredUrl = dotenv.env['API_BASE_URL'] ?? 'http://localhost:8300';
    
    // 如果是Android模拟器，需要特殊处理局域网地址
    if (Platform.isAndroid && isAndroidEmulator) {
      if (configuredUrl.contains('************')) {
        // 方案1: 使用Android模拟器专用地址
        final emulatorUrl = configuredUrl.replaceAll('************', '********');
        print('🤖 Android模拟器检测：API地址调整为 $emulatorUrl');
        return emulatorUrl;
      } else if (configuredUrl.contains('localhost') || configuredUrl.contains('127.0.0.1')) {
        // 本地地址也需要映射
        final emulatorUrl = configuredUrl
            .replaceAll('localhost', '********')
            .replaceAll('127.0.0.1', '********');
        print('🤖 Android模拟器检测：本地地址调整为 $emulatorUrl');
        return emulatorUrl;
      }
    }
    
    // 物理设备或其他平台直接使用配置的地址
    if (Platform.isAndroid && !isAndroidEmulator) {
      print('📱 Android物理设备检测：使用原始API地址 $configuredUrl');
    } else if (Platform.isIOS) {
      print('🍎 iOS设备检测：使用原始API地址 $configuredUrl');
    }
    
    return configuredUrl;
  }

  /// XOR 加密密钥
  static String get xorEncryptionKey {
    return dotenv.env['XOR_ENCRYPTION_KEY'] ?? 'gExXgO8x0OhkwHSV';
  }

  /// 连接超时时间（秒）
  static int get apiConnectTimeout {
    return int.tryParse(dotenv.env['API_CONNECT_TIMEOUT'] ?? '30') ?? 30;
  }

  /// 接收超时时间（秒）
  static int get apiReceiveTimeout {
    return int.tryParse(dotenv.env['API_RECEIVE_TIMEOUT'] ?? '30') ?? 30;
  }

  /// 发送超时时间（秒）
  static int get apiSendTimeout {
    return int.tryParse(dotenv.env['API_SEND_TIMEOUT'] ?? '30') ?? 30;
  }

  /// 是否启用调试模式
  static bool get debugMode {
    return dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true';
  }

  /// 是否启用 API 日志
  static bool get enableApiLogging {
    return dotenv.env['ENABLE_API_LOGGING']?.toLowerCase() == 'true';
  }

  /// 默认分页大小
  static int get defaultPageSize {
    return int.tryParse(dotenv.env['DEFAULT_PAGE_SIZE'] ?? '20') ?? 20;
  }

  /// 最大分页大小
  static int get maxPageSize {
    return int.tryParse(dotenv.env['MAX_PAGE_SIZE'] ?? '100') ?? 100;
  }

  /// 缓存持续时间（分钟）
  static int get cacheDurationMinutes {
    return int.tryParse(dotenv.env['CACHE_DURATION_MINUTES'] ?? '30') ?? 30;
  }

  /// 旧的 BASE_URL（保持兼容性）
  static String get baseUrl {
    return dotenv.env['BASE_URL'] ?? apiBaseUrl;
  }

  /// 获取所有配置信息（用于调试）
  static Map<String, dynamic> getAllConfig() {
    return {
      'environment': isProduction ? 'production' : 'development',
      'apiBaseUrl': apiBaseUrl,
      'xorEncryptionKey': '***${xorEncryptionKey.substring(xorEncryptionKey.length - 4)}', // 隐藏密钥
      'apiConnectTimeout': apiConnectTimeout,
      'apiReceiveTimeout': apiReceiveTimeout,
      'apiSendTimeout': apiSendTimeout,
      'debugMode': debugMode,
      'enableApiLogging': enableApiLogging,
      'defaultPageSize': defaultPageSize,
      'maxPageSize': maxPageSize,
      'cacheDurationMinutes': cacheDurationMinutes,
    };
  }

  /// 验证配置是否完整
  static bool validateConfig() {
    final requiredKeys = [
      'API_BASE_URL',
      'XOR_ENCRYPTION_KEY',
    ];

    for (final key in requiredKeys) {
      if (dotenv.env[key] == null || dotenv.env[key]!.isEmpty) {
        print('❌ 缺少必需的配置项: $key');
        return false;
      }
    }

    return true;
  }

  /// 打印配置信息（仅在调试模式下）
  static void printConfig() {
    if (debugMode) {
      print('📋 应用配置信息:');
      final config = getAllConfig();
      config.forEach((key, value) {
        print('  $key: $value');
      });
    }
  }

  /// 网络诊断信息
  static void printNetworkDiagnostics() {
    print('🔍 网络诊断信息:');
    print('  平台: ${Platform.operatingSystem}');
    print('  是否为Android: ${Platform.isAndroid}');
    print('  是否为iOS: ${Platform.isIOS}');
    
    if (Platform.isAndroid) {
      print('  是否为模拟器: $isAndroidEmulator');
      print('  设备类型: ${isAndroidEmulator ? '模拟器' : '物理设备'}');
      
      // 打印环境变量
      final envVars = [
        'ANDROID_BRAND', 'ANDROID_MODEL', 'ANDROID_PRODUCT', 
        'ANDROID_DEVICE', 'ANDROID_EMULATOR'
      ];
      
      print('  环境变量:');
      for (final key in envVars) {
        final value = Platform.environment[key] ?? '未设置';
        print('    $key: $value');
      }
    }
    
    print('  API基础地址: $apiBaseUrl');
    print('  连接超时: ${apiConnectTimeout}秒');
    print('  接收超时: ${apiReceiveTimeout}秒');
    print('  调试模式: $debugMode');
    print('  API日志: $enableApiLogging');
  }
}
