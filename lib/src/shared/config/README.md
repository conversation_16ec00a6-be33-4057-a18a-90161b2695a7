# 应用配置管理

## 概述

本模块提供统一的环境配置管理，支持多环境配置和动态加载。

## 功能特性

- 🌍 **多环境支持**: 开发、生产环境独立配置
- 🔒 **安全管理**: 敏感信息（如密钥）统一管理
- ⚡ **动态加载**: 运行时根据环境自动加载配置
- 🛡️ **配置验证**: 启动时验证必需配置项
- 📊 **调试支持**: 开发模式下打印配置信息

## 文件结构

```
lib/src/shared/config/
├── app_config.dart          # 配置管理类
└── README.md               # 本文档

assets/env/
├── .env.development        # 开发环境配置
└── .env.production         # 生产环境配置
```

## 配置文件

### 开发环境 (.env.development)

```env
# 环境标识
IS_PRODUCTION=0

# 后端服务地址
API_BASE_URL=http://10.60.81.105:8300

# XOR 加密密钥
XOR_ENCRYPTION_KEY=gExXgO8x0OhkwHSV

# 网络超时配置（秒）
API_CONNECT_TIMEOUT=30
API_RECEIVE_TIMEOUT=30
API_SEND_TIMEOUT=30

# 调试模式
DEBUG_MODE=true
ENABLE_API_LOGGING=true

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# 缓存配置
CACHE_DURATION_MINUTES=30
```

### 生产环境 (.env.production)

```env
# 环境标识
IS_PRODUCTION=1

# 后端服务地址（生产环境）
API_BASE_URL=https://api.worldtune.com

# XOR 加密密钥
XOR_ENCRYPTION_KEY=gExXgO8x0OhkwHSV

# 网络超时配置（秒）
API_CONNECT_TIMEOUT=30
API_RECEIVE_TIMEOUT=30
API_SEND_TIMEOUT=30

# 调试模式
DEBUG_MODE=false
ENABLE_API_LOGGING=false

# 分页配置
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# 缓存配置
CACHE_DURATION_MINUTES=60
```

## 使用方法

### 1. 初始化配置

在应用启动时调用：

```dart
import 'package:world_tune/src/shared/config/app_config.dart';

Future<void> main() async {
  // 初始化应用配置
  await AppConfig.initialize();
  
  // 验证配置
  if (!AppConfig.validateConfig()) {
    throw Exception('应用配置验证失败，请检查环境变量');
  }
  
  // 打印配置信息（仅在调试模式下）
  AppConfig.printConfig();

  runApp(MyApp());
}
```

### 2. 访问配置

```dart
// 获取 API 基础地址
String apiUrl = AppConfig.apiBaseUrl;

// 获取加密密钥
String encryptionKey = AppConfig.xorEncryptionKey;

// 检查环境
bool isProd = AppConfig.isProduction;
bool isDev = AppConfig.isDevelopment;

// 获取超时配置
int connectTimeout = AppConfig.apiConnectTimeout;
int receiveTimeout = AppConfig.apiReceiveTimeout;

// 获取调试配置
bool debugMode = AppConfig.debugMode;
bool enableLogging = AppConfig.enableApiLogging;

// 获取分页配置
int pageSize = AppConfig.defaultPageSize;
int maxPageSize = AppConfig.maxPageSize;
```

### 3. 配置验证

```dart
// 验证配置完整性
bool isValid = AppConfig.validateConfig();

// 获取所有配置（用于调试）
Map<String, dynamic> allConfig = AppConfig.getAllConfig();
```

## 环境切换

### 开发环境运行

```bash
# 使用开发环境配置
flutter run -t lib/main_development.dart

# 或者使用环境变量
flutter run --dart-define=ENVIRONMENT=development
```

### 生产环境运行

```bash
# 使用生产环境配置
flutter run -t lib/main_production.dart

# 或者使用环境变量
flutter run --dart-define=ENVIRONMENT=production
```

## 配置项说明

| 配置项 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `API_BASE_URL` | String | ✅ | 后端 API 基础地址 |
| `XOR_ENCRYPTION_KEY` | String | ✅ | XOR 加密密钥 |
| `IS_PRODUCTION` | String | ❌ | 环境标识：1-生产，0-开发 |
| `API_CONNECT_TIMEOUT` | String | ❌ | 连接超时时间（秒），默认 30 |
| `API_RECEIVE_TIMEOUT` | String | ❌ | 接收超时时间（秒），默认 30 |
| `API_SEND_TIMEOUT` | String | ❌ | 发送超时时间（秒），默认 30 |
| `DEBUG_MODE` | String | ❌ | 调试模式：true/false |
| `ENABLE_API_LOGGING` | String | ❌ | API 日志：true/false |
| `DEFAULT_PAGE_SIZE` | String | ❌ | 默认分页大小，默认 20 |
| `MAX_PAGE_SIZE` | String | ❌ | 最大分页大小，默认 100 |
| `CACHE_DURATION_MINUTES` | String | ❌ | 缓存时长（分钟），默认 30 |

## 最佳实践

### 1. 安全性

- ❌ **不要**将敏感信息硬编码在代码中
- ✅ **使用**环境变量管理敏感配置
- ✅ **隐藏**密钥在日志输出中的完整内容

### 2. 环境管理

- ✅ **分离**开发和生产环境配置
- ✅ **验证**配置完整性
- ✅ **提供**合理的默认值

### 3. 调试支持

- ✅ **启用**开发环境的详细日志
- ✅ **禁用**生产环境的调试信息
- ✅ **提供**配置检查工具

## 故障排除

### 配置加载失败

```
❌ 环境配置加载失败: Exception: Unable to load asset
```

**解决方案**：
1. 检查 `pubspec.yaml` 中是否包含环境文件
2. 确认环境文件路径正确
3. 重新运行 `flutter clean && flutter pub get`

### 配置验证失败

```
❌ 缺少必需的配置项: API_BASE_URL
```

**解决方案**：
1. 检查环境文件中是否包含必需的配置项
2. 确认配置项名称拼写正确
3. 确认配置项值不为空

### 网络请求失败

**检查清单**：
1. ✅ `API_BASE_URL` 是否正确
2. ✅ 网络连接是否正常
3. ✅ 后端服务是否运行
4. ✅ 超时配置是否合理
