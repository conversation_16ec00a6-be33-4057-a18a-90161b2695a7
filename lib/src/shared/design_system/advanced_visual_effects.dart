import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// 高级视觉效果库
/// 
/// 提供粒子背景、3D变换、动态模糊等高级视觉效果
class AdvancedVisualEffects {
  AdvancedVisualEffects._();

  // ==================== 粒子背景系统 ====================
  
  /// 粒子背景组件
  static Widget particleBackground({
    required Widget child,
    int particleCount = 50,
    Color particleColor = Colors.white,
    double particleSize = 2.0,
    double animationSpeed = 1.0,
    bool enableInteraction = true,
  }) {
    return Stack(
      children: [
        Positioned.fill(
          child: _ParticleBackgroundPainter(
            particleCount: particleCount,
            particleColor: particleColor,
            particleSize: particleSize,
            animationSpeed: animationSpeed,
            enableInteraction: enableInteraction,
          ),
        ),
        child,
      ],
    );
  }

  // ==================== 3D卡片效果 ====================
  
  /// 3D卡片变换
  static Widget card3D({
    required Widget child,
    double depth = 20.0,
    double rotationX = 0.0,
    double rotationY = 0.0,
    bool enableHover = true,
    Duration animationDuration = const Duration(milliseconds: 200),
  }) {
    return _Card3DWidget(
      depth: depth,
      rotationX: rotationX,
      rotationY: rotationY,
      enableHover: enableHover,
      animationDuration: animationDuration,
      child: child,
    );
  }

  // ==================== 霓虹发光文本 ====================
  
  /// 霓虹发光文本
  static Widget neonText({
    required String text,
    required TextStyle style,
    Color glowColor = const Color(0xFF00FFFF),
    double glowIntensity = 0.8,
    int glowLayers = 3,
  }) {
    return Stack(
      children: [
        // 发光层
        for (int i = glowLayers; i > 0; i--)
          Text(
            text,
            style: style.copyWith(
              foreground: Paint()
                ..style = PaintingStyle.stroke
                ..strokeWidth = i * 2.0
                ..color = glowColor.withOpacity(glowIntensity / i),
            ),
          ),
        // 主文本
        Text(text, style: style),
      ],
    );
  }

  // ==================== 动态模糊背景 ====================
  
  /// 动态模糊背景
  static Widget dynamicBlurBackground({
    required Widget child,
    double blurIntensity = 10.0,
    Color overlayColor = Colors.transparent,
    bool animated = true,
  }) {
    return Stack(
      children: [
        Positioned.fill(
          child: BackdropFilter(
            filter: ui.ImageFilter.blur(
              sigmaX: blurIntensity,
              sigmaY: blurIntensity,
            ),
            child: Container(
              color: overlayColor,
            ),
          ),
        ),
        child,
      ],
    );
  }

  // ==================== 波纹扩散效果 ====================
  
  /// 波纹扩散动画
  static Widget rippleEffect({
    required Widget child,
    Color rippleColor = Colors.white,
    double maxRadius = 100.0,
    Duration duration = const Duration(milliseconds: 1000),
    bool autoStart = true,
  }) {
    return _RippleEffectWidget(
      rippleColor: rippleColor,
      maxRadius: maxRadius,
      duration: duration,
      autoStart: autoStart,
      child: child,
    );
  }

  // ==================== 触觉反馈系统 ====================
  
  /// 触觉反馈
  static void hapticFeedback(HapticFeedbackType type) {
    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
    }
  }

  // ==================== 渐变动画背景 ====================
  
  /// 渐变动画背景
  static Widget animatedGradientBackground({
    required Widget child,
    required List<Color> colors,
    Duration duration = const Duration(seconds: 3),
    bool reverse = true,
  }) {
    return _AnimatedGradientBackground(
      colors: colors,
      duration: duration,
      reverse: reverse,
      child: child,
    );
  }
}

// ==================== 触觉反馈类型 ====================

enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
}

// ==================== 粒子背景绘制器 ====================

class _ParticleBackgroundPainter extends StatefulWidget {
  final int particleCount;
  final Color particleColor;
  final double particleSize;
  final double animationSpeed;
  final bool enableInteraction;

  const _ParticleBackgroundPainter({
    required this.particleCount,
    required this.particleColor,
    required this.particleSize,
    required this.animationSpeed,
    required this.enableInteraction,
  });

  @override
  State<_ParticleBackgroundPainter> createState() => _ParticleBackgroundPainterState();
}

class _ParticleBackgroundPainterState extends State<_ParticleBackgroundPainter>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  final List<Particle> _particles = [];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: (10 / widget.animationSpeed).round()),
      vsync: this,
    )..repeat();
    
    _initializeParticles();
  }

  void _initializeParticles() {
    _particles.clear();
    for (int i = 0; i < widget.particleCount; i++) {
      _particles.add(Particle());
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          painter: ParticlePainter(
            particles: _particles,
            animationValue: _controller.value,
            particleColor: widget.particleColor,
            particleSize: widget.particleSize,
          ),
          size: Size.infinite,
        );
      },
    );
  }
}

// ==================== 粒子类 ====================

class Particle {
  late double x;
  late double y;
  late double vx;
  late double vy;
  late double opacity;
  late double size;

  Particle() {
    reset();
  }

  void reset() {
    x = math.Random().nextDouble();
    y = math.Random().nextDouble();
    vx = (math.Random().nextDouble() - 0.5) * 0.002;
    vy = (math.Random().nextDouble() - 0.5) * 0.002;
    opacity = math.Random().nextDouble() * 0.8 + 0.2;
    size = math.Random().nextDouble() * 0.5 + 0.5;
  }

  void update() {
    x += vx;
    y += vy;

    if (x < 0 || x > 1 || y < 0 || y > 1) {
      reset();
    }
  }
}

// ==================== 粒子绘制器 ====================

class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final double animationValue;
  final Color particleColor;
  final double particleSize;

  ParticlePainter({
    required this.particles,
    required this.animationValue,
    required this.particleColor,
    required this.particleSize,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = particleColor
      ..style = PaintingStyle.fill;

    for (final particle in particles) {
      particle.update();
      
      paint.color = particleColor.withOpacity(particle.opacity);
      
      canvas.drawCircle(
        Offset(
          particle.x * size.width,
          particle.y * size.height,
        ),
        particleSize * particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// ==================== 3D卡片组件 ====================

class _Card3DWidget extends StatefulWidget {
  final Widget child;
  final double depth;
  final double rotationX;
  final double rotationY;
  final bool enableHover;
  final Duration animationDuration;

  const _Card3DWidget({
    required this.child,
    required this.depth,
    required this.rotationX,
    required this.rotationY,
    required this.enableHover,
    required this.animationDuration,
  });

  @override
  State<_Card3DWidget> createState() => _Card3DWidgetState();
}

class _Card3DWidgetState extends State<_Card3DWidget>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _hoverAnimation;
  
  double _rotationX = 0.0;
  double _rotationY = 0.0;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _hoverAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOutCubic,
    ));
    
    _rotationX = widget.rotationX;
    _rotationY = widget.rotationY;
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: widget.enableHover ? (_) => _hoverController.forward() : null,
      onExit: widget.enableHover ? (_) => _hoverController.reverse() : null,
      onHover: widget.enableHover ? _handleHover : null,
      child: AnimatedBuilder(
        animation: _hoverAnimation,
        builder: (context, child) {
          return Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..setEntry(3, 2, 0.001)
              ..rotateX(_rotationX * _hoverAnimation.value)
              ..rotateY(_rotationY * _hoverAnimation.value)
              ..translate(0.0, 0.0, widget.depth * _hoverAnimation.value),
            child: widget.child,
          );
        },
      ),
    );
  }

  void _handleHover(PointerHoverEvent event) {
    // 实现鼠标悬停时的3D旋转效果
    // 这里可以根据鼠标位置计算旋转角度
  }
}

// ==================== 波纹效果组件 ====================

class _RippleEffectWidget extends StatefulWidget {
  final Widget child;
  final Color rippleColor;
  final double maxRadius;
  final Duration duration;
  final bool autoStart;

  const _RippleEffectWidget({
    required this.child,
    required this.rippleColor,
    required this.maxRadius,
    required this.duration,
    required this.autoStart,
  });

  @override
  State<_RippleEffectWidget> createState() => _RippleEffectWidgetState();
}

class _RippleEffectWidgetState extends State<_RippleEffectWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _radiusAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _radiusAnimation = Tween<double>(
      begin: 0.0,
      end: widget.maxRadius,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.8,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    if (widget.autoStart) {
      _controller.repeat();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Container(
              width: _radiusAnimation.value * 2,
              height: _radiusAnimation.value * 2,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: widget.rippleColor.withOpacity(_opacityAnimation.value),
                  width: 2.0,
                ),
              ),
            );
          },
        ),
        widget.child,
      ],
    );
  }
}

// ==================== 动画渐变背景 ====================

class _AnimatedGradientBackground extends StatefulWidget {
  final Widget child;
  final List<Color> colors;
  final Duration duration;
  final bool reverse;

  const _AnimatedGradientBackground({
    required this.child,
    required this.colors,
    required this.duration,
    required this.reverse,
  });

  @override
  State<_AnimatedGradientBackground> createState() => _AnimatedGradientBackgroundState();
}

class _AnimatedGradientBackgroundState extends State<_AnimatedGradientBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    if (widget.reverse) {
      _controller.repeat(reverse: true);
    } else {
      _controller.repeat();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: widget.colors,
              stops: _generateStops(_controller.value),
            ),
          ),
          child: widget.child,
        );
      },
    );
  }

  List<double> _generateStops(double animationValue) {
    final stops = <double>[];
    for (int i = 0; i < widget.colors.length; i++) {
      final baseStop = i / (widget.colors.length - 1);
      final offset = math.sin(animationValue * 2 * math.pi + i) * 0.1;
      stops.add((baseStop + offset).clamp(0.0, 1.0));
    }
    return stops;
  }
}
