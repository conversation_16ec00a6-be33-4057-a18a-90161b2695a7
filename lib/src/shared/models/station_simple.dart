/// 简化的电台模型，用于UI显示和本地存储
class StationSimple {
  final String id;
  final String name;
  final String url;
  final String country;
  final String language;
  final String favicon;
  final String homepage;
  final List<String> tags;
  final int votes;
  final bool isFavorite;
  final int playCount;
  final DateTime? lastPlayed;

  const StationSimple({
    required this.id,
    required this.name,
    required this.url,
    required this.country,
    required this.language,
    this.favicon = '',
    this.homepage = '',
    this.tags = const [],
    this.votes = 0,
    this.isFavorite = false,
    this.playCount = 0,
    this.lastPlayed,
  });

  factory StationSimple.fromJson(Map<String, dynamic> json) {
    return StationSimple(
      id: json['id'] as String,
      name: json['name'] as String,
      url: json['url'] as String,
      country: json['country'] as String,
      language: json['language'] as String,
      favicon: json['favicon'] as String? ?? '',
      homepage: json['homepage'] as String? ?? '',
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      votes: json['votes'] as int? ?? 0,
      isFavorite: json['isFavorite'] as bool? ?? false,
      playCount: json['playCount'] as int? ?? 0,
      lastPlayed: json['lastPlayed'] != null
          ? DateTime.parse(json['lastPlayed'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'country': country,
      'language': language,
      'favicon': favicon,
      'homepage': homepage,
      'tags': tags,
      'votes': votes,
      'isFavorite': isFavorite,
      'playCount': playCount,
      'lastPlayed': lastPlayed?.toIso8601String(),
    };
  }

  /// 从示例数据创建简化模型
  factory StationSimple.fromSampleData(Map<String, dynamic> data) {
    return StationSimple(
      id: data['stationuuid'] as String,
      name: data['name'] as String,
      url: (data['url_resolved'] as String).isNotEmpty
          ? data['url_resolved'] as String
          : data['url'] as String,
      country: data['country'] as String,
      language: data['language'] as String,
      favicon: data['favicon'] as String? ?? '',
      homepage: data['homepage'] as String? ?? '',
      tags: (data['tags'] as String).split(',').map((tag) => tag.trim()).toList(),
      votes: data['votes'] as int? ?? 0,
    );
  }

  /// 从电台详情响应创建简化模型
  static StationSimple fromRadioDetailResponse(dynamic radioDetailResponse) {
    // 只支持 RadioDetailResponse 类型
    final stationInfo = radioDetailResponse.stationInfo;
    final country = radioDetailResponse.country;
    final tags = radioDetailResponse.tags;

    // 处理tags
    List<String> tagList = [];
    if (tags != null && tags is List) {
      for (final tag in tags) {
        if (tag != null) {
          // 假设tags是RadioTag对象列表，有name属性
          final tagName = tag.name?.toString() ?? '';
          if (tagName.isNotEmpty) {
            tagList.add(tagName);
          }
        }
      }
    }
    
    // 如果tags为空，尝试从stationInfo的tags字符串解析
    if (tagList.isEmpty && stationInfo.tags != null && stationInfo.tags is String && (stationInfo.tags as String).isNotEmpty) {
      final tagsString = stationInfo.tags as String;
      tagList = tagsString.split(',').map((String tag) => tag.trim()).where((t) => t.isNotEmpty).toList();
    }

    return StationSimple(
      id: stationInfo.stationUuid as String,
      name: stationInfo.name as String,
      url: (stationInfo.urlResolved?.isNotEmpty == true)
          ? stationInfo.urlResolved! as String
          : stationInfo.url as String,
      country: (country?.name as String?) ?? (stationInfo.country as String?) ?? '',
      language: (stationInfo.language as String?) ?? '',
      favicon: (stationInfo.favicon as String?) ?? '',
      homepage: (stationInfo.homepage as String?) ?? '',
      tags: tagList,
      votes: stationInfo.votes as int,
    );
  }
}

/// 电台分类模型
class StationCategory {
  final String id;
  final String name;
  final String displayName;
  final List<StationSimple> stations;
  final int totalCount;

  const StationCategory({
    required this.id,
    required this.name,
    required this.displayName,
    this.stations = const [],
    this.totalCount = 0,
  });

  factory StationCategory.fromJson(Map<String, dynamic> json) {
    return StationCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      stations: (json['stations'] as List<dynamic>?)
          ?.map((e) => StationSimple.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      totalCount: json['totalCount'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'stations': stations.map((e) => e.toJson()).toList(),
      'totalCount': totalCount,
    };
  }
}

/// 用户收藏和历史记录模型
class UserPreferences {
  final List<String> favoriteStationIds;
  final List<PlayHistory> playHistory;
  final String language;
  final double volume;
  final bool autoPlay;

  const UserPreferences({
    this.favoriteStationIds = const [],
    this.playHistory = const [],
    this.language = 'en',
    this.volume = 0.7,
    this.autoPlay = true,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      favoriteStationIds: (json['favoriteStationIds'] as List<dynamic>?)
          ?.cast<String>() ?? [],
      playHistory: (json['playHistory'] as List<dynamic>?)
          ?.map((e) => PlayHistory.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      language: json['language'] as String? ?? 'en',
      volume: (json['volume'] as num?)?.toDouble() ?? 0.7,
      autoPlay: json['autoPlay'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'favoriteStationIds': favoriteStationIds,
      'playHistory': playHistory.map((e) => e.toJson()).toList(),
      'language': language,
      'volume': volume,
      'autoPlay': autoPlay,
    };
  }
}

/// 播放历史记录
class PlayHistory {
  final String stationId;
  final String stationName;
  final DateTime playedAt;
  final Duration duration;
  final String? stationUrl;
  final String? stationCountry;
  final String? stationFavicon;
  final String? stationLanguage;

  const PlayHistory({
    required this.stationId,
    required this.stationName,
    required this.playedAt,
    this.duration = Duration.zero,
    this.stationUrl,
    this.stationCountry,
    this.stationFavicon,
    this.stationLanguage,
  });

  factory PlayHistory.fromJson(Map<String, dynamic> json) {
    return PlayHistory(
      stationId: json['stationId'] as String,
      stationName: json['stationName'] as String,
      playedAt: DateTime.parse(json['playedAt'] as String),
      duration: Duration(
        milliseconds: json['duration'] as int? ?? 0,
      ),
      stationUrl: json['stationUrl'] as String?,
      stationCountry: json['stationCountry'] as String?,
      stationFavicon: json['stationFavicon'] as String?,
      stationLanguage: json['stationLanguage'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stationId': stationId,
      'stationName': stationName,
      'playedAt': playedAt.toIso8601String(),
      'duration': duration.inMilliseconds,
      'stationUrl': stationUrl,
      'stationCountry': stationCountry,
      'stationFavicon': stationFavicon,
      'stationLanguage': stationLanguage,
    };
  }
}
