/// 完整的电台数据模型（来自示例数据，已废弃）
@Deprecated('Use RadioStation from radio_models.dart instead')
class LegacyRadioStation {
  final String changeUuid;
  final String stationUuid;
  final String serverUuid;
  final String name;
  final String url;
  final String urlResolved;
  final String homepage;
  final String favicon;
  final String tags;
  final String country;
  final String countryCode;
  final String iso31662;
  final String state;
  final String language;
  final String languageCodes;
  final int votes;
  final String lastChangeTime;
  final String lastChangeTimeIso8601;
  final String codec;
  final int bitrate;
  final int hls;
  final int lastCheckOk;
  final String lastCheckTime;
  final String lastCheckTimeIso8601;
  final String lastCheckOkTime;
  final String lastCheckOkTimeIso8601;
  final String lastLocalCheckTime;
  final String lastLocalCheckTimeIso8601;
  final String clickTimestamp;
  final String clickTimestampIso8601;
  final int clickCount;
  final int clickTrend;
  final int sslError;
  final double? geoLat;
  final double? geoLong;
  final double? geoDistance;
  final bool hasExtendedInfo;

  const LegacyRadioStation({
    required this.changeUuid,
    required this.stationUuid,
    required this.serverUuid,
    required this.name,
    required this.url,
    required this.urlResolved,
    required this.homepage,
    required this.favicon,
    required this.tags,
    required this.country,
    required this.countryCode,
    required this.iso31662,
    required this.state,
    required this.language,
    required this.languageCodes,
    required this.votes,
    required this.lastChangeTime,
    required this.lastChangeTimeIso8601,
    required this.codec,
    required this.bitrate,
    required this.hls,
    required this.lastCheckOk,
    required this.lastCheckTime,
    required this.lastCheckTimeIso8601,
    required this.lastCheckOkTime,
    required this.lastCheckOkTimeIso8601,
    required this.lastLocalCheckTime,
    required this.lastLocalCheckTimeIso8601,
    required this.clickTimestamp,
    required this.clickTimestampIso8601,
    required this.clickCount,
    required this.clickTrend,
    required this.sslError,
    this.geoLat,
    this.geoLong,
    this.geoDistance,
    required this.hasExtendedInfo,
  });

  factory LegacyRadioStation.fromJson(Map<String, dynamic> json) {
    return LegacyRadioStation(
      changeUuid: json['changeuuid'] as String,
      stationUuid: json['stationuuid'] as String,
      serverUuid: json['serveruuid'] as String,
      name: json['name'] as String,
      url: json['url'] as String,
      urlResolved: json['url_resolved'] as String,
      homepage: json['homepage'] as String,
      favicon: json['favicon'] as String,
      tags: json['tags'] as String,
      country: json['country'] as String,
      countryCode: json['countrycode'] as String,
      iso31662: json['iso_3166_2'] as String,
      state: json['state'] as String,
      language: json['language'] as String,
      languageCodes: json['languagecodes'] as String,
      votes: json['votes'] as int,
      lastChangeTime: json['lastchangetime'] as String,
      lastChangeTimeIso8601: json['lastchangetime_iso8601'] as String,
      codec: json['codec'] as String,
      bitrate: json['bitrate'] as int,
      hls: json['hls'] as int,
      lastCheckOk: json['lastcheckok'] as int,
      lastCheckTime: json['lastchecktime'] as String,
      lastCheckTimeIso8601: json['lastchecktime_iso8601'] as String,
      lastCheckOkTime: json['lastcheckoktime'] as String,
      lastCheckOkTimeIso8601: json['lastcheckoktime_iso8601'] as String,
      lastLocalCheckTime: json['lastlocalchecktime'] as String,
      lastLocalCheckTimeIso8601: json['lastlocalchecktime_iso8601'] as String,
      clickTimestamp: json['clicktimestamp'] as String,
      clickTimestampIso8601: json['clicktimestamp_iso8601'] as String,
      clickCount: json['clickcount'] as int,
      clickTrend: json['clicktrend'] as int,
      sslError: json['ssl_error'] as int,
      geoLat: json['geo_lat'] as double?,
      geoLong: json['geo_long'] as double?,
      geoDistance: json['geo_distance'] as double?,
      hasExtendedInfo: json['has_extended_info'] as bool,
    );
  }
}

// 电台分类枚举
enum RadioCategory {
  music('music', 'Music'),
  news('news', 'News'),
  sports('sports', 'Sports'),
  talk('talk', 'Talk'),
  culture('culture', 'Culture'),
  lifestyle('lifestyle', 'Lifestyle'),
  entertainment('entertainment', 'Entertainment'),
  traffic('traffic', 'Traffic'),
  youth('youth', 'Youth'),
  literature('literature', 'Literature'),
  information('information', 'Information'),
  fullService('full service', 'Full Service'),
  oldies('oldies', 'Oldies'),
  publicRadio('public radio', 'Public Radio'),
  other('other', 'Other');

  const RadioCategory(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static RadioCategory fromString(String value) {
    return RadioCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => RadioCategory.other,
    );
  }

  static List<RadioCategory> getMainCategories() {
    return [
      RadioCategory.music,
      RadioCategory.news,
      RadioCategory.sports,
      RadioCategory.talk,
      RadioCategory.culture,
      RadioCategory.lifestyle,
    ];
  }
}

// 播放状态枚举
enum PlaybackState {
  stopped,
  loading,
  playing,
  paused,
  error,
}

// 当前播放信息模型
class CurrentPlayback {
  final LegacyRadioStation? station;
  final PlaybackState state;
  final Duration position;
  final String? errorMessage;

  const CurrentPlayback({
    this.station,
    this.state = PlaybackState.stopped,
    this.position = Duration.zero,
    this.errorMessage,
  });

  factory CurrentPlayback.fromJson(Map<String, dynamic> json) {
    return CurrentPlayback(
      // station: json['station'] != null
      //     ? LegacyRadioStation.fromJson(json['station'] as Map<String, dynamic>)
      //     : null,
      state: PlaybackState.values.firstWhere(
        (e) => e.toString() == json['state'],
        orElse: () => PlaybackState.stopped,
      ),
      position: Duration(milliseconds: json['position'] as int? ?? 0),
      errorMessage: json['errorMessage'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // 'station': station?.toJson(),
      'state': state.toString(),
      'position': position.inMilliseconds,
      'errorMessage': errorMessage,
    };
  }

  @override
  String toString() {
    final stationName = station?.name ?? 'No Station';
    final positionStr = '${position.inMinutes}:${(position.inSeconds % 60).toString().padLeft(2, '0')}';
    final errorStr = errorMessage != null ? ' (Error: $errorMessage)' : '';
    return 'CurrentPlayback(station: $stationName, state: ${state.name}, position: $positionStr$errorStr)';
  }
}
