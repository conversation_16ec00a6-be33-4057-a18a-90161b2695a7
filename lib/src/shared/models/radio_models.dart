import 'package:json_annotation/json_annotation.dart';

part 'radio_models.g.dart';

/// 电台信息模型
@JsonSerializable()
class RadioStation {
  /// 电台ID
  final int id;
  
  /// 电台UUID唯一标识
  @JsonKey(name: 'station_uuid')
  final String stationUuid;
  
  /// 服务器UUID
  @Json<PERSON>ey(name: 'server_uuid')
  final String serverUuid;
  
  /// 变更UUID
  @JsonKey(name: 'change_uuid')
  final String changeUuid;
  
  /// 电台名称
  final String name;
  
  /// 播放URL
  final String url;
  
  /// 解析后的播放URL
  @JsonKey(name: 'url_resolved')
  final String? urlResolved;
  
  /// 电台官网
  final String? homepage;
  
  /// 电台图标URL
  final String? favicon;
  
  /// 国家名称
  final String? country;
  
  /// 国家代码
  @JsonKey(name: 'country_code')
  final String? countryCode;
  
  /// ISO 3166-2代码
  @JsonKey(name: 'iso_3166_2')
  final String? iso31662;
  
  /// 州/省
  final String? state;
  
  /// 语言名称
  final String? language;
  
  /// 语言代码（逗号分隔）
  @JsonKey(name: 'language_codes')
  final String? languageCodes;
  
  /// 标签（逗号分隔）
  final String? tags;
  
  /// 音频编码格式
  final String? codec;
  
  /// 比特率
  final int bitrate;
  
  /// 是否HLS流：1-是，0-否
  @JsonKey(name: 'is_hls')
  final int isHls;
  
  /// 投票数
  final int votes;
  
  /// 点击次数
  @JsonKey(name: 'click_count')
  final int clickCount;
  
  /// 点击趋势
  @JsonKey(name: 'click_trend')
  final int clickTrend;
  
  /// SSL错误状态
  @JsonKey(name: 'ssl_error')
  final int sslError;
  
  /// 纬度
  @JsonKey(name: 'geo_lat')
  final double? geoLat;
  
  /// 经度
  @JsonKey(name: 'geo_long')
  final double? geoLong;
  
  /// 地理距离
  @JsonKey(name: 'geo_distance')
  final double? geoDistance;
  
  /// 是否有扩展信息
  @JsonKey(name: 'has_extended_info')
  final int hasExtendedInfo;
  
  /// 最后检查是否正常
  @JsonKey(name: 'last_check_ok')
  final int lastCheckOk;
  
  /// 最后变更时间
  @JsonKey(name: 'last_change_time')
  final String? lastChangeTime;
  
  /// 最后检查时间
  @JsonKey(name: 'last_check_time')
  final String? lastCheckTime;
  
  /// 最后检查正常时间
  @JsonKey(name: 'last_check_ok_time')
  final String? lastCheckOkTime;
  
  /// 最后本地检查时间
  @JsonKey(name: 'last_local_check_time')
  final String? lastLocalCheckTime;
  
  /// 最后点击时间
  @JsonKey(name: 'click_timestamp')
  final String? clickTimestamp;
  
  /// 状态：1-正常，0-禁用
  final int status;
  
  /// 创建时间
  @JsonKey(name: 'created_at')
  final String? createdAt;
  
  /// 更新时间
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const RadioStation({
    required this.id,
    required this.stationUuid,
    required this.serverUuid,
    required this.changeUuid,
    required this.name,
    required this.url,
    this.urlResolved,
    this.homepage,
    this.favicon,
    this.country,
    this.countryCode,
    this.iso31662,
    this.state,
    this.language,
    this.languageCodes,
    this.tags,
    this.codec,
    this.bitrate = 0,
    this.isHls = 0,
    this.votes = 0,
    this.clickCount = 0,
    this.clickTrend = 0,
    this.sslError = 0,
    this.geoLat,
    this.geoLong,
    this.geoDistance,
    this.hasExtendedInfo = 0,
    this.lastCheckOk = 0,
    this.lastChangeTime,
    this.lastCheckTime,
    this.lastCheckOkTime,
    this.lastLocalCheckTime,
    this.clickTimestamp,
    this.status = 1,
    this.createdAt,
    this.updatedAt,
  });

  factory RadioStation.fromJson(Map<String, dynamic> json) =>
      _$RadioStationFromJson(json);

  Map<String, dynamic> toJson() => _$RadioStationToJson(this);

  /// 获取实际播放URL
  String get playUrl => urlResolved?.isNotEmpty == true ? urlResolved! : url;
  
  /// 是否为HLS流
  bool get isHlsStream => isHls == 1;
  
  /// 是否可用
  bool get isAvailable => status == 1;
  
  /// 是否最后检查正常
  bool get isLastCheckOk => lastCheckOk == 1;
  
  /// 获取语言代码列表
  List<String> get languageCodeList {
    if (languageCodes == null || languageCodes!.isEmpty) return [];
    return languageCodes!.split(',').map((e) => e.trim()).toList();
  }
  
  /// 获取标签列表
  List<String> get tagList {
    if (tags == null || tags!.isEmpty) return [];
    return tags!.split(',').map((e) => e.trim()).toList();
  }
}

/// 电台列表响应模型
@JsonSerializable()
class RadioListResponse {
  /// 电台列表
  final List<RadioStation> list;
  
  /// 分页信息
  final PageInfo page;

  const RadioListResponse({
    required this.list,
    required this.page,
  });

  factory RadioListResponse.fromJson(Map<String, dynamic> json) =>
      _$RadioListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$RadioListResponseToJson(this);
}

/// 分页信息模型
@JsonSerializable()
class PageInfo {
  /// 当前页码
  final int page;
  
  /// 每页数量
  @JsonKey(name: 'page_size')
  final int pageSize;
  
  /// 总记录数
  final int total;

  const PageInfo({
    required this.page,
    required this.pageSize,
    required this.total,
  });

  factory PageInfo.fromJson(Map<String, dynamic> json) =>
      _$PageInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PageInfoToJson(this);
  
  /// 总页数
  int get totalPages => (total / pageSize).ceil();
  
  /// 是否有下一页
  bool get hasNextPage => page < totalPages;
  
  /// 是否有上一页
  bool get hasPreviousPage => page > 1;
}

/// 电台标签模型
@JsonSerializable()
class RadioTag {
  /// 标签ID
  final int id;
  
  /// 标签名称
  final String name;
  
  /// 排序权重
  @JsonKey(name: 'sort_order')
  final int sortOrder;
  
  /// 展示状态：1-展示，0-隐藏
  @JsonKey(name: 'is_show')
  final int isShow;
  
  /// 创建时间
  @JsonKey(name: 'created_at')
  final String? createdAt;
  
  /// 更新时间
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const RadioTag({
    required this.id,
    required this.name,
    required this.sortOrder,
    required this.isShow,
    this.createdAt,
    this.updatedAt,
  });

  factory RadioTag.fromJson(Map<String, dynamic> json) =>
      _$RadioTagFromJson(json);

  Map<String, dynamic> toJson() => _$RadioTagToJson(this);
  
  /// 是否显示
  bool get isVisible => isShow == 1;
}

/// 电台标签列表响应模型
@JsonSerializable()
class RadioTagListResponse {
  /// 标签列表
  final List<RadioTag> list;

  const RadioTagListResponse({
    required this.list,
  });

  factory RadioTagListResponse.fromJson(Map<String, dynamic> json) =>
      _$RadioTagListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$RadioTagListResponseToJson(this);
}

/// 国家信息模型
@JsonSerializable()
class Country {
  /// 国家ID
  final int id;
  
  /// 国家名称
  final String name;
  
  /// 国家代码
  final String code;
  
  /// ISO 3166-2代码
  @JsonKey(name: 'iso_3166_2')
  final String? iso31662;
  
  /// 排序权重
  @JsonKey(name: 'sort_order')
  final int sortOrder;
  
  /// 展示状态：1-展示，0-隐藏
  @JsonKey(name: 'is_show')
  final int isShow;
  
  /// 创建时间
  @JsonKey(name: 'created_at')
  final String? createdAt;
  
  /// 更新时间
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const Country({
    required this.id,
    required this.name,
    required this.code,
    this.iso31662,
    this.sortOrder = 0,
    this.isShow = 1,
    this.createdAt,
    this.updatedAt,
  });

  factory Country.fromJson(Map<String, dynamic> json) =>
      _$CountryFromJson(json);

  Map<String, dynamic> toJson() => _$CountryToJson(this);
  
  /// 是否显示
  bool get isVisible => isShow == 1;
}

/// 电台详情响应模型
@JsonSerializable()
class RadioDetailResponse {
  /// 电台基本信息
  @JsonKey(name: 'station_info')
  final RadioStation stationInfo;
  
  /// 关联标签列表
  final List<RadioTag> tags;
  
  /// 国家信息
  final Country country;

  const RadioDetailResponse({
    required this.stationInfo,
    required this.tags,
    required this.country,
  });

  factory RadioDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$RadioDetailResponseFromJson(json);

  Map<String, dynamic> toJson() => _$RadioDetailResponseToJson(this);
}

/// 电台列表请求参数模型
@JsonSerializable()
class RadioListRequest {
  /// 页码，从1开始
  final int page;
  
  /// 每页数量，最大100
  @JsonKey(name: 'page_size')
  final int pageSize;
  
  /// 国家ID筛选
  @JsonKey(name: 'country_id')
  final int? countryId;
  
  /// 省/州名称筛选（配合country_id使用）
  final String? state;
  
  /// 语言代码数组筛选，如 ["en", "zh"]
  @JsonKey(name: 'language_codes')
  final List<String>? languageCodes;
  
  /// 标签ID数组筛选
  @JsonKey(name: 'tag_ids')
  final List<int>? tagIds;
  
  /// 关键字搜索
  final String? keyword;
  
  /// 排序字段：votes, click_count, name, created_at
  @JsonKey(name: 'sort_by')
  final String? sortBy;
  
  /// 排序方式：asc, desc
  @JsonKey(name: 'sort_order')
  final String? sortOrder;

  const RadioListRequest({
    required this.page,
    required this.pageSize,
    this.countryId,
    this.state,
    this.languageCodes,
    this.tagIds,
    this.keyword,
    this.sortBy,
    this.sortOrder,
  });

  factory RadioListRequest.fromJson(Map<String, dynamic> json) =>
      _$RadioListRequestFromJson(json);

  Map<String, dynamic> toJson() {
    final json = _$RadioListRequestToJson(this);

    // print('💥💥💥 RadioListRequest.toJson() 调试');
    // print('💥 原始JSON (移除null值前): $json');
    // print('💥 state字段值: ${json['state']} ${json['state'] == null ? '(null-查询所有地区)' : '(指定地区)'}');

    // 移除null值，避免后端处理问题
    json.removeWhere((key, value) => value == null);

    // print('💥 最终JSON (移除null值后): $json');
    // print('💥 ⚠️ 重要：${json.containsKey('state') ? 'state字段已包含在请求中' : 'state字段被移除(因为是null)'}');

    return json;
  }
}

/// 省/州信息模型
@JsonSerializable()
class CountryState {
  /// 省/州名称
  @JsonKey(name: 'state_name')
  final String stateName;
  
  /// 该省/州的电台数量
  @JsonKey(name: 'station_count')
  final int stationCount;

  const CountryState({
    required this.stateName,
    required this.stationCount,
  });

  factory CountryState.fromJson(Map<String, dynamic> json) {
    return CountryState(
      stateName: json['state_name'] as String,
      stationCount: json['station_count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state_name': stateName,
      'station_count': stationCount,
    };
  }
  
  /// 是否有电台（用于筛选）
  bool get hasStations => stationCount > 0;
  
  /// 显示名称（包含电台数量）
  String get displayName => '$stateName ($stationCount)';
}

/// 国家省州列表响应模型
@JsonSerializable()
class CountryStatesResponse {
  /// 国家代码
  @JsonKey(name: 'country_code')
  final String countryCode;
  
  /// 国家名称
  @JsonKey(name: 'country_name')
  final String countryName;
  
  /// 省/州列表
  @JsonKey(name: 'list')
  final List<CountryState> states;
  
  /// 该国家总电台数
  @JsonKey(name: 'total_stations')
  final int totalStations;

  const CountryStatesResponse({
    required this.countryCode,
    required this.countryName,
    required this.states,
    required this.totalStations,
  });

  factory CountryStatesResponse.fromJson(Map<String, dynamic> json) {
    return CountryStatesResponse(
      countryCode: json['country_code'] as String,
      countryName: json['country_name'] as String,
      states: (json['list'] as List<dynamic>)
          .map((item) => CountryState.fromJson(item as Map<String, dynamic>))
          .toList(),
      totalStations: json['total_stations'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'country_code': countryCode,
      'country_name': countryName,
      'list': states.map((state) => state.toJson()).toList(),
      'total_stations': totalStations,
    };
  }
  
  /// 获取有电台的省州列表（过滤掉电台数量为0的）
  List<CountryState> get availableStates => 
      states.where((state) => state.hasStations).toList();
}
