// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'radio_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RadioStation _$RadioStationFromJson(Map<String, dynamic> json) => RadioStation(
      id: json['id'] as int,
      stationUuid: json['station_uuid'] as String,
      serverUuid: json['server_uuid'] as String,
      changeUuid: json['change_uuid'] as String,
      name: json['name'] as String,
      url: json['url'] as String,
      urlResolved: json['url_resolved'] as String?,
      homepage: json['homepage'] as String?,
      favicon: json['favicon'] as String?,
      country: json['country'] as String?,
      countryCode: json['country_code'] as String?,
      iso31662: json['iso_3166_2'] as String?,
      state: json['state'] as String?,
      language: json['language'] as String?,
      languageCodes: json['language_codes'] as String?,
      tags: json['tags'] as String?,
      codec: json['codec'] as String?,
      bitrate: (json['bitrate'] as num?)?.toInt() ?? 0,
      isHls: (json['is_hls'] as num?)?.toInt() ?? 0,
      votes: (json['votes'] as num?)?.toInt() ?? 0,
      clickCount: (json['click_count'] as num?)?.toInt() ?? 0,
      clickTrend: (json['click_trend'] as num?)?.toInt() ?? 0,
      sslError: (json['ssl_error'] as num?)?.toInt() ?? 0,
      geoLat: (json['geo_lat'] as num?)?.toDouble(),
      geoLong: (json['geo_long'] as num?)?.toDouble(),
      geoDistance: (json['geo_distance'] as num?)?.toDouble(),
      hasExtendedInfo: (json['has_extended_info'] as num?)?.toInt() ?? 0,
      lastCheckOk: (json['last_check_ok'] as num?)?.toInt() ?? 0,
      lastChangeTime: json['last_change_time'] as String?,
      lastCheckTime: json['last_check_time'] as String?,
      lastCheckOkTime: json['last_check_ok_time'] as String?,
      lastLocalCheckTime: json['last_local_check_time'] as String?,
      clickTimestamp: json['click_timestamp'] as String?,
      status: (json['status'] as num?)?.toInt() ?? 1,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$RadioStationToJson(RadioStation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'station_uuid': instance.stationUuid,
      'server_uuid': instance.serverUuid,
      'change_uuid': instance.changeUuid,
      'name': instance.name,
      'url': instance.url,
      'url_resolved': instance.urlResolved,
      'homepage': instance.homepage,
      'favicon': instance.favicon,
      'country': instance.country,
      'country_code': instance.countryCode,
      'iso_3166_2': instance.iso31662,
      'state': instance.state,
      'language': instance.language,
      'language_codes': instance.languageCodes,
      'tags': instance.tags,
      'codec': instance.codec,
      'bitrate': instance.bitrate,
      'is_hls': instance.isHls,
      'votes': instance.votes,
      'click_count': instance.clickCount,
      'click_trend': instance.clickTrend,
      'ssl_error': instance.sslError,
      'geo_lat': instance.geoLat,
      'geo_long': instance.geoLong,
      'geo_distance': instance.geoDistance,
      'has_extended_info': instance.hasExtendedInfo,
      'last_check_ok': instance.lastCheckOk,
      'last_change_time': instance.lastChangeTime,
      'last_check_time': instance.lastCheckTime,
      'last_check_ok_time': instance.lastCheckOkTime,
      'last_local_check_time': instance.lastLocalCheckTime,
      'click_timestamp': instance.clickTimestamp,
      'status': instance.status,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

RadioListResponse _$RadioListResponseFromJson(Map<String, dynamic> json) =>
    RadioListResponse(
      list: (json['list'] as List<dynamic>)
          .map((e) => RadioStation.fromJson(e as Map<String, dynamic>))
          .toList(),
      page: PageInfo.fromJson(json['page'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RadioListResponseToJson(RadioListResponse instance) =>
    <String, dynamic>{
      'list': instance.list,
      'page': instance.page,
    };

PageInfo _$PageInfoFromJson(Map<String, dynamic> json) => PageInfo(
      page: json['page'] as int,
      pageSize: json['page_size'] as int,
      total: json['total'] as int,
    );

Map<String, dynamic> _$PageInfoToJson(PageInfo instance) => <String, dynamic>{
      'page': instance.page,
      'page_size': instance.pageSize,
      'total': instance.total,
    };

RadioTag _$RadioTagFromJson(Map<String, dynamic> json) => RadioTag(
      id: json['id'] as int,
      name: json['name'] as String,
      sortOrder: (json['sort_order'] as num?)?.toInt() ?? 0,
      isShow: (json['is_show'] as num?)?.toInt() ?? 1,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$RadioTagToJson(RadioTag instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'sort_order': instance.sortOrder,
      'is_show': instance.isShow,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

RadioTagListResponse _$RadioTagListResponseFromJson(
        Map<String, dynamic> json) =>
    RadioTagListResponse(
      list: (json['list'] as List<dynamic>)
          .map((e) => RadioTag.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RadioTagListResponseToJson(
        RadioTagListResponse instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

Country _$CountryFromJson(Map<String, dynamic> json) => Country(
      id: json['id'] as int,
      name: json['name'] as String,
      code: json['code'] as String,
      iso31662: json['iso_3166_2'] as String?,
      sortOrder: (json['sort_order'] as num?)?.toInt() ?? 0,
      isShow: (json['is_show'] as num?)?.toInt() ?? 1,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$CountryToJson(Country instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'iso_3166_2': instance.iso31662,
      'sort_order': instance.sortOrder,
      'is_show': instance.isShow,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

RadioDetailResponse _$RadioDetailResponseFromJson(Map<String, dynamic> json) =>
    RadioDetailResponse(
      stationInfo: RadioStation.fromJson(json['station_info'] as Map<String, dynamic>),
      tags: (json['tags'] as List<dynamic>)
          .map((e) => RadioTag.fromJson(e as Map<String, dynamic>))
          .toList(),
      country: Country.fromJson(json['country'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$RadioDetailResponseToJson(RadioDetailResponse instance) =>
    <String, dynamic>{
      'station_info': instance.stationInfo,
      'tags': instance.tags,
      'country': instance.country,
    };

RadioListRequest _$RadioListRequestFromJson(Map<String, dynamic> json) =>
    RadioListRequest(
      page: json['page'] as int,
      pageSize: json['page_size'] as int,
      countryId: json['country_id'] as int?,
      state: json['state'] as String?,
      languageCodes: (json['language_codes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      tagIds: (json['tag_ids'] as List<dynamic>?)?.map((e) => e as int).toList(),
      keyword: json['keyword'] as String?,
      sortBy: json['sort_by'] as String?,
      sortOrder: json['sort_order'] as String?,
    );

Map<String, dynamic> _$RadioListRequestToJson(RadioListRequest instance) =>
    <String, dynamic>{
      'page': instance.page,
      'page_size': instance.pageSize,
      'country_id': instance.countryId,
      'state': instance.state,
      'language_codes': instance.languageCodes,
      'tag_ids': instance.tagIds,
      'keyword': instance.keyword,
      'sort_by': instance.sortBy,
      'sort_order': instance.sortOrder,
    };
