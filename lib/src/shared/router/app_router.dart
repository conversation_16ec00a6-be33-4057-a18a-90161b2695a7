import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/splash/splash_page.dart';
import '../widgets/main_scaffold.dart';

/// 应用路由配置
class AppRouter {
  static const String splash = '/splash';
  static const String home = '/';
  static const String explore = '/explore';
  static const String library = '/library';
  // 注意：删除了player路由，现在使用Modal实现

  static final GoRouter router = GoRouter(
    initialLocation: splash,
    routes: [
      // 启动页面（独立路由）
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),
      
      // 主应用Shell路由 - 使用IndexedStack管理页面状态
      ShellRoute(
        builder: (context, state, child) {
          return MainScaffold(child: child);
        },
        routes: [
          // 首页
          GoRoute(
            path: home,
            name: 'home',
            builder: (context, state) => const SizedBox.shrink(),
          ),

          // 发现页
          GoRoute(
            path: explore,
            name: 'explore',  
            builder: (context, state) => const SizedBox.shrink(),
          ),

          // 收藏页
          GoRoute(
            path: library,
            name: 'library',
            builder: (context, state) => const SizedBox.shrink(),
          ),
        ],
      ),
    ],
  );
}

/// 底部导航栏项目
enum BottomNavItem {
  home(
    icon: Icons.home_outlined,
    activeIcon: Icons.home,
    label: 'home',
    route: AppRouter.home,
  ),
  explore(
    icon: Icons.explore_outlined,
    activeIcon: Icons.explore,
    label: 'explore',
    route: AppRouter.explore,
  ),
  library(
    icon: Icons.library_music_outlined,
    activeIcon: Icons.library_music,
    label: 'library',
    route: AppRouter.library,
  );

  const BottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.route,
  });

  final IconData icon;
  final IconData activeIcon;
  final String label;
  final String route;

  static List<BottomNavItem> get items => BottomNavItem.values;
}
