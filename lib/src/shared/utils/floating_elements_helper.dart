import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/audio_service.dart';

/// 悬浮元素辅助工具类
/// 
/// 用于计算底部悬浮元素（广告和播放器）的高度，
/// 供各个页面使用以预留足够的底部空间
class FloatingElementsHelper {
  
  /// 计算底部悬浮元素的总高度
  static double getFloatingElementsHeight(BuildContext context, WidgetRef ref) {
    final currentStation = ref.watch(currentStationProvider);
    final showMiniPlayer = currentStation != null;
    final mediaQuery = MediaQuery.of(context);
    
    double totalHeight = 0;
    
    // GlobalBannerAd 高度检查 - 紧凑的60px高度
    // 只有在符合显示条件时才计算高度
    if (_shouldShowFloatingAd(context, showMiniPlayer ? 80.0 : 0.0)) {
      totalHeight += 60 + 16; // 广告高度60px + 上下边距8*2=16px
    }
    
    // MiniPlayer 高度
    if (showMiniPlayer) {
      totalHeight += 80 + 16; // MiniPlayer 高度 + 边距
    }
    
    // 底部安全区域
    totalHeight += mediaQuery.padding.bottom;
    
    // 额外的安全边距，确保内容不被遮挡
    totalHeight += 20;
    
    return totalHeight;
  }

  /// 检查是否应该显示悬浮广告
  static bool _shouldShowFloatingAd(BuildContext context, double bottomOffset) {
    final mediaQuery = MediaQuery.of(context);
    final screenSize = mediaQuery.size;
    final bottomInset = mediaQuery.viewInsets.bottom;
    final bottomPadding = mediaQuery.padding.bottom;
    final orientation = mediaQuery.orientation;

    // 1. 检查屏幕尺寸 - 最小高度要求
    final minHeightRequired = orientation == Orientation.portrait ? 600.0 : 400.0;
    if (screenSize.height < minHeightRequired) {
      return false;
    }

    // 2. 检查键盘状态 - 键盘弹出时隐藏
    if (bottomInset > 0) {
      return false;
    }

    // 3. 检查屏幕方向 - 横屏时更严格的限制
    if (orientation == Orientation.landscape) {
      // 横屏时检查宽度是否足够
      if (screenSize.width < 600) {
        return false;
      }
      
      // 横屏时减少悬浮元素高度要求
      final reservedSpace = 120 + bottomOffset + bottomPadding; // 横屏预留空间更少
      final availableContentSpace = screenSize.height - reservedSpace;
      if (availableContentSpace < 200) {
        return false;
      }
    } else {
      // 竖屏检查可用空间
      final reservedSpace = 200 + bottomOffset + bottomPadding + 100; // 预留导航栏空间
      final availableContentSpace = screenSize.height - reservedSpace;
      if (availableContentSpace < 400) {
        return false;
      }
    }

    // 4. 检查设备类型适配
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    if (devicePixelRatio > 3.0) {
      // 高分辨率设备，可能是小屏设备
      if (screenSize.height / devicePixelRatio < 200) {
        return false;
      }
    }

    // 5. 检查悬浮元素总高度限制
    final totalFloatingHeight = 60 + bottomOffset + 32; // 广告高度60px + 偏移 + 边距
    final maxFloatingRatio = orientation == Orientation.portrait ? 0.25 : 0.35; // 最大占用屏幕比例
    if (totalFloatingHeight > screenSize.height * maxFloatingRatio) {
      return false;
    }

    return true;
  }
}
