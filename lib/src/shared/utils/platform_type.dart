import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 平台类型枚举
enum PlatformType {
  android,
  ios,
  web,
  desktop,
}

/// 检测当前平台类型
PlatformType detectPlatformType() {
  if (kIsWeb) {
    return PlatformType.web;
  }
  
  if (Platform.isAndroid) {
    return PlatformType.android;
  }
  
  if (Platform.isIOS) {
    return PlatformType.ios;
  }
  
  return PlatformType.desktop;
}

/// 平台类型提供者
final platformTypeProvider = Provider<PlatformType>((ref) {
  return detectPlatformType();
}); 