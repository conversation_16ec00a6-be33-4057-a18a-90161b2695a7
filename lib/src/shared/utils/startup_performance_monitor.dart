import 'dart:developer' as developer;

/// 启动性能监控工具
class StartupPerformanceMonitor {
  static final StartupPerformanceMonitor _instance = StartupPerformanceMonitor._internal();
  factory StartupPerformanceMonitor() => _instance;
  StartupPerformanceMonitor._internal();

  final Map<String, DateTime> _timestamps = {};
  final Map<String, Duration> _durations = {};
  DateTime? _appStartTime;

  /// 记录应用启动时间
  void recordAppStart() {
    _appStartTime = DateTime.now();
    _timestamps['app_start'] = _appStartTime!;
    print('📊 应用启动时间记录: ${_appStartTime!.millisecondsSinceEpoch}');
  }

  /// 记录关键时间点
  void recordTimestamp(String event) {
    final now = DateTime.now();
    _timestamps[event] = now;
    
    if (_appStartTime != null) {
      final duration = now.difference(_appStartTime!);
      _durations[event] = duration;
      print('📊 性能指标 [$event]: ${duration.inMilliseconds}ms');
    }
  }

  /// 记录两个时间点之间的持续时间
  void recordDuration(String startEvent, String endEvent) {
    final startTime = _timestamps[startEvent];
    final endTime = _timestamps[endEvent];
    
    if (startTime != null && endTime != null) {
      final duration = endTime.difference(startTime);
      _durations['${startEvent}_to_$endEvent'] = duration;
      print('📊 持续时间 [$startEvent → $endEvent]: ${duration.inMilliseconds}ms');
    }
  }

  /// 获取性能报告
  Map<String, dynamic> getPerformanceReport() {
    final report = <String, dynamic>{};
    
    if (_appStartTime != null) {
      report['app_start_time'] = _appStartTime!.millisecondsSinceEpoch;
      report['total_startup_time'] = DateTime.now().difference(_appStartTime!).inMilliseconds;
    }
    
    report['timestamps'] = _timestamps.map((key, value) => 
        MapEntry(key, value.millisecondsSinceEpoch));
    
    report['durations'] = _durations.map((key, value) => 
        MapEntry(key, value.inMilliseconds));
    
    return report;
  }

  /// 打印性能报告
  void printPerformanceReport() {
    print('📊 ===== 启动性能报告 =====');
    
    if (_appStartTime != null) {
      final totalTime = DateTime.now().difference(_appStartTime!);
      print('📊 总启动时间: ${totalTime.inMilliseconds}ms');
    }
    
    print('📊 关键时间点:');
    _durations.forEach((event, duration) {
      print('   - $event: ${duration.inMilliseconds}ms');
    });
    
    // 性能评估
    final totalMs = _durations['first_content_visible']?.inMilliseconds ?? 0;
    String performance;
    if (totalMs < 1000) {
      performance = '🚀 优秀 (< 1秒)';
    } else if (totalMs < 2000) {
      performance = '✅ 良好 (1-2秒)';
    } else if (totalMs < 3000) {
      performance = '⚠️ 一般 (2-3秒)';
    } else {
      performance = '❌ 需要优化 (> 3秒)';
    }
    
    print('📊 性能评级: $performance');
    print('📊 ========================');
  }

  /// 发送性能数据到分析服务（可选）
  void sendPerformanceData() {
    final report = getPerformanceReport();
    
    // 这里可以集成Firebase Analytics、Sentry等服务
    developer.log('Performance Report', name: 'StartupPerformance', error: report);
  }

  /// 重置监控数据
  void reset() {
    _timestamps.clear();
    _durations.clear();
    _appStartTime = null;
  }

  /// 获取特定事件的持续时间
  Duration? getDuration(String event) {
    return _durations[event];
  }

  /// 检查是否达到性能目标
  bool isPerformanceTargetMet({int targetMs = 1000}) {
    final firstContentVisible = _durations['first_content_visible'];
    if (firstContentVisible == null) return false;
    
    return firstContentVisible.inMilliseconds <= targetMs;
  }
}

/// 性能监控的便捷方法
class PerformanceTracker {
  static final _monitor = StartupPerformanceMonitor();
  
  static void start() => _monitor.recordAppStart();
  static void mark(String event) => _monitor.recordTimestamp(event);
  static void report() => _monitor.printPerformanceReport();
  static bool isTargetMet({int targetMs = 1000}) => 
      _monitor.isPerformanceTargetMet(targetMs: targetMs);
}
