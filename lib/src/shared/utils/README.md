# 工具类模块 (Utils)

本模块包含了 World Tune 应用中使用的各种工具类和辅助函数。

## 文件结构

```
utils/
├── README.md                    # 本文档
├── xor_crypto.dart             # XOR 加密解密工具
├── radio_data_adapter.dart     # 电台数据适配器
└── text_overflow_handler.dart  # 文本溢出处理工具
```

## 工具类说明

### XorCrypto (xor_crypto.dart)

XOR 加密解密工具类，用于与后端 API 的安全通信。

#### 主要功能

- **数据加密**: 将字符串或 JSON 数据进行 XOR 加密
- **数据解密**: 解密 Base64 编码的加密数据
- **请求封装**: 创建加密的 API 请求体
- **响应解析**: 解析加密的 API 响应数据

#### 使用示例

```dart
// 加密 JSON 数据
final params = {'page': 1, 'page_size': 20};
final encrypted = XorCrypto.encodeJson(params);

// 解密响应数据
final response = {'header': {...}, 'data': 'encrypted_string'};
final decrypted = XorCrypto.parseEncryptedResponse(response);

// 创建加密请求
final request = XorCrypto.createEncryptedRequest(params);
```

#### 技术细节

- **加密算法**: XOR 异或运算
- **编码方式**: Base64 编码
- **密钥**: `gExXgO8x0OhkwHSV`
- **字符编码**: UTF-8

### RadioDataAdapter (radio_data_adapter.dart)

电台数据适配器，用于在 API 数据模型和 UI 数据模型之间进行转换。

#### 主要功能

- **模型转换**: RadioStation ↔ StationSimple
- **列表转换**: 批量转换电台列表
- **分类转换**: RadioTag → StationCategory
- **数据格式化**: 投票数、点击数等数据的格式化显示

#### 使用示例

```dart
// 转换单个电台
final stationSimple = RadioDataAdapter.radioStationToStationSimple(radioStation);

// 转换电台列表
final stationList = RadioDataAdapter.radioStationListToStationSimpleList(radioStations);

// 格式化显示数据
final votesText = RadioDataAdapter.formatVotes(1500); // "1.5K"
final playsText = RadioDataAdapter.formatClickCount(5000); // "5.0K plays"
```

#### 转换规则

- **ID 映射**: station_uuid → id
- **URL 处理**: 优先使用 url_resolved，回退到 url
- **标签提取**: 从编码格式、比特率等信息生成标签
- **国家/语言**: 通过 ID 映射到名称（可扩展为 API 查询）

### TextOverflowHandler (text_overflow_handler.dart)

文本溢出处理工具，确保 UI 中的文本显示不会导致溢出错误。

#### 主要功能

- **安全文本**: 创建带溢出处理的 Text 组件
- **长度限制**: 限制文本最大长度
- **省略号处理**: 自动添加省略号
- **多行支持**: 支持多行文本的溢出处理

#### 使用示例

```dart
// 安全显示文本
TextOverflowHandler.safeText(
  'Very long station name that might overflow',
  maxLines: 1,
  style: TextStyle(fontSize: 16),
)

// 限制文本长度
final shortText = TextOverflowHandler.limitText(longText, maxLength: 50);
```

## 设计原则

### 1. 单一职责

每个工具类都有明确的职责范围：
- XorCrypto 专注于加密解密
- RadioDataAdapter 专注于数据转换
- TextOverflowHandler 专注于文本处理

### 2. 静态方法

大部分工具方法都是静态的，便于直接调用，无需实例化。

### 3. 错误处理

所有工具类都包含适当的错误处理：
- 参数验证
- 异常捕获
- 友好的错误信息

### 4. 可扩展性

工具类设计考虑了未来的扩展需求：
- 支持自定义密钥
- 支持多种数据格式
- 支持配置化参数

## 最佳实践

### 1. 加密安全

```dart
// ✅ 正确：使用统一的加密工具
final encrypted = XorCrypto.encodeJson(data);

// ❌ 错误：直接发送明文数据
final response = await dio.post('/api', data: data);
```

### 2. 数据转换

```dart
// ✅ 正确：使用适配器转换
final stations = RadioDataAdapter.radioStationListToStationSimpleList(apiData);

// ❌ 错误：手动转换数据
final stations = apiData.map((item) => StationSimple(...)).toList();
```

### 3. 文本处理

```dart
// ✅ 正确：使用安全文本组件
TextOverflowHandler.safeText(station.name, maxLines: 1)

// ❌ 错误：直接使用可能溢出的文本
Text(station.name)
```

## 性能考虑

### 1. 缓存机制

- 国家/语言映射表使用静态缓存
- 避免重复的数据转换操作

### 2. 内存管理

- 及时释放大型数据对象
- 使用 const 构造函数减少内存分配

### 3. 计算优化

- 批量操作优于单个操作
- 延迟计算非必要数据

## 测试策略

### 1. 单元测试

每个工具类都应该有对应的单元测试：

```dart
// test/utils/xor_crypto_test.dart
test('should encrypt and decrypt data correctly', () {
  final original = 'test data';
  final encrypted = XorCrypto.encodeData(original);
  final decrypted = XorCrypto.decodeData(encrypted);
  expect(decrypted, equals(original));
});
```

### 2. 集成测试

测试工具类在实际场景中的使用：

```dart
// test/integration/api_integration_test.dart
test('should handle encrypted API communication', () async {
  final request = XorCrypto.createEncryptedRequest({'test': 'data'});
  final response = await apiService.post('/test', request);
  final data = XorCrypto.parseEncryptedResponse(response);
  expect(data, isNotNull);
});
```

## 维护指南

### 1. 版本兼容性

- 保持向后兼容性
- 废弃方法使用 @deprecated 注解
- 提供迁移指南

### 2. 文档更新

- 及时更新 README 文档
- 保持代码注释的准确性
- 提供使用示例

### 3. 代码质量

- 遵循 Dart 编码规范
- 使用静态分析工具
- 定期重构优化代码

## 常见问题

### Q: 加密解密性能如何？

A: XOR 加密是对称加密中最快的算法之一，性能开销很小。

### Q: 如何添加新的数据转换规则？

A: 在 RadioDataAdapter 中添加新的静态方法，遵循现有的命名规范。

### Q: 文本溢出处理会影响性能吗？

A: TextOverflowHandler 使用 Flutter 内置的文本处理机制，性能影响微乎其微。

### Q: 如何扩展支持其他加密算法？

A: 可以创建新的加密工具类，或在 XorCrypto 中添加算法选择参数。
