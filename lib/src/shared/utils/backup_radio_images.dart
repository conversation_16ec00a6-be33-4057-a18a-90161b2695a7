import 'dart:math';

/// 备用电台图片管理器
/// 用于当电台的favicon加载失败时提供本地备用图片
class BackupRadioImages {
  /// 备用电台图片列表
  static const List<String> _backupImages = [
    'assets/radio_imgs/radio-520.png',
    'assets/radio_imgs/radio-536.png',
    'assets/radio_imgs/radio-537.png',
    'assets/radio_imgs/radio-558.png',
    'assets/radio_imgs/radio-631.png',
    'assets/radio_imgs/radio-645.png',
    'assets/radio_imgs/radio-652.png',
    'assets/radio_imgs/radio-658.png',
    'assets/radio_imgs/radio-703.png',
    'assets/radio_imgs/radio-710.png',
    'assets/radio_imgs/radio-713.png',
    'assets/radio_imgs/radio-723.png',
    'assets/radio_imgs/radio-843.png',
    'assets/radio_imgs/radio-855.png',
    'assets/radio_imgs/radio-905.png',
    'assets/radio_imgs/radio-936.png',
  ];

  /// 获取随机备用图片
  /// 
  /// [stationId] 电台ID，用作随机种子确保同一电台总是显示相同的备用图片
  static String getRandomBackupImage([String? stationId]) {
    if (stationId != null && stationId.isNotEmpty) {
      // 使用电台ID作为种子，确保同一电台总是显示相同的备用图片
      final seed = stationId.hashCode.abs();
      final index = seed % _backupImages.length;
      return _backupImages[index];
    } else {
      // 如果没有电台ID，则完全随机选择
      final random = Random();
      final index = random.nextInt(_backupImages.length);
      return _backupImages[index];
    }
  }

  /// 获取所有备用图片列表
  static List<String> getAllBackupImages() {
    return List.from(_backupImages);
  }

  /// 获取备用图片数量
  static int getBackupImageCount() {
    return _backupImages.length;
  }

  /// 根据索引获取备用图片
  static String getBackupImageByIndex(int index) {
    if (index >= 0 && index < _backupImages.length) {
      return _backupImages[index];
    }
    // 如果索引越界，返回第一张图片
    return _backupImages.first;
  }
} 