import 'dart:async';
import 'dart:io';

/// 图片URL验证器 - 统一处理网络超时问题
/// 
/// 功能实现：3秒超时检测，异常端口过滤，问题域名黑名单
/// 使用场景：StationImageBuilder和AudioHandler的统一图片验证
/// 实现日期：2025-01-27
class ImageUrlValidator {
  /// 问题域名黑名单
  static final Set<String> _problematicDomains = {
    'i.iheart.com',
    'external-content.duckduckgo.com', 
    'www.radiotaiwan.tw', // 该域名经常使用异常端口，导致超时
    'radiotaiwan.tw', // 包含子域名变体
  };

  /// 失败URL缓存 - 避免重复尝试已知失败的URL
  static final Set<String> _failedUrls = <String>{};
  static DateTime? _lastCacheCleanup;

  /// 正常端口列表
  static final Set<int> _normalPorts = {80, 443, 8080, 8000, 3000, 8443};

  /// 验证图片URL是否安全可用
  /// 
  /// [url] - 要验证的图片URL
  /// [useCache] - 是否使用失败缓存（默认true）
  /// [enableTimeout] - 是否启用3秒超时检测（默认true）
  /// 
  /// 返回：true表示URL可以安全使用，false表示应该使用备用图片
  static Future<bool> isUrlSafe(
    String? url, {
    bool useCache = true,
    bool enableTimeout = true,
  }) async {
    // 基础检查
    if (!_isValidUrl(url)) {
      print('⚠️ ImageUrlValidator: URL格式无效 - $url');
      return false;
    }

    // 检查失败缓存
    if (useCache && _isInFailedCache(url!)) {
      print('⚠️ ImageUrlValidator: URL在失败缓存中，跳过 - $url');
      return false;
    }

    // 检查问题域名和异常端口
    if (_hasKnownIssues(url!)) {
      print('⚠️ ImageUrlValidator: URL有已知问题，跳过 - $url');
      _addToFailedCache(url);
      return false;
    }

    // 3秒超时检测
    if (enableTimeout) {
      final isReachable = await _checkUrlReachability(url);
      if (!isReachable) {
        print('⚠️ ImageUrlValidator: URL不可达或超时，跳过 - $url');
        _addToFailedCache(url);
        return false;
      }
    }

    print('✅ ImageUrlValidator: URL验证通过 - $url');
    return true;
  }

  /// 基础URL格式验证
  static bool _isValidUrl(String? url) {
    if (url == null || url.isEmpty || url.trim().isEmpty) {
      return false;
    }
    
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return false;
    }

    final uri = Uri.tryParse(url);
    return uri != null;
  }

  /// 检查URL是否有已知问题
  static bool _hasKnownIssues(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return true;

    // 检查问题域名
    if (_problematicDomains.contains(uri.host)) {
      return true;
    }

    // 检查异常端口
    if (uri.port > 0 && !_normalPorts.contains(uri.port)) {
      return true;
    }

    return false;
  }

  /// 检查URL是否有已知问题（公共方法）
  static bool hasKnownIssues(String url) {
    return _hasKnownIssues(url);
  }

  /// 3秒超时检测URL可达性
  static Future<bool> _checkUrlReachability(String url) async {
    try {
      final uri = Uri.parse(url);
      
      // 创建HTTP客户端
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 3);
      client.idleTimeout = const Duration(seconds: 3);

      // 发送HEAD请求检测可达性
      final request = await client.headUrl(uri).timeout(
        const Duration(seconds: 3),
        onTimeout: () {
          print('⏱️ ImageUrlValidator: 3秒超时 - $url');
          throw TimeoutException('URL检测超时', const Duration(seconds: 3));
        },
      );

      // 设置用户代理，避免被服务器拒绝
      request.headers.set('User-Agent', 'WorldTune/1.0');
      
      final response = await request.close();
      client.close();

      // 检查响应状态
      final isSuccess = response.statusCode >= 200 && response.statusCode < 400;
      print('🌐 ImageUrlValidator: URL检测结果 - $url, 状态码: ${response.statusCode}');
      
      return isSuccess;
    } on TimeoutException {
      print('⏱️ ImageUrlValidator: 连接超时 - $url');
      return false;
    } on SocketException catch (e) {
      print('🔌 ImageUrlValidator: 网络连接失败 - $url, 错误: $e');
      return false;
    } catch (e) {
      print('❌ ImageUrlValidator: 未知错误 - $url, 错误: $e');
      return false;
    }
  }

  /// 检查URL是否在失败缓存中
  static bool _isInFailedCache(String url) {
    _cleanupFailedCache();
    return _failedUrls.contains(url);
  }

  /// 检查URL是否在失败缓存中（公共方法）
  static bool isInFailedCache(String url) {
    return _isInFailedCache(url);
  }

  /// 添加URL到失败缓存
  static void _addToFailedCache(String url) {
    _failedUrls.add(url);
    print('📝 ImageUrlValidator: 添加到失败缓存 - $url');
  }

  /// 清理失败缓存（每小时清理一次）
  static void _cleanupFailedCache() {
    final now = DateTime.now();
    if (_lastCacheCleanup == null || 
        now.difference(_lastCacheCleanup!).inHours >= 1) {
      final oldSize = _failedUrls.length;
      _failedUrls.clear();
      _lastCacheCleanup = now;
      print('🧹 ImageUrlValidator: 清理失败缓存，删除${oldSize}个记录');
    }
  }

  /// 手动添加问题域名
  static void addProblematicDomain(String domain) {
    _problematicDomains.add(domain);
    print('⚠️ ImageUrlValidator: 添加问题域名 - $domain');
  }

  /// 手动添加失败URL
  static void addFailedUrl(String url) {
    _addToFailedCache(url);
  }

  /// 清空所有缓存（用于测试或重置）
  static void clearAllCache() {
    _failedUrls.clear();
    _lastCacheCleanup = null;
    print('🧹 ImageUrlValidator: 清空所有缓存');
  }

  /// 获取统计信息
  static Map<String, dynamic> getStats() {
    return {
      'problematicDomains': _problematicDomains.length,
      'failedUrls': _failedUrls.length,
      'lastCleanup': _lastCacheCleanup?.toIso8601String(),
    };
  }
}