import 'package:flutter/material.dart';

/// 通用文本溢出处理工具类
/// 
/// 提供一劳永逸的文本溢出解决方案，基于Flutter官方最佳实践
/// 支持多种布局场景和自适应处理
class TextOverflowHandler {
  /// 创建一个安全的文本组件，自动处理溢出
  /// 
  /// [text] - 要显示的文本
  /// [style] - 文本样式
  /// [maxLines] - 最大行数，默认为1
  /// [overflow] - 溢出处理方式，默认为省略号
  /// [textAlign] - 文本对齐方式
  /// [softWrap] - 是否软换行
  static Widget safeText(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    bool softWrap = true,
  }) {
    return Text(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      softWrap: softWrap,
    );
  }

  /// 创建一个在Row中安全的文本组件
  /// 
  /// 自动使用Expanded包装，防止Row溢出
  static Widget safeTextInRow(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    int flex = 1,
  }) {
    return Expanded(
      flex: flex,
      child: safeText(
        text,
        style: style,
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
      ),
    );
  }

  /// 创建一个在Column中安全的文本组件
  /// 
  /// 当Column在滚动视图中时，使用Flexible包装
  static Widget safeTextInColumn(
    String text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    FlexFit fit = FlexFit.loose,
  }) {
    return Flexible(
      fit: fit,
      child: safeText(
        text,
        style: style,
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
      ),
    );
  }

  /// 创建一个自适应的文本组件
  /// 
  /// 根据可用空间自动调整文本大小和行数
  static Widget adaptiveText(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    double? minFontSize,
    double? maxFontSize,
    TextAlign? textAlign,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final textStyle = style ?? Theme.of(context).textTheme.bodyMedium!;
        final fontSize = textStyle.fontSize ?? 14.0;
        
        // 根据约束调整字体大小
        double adjustedFontSize = fontSize;
        if (maxFontSize != null && fontSize > maxFontSize) {
          adjustedFontSize = maxFontSize;
        }
        if (minFontSize != null && fontSize < minFontSize) {
          adjustedFontSize = minFontSize;
        }
        
        return safeText(
          text,
          style: textStyle.copyWith(fontSize: adjustedFontSize),
          maxLines: maxLines,
          textAlign: textAlign,
        );
      },
    );
  }

  /// 创建一个带有渐变遮罩的文本组件
  /// 
  /// 当文本过长时，在末尾显示渐变遮罩效果
  static Widget gradientMaskText(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    TextAlign? textAlign,
    List<Color>? gradientColors,
  }) {
    return ShaderMask(
      shaderCallback: (bounds) {
        return LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: gradientColors ?? [
            Colors.black,
            Colors.black,
            Colors.transparent,
          ],
          stops: const [0.0, 0.8, 1.0],
        ).createShader(bounds);
      },
      blendMode: BlendMode.dstIn,
      child: safeText(
        text,
        style: style,
        maxLines: maxLines,
        textAlign: textAlign,
      ),
    );
  }

  /// 创建一个在容器中安全的文本组件
  /// 
  /// 自动处理容器约束，防止溢出
  static Widget safeTextInContainer(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    EdgeInsets? padding,
    double? width,
    double? height,
  }) {
    Widget textWidget = safeText(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
    );

    if (padding != null) {
      textWidget = Padding(
        padding: padding,
        child: textWidget,
      );
    }

    return Container(
      width: width,
      height: height,
      child: textWidget,
    );
  }

  /// 创建一个多行文本组件，自动处理高度约束
  /// 
  /// 适用于需要显示多行文本的场景
  static Widget multiLineText(
    String text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    double? maxHeight,
  }) {
    Widget textWidget = safeText(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      softWrap: true,
    );

    if (maxHeight != null) {
      textWidget = ConstrainedBox(
        constraints: BoxConstraints(maxHeight: maxHeight),
        child: textWidget,
      );
    }

    return textWidget;
  }

  /// 创建一个带有工具提示的文本组件
  /// 
  /// 当文本被截断时，长按可显示完整内容
  static Widget tooltipText(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    String? tooltipMessage,
  }) {
    return Tooltip(
      message: tooltipMessage ?? text,
      child: safeText(
        text,
        style: style,
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
      ),
    );
  }

  /// 创建一个响应式文本组件
  /// 
  /// 根据屏幕尺寸自动调整文本样式
  static Widget responsiveText(
    String text, {
    TextStyle? mobileStyle,
    TextStyle? tabletStyle,
    TextStyle? desktopStyle,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        TextStyle? style;
        
        if (constraints.maxWidth < 600) {
          style = mobileStyle ?? Theme.of(context).textTheme.bodyMedium;
        } else if (constraints.maxWidth < 1200) {
          style = tabletStyle ?? Theme.of(context).textTheme.bodyLarge;
        } else {
          style = desktopStyle ?? Theme.of(context).textTheme.headlineSmall;
        }

        return safeText(
          text,
          style: style,
          maxLines: maxLines,
          overflow: overflow,
          textAlign: textAlign,
        );
      },
    );
  }

  /// 创建一个在Flex布局中安全的文本组件
  ///
  /// 自动根据父组件类型选择合适的包装器
  static Widget safeTextInFlex(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    int flex = 1,
    FlexFit fit = FlexFit.tight,
  }) {
    return Flexible(
      flex: flex,
      fit: fit,
      child: safeText(
        text,
        style: style,
        maxLines: maxLines,
        overflow: overflow,
        textAlign: textAlign,
      ),
    );
  }

  /// 创建一个超级安全的文本组件，专门处理复杂布局溢出
  ///
  /// 使用多层保护机制，确保在任何情况下都不会溢出
  static Widget superSafeText(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    double? maxWidth,
    bool useIntrinsicWidth = false,
  }) {
    Widget textWidget = safeText(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
    );

    // 如果指定了最大宽度，使用ConstrainedBox
    if (maxWidth != null) {
      textWidget = ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: textWidget,
      );
    }

    // 如果需要内在宽度，使用IntrinsicWidth
    if (useIntrinsicWidth) {
      textWidget = IntrinsicWidth(child: textWidget);
    }

    // 最外层使用Flexible确保不会溢出父容器
    return Flexible(
      child: textWidget,
    );
  }

  /// 创建一个在复杂Row布局中绝对安全的文本组件
  ///
  /// 专门处理像电台列表项这样的复杂布局
  static Widget ultraSafeTextInRow(
    String text, {
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    int flex = 1,
    double? minWidth,
    double? maxWidth,
  }) {
    Widget textWidget = safeText(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
    );

    // 添加宽度约束
    if (minWidth != null || maxWidth != null) {
      textWidget = ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: minWidth ?? 0,
          maxWidth: maxWidth ?? double.infinity,
        ),
        child: textWidget,
      );
    }

    return Expanded(
      flex: flex,
      child: textWidget,
    );
  }
}

/// 文本溢出处理扩展
/// 
/// 为String类型添加便捷的溢出处理方法
extension TextOverflowExtension on String {
  /// 创建安全的文本组件
  Widget toSafeText({
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
  }) {
    return TextOverflowHandler.safeText(
      this,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
    );
  }

  /// 创建在Row中安全的文本组件
  Widget toSafeTextInRow({
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    int flex = 1,
  }) {
    return TextOverflowHandler.safeTextInRow(
      this,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      flex: flex,
    );
  }

  /// 创建带工具提示的文本组件
  Widget toTooltipText({
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    String? tooltipMessage,
  }) {
    return TextOverflowHandler.tooltipText(
      this,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      tooltipMessage: tooltipMessage,
    );
  }

  /// 创建超级安全的文本组件
  Widget toSuperSafeText({
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    double? maxWidth,
    bool useIntrinsicWidth = false,
  }) {
    return TextOverflowHandler.superSafeText(
      this,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      maxWidth: maxWidth,
      useIntrinsicWidth: useIntrinsicWidth,
    );
  }

  /// 创建在复杂Row布局中绝对安全的文本组件
  Widget toUltraSafeTextInRow({
    TextStyle? style,
    int? maxLines = 1,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
    int flex = 1,
    double? minWidth,
    double? maxWidth,
  }) {
    return TextOverflowHandler.ultraSafeTextInRow(
      this,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      flex: flex,
      minWidth: minWidth,
      maxWidth: maxWidth,
    );
  }
}
