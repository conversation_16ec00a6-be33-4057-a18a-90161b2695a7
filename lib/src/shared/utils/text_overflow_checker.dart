import 'package:flutter/material.dart';

/// 文本溢出检查和修复工具
/// 
/// 提供运行时检查和自动修复文本溢出问题的功能
class TextOverflowChecker {
  /// 检查是否存在潜在的文本溢出风险
  /// 
  /// 在开发模式下使用，帮助发现可能的溢出问题
  static Widget checkAndWrap(
    Widget child, {
    String? debugLabel,
    bool enableInRelease = false,
  }) {
    // 在发布模式下默认不启用检查（除非明确指定）
    if (!enableInRelease && !_isDebugMode()) {
      return child;
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return _OverflowDetector(
          constraints: constraints,
          debugLabel: debugLabel,
          child: child,
        );
      },
    );
  }

  /// 自动修复Text组件的溢出问题
  /// 
  /// 将普通的Text组件自动转换为安全的文本组件
  static Widget autoFix(Widget widget) {
    if (widget is Text) {
      return _convertTextToSafe(widget);
    }
    return widget;
  }

  /// 批量检查和修复一个Widget树中的所有Text组件
  /// 
  /// 递归遍历Widget树，自动修复所有可能的溢出问题
  static Widget autoFixTree(Widget widget) {
    return _AutoFixWrapper(child: widget);
  }

  /// 检查是否为调试模式
  static bool _isDebugMode() {
    bool isDebug = false;
    assert(() {
      isDebug = true;
      return true;
    }());
    return isDebug;
  }

  /// 将Text组件转换为安全的文本组件
  static Widget _convertTextToSafe(Text textWidget) {
    return Text(
      textWidget.data ?? '',
      style: textWidget.style,
      maxLines: textWidget.maxLines ?? 1,
      overflow: textWidget.overflow ?? TextOverflow.ellipsis,
      textAlign: textWidget.textAlign,
      softWrap: textWidget.softWrap ?? true,
      textDirection: textWidget.textDirection,
      locale: textWidget.locale,
      textScaleFactor: textWidget.textScaleFactor,
      semanticsLabel: textWidget.semanticsLabel,
      textWidthBasis: textWidget.textWidthBasis ?? TextWidthBasis.parent,
      textHeightBehavior: textWidget.textHeightBehavior,
    );
  }
}

/// 溢出检测器组件
class _OverflowDetector extends StatelessWidget {
  const _OverflowDetector({
    required this.constraints,
    required this.child,
    this.debugLabel,
  });

  final BoxConstraints constraints;
  final Widget child;
  final String? debugLabel;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        // 在调试模式下显示约束信息 - 已禁用以避免影响用户界面
        // if (_isDebugMode())
        //   Positioned(
        //     top: 0,
        //     right: 0,
        //     child: Container(
        //       padding: const EdgeInsets.all(2),
        //       decoration: BoxDecoration(
        //         color: Colors.red.withOpacity(0.7),
        //         borderRadius: BorderRadius.circular(2),
        //       ),
        //       child: Text(
        //         '${constraints.maxWidth.toInt()}x${constraints.maxHeight.toInt()}',
        //         style: const TextStyle(
        //           color: Colors.white,
        //           fontSize: 8,
        //         ),
        //       ),
        //     ),
        //   ),
      ],
    );
  }

  bool _isDebugMode() {
    bool isDebug = false;
    assert(() {
      isDebug = true;
      return true;
    }());
    return isDebug;
  }
}

/// 自动修复包装器
class _AutoFixWrapper extends StatelessWidget {
  const _AutoFixWrapper({required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return _processWidget(child);
  }

  Widget _processWidget(Widget widget) {
    // 如果是Text组件，自动修复
    if (widget is Text) {
      return TextOverflowChecker._convertTextToSafe(widget);
    }

    // 如果是容器类组件，递归处理子组件
    if (widget is SingleChildRenderObjectWidget) {
      final child = _getChild(widget);
      if (child != null) {
        return _cloneWithChild(widget, _processWidget(child));
      }
    }

    // 如果是多子组件，递归处理所有子组件
    if (widget is MultiChildRenderObjectWidget) {
      final children = _getChildren(widget);
      if (children != null) {
        final processedChildren = children.map(_processWidget).toList();
        return _cloneWithChildren(widget, processedChildren);
      }
    }

    return widget;
  }

  Widget? _getChild(SingleChildRenderObjectWidget widget) {
    // 这里需要根据具体的Widget类型来获取子组件
    // 由于Flutter的限制，我们无法直接访问私有字段
    // 在实际使用中，可以针对特定的Widget类型进行处理
    return null;
  }

  List<Widget>? _getChildren(MultiChildRenderObjectWidget widget) {
    // 同样，这里需要根据具体的Widget类型来获取子组件列表
    return null;
  }

  Widget _cloneWithChild(SingleChildRenderObjectWidget widget, Widget child) {
    // 根据Widget类型创建新的实例
    // 这里是一个简化的实现，实际使用中需要更完整的处理
    return widget;
  }

  Widget _cloneWithChildren(MultiChildRenderObjectWidget widget, List<Widget> children) {
    // 根据Widget类型创建新的实例
    return widget;
  }
}

/// 文本溢出修复扩展
extension TextOverflowFixExtension on Widget {
  /// 自动检查和修复溢出问题
  Widget autoFixOverflow({String? debugLabel}) {
    return TextOverflowChecker.checkAndWrap(
      TextOverflowChecker.autoFix(this),
      debugLabel: debugLabel,
    );
  }

  /// 递归修复整个Widget树的溢出问题
  Widget autoFixTreeOverflow() {
    return TextOverflowChecker.autoFixTree(this);
  }
}

/// 开发时的文本溢出调试工具
class TextOverflowDebugger {
  static bool _enabled = false;
  static final List<String> _overflowReports = [];

  /// 启用溢出调试
  static void enable() {
    _enabled = true;
  }

  /// 禁用溢出调试
  static void disable() {
    _enabled = false;
  }

  /// 报告溢出问题
  static void reportOverflow(String message) {
    if (_enabled) {
      _overflowReports.add(message);
      debugPrint('🚨 Text Overflow: $message');
    }
  }

  /// 获取所有溢出报告
  static List<String> getReports() {
    return List.from(_overflowReports);
  }

  /// 清除所有报告
  static void clearReports() {
    _overflowReports.clear();
  }

  /// 打印溢出统计
  static void printStatistics() {
    if (_enabled) {
      debugPrint('📊 Text Overflow Statistics:');
      debugPrint('Total reports: ${_overflowReports.length}');
      
      final groupedReports = <String, int>{};
      for (final report in _overflowReports) {
        groupedReports[report] = (groupedReports[report] ?? 0) + 1;
      }
      
      groupedReports.forEach((message, count) {
        debugPrint('  $count x $message');
      });
    }
  }
}
