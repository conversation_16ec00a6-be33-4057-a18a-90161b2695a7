import 'package:flutter/material.dart';
import '../models/models.dart';
import 'backup_radio_images.dart';
import 'image_url_validator.dart';

/// 电台图片构建工具类
/// 统一处理电台图片加载、备用图片显示和错误处理
class StationImageBuilder {
  /// 构建电台图片Widget
  /// 
  /// [station] 电台信息
  /// [width] 图片宽度
  /// [height] 图片高度
  /// [fit] 图片填充方式
  /// [borderRadius] 圆角半径
  static Widget buildStationImage({
    required StationSimple station,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    Widget imageWidget;

    // 使用统一的URL验证器进行快速检查
    if (_isUrlSafeToLoad(station.favicon)) {
      // 通过验证的URL，尝试加载网络图片，失败时使用备用图片
      imageWidget = Image.network(
        station.favicon,
        width: width,
        height: height,
        fit: fit,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) {
            return child;
          }
          // 在加载期间直接显示备用图片，避免长时间loading
          return _buildBackupImage(station, width, height, fit);
        },
        errorBuilder: (context, error, stackTrace) {
          // 图片加载失败，标记URL为失败并使用备用图片
          ImageUrlValidator.addFailedUrl(station.favicon);
          print('📷 电台${station.name}的favicon加载失败，已加入黑名单');
          return _buildBackupImage(station, width, height, fit);
        },
        headers: _getImageHeaders(station.favicon),
      );
    } else {
      // URL验证失败或在黑名单中，直接使用备用图片
      // print('🚫 电台${station.name}的favicon验证失败，使用备用图片: "${station.favicon}"');
      imageWidget = _buildBackupImage(station, width, height, fit);
    }

    // 如果指定了圆角，包装在ClipRRect中
    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// 构建备用图片
  static Widget _buildBackupImage(StationSimple station, double? width, double? height, BoxFit fit) {
    final backupImagePath = BackupRadioImages.getRandomBackupImage(station.id);
    return Image.asset(
      backupImagePath,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        // 如果连备用图片都加载失败，则使用渐变占位图
        print('⚠️ 备用图片${backupImagePath}也加载失败，使用占位图');
        return _buildPlaceholder(width, height);
      },
    );
  }

  /// 构建渐变占位图
  static Widget _buildPlaceholder(double? width, double? height) {
    // 随机生成不同的渐变色彩
    final colors = [
      [Colors.blue.shade400, Colors.blue.shade600, Colors.blue.shade800],
      [Colors.green.shade400, Colors.green.shade600, Colors.green.shade800],
      [Colors.purple.shade400, Colors.purple.shade600, Colors.purple.shade800],
      [Colors.orange.shade400, Colors.orange.shade600, Colors.orange.shade800],
      [Colors.teal.shade400, Colors.teal.shade600, Colors.teal.shade800],
      [Colors.indigo.shade400, Colors.indigo.shade600, Colors.indigo.shade800],
    ];
    
    final colorSet = colors[DateTime.now().millisecond % colors.length];
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: colorSet,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.radio,
              color: Colors.white.withOpacity(0.9),
              size: (width != null && width < 60) ? 20 : 28,
            ),
            if (width == null || width >= 60) ...[
              const SizedBox(height: 4),
              Text(
                'FM',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: (width != null && width < 60) ? 8 : 10,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 1.2,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 验证图片URL是否安全可以加载（同步版本）
  /// 
  /// 使用ImageUrlValidator进行快速同步检查：
  /// 1. 检查基础URL格式
  /// 2. 检查是否在失败缓存中
  /// 3. 检查问题域名和异常端口
  /// 
  /// 注意：不进行网络超时检测，因为Widget构建必须是同步的
  static bool _isUrlSafeToLoad(String? url) {
    if (url == null || url.isEmpty || url.trim().isEmpty) {
      return false;
    }
    
    // 检查URL格式
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return false;
    }
    
    final uri = Uri.tryParse(url);
    if (uri == null) {
      return false;
    }
    
    // 使用ImageUrlValidator的同步检查
    // 检查是否在失败缓存中
    if (ImageUrlValidator.isInFailedCache(url)) {
      return false;
    }
    
    // 检查是否有已知问题
    if (ImageUrlValidator.hasKnownIssues(url)) {
      return false;
    }
    
    return true;
  }

  /// 为复杂API图片URL生成合适的HTTP头
  static Map<String, String> _getImageHeaders(String url) {
    final headers = <String, String>{
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
      'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.9',
      'Cache-Control': 'max-age=31536000',
      'Connection': 'close', // 避免长连接导致的超时问题
      'Timeout': '10', // 建议服务器10秒超时
    };
    
    // 对于特定域名添加专门的头信息
    if (url.contains('iheart.com')) {
      headers['Referer'] = 'https://www.iheart.com/';
      headers['Origin'] = 'https://www.iheart.com';
      // iHeartRadio可能需要特定的User-Agent
      headers['User-Agent'] = 'Mozilla/5.0 (compatible; WorldTuneApp/1.0; +https://worldtune.app)';
    }
    
    return headers;
  }
} 