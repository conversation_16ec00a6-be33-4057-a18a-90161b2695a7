import 'dart:convert';
import 'dart:typed_data';
import '../config/app_config.dart';

/// XOR加密解密工具类
/// 基于后端Go代码实现，用于API请求和响应的加密解密
class XorCrypto {
  /// 获取XOR加密密钥
  static String get _defaultKey => AppConfig.xorEncryptionKey;

  /// 对数据进行XOR加密
  /// 
  /// [data] 要加密的原始数据
  /// [key] 加密密钥，默认使用预设密钥
  /// 
  /// 返回Base64编码的加密字符串
  static String encodeData(String data, [String? key]) {
    if (data.isEmpty) return '';
    
    final encryptionKey = key ?? _defaultKey;
    if (encryptionKey.isEmpty) return '';

    // 将字符串转换为字节数组
    final inputBytes = utf8.encode(data);
    final keyBytes = utf8.encode(encryptionKey);
    
    // 执行XOR加密
    final encryptedBytes = Uint8List(inputBytes.length);
    for (int i = 0; i < inputBytes.length; i++) {
      encryptedBytes[i] = inputBytes[i] ^ keyBytes[i % keyBytes.length];
    }
    
    // 返回Base64编码的结果
    return base64.encode(encryptedBytes);
  }

  /// 对数据进行XOR解密
  /// 
  /// [encryptedData] Base64编码的加密数据
  /// [key] 解密密钥，默认使用预设密钥
  /// 
  /// 返回解密后的原始字符串
  /// 抛出异常如果参数为空或解密失败
  static String decodeData(String encryptedData, [String? key]) {
    if (encryptedData.isEmpty) {
      throw ArgumentError('加密数据不能为空');
    }
    
    final decryptionKey = key ?? _defaultKey;
    if (decryptionKey.isEmpty) {
      throw ArgumentError('解密密钥不能为空');
    }

    try {
      // Base64解码
      final encryptedBytes = base64.decode(encryptedData);
      final keyBytes = utf8.encode(decryptionKey);
      
      // 执行XOR解密
      final decryptedBytes = Uint8List(encryptedBytes.length);
      for (int i = 0; i < encryptedBytes.length; i++) {
        decryptedBytes[i] = encryptedBytes[i] ^ keyBytes[i % keyBytes.length];
      }
      
      // 返回UTF-8解码的字符串
      return utf8.decode(decryptedBytes);
    } catch (e) {
      throw FormatException('解密失败: $e');
    }
  }

  /// 加密JSON对象
  /// 
  /// [jsonData] 要加密的JSON对象
  /// [key] 加密密钥，默认使用预设密钥
  /// 
  /// 返回加密后的Base64字符串
  static String encodeJson(Map<String, dynamic> jsonData, [String? key]) {
    final jsonString = jsonEncode(jsonData);
    return encodeData(jsonString, key);
  }

  /// 解密JSON字符串
  /// 
  /// [encryptedData] 加密的Base64字符串
  /// [key] 解密密钥，默认使用预设密钥
  /// 
  /// 返回解密后的JSON对象
  static Map<String, dynamic> decodeJson(String encryptedData, [String? key]) {
    final decryptedString = decodeData(encryptedData, key);
    return jsonDecode(decryptedString) as Map<String, dynamic>;
  }

  /// 创建加密的请求体
  /// 
  /// [params] 请求参数
  /// [key] 加密密钥，默认使用预设密钥
  /// 
  /// 返回包含bsg字段的请求体
  static Map<String, dynamic> createEncryptedRequest(
    Map<String, dynamic> params, [
    String? key,
  ]) {
    final encryptedData = encodeJson(params, key);
    return {'bsg': encryptedData};
  }

  /// 解析加密的响应数据
  /// 
  /// [response] API响应数据
  /// [key] 解密密钥，默认使用预设密钥
  /// 
  /// 返回解密后的数据部分
  static Map<String, dynamic> parseEncryptedResponse(
    Map<String, dynamic> response, [
    String? key,
  ]) {
    // 检查响应格式
    if (!response.containsKey('header') || !response.containsKey('data')) {
      throw FormatException('响应格式错误：缺少header或data字段');
    }

    final header = response['header'] as Map<String, dynamic>;
    final code = header['code'] as int;
    
    // 检查响应状态
    if (code != 0) {
      final message = header['msg'] as String? ?? '未知错误';
      throw Exception('API错误 (code: $code): $message');
    }

    // 解密data字段
    final encryptedData = response['data'] as String;
    return decodeJson(encryptedData, key);
  }
}
