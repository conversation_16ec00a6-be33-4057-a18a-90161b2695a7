# 文本溢出处理工具 (TextOverflowHandler)

一劳永逸的Flutter文本溢出解决方案，基于Flutter官方最佳实践。

## 📋 功能概述

### 核心特性
- **自动溢出处理**: 智能处理各种布局场景下的文本溢出
- **多种布局支持**: Row、Column、Flex、Container等布局的专用方法
- **响应式设计**: 根据屏幕尺寸自动调整文本样式
- **性能优化**: 避免不必要的重建和布局计算
- **易于使用**: 简洁的API和扩展方法

### 解决的问题
- ✅ RenderFlex overflow错误
- ✅ 文本在Row中溢出
- ✅ 文本在Column中溢出
- ✅ 容器约束问题
- ✅ 响应式文本显示
- ✅ 多行文本处理

## 🚀 快速开始

### 基本用法

```dart
import '../utils/text_overflow_handler.dart';

// 基本安全文本
TextOverflowHandler.safeText(
  "这是一段可能很长的文本",
  style: TextStyle(fontSize: 16),
  maxLines: 1,
)

// 使用扩展方法
"这是一段文本".toSafeText(
  style: TextStyle(fontSize: 16),
  maxLines: 1,
)
```

### Row布局中的文本

```dart
Row(
  children: [
    Icon(Icons.star),
    SizedBox(width: 8),
    // 自动使用Expanded包装，防止溢出
    TextOverflowHandler.safeTextInRow(
      "这是一个很长的电台名称，可能会溢出",
      style: TextStyle(fontWeight: FontWeight.bold),
      maxLines: 1,
    ),
    IconButton(onPressed: () {}, icon: Icon(Icons.play_arrow)),
  ],
)
```

### Column布局中的文本

```dart
Column(
  children: [
    Text("标题"),
    // 在滚动视图中使用Flexible
    TextOverflowHandler.safeTextInColumn(
      "这是一段描述文本，可能会很长",
      maxLines: 3,
      fit: FlexFit.loose,
    ),
  ],
)
```

## 🛠️ API参考

### 核心方法

#### `safeText()`
创建一个安全的文本组件，自动处理溢出。

```dart
static Widget safeText(
  String text, {
  TextStyle? style,
  int? maxLines = 1,
  TextOverflow overflow = TextOverflow.ellipsis,
  TextAlign? textAlign,
  bool softWrap = true,
})
```

#### `safeTextInRow()`
在Row中安全显示文本，自动使用Expanded包装。

```dart
static Widget safeTextInRow(
  String text, {
  TextStyle? style,
  int? maxLines = 1,
  TextOverflow overflow = TextOverflow.ellipsis,
  TextAlign? textAlign,
  int flex = 1,
})
```

#### `safeTextInColumn()`
在Column中安全显示文本，使用Flexible包装。

```dart
static Widget safeTextInColumn(
  String text, {
  TextStyle? style,
  int? maxLines,
  TextOverflow overflow = TextOverflow.ellipsis,
  TextAlign? textAlign,
  FlexFit fit = FlexFit.loose,
})
```

### 高级方法

#### `adaptiveText()`
根据可用空间自动调整文本大小。

```dart
static Widget adaptiveText(
  String text, {
  TextStyle? style,
  int? maxLines = 1,
  double? minFontSize,
  double? maxFontSize,
  TextAlign? textAlign,
})
```

#### `responsiveText()`
根据屏幕尺寸自动调整文本样式。

```dart
static Widget responsiveText(
  String text, {
  TextStyle? mobileStyle,
  TextStyle? tabletStyle,
  TextStyle? desktopStyle,
  int? maxLines = 1,
  TextOverflow overflow = TextOverflow.ellipsis,
  TextAlign? textAlign,
})
```

#### `tooltipText()`
带有工具提示的文本，长按显示完整内容。

```dart
static Widget tooltipText(
  String text, {
  TextStyle? style,
  int? maxLines = 1,
  TextOverflow overflow = TextOverflow.ellipsis,
  TextAlign? textAlign,
  String? tooltipMessage,
})
```

#### `gradientMaskText()`
带有渐变遮罩效果的文本。

```dart
static Widget gradientMaskText(
  String text, {
  TextStyle? style,
  int? maxLines = 1,
  TextAlign? textAlign,
  List<Color>? gradientColors,
})
```

## 📱 实际应用示例

### MiniPlayer中的应用

```dart
// 电台名称
TextOverflowHandler.safeText(
  station.name,
  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
    fontWeight: FontWeight.w600,
    fontSize: 15,
  ),
  maxLines: 1,
),

// 状态文本在Row中
Row(
  children: [
    Icon(Icons.radio, size: 14),
    SizedBox(width: 4),
    TextOverflowHandler.safeTextInRow(
      "正在播放...",
      style: TextStyle(color: Colors.green),
      maxLines: 1,
    ),
    AudioVisualizer(...),
  ],
)
```

### StationCard中的应用

```dart
Column(
  crossAxisAlignment: CrossAxisAlignment.start,
  children: [
    TextOverflowHandler.safeText(
      station.name,
      style: TextStyle(fontWeight: FontWeight.w600),
      maxLines: 2,
    ),
    SizedBox(height: 4),
    TextOverflowHandler.safeText(
      station.country,
      style: TextStyle(color: Colors.grey[600]),
      maxLines: 1,
    ),
  ],
)
```

### 响应式文本示例

```dart
TextOverflowHandler.responsiveText(
  "World Tune - 全球电台",
  mobileStyle: TextStyle(fontSize: 16),
  tabletStyle: TextStyle(fontSize: 20),
  desktopStyle: TextStyle(fontSize: 24),
  maxLines: 1,
)
```

## 🎯 最佳实践

### 1. 选择合适的方法
- **普通文本**: 使用 `safeText()`
- **Row中的文本**: 使用 `safeTextInRow()`
- **Column中的文本**: 使用 `safeTextInColumn()`
- **需要工具提示**: 使用 `tooltipText()`
- **响应式需求**: 使用 `responsiveText()`

### 2. 性能优化
```dart
// ✅ 推荐：指定maxLines
TextOverflowHandler.safeText(
  text,
  maxLines: 1, // 明确指定行数
)

// ❌ 避免：不指定maxLines可能影响性能
TextOverflowHandler.safeText(text)
```

### 3. 布局约束
```dart
// ✅ 推荐：在Row中使用专用方法
Row(
  children: [
    Icon(Icons.star),
    TextOverflowHandler.safeTextInRow(text), // 自动Expanded
    Icon(Icons.more),
  ],
)

// ❌ 避免：手动包装可能出错
Row(
  children: [
    Icon(Icons.star),
    Expanded(child: Text(text)), // 可能忘记overflow处理
    Icon(Icons.more),
  ],
)
```

### 4. 样式一致性
```dart
// ✅ 推荐：使用主题样式
TextOverflowHandler.safeText(
  text,
  style: Theme.of(context).textTheme.bodyMedium,
)

// ✅ 推荐：扩展主题样式
TextOverflowHandler.safeText(
  text,
  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
    fontWeight: FontWeight.bold,
  ),
)
```

## 🔧 扩展方法

为String类型提供便捷的扩展方法：

```dart
// 基本用法
"文本内容".toSafeText(maxLines: 1)

// Row中使用
"文本内容".toSafeTextInRow(flex: 2)

// 带工具提示
"文本内容".toTooltipText(tooltipMessage: "完整的文本内容")

// 超级安全文本（新增）
"文本内容".toSuperSafeText(maxWidth: 200)

// 复杂Row布局中的绝对安全文本（新增）
"文本内容".toUltraSafeTextInRow(flex: 2, maxWidth: 150)
```

## 🆕 新增功能

### 超级安全文本组件

针对复杂布局场景，新增了更强大的防溢出方法：

```dart
// 超级安全文本 - 多层保护机制
TextOverflowHandler.superSafeText(
  "这是一段可能在复杂布局中溢出的文本",
  maxWidth: 200,
  useIntrinsicWidth: true,
  maxLines: 1,
)

// 复杂Row布局中的绝对安全文本
TextOverflowHandler.ultraSafeTextInRow(
  "电台名称可能很长需要特殊处理",
  flex: 2,
  maxWidth: 150,
  minWidth: 50,
  maxLines: 1,
)
```

### 自动溢出检查工具

开发时可以使用自动检查工具：

```dart
import '../utils/text_overflow_checker.dart';

// 自动检查和修复单个组件
Widget myWidget = Text("可能溢出的文本").autoFixOverflow();

// 递归修复整个Widget树
Widget myPage = MyPage().autoFixTreeOverflow();

// 开发时启用溢出调试
TextOverflowDebugger.enable();
```

## 🐛 常见问题

### Q: 为什么还是出现溢出？
A: 检查是否在正确的布局上下文中使用了对应的方法。Row中使用`safeTextInRow()`，Column中使用`safeTextInColumn()`。

### Q: 如何处理多行文本？
A: 使用`multiLineText()`方法，并设置合适的`maxHeight`约束。

### Q: 性能如何？
A: 工具类基于Flutter官方最佳实践，性能开销极小。避免了不必要的重建和布局计算。

### Q: 如何自定义溢出效果？
A: 使用`gradientMaskText()`创建渐变遮罩效果，或者直接使用`safeText()`并自定义`overflow`参数。

## 📚 相关资源

- [Flutter Layout约束详解](https://flutter.dev/docs/development/ui/layout/constraints)
- [Flutter文本处理最佳实践](https://flutter.dev/docs/development/ui/widgets/text)
- [RenderFlex溢出解决方案](https://flutter.dev/docs/testing/common-errors#renderflex-overflow)

## 🔄 更新日志

### v1.0.0
- ✨ 初始版本发布
- ✨ 支持基本的文本溢出处理
- ✨ 提供Row、Column专用方法
- ✨ 添加响应式和自适应文本支持
- ✨ 包含扩展方法和工具提示功能
