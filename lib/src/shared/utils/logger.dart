import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 简单的日志记录器
class Logger extends ProviderObserver {
  // 需要过滤的频繁更新的Provider类型
  static const Set<String> _filteredProviders = {
    'StreamProvider<CurrentPlayback>',
    'StateProvider<Duration>',
    'StateProvider<double>',
    'FutureProvider<Duration>',
  };

  // 需要过滤的Provider名称模式
  static const Set<String> _filteredProviderNames = {
    'playbackStream',
    'positionStream',
    'volumeProvider',
    'progressProvider',
  };

  @override
  void didUpdateProvider(
    ProviderBase provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    // 只在调试模式下打印日志
    if (!kDebugMode) return;

    final providerType = provider.runtimeType.toString();
    final providerName = provider.name ?? '';

    // 过滤频繁更新的Provider
    if (_filteredProviders.contains(providerType) ||
        _filteredProviderNames.any((name) => providerName.contains(name))) {
      return;
    }

    // 特殊处理CurrentPlayback，只在状态真正改变时打印
    if (providerType.contains('CurrentPlayback')) {
      if (previousValue != null && newValue != null) {
        // 这里可以添加更精细的比较逻辑
        return;
      }
    }

    // 打印有意义的Provider更新
    final displayName = provider.name ?? provider.runtimeType.toString();
    print('[Provider] $displayName updated');

    // 对于重要的状态变化，打印详细信息
    if (_isImportantProvider(providerType, providerName)) {
      print('  Previous: $previousValue');
      print('  New: $newValue');
    }
  }

  /// 判断是否为重要的Provider，需要打印详细信息
  bool _isImportantProvider(String providerType, String providerName) {
    return providerType.contains('StateNotifier') ||
           providerName.contains('country') ||
           providerName.contains('station') ||
           providerName.contains('error');
  }

  /// 记录日志信息
  void log(ProviderContainer container, String message) {
    if (kDebugMode) {
      print('[LOG] $message');
    }
  }
}