import '../models/radio_models.dart';
import '../models/station_simple.dart';

/// 电台数据适配器
/// 用于将API返回的RadioStation模型转换为UI使用的StationSimple模型
class RadioDataAdapter {
  /// 将RadioStation转换为StationSimple
  static StationSimple radioStationToStationSimple(RadioStation radioStation) {
    return StationSimple(
      id: radioStation.stationUuid,
      name: radioStation.name,
      url: radioStation.url ?? radioStation.urlResolved ?? '',
      country: radioStation.country ?? '',
      language: radioStation.language ?? '',
      favicon: radioStation.favicon ?? '',
      homepage: radioStation.homepage ?? '',
      tags: _extractTags(radioStation),
      votes: radioStation.votes ?? 0,
      isFavorite: false, // 需要从本地存储获取
      playCount: radioStation.clickCount ?? 0,
    );
  }

  /// 将RadioStation列表转换为StationSimple列表
  static List<StationSimple> radioStationListToStationSimpleList(
    List<RadioStation> radioStations,
  ) {
    return radioStations
        .map((station) => radioStationToStationSimple(station))
        .toList();
  }

  /// 将RadioTag转换为StationCategory
  static StationCategory radioTagToStationCategory(
    RadioTag tag,
    List<RadioStation> stations,
  ) {
    return StationCategory(
      id: tag.id.toString(),
      name: tag.name,
      displayName: tag.name,
      stations: radioStationListToStationSimpleList(stations),
      totalCount: stations.length,
    );
  }

  /// 从RadioStation提取标签
  static List<String> _extractTags(RadioStation station) {
    final tags = <String>[];
    
    // 添加编码格式标签
    if (station.codec != null && station.codec!.isNotEmpty) {
      tags.add(station.codec!);
    }
    
    // 添加比特率标签
    if (station.bitrate != null && station.bitrate! > 0) {
      tags.add('${station.bitrate}kbps');
    }
    
    // 添加流类型标签
    if (station.isHls == true) {
      tags.add('HLS');
    }
    
    // 添加内置标签
    if (station.tags != null && station.tags!.isNotEmpty) {
      tags.addAll(station.tags!.split(',').map((tag) => tag.trim()));
    }
    
    return tags;
  }



  /// 创建电台列表请求参数
  static RadioListRequest createRadioListRequest({
    int page = 1,
    int pageSize = 20,
    String? keyword,
    List<String>? languageCodes,
    String sortBy = 'votes',
    String sortOrder = 'desc',
  }) {
    return RadioListRequest(
      page: page,
      pageSize: pageSize,
      keyword: keyword,
      languageCodes: languageCodes,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
  }

  /// 检查电台是否可播放
  static bool isStationPlayable(RadioStation station) {
    final url = station.url ?? station.urlResolved ?? '';
    return station.status == 1 && 
           url.isNotEmpty;
  }

  /// 获取电台质量描述
  static String getStationQualityDescription(RadioStation station) {
    final quality = <String>[];
    
    final bitrate = station.bitrate ?? 0;
    if (bitrate > 0) {
      if (bitrate >= 320) {
        quality.add('High Quality');
      } else if (bitrate >= 128) {
        quality.add('Good Quality');
      } else {
        quality.add('Standard Quality');
      }
    }
    
    if (station.isHls == true) {
      quality.add('Adaptive Streaming');
    }
    
    return quality.join(' • ');
  }

  /// 获取电台受欢迎程度描述
  static String getStationPopularityDescription(RadioStation station) {
    final votes = station.votes ?? 0;
    if (votes > 1000) {
      return 'Very Popular';
    } else if (votes > 500) {
      return 'Popular';
    } else if (votes > 100) {
      return 'Liked';
    } else {
      return 'New';
    }
  }

  /// 格式化投票数显示
  static String formatVotes(int votes) {
    if (votes >= 1000000) {
      return '${(votes / 1000000).toStringAsFixed(1)}M';
    } else if (votes >= 1000) {
      return '${(votes / 1000).toStringAsFixed(1)}K';
    } else {
      return votes.toString();
    }
  }

  /// 格式化点击数显示
  static String formatClickCount(int clickCount) {
    if (clickCount >= 1000000) {
      return '${(clickCount / 1000000).toStringAsFixed(1)}M plays';
    } else if (clickCount >= 1000) {
      return '${(clickCount / 1000).toStringAsFixed(1)}K plays';
    } else {
      return '$clickCount plays';
    }
  }
}
