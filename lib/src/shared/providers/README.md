# 状态管理提供者 (Providers)

本目录包含应用的核心状态管理提供者，使用Riverpod进行状态管理。

## 📁 文件结构

### country_provider.dart
国家选择状态管理，负责：
- 国家列表加载和缓存
- 用户国家选择状态
- 设备国家自动检测
- 国家选择持久化存储

### radio_providers.dart  
电台数据状态管理，负责：
- 电台列表数据加载
- 分页和缓存管理
- 搜索和分类过滤
- 数据刷新通知

## 🌍 国家选择器 (CountrySelectionProvider)

### 核心功能
1. **自动初始化**: 应用启动时自动加载国家列表和用户选择
2. **设备检测**: 首次使用时尝试检测设备当前国家
3. **持久化存储**: 用户选择自动保存到本地存储
4. **状态同步**: 实时同步国家选择状态到所有监听者

### 初始化流程
```
应用启动 → 加载国家列表 → 检查已保存选择 → 设备国家检测 → 设置默认国家 → 通知监听者
```

### 状态结构
```dart
class CountrySelectionState {
  final List<Country> countries;      // 所有可用国家
  final Country? selectedCountry;     // 当前选择的国家
  final bool isLoading;              // 是否正在加载
  final String? error;               // 错误信息
  final String searchQuery;          // 搜索查询
}
```

### 使用示例
```dart
// 监听国家选择状态
ref.listen(countrySelectionProvider, (previous, next) {
  if (next.selectedCountry != null && !next.isLoading) {
    // 国家选择完成，可以加载相关数据
    loadCountrySpecificData(next.selectedCountry!);
  }
});

// 获取当前选择的国家
final selectedCountry = ref.watch(selectedCountryProvider);

// 手动选择国家
await ref.read(countrySelectionProvider.notifier).selectCountry(country);
```

## 📻 电台数据提供者 (Radio Providers)

### 提供者列表
- `popularRadiosProvider`: 热门电台
- `latestRadiosProvider`: 最新电台  
- `searchRadiosProvider`: 搜索结果
- `categoryRadiosProvider`: 分类电台
- `homeTabRadiosProvider`: 首页Tab电台

### 数据加载模式
1. **分页加载**: 支持无限滚动分页
2. **缓存机制**: 智能缓存减少网络请求
3. **国家感知**: 自动响应国家切换
4. **错误处理**: 完善的错误状态管理

### 缓存策略
- **时间缓存**: 数据有效期30分钟
- **国家缓存**: 按国家分别缓存
- **强制刷新**: 支持跳过缓存强制刷新

## 🔄 数据刷新机制

### 全局刷新通知
```dart
// 触发全局数据刷新
ref.read(dataRefreshNotifierProvider.notifier).triggerRefresh();

// 监听全局刷新事件
ref.listen(dataRefreshNotifierProvider, (previous, next) {
  if (previous != next && next > 0) {
    // 执行数据刷新逻辑
    refreshAllData();
  }
});
```

### 国家切换响应
当用户切换国家时，所有相关的电台数据会自动刷新：
1. 国家选择器状态更新
2. 触发全局刷新通知
3. 各页面监听并重新加载数据

## 🐛 故障排除

### 常见问题

#### 1. 数据加载延迟
**症状**: 首次进入页面时数据加载缓慢或不加载
**原因**: 国家选择器初始化未完成
**解决**: 确保在数据加载前检查国家选择状态

```dart
// 正确的数据加载方式
final countryState = ref.read(countrySelectionProvider);
if (countryState.selectedCountry != null && !countryState.isLoading) {
  // 安全加载数据
  loadData(countryState.selectedCountry!.id);
}
```

#### 2. 状态不同步
**症状**: 切换国家后部分数据未更新
**原因**: 监听器设置不正确或缓存未清理
**解决**: 检查监听器配置和缓存清理逻辑

#### 3. 内存泄漏
**症状**: 长时间使用后应用变慢
**原因**: 监听器未正确释放
**解决**: 确保在dispose中取消监听

### 调试日志
启用详细日志来跟踪状态变化：
- `🌍` 国家选择相关
- `📻` 电台数据加载
- `🔄` 数据刷新事件
- `💾` 缓存操作

## 📝 最佳实践

1. **监听器管理**: 及时取消不需要的监听器
2. **错误处理**: 为所有异步操作添加错误处理
3. **缓存策略**: 合理使用缓存减少网络请求
4. **状态检查**: 在使用数据前检查加载状态
5. **日志记录**: 添加详细日志便于调试

## 🔄 更新历史

- **2024-07-14**: 修复首次加载数据问题，改进国家选择器初始化
- **2024-07-13**: 添加全局数据刷新机制
- **2024-07-12**: 实现国家感知的电台数据加载
- **2024-07-11**: 基础状态管理架构建立
