import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/radio_models.dart';
import '../services/radio_api_service.dart';
import '../services/device_country_service.dart';
import 'radio_providers.dart';

/// 国家选择状态
class CountrySelectionState {
  final List<Country> countries;
  final Country? selectedCountry;
  final bool isLoading;
  final String? error;
  final String searchQuery;

  const CountrySelectionState({
    this.countries = const [],
    this.selectedCountry,
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
  });

  CountrySelectionState copyWith({
    List<Country>? countries,
    Country? selectedCountry,
    bool? isLoading,
    String? error,
    String? searchQuery,
  }) {
    return CountrySelectionState(
      countries: countries ?? this.countries,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  /// 根据搜索查询过滤国家列表
  List<Country> get filteredCountries {
    if (searchQuery.isEmpty) return countries;
    
    return countries.where((country) {
      return country.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
             country.code.toLowerCase().contains(searchQuery.toLowerCase());
    }).toList();
  }
}

/// 国家选择状态管理Provider
class CountrySelectionNotifier extends StateNotifier<CountrySelectionState> {
  final Ref _ref;

  CountrySelectionNotifier(this._ref) : super(const CountrySelectionState()) {
    _initialize();
  }

  static const String _selectedCountryKey = 'selected_country_id';
  static const String _deviceCountryDetectedKey = 'device_country_detected';
  static const String _fallbackCountryName = 'United States'; // 备用国家

  /// 初始化：加载国家列表和已选择的国家
  Future<void> _initialize() async {
    print('🌍 开始初始化国家选择器...');
    try {
      await loadCountries();
      await loadSelectedCountry();
      print('🌍 国家选择器初始化完成');
    } catch (e) {
      print('❌ 国家选择器初始化失败: $e');
      // 即使失败也要设置一个默认国家
      state = state.copyWith(
        selectedCountry: const Country(id: 226, name: 'United States', code: 'US'),
        isLoading: false,
      );
    }
  }

  /// 加载国家列表
  Future<void> loadCountries() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final radioApiService = RadioApiService();
      final countries = await radioApiService.getCountryList(isShow: 1);
      
      // 按名称排序，常用国家排在前面
      final sortedCountries = _sortCountriesByPopularity(countries);
      
      state = state.copyWith(
        countries: sortedCountries,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: '加载国家列表失败: $e',
        isLoading: false,
      );
    }
  }

  /// 加载已选择的国家（优化版）
  Future<void> loadSelectedCountry() async {
    print('🌍 开始快速加载已选择的国家...');
    try {
      final prefs = await SharedPreferences.getInstance();
      final selectedCountryId = prefs.getInt(_selectedCountryKey);
      final deviceDetected = prefs.getBool(_deviceCountryDetectedKey) ?? false;

      // 首先尝试快速检测设备国家（优先级提高）
      Country? detectedCountry;
      try {
        // 只有在已经有过设备检测的情况下才进行快速检测
        // 这样可以避免首次启动时的延迟
        if (deviceDetected) {
          print('🌍 快速检测设备国家...');
          detectedCountry = await _detectDeviceCountryWithTimeout();

          // 如果检测到设备国家，并且与保存的不同，优先使用设备国家
          if (detectedCountry != null &&
              selectedCountryId != null &&
              detectedCountry.id != selectedCountryId) {
            print('🌍 检测到设备国家变更: ${detectedCountry.name} (${detectedCountry.code})');
            await selectCountry(detectedCountry, triggerDataRefresh: true);
            return;
          }
        }
      } catch (e) {
        print('⚠️ 快速设备国家检测失败: $e');
        // 快速检测失败不影响后续流程
      }

      // 如果没有检测到设备国家变更，使用已保存的国家选择
      if (selectedCountryId != null && state.countries.isNotEmpty) {
        final savedCountry = state.countries.firstWhere(
          (c) => c.id == selectedCountryId,
          orElse: () => _getDefaultCountry(),
        );
        print('🌍 使用已保存的国家选择: ${savedCountry.name}');
        state = state.copyWith(selectedCountry: savedCountry);

        // 异步检测设备国家，如果不同则更新（不阻塞启动）
        // 即使快速检测没有发现变更，也进行一次完整的异步检测
        _detectAndUpdateDeviceCountryAsync();
        return;
      }

      // 如果没有保存的选择，才进行完整设备检测
      try {
        print('🌍 首次启动，检测设备国家...');
        detectedCountry = await _detectDeviceCountryWithTimeout();
        if (detectedCountry != null) {
          print('🌍 检测到设备国家: ${detectedCountry.name} (${detectedCountry.code})');
          await selectCountry(detectedCountry);
          await prefs.setBool(_deviceCountryDetectedKey, true);
          return;
        }
      } catch (e) {
        print('⚠️ 设备国家检测失败: $e');
      }

      // 检测失败，使用默认国家
      final defaultCountry = _getFallbackCountry();
      print('🌍 使用默认国家: ${defaultCountry.name}');
      await selectCountry(defaultCountry);

      print('🌍 国家选择加载完成: ${state.selectedCountry?.name}');
    } catch (e) {
      print('❌ 加载已选择国家失败: $e');
      // 如果加载失败，设置备用国家
      final defaultCountry = _getFallbackCountry();
      await selectCountry(defaultCountry);
    }
  }

  /// 异步检测并更新设备国家（不阻塞启动）
  void _detectAndUpdateDeviceCountryAsync() {
    // 减少延迟时间，让国家变更更快显示
    Future.delayed(const Duration(milliseconds: 200), () async {
      try {
        final detectedCountry = await _detectDeviceCountryWithTimeout();
        if (detectedCountry != null &&
            detectedCountry.id != state.selectedCountry?.id) {
          print('🌍 检测到设备国家变更，更新为: ${detectedCountry.name}');
          await selectCountry(detectedCountry, triggerDataRefresh: true);
        }
      } catch (e) {
        print('⚠️ 异步设备国家检测失败: $e');
      }
    });
  }

  /// 带超时的设备国家检测
  Future<Country?> _detectDeviceCountryWithTimeout() async {
    try {
      return await Future.any([
        _detectDeviceCountry(),
        Future.delayed(const Duration(milliseconds: 800), () => null),
      ]);
    } catch (e) {
      print('⚠️ 设备国家检测超时: $e');
      return null;
    }
  }

  /// 选择国家
  Future<void> selectCountry(Country country, {bool triggerDataRefresh = false}) async {
    print('🌍 选择国家: ${country.name} (${country.code})');
    state = state.copyWith(selectedCountry: country);

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_selectedCountryKey, country.id);
      print('🌍 国家选择已保存到本地存储');
    } catch (e) {
      // 持久化失败不影响当前状态
      print('❌ 保存选择的国家失败: $e');
    }

    // 如果需要触发数据刷新（自动检测国家变更时）
    if (triggerDataRefresh) {
      _triggerGlobalDataRefresh(country);
    }
  }

  /// 触发全局数据刷新（当设备国家自动变更时）
  void _triggerGlobalDataRefresh(Country country) {
    // 延迟一点时间，确保状态已经更新
    Future.delayed(const Duration(milliseconds: 100), () {
      try {
        print('🔄 触发全局数据刷新，国家: ${country.name}');

        // 直接触发所有电台数据的刷新
        _forceRefreshAllRadioData(country);

        // 触发全局数据刷新通知
        _ref.read(dataRefreshNotifierProvider.notifier).triggerRefresh();

      } catch (e) {
        print('❌ 触发全局数据刷新失败: $e');
      }
    });
  }

  /// 强制刷新所有电台数据
  void _forceRefreshAllRadioData(Country country) {
    try {
      // 刷新热门电台
      _ref.read(popularRadiosProvider.notifier).loadPopularRadios(
        refresh: true,
        countryId: country.id,
      );

      // 刷新最新电台
      _ref.read(latestRadiosProvider.notifier).loadLatestRadios(
        refresh: true,
        countryId: country.id,
      );

      // 刷新分类电台（重置状态）
      _ref.read(categoryRadiosProvider.notifier).reset();

      // 重置首页Tab专用的分类电台
      _ref.read(homeTabRadiosProvider.notifier).reset();

      // 刷新搜索结果（清空当前搜索）
      _ref.read(searchRadiosProvider.notifier).reset();

      // 无效化推荐电台缓存，触发重新获取
      _ref.invalidate(recommendedRadiosProvider);

      print('✅ 所有电台数据刷新完成');
    } catch (e) {
      // 静默处理错误，避免影响用户体验
      print('❌ 刷新电台数据时出错: $e');
    }
  }

  /// 更新搜索查询
  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  /// 清除搜索查询
  void clearSearchQuery() {
    state = state.copyWith(searchQuery: '');
  }

  /// 检测设备国家
  Future<Country> _detectDeviceCountry() async {
    try {
      print('🔍 开始检测设备国家...');

      // 获取设备国家代码
      final deviceCountryCode = await DeviceCountryService.getDeviceCountryCode();

      if (deviceCountryCode != null) {
        print('📱 检测到设备国家代码: $deviceCountryCode');

        // 根据国家代码获取英文名称
        final countryName = DeviceCountryService.getCountryNameByCode(deviceCountryCode);

        if (countryName != null && state.countries.isNotEmpty) {
          // 尝试在国家列表中找到对应的国家
          final deviceCountry = state.countries.where((c) =>
            c.name == countryName || c.code.toUpperCase() == deviceCountryCode.toUpperCase()
          ).firstOrNull;

          if (deviceCountry != null) {
            print('✅ 成功匹配到设备国家: ${deviceCountry.name} (${deviceCountry.code})');
            return deviceCountry;
          } else {
            print('⚠️ 未在国家列表中找到匹配的国家: $countryName ($deviceCountryCode)');
          }
        }
      } else {
        print('⚠️ 无法获取设备国家代码');
      }
    } catch (e) {
      print('❌ 检测设备国家时出错: $e');
    }

    // 检测失败，返回备用国家
    print('🔄 使用备用国家');
    return _getFallbackCountry();
  }

  /// 获取备用国家（原默认国家逻辑）
  Country _getFallbackCountry() {
    return _getDefaultCountry();
  }

  /// 获取默认国家
  Country _getDefaultCountry() {
    if (state.countries.isEmpty) {
      return const Country(id: 226, name: 'United States', code: 'US');
    }

    // 尝试找到备用国家
    final defaultCountry = state.countries.firstWhere(
      (c) => c.name == _fallbackCountryName,
      orElse: () => state.countries.first,
    );

    return defaultCountry;
  }

  /// 按热门程度排序国家（常用国家在前）
  List<Country> _sortCountriesByPopularity(List<Country> countries) {
    // 定义常用国家列表（按优先级排序）
    const popularCountries = [
      'United States',
      'United Kingdom', 
      'Germany',
      'France',
      'Canada',
      'Australia',
      'Japan',
      'China',
      'Italy',
      'Spain',
      'Netherlands',
      'Russia',
      'Brazil',
      'India',
      'Mexico',
    ];
    
    final List<Country> sorted = [];
    final List<Country> remaining = List.from(countries);
    
    // 先添加热门国家
    for (final popularName in popularCountries) {
      final index = remaining.indexWhere((c) => c.name == popularName);
      if (index != -1) {
        sorted.add(remaining.removeAt(index));
      }
    }
    
    // 剩余国家按字母顺序排序
    remaining.sort((a, b) => a.name.compareTo(b.name));
    sorted.addAll(remaining);
    
    return sorted;
  }
}

/// 国家选择Provider
final countrySelectionProvider = StateNotifierProvider<CountrySelectionNotifier, CountrySelectionState>(
  (ref) => CountrySelectionNotifier(ref),
);

/// 当前选择的国家Provider
final selectedCountryProvider = Provider<Country?>((ref) {
  return ref.watch(countrySelectionProvider).selectedCountry;
});

/// 过滤后的国家列表Provider
final filteredCountriesProvider = Provider<List<Country>>((ref) {
  return ref.watch(countrySelectionProvider).filteredCountries;
}); 