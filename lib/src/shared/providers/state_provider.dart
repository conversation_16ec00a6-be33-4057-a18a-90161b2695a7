import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/radio_models.dart';
import '../services/radio_api_service.dart';
import 'country_provider.dart';

/// 省州选择状态
class StateSelectionState {
  final List<CountryState> states;
  final CountryState? selectedState;
  final bool isLoading;
  final String? error;
  final String? currentCountryCode;
  final int? currentCountryId;

  const StateSelectionState({
    this.states = const [],
    this.selectedState,
    this.isLoading = false,
    this.error,
    this.currentCountryCode,
    this.currentCountryId,
  });

  StateSelectionState copyWith({
    List<CountryState>? states,
    CountryState? selectedState,
    bool? isLoading,
    String? error,
    String? currentCountryCode,
    int? currentCountryId,
    bool clearSelectedState = false,
  }) {
    print('⚡ StateSelectionState.copyWith 被调用');
    print('⚡ clearSelectedState: $clearSelectedState');
    print('⚡ selectedState参数: $selectedState');
    print('⚡ 当前this.selectedState: ${this.selectedState}');
    
    final newState = StateSelectionState(
      states: states ?? this.states,
      selectedState: clearSelectedState ? null : (selectedState ?? this.selectedState),
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      currentCountryCode: currentCountryCode ?? this.currentCountryCode,
      currentCountryId: currentCountryId ?? this.currentCountryId,
    );
    
    print('⚡ 新状态的selectedState: ${newState.selectedState}');
    
    return newState;
  }

  /// 获取有电台的省州列表（过滤掉电台数量为0的）
  List<CountryState> get availableStates => 
      states.where((state) => state.hasStations).toList();
}

/// 省州选择状态管理Provider
class StateSelectionNotifier extends StateNotifier<StateSelectionState> {
  final Ref _ref;
  final RadioApiService _apiService;

  StateSelectionNotifier(this._ref) 
      : _apiService = RadioApiService(),
        super(const StateSelectionState()) {
    _initialize();
  }

  static const String _selectedStateKey = 'selected_state_name';
  static const String _selectedCountryIdKey = 'selected_country_id_for_state';

  /// 初始化：监听国家变化并加载对应的省州列表
  void _initialize() {
    print('🔄 StateProvider: 开始初始化');
    
    // 监听国家选择变化
    _ref.listen(selectedCountryProvider, (previous, next) {
      print('🔄 StateProvider: 监听到国家变化事件 - ${previous?.name} -> ${next?.name}');
      _onCountryChanged(previous, next);
    });

    // 如果当前已有选中的国家，立即加载省州列表
    final currentCountry = _ref.read(selectedCountryProvider);
    print('🔄 StateProvider: 当前选中的国家 - ${currentCountry?.name} (ID: ${currentCountry?.id})');
    if (currentCountry != null) {
      print('🔄 StateProvider: 立即为当前国家加载省州列表');
      _loadStatesForCountry(currentCountry, shouldLoadSelectedState: true);
    } else {
      print('🔄 StateProvider: 当前没有选中的国家，跳过省州加载');
    }
  }

  /// 处理国家变化
  void _onCountryChanged(Country? previous, Country? current) {
    if (current == null) {
      // 没有选中国家，清空省州数据
      state = const StateSelectionState();
      return;
    }

    if (previous?.id != current.id) {
      // 国家发生变化，清空当前选中的省州并加载新国家的省州列表
      print('🗺️ 国家变化: ${previous?.name} -> ${current.name}');
      state = state.copyWith(
        clearSelectedState: true,
        currentCountryCode: current.code,
        currentCountryId: current.id,
      );
      
      _loadStatesForCountry(current, shouldLoadSelectedState: true);
    }
  }

  /// 为指定国家加载省州列表
  Future<void> _loadStatesForCountry(Country country, {bool shouldLoadSelectedState = false}) async {
    if (state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      error: null,
      currentCountryCode: country.code,
      currentCountryId: country.id,
    );

    try {
      print('🌍 StateProvider: 开始为国家加载省州 - ${country.name} (ID: ${country.id})');
      final response = await _apiService.getCountryStates(countryId: country.id);
      print('🗺️ StateProvider: 成功加载${country.name}的省州列表: ${response.states.length}个');

      state = state.copyWith(
        states: response.states,
        isLoading: false,
      );

      // 如果需要，尝试恢复之前选中的省州
      if (shouldLoadSelectedState) {
        await _loadSelectedState();
      }
    } catch (e) {
      print('❌ StateProvider: 加载省州列表失败 - 国家: ${country.name} (ID: ${country.id})');
      print('❌ StateProvider: 详细错误信息: $e');
      print('❌ StateProvider: 错误类型: ${e.runtimeType}');
      state = state.copyWith(
        isLoading: false,
        error: 'load_states_failed',
      );
    }
  }

  /// 加载已保存的省州选择
  Future<void> _loadSelectedState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedStateName = prefs.getString(_selectedStateKey);
      final savedCountryId = prefs.getInt(_selectedCountryIdKey);

      // 只有当保存的国家ID与当前国家ID一致时，才恢复省州选择
      if (savedStateName != null && 
          savedCountryId != null && 
          savedCountryId == state.currentCountryId) {
        
        final savedState = state.availableStates
            .cast<CountryState?>()
            .firstWhere(
              (s) => s?.stateName == savedStateName,
              orElse: () => null,
            );

        if (savedState != null) {
          state = state.copyWith(selectedState: savedState);
          print('🗺️ 恢复省州选择: ${savedState.stateName}');
        }
      }
    } catch (e) {
      print('⚠️ 加载保存的省州选择失败: $e');
    }
  }

  /// 选择省州
  Future<void> selectState(CountryState? selectedState) async {
    print('🗺️🗺️🗺️ selectState被调用: ${selectedState?.stateName ?? 'null(清除选择)'}');
    print('🗺️ 当前状态before: ${state.selectedState?.stateName ?? 'null'}');
    print('🗺️ 当前国家ID: ${state.currentCountryId}');

    // 更新状态 - 🔧 修复：正确处理 null 值
    final oldState = state;
    if (selectedState == null) {
      // 明确使用 clearSelectedState: true 来清除状态
      state = state.copyWith(clearSelectedState: true);
      print('🗺️ 🔧 使用 clearSelectedState: true 清除状态');
    } else {
      // 设置具体的状态值
      state = state.copyWith(selectedState: selectedState);
      print('🗺️ 🔧 设置具体状态值: ${selectedState.stateName}');
    }

    print('🗺️ 状态更新after: ${state.selectedState?.stateName ?? 'null'}');
    print('🗺️ 状态更新验证: ${oldState.selectedState?.stateName ?? 'null'} -> ${state.selectedState?.stateName ?? 'null'}');

    // 持久化到本地存储
    try {
      final prefs = await SharedPreferences.getInstance();
      if (selectedState != null && state.currentCountryId != null) {
        await prefs.setString(_selectedStateKey, selectedState.stateName);
        await prefs.setInt(_selectedCountryIdKey, state.currentCountryId!);
        print('🗺️ ✅ 省州选择已保存到本地存储: ${selectedState.stateName}');
      } else {
        await prefs.remove(_selectedStateKey);
        await prefs.remove(_selectedCountryIdKey);
        print('🗺️ ✅ 已清空保存的省州选择（设置为null）');
      }
    } catch (e) {
      print('❌ 保存省州选择失败: $e');
    }

    // 验证最终状态
    print('🗺️ 最终验证 - selectedState: ${state.selectedState?.stateName ?? 'null'}');
  }

  /// 手动刷新当前国家的省州列表
  Future<void> refreshStates() async {
    final currentCountry = _ref.read(selectedCountryProvider);
    if (currentCountry != null) {
      await _loadStatesForCountry(currentCountry);
    }
  }

  /// 清空省州选择
  void clearSelection() {
    selectState(null);
  }

  /// 清除本地存储的省州选择缓存（用于应用启动时重置）
  Future<void> clearStoredSelection() async {
    print('🗺️ 清除本地存储的省州选择缓存');
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_selectedStateKey);
      await prefs.remove(_selectedCountryIdKey);
      print('🗺️ ✅ 已清除本地存储的省州选择缓存');

      // 同时清除内存中的状态
      state = state.copyWith(clearSelectedState: true);
      print('🗺️ ✅ 已清除内存中的省州选择状态');
    } catch (e) {
      print('❌ 清除省州选择缓存失败: $e');
    }
  }
}

/// 省州选择Provider
final stateSelectionProvider = StateNotifierProvider<StateSelectionNotifier, StateSelectionState>(
  (ref) => StateSelectionNotifier(ref),
);

/// 当前选择的省州Provider
final selectedStateProvider = Provider<CountryState?>((ref) {
  return ref.watch(stateSelectionProvider).selectedState;
});

/// 可用省州列表Provider
final availableStatesProvider = Provider<List<CountryState>>((ref) {
  return ref.watch(stateSelectionProvider).availableStates;
});

/// 省州加载状态Provider
final stateLoadingProvider = Provider<bool>((ref) {
  return ref.watch(stateSelectionProvider).isLoading;
});

/// 省州错误状态Provider
final stateErrorProvider = Provider<String?>((ref) {
  return ref.watch(stateSelectionProvider).error;
});