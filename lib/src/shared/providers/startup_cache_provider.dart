import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/startup_cache_service.dart';
import '../models/models.dart';

/// 启动缓存状态
class StartupCacheState {
  final bool isLoading;
  final bool hasCache;
  final StartupCacheData? cacheData;
  final String? error;
  final DateTime? lastCacheTime;

  const StartupCacheState({
    this.isLoading = false,
    this.hasCache = false,
    this.cacheData,
    this.error,
    this.lastCacheTime,
  });

  StartupCacheState copyWith({
    bool? isLoading,
    bool? hasCache,
    StartupCacheData? cacheData,
    String? error,
    DateTime? lastCacheTime,
  }) {
    return StartupCacheState(
      isLoading: isLoading ?? this.isLoading,
      hasCache: hasCache ?? this.hasCache,
      cacheData: cacheData ?? this.cacheData,
      error: error ?? this.error,
      lastCacheTime: lastCacheTime ?? this.lastCacheTime,
    );
  }
}

/// 启动缓存服务提供者
final startupCacheServiceProvider = Provider<StartupCacheService>((ref) {
  return StartupCacheService();
});

/// 启动缓存状态管理
class StartupCacheNotifier extends StateNotifier<StartupCacheState> {
  final StartupCacheService _cacheService;

  StartupCacheNotifier(this._cacheService) : super(const StartupCacheState()) {
    _initialize();
  }

  /// 初始化缓存服务
  Future<void> _initialize() async {
    print('🔧 开始初始化启动缓存服务...');
    state = state.copyWith(isLoading: true);

    try {
      await _cacheService.initialize();
      print('🔧 缓存服务初始化完成，开始加载缓存数据...');
      await _loadCachedData();
      print('🔧 启动缓存初始化完成');
    } catch (e) {
      print('❌ 启动缓存初始化失败: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载缓存数据
  Future<void> _loadCachedData() async {
    try {
      // print('🔍 正在从缓存服务获取数据...');
      final cacheData = await _cacheService.getCachedHomeData();

      // print('🔍 缓存数据获取结果:');
      // print('   - 数据存在: ${cacheData != null}');
      if (cacheData != null) {
        // print('   - 首页电台: ${cacheData.homeStations.length}个');
        // print('   - 热门推荐: ${cacheData.popularStations.length}个');
        // print('   - 点击最高: ${cacheData.mostClickedStations.length}个');
        // print('   - 高质量: ${cacheData.highQualityStations.length}个');
        // print('   - 最新上架: ${cacheData.latestStations.length}个');
        // print('   - 分类: ${cacheData.categories.length}个');
        // print('   - 国家: ${cacheData.selectedCountry.name}');
      }

      state = state.copyWith(
        isLoading: false,
        hasCache: cacheData != null,
        cacheData: cacheData,
        lastCacheTime: cacheData != null ? DateTime.now() : null,
      );

      print('🔍 缓存状态更新完成: hasCache=${cacheData != null}');
    } catch (e) {
      print('❌ 加载缓存数据失败: $e');
      state = state.copyWith(
        isLoading: false,
        hasCache: false,
        error: e.toString(),
      );
    }
  }

  /// 保存首页数据到缓存
  Future<void> saveHomeDataToCache({
    required List<StationSimple> homeStations,
    required List<StationSimple> popularStations,
    required List<StationSimple> mostClickedStations,
    required List<StationSimple> highQualityStations,
    required List<StationSimple> latestStations,
    required List<RadioCategory> categories,
    required Country selectedCountry,
    int? selectedCategoryId, // 新增：当前选中的分类ID
  }) async {
    try {
      await _cacheService.cacheHomeData(
        homeStations: homeStations,
        popularStations: popularStations,
        mostClickedStations: mostClickedStations,
        highQualityStations: highQualityStations,
        latestStations: latestStations,
        categories: categories,
        selectedCountry: selectedCountry,
        selectedCategoryId: selectedCategoryId,
      );

      // 更新状态
      final cacheData = StartupCacheData(
        homeStations: homeStations,
        popularStations: popularStations,
        mostClickedStations: mostClickedStations,
        highQualityStations: highQualityStations,
        latestStations: latestStations,
        categories: categories,
        selectedCountry: selectedCountry,
        selectedCategoryId: selectedCategoryId,
      );

      state = state.copyWith(
        hasCache: true,
        cacheData: cacheData,
        lastCacheTime: DateTime.now(),
      );

      // print('✅ 首页数据已保存到启动缓存');
    } catch (e) {
      // print('❌ 保存首页数据到缓存失败: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// 清空缓存
  Future<void> clearCache() async {
    try {
      await _cacheService.clearAllCache();
      
      state = state.copyWith(
        hasCache: false,
        cacheData: null,
        lastCacheTime: null,
      );
      
      print('🗑️ 启动缓存已清空');
    } catch (e) {
      print('❌ 清空缓存失败: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// 检查缓存是否有效
  bool isCacheValid() {
    return _cacheService.isCacheValid() && state.hasCache;
  }

  /// 获取缓存信息
  String getCacheInfo() {
    return _cacheService.getCacheInfo();
  }
}

/// 启动缓存提供者
final startupCacheProvider = StateNotifierProvider<StartupCacheNotifier, StartupCacheState>((ref) {
  final cacheService = ref.watch(startupCacheServiceProvider);
  return StartupCacheNotifier(cacheService);
});

/// 便捷的缓存数据获取器
final cachedHomeDataProvider = Provider<StartupCacheData?>((ref) {
  final cacheState = ref.watch(startupCacheProvider);
  return cacheState.cacheData;
});

/// 缓存有效性检查器
final isCacheValidProvider = Provider<bool>((ref) {
  final cacheNotifier = ref.watch(startupCacheProvider.notifier);
  return cacheNotifier.isCacheValid();
});
