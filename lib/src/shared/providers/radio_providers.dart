import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/radio_models.dart';
import '../models/station_simple.dart';
import '../models/models.dart'; // 导入RadioCategory
import '../services/radio_api_service.dart';
import '../utils/radio_data_adapter.dart';
import 'country_provider.dart';

/// 电台API服务提供者
final radioApiServiceProvider = Provider<RadioApiService>((ref) {
  return RadioApiService();
});

/// 数据缓存信息
class CacheInfo {
  final DateTime timestamp;
  final int? countryId;
  final String? query;
  
  const CacheInfo({
    required this.timestamp,
    this.countryId,
    this.query,
  });
  
  bool isExpired([Duration? ttl]) {
    final duration = ttl ?? const Duration(minutes: 30); // 默认30分钟过期，提升缓存命中率
    return DateTime.now().difference(timestamp) > duration;
  }
  
  bool isValidForRequest({int? countryId, String? query}) {
    return this.countryId == countryId && this.query == query;
  }
}

/// 电台列表状态
class RadioListState {
  final List<StationSimple> stations;
  final bool isLoading;
  final bool hasMore;
  final int currentPage;
  final String? error;
  final CacheInfo? cacheInfo;

  const RadioListState({
    this.stations = const [],
    this.isLoading = false,
    this.hasMore = true,
    this.currentPage = 1,
    this.error,
    this.cacheInfo,
  });

  RadioListState copyWith({
    List<StationSimple>? stations,
    bool? isLoading,
    bool? hasMore,
    int? currentPage,
    String? error,
    CacheInfo? cacheInfo,
  }) {
    return RadioListState(
      stations: stations ?? this.stations,
      isLoading: isLoading ?? this.isLoading,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      error: error ?? this.error,
      cacheInfo: cacheInfo ?? this.cacheInfo,
    );
  }
  
  /// 检查缓存是否有效
  bool isCacheValid({int? countryId, String? query, Duration? ttl}) {
    if (cacheInfo == null) return false;
    return !cacheInfo!.isExpired(ttl) && 
           cacheInfo!.isValidForRequest(countryId: countryId, query: query);
  }
}

/// 电台列表控制器
class RadioListNotifier extends StateNotifier<RadioListState> {
  final RadioApiService _apiService;
  static const int _pageSize = 50; // 优化：平衡加载速度和数据量，确保足够的电台数据

  RadioListNotifier(this._apiService) : super(const RadioListState());

  /// 加载热门电台
  Future<void> loadPopularRadios({
    bool refresh = false,
    int? countryId,
    String? stateName,
    bool forceRefresh = false,
    List<int>? tagIds,
  }) async {
    // 生成缓存键，包含tagIds和state信息
    var cacheQuery = tagIds != null && tagIds.isNotEmpty 
        ? 'popular_tags_${tagIds.join('_')}' 
        : 'popular_all';
    if (stateName != null && stateName.isNotEmpty) {
      cacheQuery += '_state_$stateName';
    }
    
    // 检查缓存
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('使用缓存数据：热门电台 - $cacheQuery');
      return;
    }
    
    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      print('📤 RadioListNotifier加载热门电台 - 国家ID: $countryId, 省州: $stateName, 标签: $tagIds');
      final response = await _apiService.getPopularRadios(
        page: page,
        pageSize: _pageSize,
        countryId: countryId,
        state: stateName,
        tagIds: tagIds,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(
        response.list,
      );

      final allStations = refresh 
          ? newStations 
          : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载最新电台
  Future<void> loadLatestRadios({
    bool refresh = false,
    int? countryId,
    bool forceRefresh = false,
    List<int>? tagIds,
  }) async {
    // 生成缓存键，包含tagIds信息
    final cacheQuery = tagIds != null && tagIds.isNotEmpty 
        ? 'latest_tags_${tagIds.join('_')}' 
        : 'latest_all';
    
    // 检查缓存
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('使用缓存数据：最新电台 - $cacheQuery');
      return;
    }
    
    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.getLatestRadios(
        page: page,
        pageSize: _pageSize,
        countryId: countryId,
        tagIds: tagIds,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(
        response.list,
      );

      final allStations = refresh 
          ? newStations 
          : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 搜索电台
  Future<void> searchRadios(
    String keyword, {
    bool refresh = false,
    int? countryId,
    String? state,
    List<int>? tagIds,
    String? sortBy,
    String? sortOrder,
    bool forceRefresh = false,
  }) async {
    print('📡📡📡 searchRadios 方法被调用');
    print('📡 接收到的参数:');
    print('📡   - keyword: "$keyword"');
    print('📡   - countryId: $countryId');
    print('📡   - state: "$state"');
    print('📡   - tagIds: $tagIds');
    print('📡   - sortBy: $sortBy');
    print('📡   - sortOrder: $sortOrder');
    print('📡   - refresh: $refresh');
    print('📡   - forceRefresh: $forceRefresh');
    
    // 生成缓存键，包含state信息
    final cacheQuery = state != null ? '${keyword}_state_$state' : keyword;
    
    // 优化：检查缓存，如果有效则立即返回，避免loading状态
    if (!forceRefresh && !refresh && this.state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('🚀 使用缓存数据：搜索电台 - $cacheQuery (${this.state.stations.length}个电台)');
      return;
    }

    if (this.state.isLoading) {
      print('📡 ⚠️ 已经在加载中，跳过此次请求');
      return;
    }

    if (refresh) {
      this.state = const RadioListState(isLoading: true);
    } else {
      this.state = this.state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : this.state.currentPage;
      
      print('📡📡📡 即将调用 API Service searchRadios，参数：');
      print('📡   - keyword: "$keyword"');
      print('📡   - page: $page');
      print('📡   - pageSize: $_pageSize');
      print('📡   - countryId: $countryId');
      print('📡   - state: "$state"');
      print('📡   - tagIds: $tagIds');
      print('📡   - sortBy: $sortBy');
      print('📡   - sortOrder: $sortOrder');
      
      final response = await _apiService.searchRadios(
        keyword: keyword,
        page: page,
        pageSize: _pageSize,
        countryId: countryId,
        state: state,
        tagIds: tagIds,
        sortBy: sortBy,
        sortOrder: sortOrder,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(
        response.list,
      );

      final allStations = refresh 
          ? newStations 
          : [...this.state.stations, ...newStations];

      this.state = this.state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      this.state = this.state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 按国家加载电台
  Future<void> loadRadiosByCountry({
    required int countryId,
    bool refresh = false,
    bool forceRefresh = false,
    String sortBy = 'click_count', // 默认按点击数排序，与热门推荐区分
    String sortOrder = 'desc',
    List<int>? tagIds,
  }) async {
    // 生成缓存键，包含tagIds信息
    final queryKey = tagIds != null && tagIds.isNotEmpty 
        ? 'country_${countryId}_tags_${tagIds.join('_')}' 
        : 'country_$countryId';

    // 检查缓存
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: queryKey)) {
      print('使用缓存数据：国家电台 - $queryKey');
      return;
    }

    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.getRadiosByCountry(
        countryId: countryId,
        page: page,
        pageSize: _pageSize,
        sortBy: sortBy,
        sortOrder: sortOrder,
        tagIds: tagIds,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(
        response.list,
      );

      final allStations = refresh
          ? newStations
          : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: queryKey,
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 搜索电台
  Future<void> loadRadiosBySearch({
    required String keyword,
    bool refresh = false,
    int? countryId,
    bool forceRefresh = false,
    List<int>? tagIds,
  }) async {
    // 生成缓存键，包含tagIds信息
    final cacheQuery = tagIds != null && tagIds.isNotEmpty 
        ? '${keyword}_tags_${tagIds.join('_')}' 
        : keyword;
    
    // 检查缓存
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('使用缓存数据：搜索电台 - $cacheQuery');
      return;
    }

    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.searchRadios(
        keyword: keyword,
        page: page,
        pageSize: _pageSize,
        countryId: countryId,
        tagIds: tagIds,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(
        response.list,
      );

      final allStations = refresh
          ? newStations
          : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载高质量电台（按比特率排序，支持分类过滤）
  Future<void> loadHighQualityRadios({
    bool refresh = false,
    int? countryId,
    bool forceRefresh = false,
    List<int>? tagIds,
  }) async {
    // 生成缓存键，包含tagIds信息
    final cacheQuery = tagIds != null && tagIds.isNotEmpty 
        ? 'high_quality_tags_${tagIds.join('_')}' 
        : 'high_quality_all';
    
    // 检查缓存
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('使用缓存数据：高质量电台 - $cacheQuery');
      return;
    }

    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.getRadioList(RadioListRequest(
        page: page,
        pageSize: _pageSize,
        countryId: countryId,
        tagIds: tagIds,
        sortBy: 'bitrate', // 按比特率排序，获取高质量电台
        sortOrder: 'desc',
      ));

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(
        response.list,
      );

      final allStations = refresh
          ? newStations
          : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 按分类加载电台
  Future<void> loadRadiosByCategory(
    List<int> tagIds, {
    bool refresh = false,
    int? countryId,
    bool forceRefresh = false,
  }) async {
    final queryKey = tagIds.join(',');
    
    // 检查缓存
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: queryKey)) {
      print('使用缓存数据：分类电台 - $queryKey');
      return;
    }
    
    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.getRadiosByCategory(
        tagIds: tagIds,
        page: page,
        pageSize: _pageSize,
        countryId: countryId,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(
        response.list,
      );

      print('📊 API数据统计: tag_ids=$queryKey, 返回${newStations.length}个电台');
      
      // 统计有无favicon的电台数量
      final withFavicon = newStations.where((s) => s.favicon.isNotEmpty && s.favicon.trim().isNotEmpty).length;
      final withoutFavicon = newStations.length - withFavicon;
      print('📊 Favicon统计: 有图片${withFavicon}个, 无图片${withoutFavicon}个 (${((withoutFavicon / newStations.length) * 100).toInt()}%无图片)');

      final allStations = refresh 
          ? newStations 
          : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: queryKey,
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 强制刷新（清除缓存）
  void forceRefresh({int? countryId}) {
    state = const RadioListState(isLoading: true);
  }

  /// 重置状态
  void reset() {
    state = const RadioListState();
  }
}

/// 热门电台提供者
final popularRadiosProvider = StateNotifierProvider<RadioListNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  return RadioListNotifier(apiService);
});

/// 最新电台提供者
final latestRadiosProvider = StateNotifierProvider<RadioListNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  return RadioListNotifier(apiService);
});

/// 搜索电台提供者
final searchRadiosProvider = StateNotifierProvider<RadioListNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  return RadioListNotifier(apiService);
});

/// 分类电台提供者
final categoryRadiosProvider = StateNotifierProvider<RadioListNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  return RadioListNotifier(apiService);
});

/// 首页Tab专用的分类电台提供者（支持预加载缓存）
final homeTabRadiosProvider = StateNotifierProvider<HomeTabRadioNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  final preloadCache = ref.watch(preloadCacheProvider.notifier);
  return HomeTabRadioNotifier(apiService, preloadCache);
});

/// 首页Tab电台控制器（支持预加载缓存）
class HomeTabRadioNotifier extends StateNotifier<RadioListState> {
  final RadioApiService _apiService;
  final PreloadCacheNotifier _preloadCache;

  HomeTabRadioNotifier(this._apiService, this._preloadCache) : super(const RadioListState());

  /// 按分类加载电台（使用tag_ids参数）
  Future<void> loadRadiosByCategory(
    List<int> tagIds, {
    bool refresh = false,
    int? countryId,
    bool forceRefresh = false,
  }) async {
    final queryKey = tagIds.join(',');
    
    // 优先检查预加载缓存（除非强制刷新）
    if (!forceRefresh) {
      final cachedData = _preloadCache.getCategoryData(tagIds.first.toString()); // 使用第一个tagId作为key
      if (cachedData != null && cachedData.stations.isNotEmpty) {
        print('🚀 使用预加载缓存数据：tag_ids $queryKey (${cachedData.stations.length}个电台)');
        state = cachedData;
        return;
      }
    }

    // 如果预加载正在进行中，等待一下再检查
    final preloadState = _preloadCache.state;
    if (preloadState.isPreloading && !forceRefresh && !refresh) {
      print('⏳ 预加载进行中，等待缓存：tag_ids $queryKey');
      // 等待最多2秒，然后再检查缓存
      await Future.delayed(const Duration(milliseconds: 2000));
      final cachedData = _preloadCache.getCategoryData(tagIds.first.toString()); // 使用第一个tagId作为key
      if (cachedData != null && cachedData.stations.isNotEmpty) {
        print('🚀 等待后使用预加载缓存：tag_ids $queryKey (${cachedData.stations.length}个电台)');
        state = cachedData;
        return;
      }
    }

    // 缓存中没有数据，使用原有逻辑加载
    print('📡 预加载缓存未命中，从API加载：tag_ids $queryKey');

    // 检查普通缓存
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: queryKey)) {
      print('🚀 使用普通缓存数据：按分类加载电台 - tag_ids $queryKey (${state.stations.length}个电台)');
      return;
    }

    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.getRadiosByCategory(
        tagIds: tagIds,
        page: page,
        pageSize: 100, // 修正：使用配置的最大分页大小，避免超过后端限制
        countryId: countryId,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(
        response.list,
      );

      final allStations = refresh
          ? newStations
          : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: queryKey,
        ),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }



  /// 重置状态
  void reset() {
    state = const RadioListState();
  }
}

/// 电台标签提供者
final radioTagsProvider = FutureProvider<List<RadioTag>>((ref) async {
  final apiService = ref.watch(radioApiServiceProvider);
  return apiService.getRadioTags(isShow: 1);
});

/// 分类缓存状态
class CategoriesState {
  final List<RadioCategory> categories;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const CategoriesState({
    this.categories = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  CategoriesState copyWith({
    List<RadioCategory>? categories,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return CategoriesState(
      categories: categories ?? this.categories,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// 检查缓存是否有效（30分钟）
  bool get isCacheValid {
    if (lastUpdated == null || categories.isEmpty) return false;
    return DateTime.now().difference(lastUpdated!) < const Duration(minutes: 30);
  }
}

/// 分类数据控制器
class CategoriesNotifier extends StateNotifier<CategoriesState> {
  final RadioApiService _apiService;

  CategoriesNotifier(this._apiService) : super(const CategoriesState());

  /// 加载分类数据
  Future<void> loadCategories({bool forceRefresh = false}) async {
    // 如果有有效缓存且不强制刷新，直接返回
    if (!forceRefresh && state.isCacheValid) {
      print('🚀 使用缓存的分类数据 (${state.categories.length}个分类)');
      return;
    }

    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final tags = await _apiService.getRadioTags(isShow: 1);

      // 按sort_order降序排序，取前10个作为主要分类
      final sortedTags = List<RadioTag>.from(tags)
        ..sort((a, b) => b.sortOrder.compareTo(a.sortOrder));

      final topTags = sortedTags.take(10).toList();

      // 转换为RadioCategory
      final categories = topTags.map((tag) => RadioCategory.fromRadioTag(tag)).toList();

      // 将"other"分类移到最后
      final otherIndex = categories.indexWhere((cat) => cat.name.toLowerCase() == 'other');
      if (otherIndex != -1) {
        final otherCategory = categories.removeAt(otherIndex);
        categories.add(otherCategory);
        print('📌 已将"other"分类移到最后位置');
      }

      state = state.copyWith(
        categories: categories,
        isLoading: false,
        lastUpdated: DateTime.now(),
      );

      print('🏷️ 动态加载了 ${categories.length} 个分类标签');
      for (final category in categories) {
        print('   - ${category.name} (sort: ${category.sortOrder})');
      }
    } catch (e) {
      print('❌ 加载动态分类失败: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}

/// 动态分类标签提供者（用于首页Tab，带缓存）
final dynamicCategoriesProvider = StateNotifierProvider<CategoriesNotifier, CategoriesState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  return CategoriesNotifier(apiService);
});

/// 国家列表提供者
final countriesProvider = FutureProvider<List<Country>>((ref) async {
  final apiService = ref.watch(radioApiServiceProvider);
  return apiService.getCountryList();
});



/// 推荐电台提供者（首页使用，感知选择的国家）
final recommendedRadiosProvider = FutureProvider<List<StationSimple>>((ref) async {
  final apiService = ref.watch(radioApiServiceProvider);
  final selectedCountry = ref.watch(selectedCountryProvider);

  try {
    final response = await apiService.getPopularRadios(
      page: 1,
      pageSize: 20, // 首页推荐电台数量
      countryId: selectedCountry?.id,
    );

    return RadioDataAdapter.radioStationListToStationSimpleList(response.list);
  } catch (e) {
    throw Exception('获取推荐电台失败: $e');
  }
});

/// 首页专用推荐区域Provider - 独立管理，不受Tab切换影响
/// 推荐通知器（支持预加载缓存）
class RecommendationNotifier extends StateNotifier<RadioListState> {
  final RadioApiService _apiService;
  final RecommendationPreloadCacheNotifier _preloadCache;
  final String _recommendationType;
  static const int _pageSize = 20;

  RecommendationNotifier(this._apiService, this._preloadCache, this._recommendationType) 
    : super(const RadioListState());

  /// 加载热门推荐（支持预加载缓存）
  Future<void> loadPopularRadios({
    bool refresh = false,
    int? countryId,
    String? stateName,
    bool forceRefresh = false,
    List<int>? tagIds,
  }) async {
    // 检查预加载缓存
    if (!forceRefresh && tagIds != null && tagIds.isNotEmpty) {
      final cachedData = _preloadCache.getRecommendationData(tagIds.first.toString(), 'hot');
      if (cachedData != null && cachedData.stations.isNotEmpty) {
        print('🚀 使用热门推荐预加载缓存：分类${tagIds.first} (${cachedData.stations.length}个电台)');
        this.state = cachedData;
        return;
      }
    }

    // 使用原有逻辑，缓存键包含省州信息
    var cacheQuery = tagIds != null && tagIds.isNotEmpty 
        ? 'popular_tags_${tagIds.join('_')}' 
        : 'popular_all';
    if (stateName != null && stateName.isNotEmpty) {
      cacheQuery += '_state_$stateName';
    }
    
    if (!forceRefresh && !refresh && this.state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('使用缓存数据：热门推荐电台 - $cacheQuery');
      return;
    }

    if (this.state.isLoading) return;

    if (refresh) {
      this.state = const RadioListState(isLoading: true);
    } else {
      this.state = this.state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : this.state.currentPage;
      print('📤 加载热门电台 - 国家ID: $countryId, 省州: $stateName, 标签: $tagIds');
      final response = await _apiService.getPopularRadios(
        page: page,
        pageSize: _pageSize,
        countryId: countryId,
        state: stateName,
        tagIds: tagIds,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(response.list);
      final allStations = refresh ? newStations : [...this.state.stations, ...newStations];

      this.state = this.state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      this.state = this.state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 加载高质量推荐（支持预加载缓存）
  Future<void> loadHighQualityRadios({
    bool refresh = false,
    int? countryId,
    bool forceRefresh = false,
    List<int>? tagIds,
  }) async {
    // 检查预加载缓存
    if (!forceRefresh && tagIds != null && tagIds.isNotEmpty) {
      final cachedData = _preloadCache.getRecommendationData(tagIds.first.toString(), 'high_quality');
      if (cachedData != null && cachedData.stations.isNotEmpty) {
        print('🚀 使用高质量推荐预加载缓存：分类${tagIds.first} (${cachedData.stations.length}个电台)');
        state = cachedData;
        return;
      }
    }

    // 使用原有逻辑
    final cacheQuery = tagIds != null && tagIds.isNotEmpty 
        ? 'high_quality_tags_${tagIds.join('_')}' 
        : 'high_quality_all';
    
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('使用缓存数据：高质量电台 - $cacheQuery');
      return;
    }

    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.getRadioList(RadioListRequest(
        page: page,
        pageSize: _pageSize * 2, // 获取更多数据用于筛选
        countryId: countryId,
        tagIds: tagIds,
        sortBy: 'click_count', // 使用支持的排序字段
        sortOrder: 'desc',
      ));

      // 筛选高质量电台
      var filteredStations = RadioDataAdapter.radioStationListToStationSimpleList(response.list)
          .where((station) {
            // 筛选条件：
            // 1. 投票数较高（说明受欢迎）
            // 2. 有homepage（说明是正规电台）
            // 3. 有favicon（说明维护良好）
            // 4. 国家来源可靠
            return station.votes > 50 && 
                   station.homepage.isNotEmpty &&
                   station.favicon.isNotEmpty &&
                   station.name.length > 3; // 名称不能太短
          }).take(_pageSize).toList();

      print('🎯 高质量电台筛选: 原始${response.list.length}个 -> 筛选后${filteredStations.length}个');

      final newStations = filteredStations;
      final allStations = refresh ? newStations : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 加载最新推荐（支持预加载缓存）
  Future<void> loadLatestRadios({
    bool refresh = false,
    int? countryId,
    bool forceRefresh = false,
    List<int>? tagIds,
  }) async {
    // 检查预加载缓存
    if (!forceRefresh && tagIds != null && tagIds.isNotEmpty) {
      final cachedData = _preloadCache.getRecommendationData(tagIds.first.toString(), 'latest');
      if (cachedData != null && cachedData.stations.isNotEmpty) {
        print('🚀 使用最新推荐预加载缓存：分类${tagIds.first} (${cachedData.stations.length}个电台)');
        state = cachedData;
        return;
      }
    }

    // 使用原有逻辑
    final cacheQuery = tagIds != null && tagIds.isNotEmpty 
        ? 'latest_tags_${tagIds.join('_')}' 
        : 'latest_all';
    
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('使用缓存数据：最新电台 - $cacheQuery');
      return;
    }

    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.getLatestRadios(
        page: page,
        pageSize: _pageSize,
        countryId: countryId,
        tagIds: tagIds,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(response.list);
      final allStations = refresh ? newStations : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 按国家加载电台（点击最高，支持预加载缓存）
  Future<void> loadRadiosByCountry({
    required int countryId,
    bool refresh = false,
    bool forceRefresh = false,
    String sortBy = 'votes',
    String sortOrder = 'desc',
    List<int>? tagIds,
  }) async {
    // 检查预加载缓存（仅当是点击最高排序时）
    if (!forceRefresh && sortBy == 'click_count' && tagIds != null && tagIds.isNotEmpty) {
      final cachedData = _preloadCache.getRecommendationData(tagIds.first.toString(), 'most_clicked');
      if (cachedData != null && cachedData.stations.isNotEmpty) {
        print('🚀 使用点击最高推荐预加载缓存：分类${tagIds.first} (${cachedData.stations.length}个电台)');
        state = cachedData;
        return;
      }
    }

    // 使用原有逻辑
    final cacheQuery = tagIds != null && tagIds.isNotEmpty 
        ? 'country_${countryId}_tags_${tagIds.join('_')}' 
        : 'country_$countryId';
    
    if (!forceRefresh && !refresh && state.isCacheValid(countryId: countryId, query: cacheQuery)) {
      print('使用缓存数据：按国家电台 - $cacheQuery');
      return;
    }

    if (state.isLoading) return;

    if (refresh) {
      state = const RadioListState(isLoading: true);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _apiService.getRadiosByCountry(
        countryId: countryId,
        page: page,
        pageSize: _pageSize,
        sortBy: sortBy,
        sortOrder: sortOrder,
        tagIds: tagIds,
      );

      final newStations = RadioDataAdapter.radioStationListToStationSimpleList(response.list);
      final allStations = refresh ? newStations : [...state.stations, ...newStations];

      state = state.copyWith(
        stations: allStations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: page + 1,
        error: null,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: cacheQuery,
        ),
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }
}

/// 使用全局Provider避免重建时重新加载数据
final homeRecommendationHotProvider = StateNotifierProvider<RecommendationNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  final preloadCache = ref.watch(recommendationPreloadCacheProvider.notifier);
  return RecommendationNotifier(apiService, preloadCache, 'hot');
});

final homeRecommendationMostClickedProvider = StateNotifierProvider<RecommendationNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  final preloadCache = ref.watch(recommendationPreloadCacheProvider.notifier);
  return RecommendationNotifier(apiService, preloadCache, 'most_clicked');
});

final homeRecommendationHighQualityProvider = StateNotifierProvider<RecommendationNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  final preloadCache = ref.watch(recommendationPreloadCacheProvider.notifier);
  return RecommendationNotifier(apiService, preloadCache, 'high_quality');
});

final homeRecommendationLatestProvider = StateNotifierProvider<RecommendationNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  final preloadCache = ref.watch(recommendationPreloadCacheProvider.notifier);
  return RecommendationNotifier(apiService, preloadCache, 'latest');
});

/// 感知国家的热门电台提供者
final countryAwarePopularRadiosProvider = StateNotifierProvider<RadioListNotifier, RadioListState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  final notifier = RadioListNotifier(apiService);
  
  // 监听国家变化，自动刷新电台列表
  ref.listen(selectedCountryProvider, (previous, next) {
    if (previous != next && next != null) {
      notifier.loadPopularRadios(
        refresh: true,
        countryId: next.id,
      );
    }
  });
  
  return notifier;
});

/// 预加载缓存状态
class PreloadCacheState {
  final Map<String, RadioListState> categoryCache; // 分类名 -> 电台数据
  final bool isPreloading; // 是否正在预加载
  final int? currentCountryId; // 当前国家ID
  final DateTime? lastPreloadTime; // 最后预加载时间
  final Set<String> loadingCategories; // 正在加载的分类
  final Set<String> completedCategories; // 已完成加载的分类
  final double preloadProgress; // 预加载进度 0.0-1.0
  final String? error; // 预加载错误信息

  const PreloadCacheState({
    this.categoryCache = const {},
    this.isPreloading = false,
    this.currentCountryId,
    this.lastPreloadTime,
    this.loadingCategories = const {},
    this.completedCategories = const {},
    this.preloadProgress = 0.0,
    this.error,
  });

  PreloadCacheState copyWith({
    Map<String, RadioListState>? categoryCache,
    bool? isPreloading,
    int? currentCountryId,
    DateTime? lastPreloadTime,
    Set<String>? loadingCategories,
    Set<String>? completedCategories,
    double? preloadProgress,
    String? error,
  }) {
    return PreloadCacheState(
      categoryCache: categoryCache ?? this.categoryCache,
      isPreloading: isPreloading ?? this.isPreloading,
      currentCountryId: currentCountryId ?? this.currentCountryId,
      lastPreloadTime: lastPreloadTime ?? this.lastPreloadTime,
      loadingCategories: loadingCategories ?? this.loadingCategories,
      completedCategories: completedCategories ?? this.completedCategories,
      preloadProgress: preloadProgress ?? this.preloadProgress,
      error: error ?? this.error,
    );
  }

  /// 检查缓存是否有效（30分钟）
  bool get isCacheValid {
    if (lastPreloadTime == null || categoryCache.isEmpty) return false;
    return DateTime.now().difference(lastPreloadTime!) < const Duration(minutes: 30);
  }

  /// 获取指定分类的缓存数据
  RadioListState? getCategoryCache(String categoryName) {
    return categoryCache[categoryName];
  }

  /// 检查指定分类是否已缓存
  bool hasCategoryCache(String categoryName) {
    return categoryCache.containsKey(categoryName) &&
           categoryCache[categoryName]!.stations.isNotEmpty;
  }
}

/// 预加载缓存控制器
class PreloadCacheNotifier extends StateNotifier<PreloadCacheState> {
  final RadioApiService _apiService;
  static const int _maxConcurrentLoads = 3; // 最大并发加载数
  static const int _stationsPerCategory = 100; // 每个分类加载的电台数量 - 修正为100个，符合后端限制

  PreloadCacheNotifier(this._apiService) : super(const PreloadCacheState());

  /// 开始预加载所有分类数据
  Future<void> preloadAllCategories(List<RadioCategory> categories, int? countryId) async {
    // 更严格的重复检查
    if (state.isPreloading ||
        (state.currentCountryId == countryId && state.categoryCache.isNotEmpty)) {
      print('⚠️ 预加载已在进行中或已完成，跳过重复请求');
      return;
    }

    print('🚀 开始预加载所有分类数据，国家ID: $countryId, 分类数量: ${categories.length}');

    state = state.copyWith(
      isPreloading: true,
      currentCountryId: countryId,
      loadingCategories: {},
      completedCategories: {},
      preloadProgress: 0.0,
      error: null,
    );

    try {
      int completedCount = 0;

      // 分批并发加载，避免同时发起太多请求
      final batches = <List<RadioCategory>>[];
      for (int i = 0; i < categories.length; i += _maxConcurrentLoads) {
        final end = (i + _maxConcurrentLoads).clamp(0, categories.length);
        batches.add(categories.sublist(i, end));
      }

      for (final batch in batches) {
        final futures = batch.map((category) =>
          _loadCategoryData(category, countryId).then((result) {
            if (result != null) {
              completedCount++;

              // 实时更新状态，确保缓存立即可用
              final progress = completedCount / categories.length;
              final updatedCache = Map<String, RadioListState>.from(state.categoryCache);
              updatedCache[category.id.toString()] = result; // 使用category.id作为key

              final updatedCompleted = Set<String>.from(state.completedCategories);
              updatedCompleted.add(category.id.toString()); // 使用category.id

              // print('💾 预加载缓存存储: ${category.name} (ID: ${category.id}) -> ${result.stations.length} 个电台');

              state = state.copyWith(
                categoryCache: updatedCache,
                completedCategories: updatedCompleted,
                preloadProgress: progress,
              );

              // 只在关键节点打印日志，减少输出
              if (completedCount % 3 == 0 || completedCount == categories.length) {
                // print('✅ 预加载进度: $completedCount/${categories.length}');
              }
            }
            return result;
          }).catchError((error) {
            print('❌ 分类 ${category.name} (ID: ${category.id}) 预加载失败: $error');
            return null;
          })
        ).toList();

        // 等待当前批次完成
        await Future.wait(futures);
      }

      // 最终状态更新
      state = state.copyWith(
        isPreloading: false,
        lastPreloadTime: DateTime.now(),
        preloadProgress: 1.0,
      );

      // print('🎉 所有分类预加载完成！成功加载 ${state.completedCategories.length}/${categories.length} 个分类');
      // print('📦 预加载缓存内容: ${state.categoryCache.keys.toList()}');
      for (final entry in state.categoryCache.entries) {
        print('   - ${entry.key}: ${entry.value.stations.length} 个电台');
      }

    } catch (e) {
      print('❌ 预加载过程中发生错误: $e');
      state = state.copyWith(
        isPreloading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载单个分类的数据
  Future<RadioListState?> _loadCategoryData(RadioCategory category, int? countryId) async {
    try {
      // 减少日志输出
      // print('📡 开始加载分类: ${category.name}');

      // 修改：使用getRadiosByCategory而不是searchRadios
      final response = await _apiService.getRadiosByCategory(
        tagIds: [category.id], // 使用标签ID
        page: 1,
        pageSize: _stationsPerCategory,
        countryId: countryId,
      );

      final stations = RadioDataAdapter.radioStationListToStationSimpleList(response.list);

      return RadioListState(
        stations: stations,
        isLoading: false,
        hasMore: response.page.hasNextPage,
        currentPage: 2,
        cacheInfo: CacheInfo(
          timestamp: DateTime.now(),
          countryId: countryId,
          query: category.id.toString(), // 使用ID作为查询key
        ),
      );
    } catch (e) {
      print('❌ 加载分类 ${category.name} 失败: $e');
      return null;
    }
  }

  /// 清空缓存（国家切换时调用）
  void clearCache() {
    print('🗑️ 清空预加载缓存');
    state = const PreloadCacheState();
  }

  /// 获取指定分类的缓存数据
  RadioListState? getCategoryData(String categoryName) {
    final result = state.getCategoryCache(categoryName);
    print('🔍 查找预加载缓存: $categoryName, 找到: ${result != null}, 缓存键: ${state.categoryCache.keys.toList()}');
    return result;
  }

  /// 检查是否有指定分类的缓存
  bool hasCategoryData(String categoryName) {
    return state.hasCategoryCache(categoryName);
  }
}

/// 预加载缓存提供者
final preloadCacheProvider = StateNotifierProvider<PreloadCacheNotifier, PreloadCacheState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  final notifier = PreloadCacheNotifier(apiService);

  // 监听国家变化，自动清空缓存并重新预加载
  ref.listen(selectedCountryProvider, (previous, next) {
    if (previous?.id != next?.id) {
      print('🌍 国家变更: ${previous?.name} -> ${next?.name}');
      notifier.clearCache();

      // 延迟一下再预加载，确保分类数据已加载
      Future.delayed(const Duration(milliseconds: 500), () {
        final categoriesState = ref.read(dynamicCategoriesProvider);
        if (categoriesState.categories.isNotEmpty) {
          notifier.preloadAllCategories(categoriesState.categories, next?.id);
        }
      });
    }
  });

  return notifier;
});

/// 推荐预加载缓存状态
class RecommendationPreloadCacheState {
  final Map<String, Map<String, RadioListState>> categoryRecommendationCache; // 分类ID -> 推荐类型 -> 数据
  final bool isPreloading;
  final int? currentCountryId;
  final DateTime? lastPreloadTime;
  final Set<String> loadingCategories;
  final Set<String> completedCategories;
  final double preloadProgress;
  final String? error;

  const RecommendationPreloadCacheState({
    this.categoryRecommendationCache = const {},
    this.isPreloading = false,
    this.currentCountryId,
    this.lastPreloadTime,
    this.loadingCategories = const {},
    this.completedCategories = const {},
    this.preloadProgress = 0.0,
    this.error,
  });

  RecommendationPreloadCacheState copyWith({
    Map<String, Map<String, RadioListState>>? categoryRecommendationCache,
    bool? isPreloading,
    int? currentCountryId,
    DateTime? lastPreloadTime,
    Set<String>? loadingCategories,
    Set<String>? completedCategories,
    double? preloadProgress,
    String? error,
  }) {
    return RecommendationPreloadCacheState(
      categoryRecommendationCache: categoryRecommendationCache ?? this.categoryRecommendationCache,
      isPreloading: isPreloading ?? this.isPreloading,
      currentCountryId: currentCountryId ?? this.currentCountryId,
      lastPreloadTime: lastPreloadTime ?? this.lastPreloadTime,
      loadingCategories: loadingCategories ?? this.loadingCategories,
      completedCategories: completedCategories ?? this.completedCategories,
      preloadProgress: preloadProgress ?? this.preloadProgress,
      error: error ?? this.error,
    );
  }

  /// 检查缓存是否有效（30分钟）
  bool get isCacheValid {
    if (lastPreloadTime == null || categoryRecommendationCache.isEmpty) return false;
    return DateTime.now().difference(lastPreloadTime!) < const Duration(minutes: 30);
  }

  /// 获取指定分类和推荐类型的缓存数据
  RadioListState? getRecommendationCache(String categoryId, String recommendationType) {
    return categoryRecommendationCache[categoryId]?[recommendationType];
  }

  /// 检查指定分类和推荐类型是否已缓存
  bool hasRecommendationCache(String categoryId, String recommendationType) {
    final categoryCache = categoryRecommendationCache[categoryId];
    if (categoryCache == null) return false;
    final recommendationCache = categoryCache[recommendationType];
    return recommendationCache != null && recommendationCache.stations.isNotEmpty;
  }
}

/// 推荐预加载缓存通知器
class RecommendationPreloadCacheNotifier extends StateNotifier<RecommendationPreloadCacheState> {
  final RadioApiService _apiService;
  static const int _maxConcurrentLoads = 2; // 最大并发加载数
  static const int _stationsPerRecommendation = 20; // 每个推荐类型加载的电台数量

  RecommendationPreloadCacheNotifier(this._apiService) : super(const RecommendationPreloadCacheState());

  /// 开始预加载所有分类的推荐数据
  Future<void> preloadAllRecommendations(List<RadioCategory> categories, int? countryId) async {
    // 检查是否需要预加载
    if (state.isPreloading ||
        (state.currentCountryId == countryId && state.categoryRecommendationCache.isNotEmpty)) {
      print('⚠️ 推荐预加载已在进行中或已完成，跳过重复请求');
      return;
    }

    print('🚀 开始预加载所有分类的推荐数据，国家ID: $countryId, 分类数量: ${categories.length}');

    state = state.copyWith(
      isPreloading: true,
      currentCountryId: countryId,
      loadingCategories: {},
      completedCategories: {},
      preloadProgress: 0.0,
      error: null,
    );

    try {
      int completedCount = 0;
      final totalTasks = categories.length * 4; // 每个分类有4种推荐类型

      // 分批并发加载
      final batches = <List<RadioCategory>>[];
      for (int i = 0; i < categories.length; i += _maxConcurrentLoads) {
        final end = (i + _maxConcurrentLoads).clamp(0, categories.length);
        batches.add(categories.sublist(i, end));
      }

      for (final batch in batches) {
        final futures = batch.map((category) =>
          _loadCategoryRecommendations(category, countryId).then((results) {
            if (results.isNotEmpty) {
              completedCount += 4; // 每个分类完成4种推荐

              // 更新缓存
              final updatedCache = Map<String, Map<String, RadioListState>>.from(state.categoryRecommendationCache);
              updatedCache[category.id.toString()] = results;

              final updatedCompleted = Set<String>.from(state.completedCategories);
              updatedCompleted.add(category.id.toString());

              final progress = completedCount / totalTasks;

              print('💾 推荐预加载缓存存储: ${category.name} (ID: ${category.id}) -> 4种推荐类型');

              state = state.copyWith(
                categoryRecommendationCache: updatedCache,
                completedCategories: updatedCompleted,
                preloadProgress: progress,
              );

              // 进度日志
              if (updatedCompleted.length % 2 == 0 || updatedCompleted.length == categories.length) {
                print('✅ 推荐预加载进度: ${updatedCompleted.length}/${categories.length}');
              }
            }
            return results;
          }).catchError((error) {
            print('❌ 分类 ${category.name} (ID: ${category.id}) 推荐预加载失败: $error');
            return <String, RadioListState>{};
          })
        ).toList();

        // 等待当前批次完成
        await Future.wait(futures);
      }

      // 最终状态更新
      state = state.copyWith(
        isPreloading: false,
        lastPreloadTime: DateTime.now(),
        preloadProgress: 1.0,
      );

      print('🎉 所有分类推荐预加载完成！成功加载 ${state.completedCategories.length}/${categories.length} 个分类');

    } catch (e) {
      print('❌ 推荐预加载过程中发生错误: $e');
      state = state.copyWith(
        isPreloading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载单个分类的所有推荐数据
  Future<Map<String, RadioListState>> _loadCategoryRecommendations(RadioCategory category, int? countryId) async {
    final results = <String, RadioListState>{};

    try {
      // 并行加载四种推荐类型
      final futures = await Future.wait([
        // 热门推荐
        _apiService.getPopularRadios(
          page: 1,
          pageSize: _stationsPerRecommendation,
          countryId: countryId,
          tagIds: [category.id],
        ),
        // 点击最高
        _apiService.getRadiosByCountry(
          countryId: countryId ?? 1,
          page: 1,
          pageSize: _stationsPerRecommendation,
          sortBy: 'click_count',
          sortOrder: 'desc',
          tagIds: [category.id],
        ),
        // 高质量：按点击数排序，但只要bitrate >= 128的高质量电台
        _apiService.getRadioList(RadioListRequest(
          page: 1,
          pageSize: _stationsPerRecommendation * 2, // 获取更多数据用于筛选
          countryId: countryId,
          tagIds: [category.id],
          sortBy: 'click_count', // 使用支持的排序字段
          sortOrder: 'desc',
        )),
        // 最新上架
        _apiService.getLatestRadios(
          page: 1,
          pageSize: _stationsPerRecommendation,
          countryId: countryId,
          tagIds: [category.id],
        ),
      ]);

      // 转换为RadioListState
      final recommendationTypes = ['hot', 'most_clicked', 'high_quality', 'latest'];
      for (int i = 0; i < futures.length; i++) {
        final response = futures[i];
        var stations = RadioDataAdapter.radioStationListToStationSimpleList(response.list);
        
        // 特殊处理高质量推荐：筛选出高质量的电台
        if (recommendationTypes[i] == 'high_quality') {
          stations = stations.where((station) {
            // 筛选条件：
            // 1. 投票数较高（说明受欢迎）
            // 2. 有homepage（说明是正规电台）
            // 3. 有favicon（说明维护良好）
            // 4. 国家来源可靠
            return station.votes > 50 && 
                   station.homepage.isNotEmpty &&
                   station.favicon.isNotEmpty &&
                   station.name.length > 3; // 名称不能太短
          }).take(_stationsPerRecommendation).toList();
          
          print('🎯 高质量电台筛选: 原始${response.list.length}个 -> 筛选后${stations.length}个');
        }
        
        results[recommendationTypes[i]] = RadioListState(
          stations: stations,
          isLoading: false,
          hasMore: response.page.hasNextPage,
          currentPage: 2,
          cacheInfo: CacheInfo(
            timestamp: DateTime.now(),
            countryId: countryId,
            query: '${category.id}_${recommendationTypes[i]}',
          ),
        );
      }

    } catch (e) {
      print('❌ 加载分类 ${category.name} 推荐数据失败: $e');
    }

    return results;
  }

  /// 清空缓存
  void clearCache() {
    print('🗑️ 清空推荐预加载缓存');
    state = const RecommendationPreloadCacheState();
  }

  /// 获取指定分类和推荐类型的缓存数据
  RadioListState? getRecommendationData(String categoryId, String recommendationType) {
    final result = state.getRecommendationCache(categoryId, recommendationType);
    print('🔍 查找推荐预加载缓存: $categoryId-$recommendationType, 找到: ${result != null}');
    return result;
  }
}

/// 推荐预加载缓存提供者
final recommendationPreloadCacheProvider = StateNotifierProvider<RecommendationPreloadCacheNotifier, RecommendationPreloadCacheState>((ref) {
  final apiService = ref.watch(radioApiServiceProvider);
  final notifier = RecommendationPreloadCacheNotifier(apiService);

  // 监听国家变化，自动清空缓存并重新预加载
  ref.listen(selectedCountryProvider, (previous, next) {
    if (previous?.id != next?.id) {
      print('🌍 推荐预加载：国家变更: ${previous?.name} -> ${next?.name}');
      notifier.clearCache();

      // 延迟一下再预加载，确保分类数据已加载
      Future.delayed(const Duration(milliseconds: 800), () {
        final categoriesState = ref.read(dynamicCategoriesProvider);
        if (categoriesState.categories.isNotEmpty) {
          notifier.preloadAllRecommendations(categoriesState.categories, next?.id);
        }
      });
    }
  });

  return notifier;
});

/// 全局数据刷新通知提供者
final dataRefreshNotifierProvider = StateNotifierProvider<DataRefreshNotifier, int>((ref) {
  return DataRefreshNotifier();
});

/// 数据刷新通知器
class DataRefreshNotifier extends StateNotifier<int> {
  DataRefreshNotifier() : super(0);

  /// 触发全局数据刷新
  void triggerRefresh() {
    state = state + 1;
  }
}
