import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';

import '../models/models.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';
import '../utils/text_overflow_handler.dart';
import '../utils/station_image_builder.dart';
import 'audio_visualizer.dart';


/// 全局迷你播放器组件
class MiniPlayer extends ConsumerWidget {
  const MiniPlayer({
    super.key,
    this.onTap,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentStation = ref.watch(currentStationProvider);
    final playbackAsync = ref.watch(currentPlaybackProvider);
    final favoritesAsync = ref.watch(favoriteStationsProvider);

    // 如果没有当前播放的电台，不显示播放器
    if (currentStation == null) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 80,
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            print('🎵 MiniPlayer tapped');
            onTap?.call();
          },
          borderRadius: BorderRadius.circular(20),
          child: Container(
            decoration: BoxDecoration(
              // 增强的玻璃态背景 - 与主页面统一色系
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF0F0F23).withOpacity(0.85), // 深蓝黑
                  const Color(0xFF1A1A2E).withOpacity(0.80), // 深紫蓝
                  const Color(0xFF16213E).withOpacity(0.75), // 深蓝
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: [
                // 主阴影
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 25,
                  offset: const Offset(0, 10),
                  spreadRadius: 0,
                ),
                // 高光效果
                BoxShadow(
                  color: Colors.white.withOpacity(0.08),
                  blurRadius: 15,
                  offset: const Offset(0, -3),
                  spreadRadius: 0,
                ),
                // 科技感光晕
                BoxShadow(
                  color: const Color(0xFF00FFFF).withOpacity(0.1),
                  blurRadius: 20,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    children: [
                      // 电台封面
                      _buildStationCover(context, currentStation),
                      const SizedBox(width: 16),

                      // 电台信息 - 使用Expanded确保占用剩余空间
                      Expanded(
                        child: _buildStationInfo(context, currentStation, playbackAsync),
                      ),

                      // 收藏按钮 - 阻止事件冒泡
                      GestureDetector(
                        onTap: () async {
                          print('🎵 Favorite button tapped');
                          // 阻止事件冒泡到父容器
                          final storageService = ref.read(storageServiceProvider);
                          final favorites = await ref.read(favoriteStationsProvider.future);
                          final isFavorite = favorites.any((fav) => fav.id == currentStation.id);
                          
                          if (isFavorite) {
                            await storageService.removeFromFavorites(currentStation.id);
                          } else {
                            await storageService.addToFavorites(currentStation);
                          }
                          ref.invalidate(favoriteStationsProvider);
                        },
                        child: _buildFavoriteButton(context, ref, currentStation, favoritesAsync),
                      ),
                      const SizedBox(width: 8),

                      // 播放控制按钮 - 阻止事件冒泡
                      GestureDetector(
                        onTap: () {
                          print('🎵 Play button tapped');
                          // 阻止事件冒泡到父容器
                          final audioService = ref.read(audioServiceProvider);
                          playbackAsync.whenData((playback) {
                            switch (playback.state) {
                              case PlaybackState.playing:
                                audioService.pause();
                                break;
                              case PlaybackState.paused:
                                // 暂停状态：快速恢复播放，无需重新加载资源
                                print('📻 恢复播放 - 使用缓存资源');
                                audioService.resume();
                                break;
                              case PlaybackState.stopped:
                                // 停止状态：重新开始播放，需要重新加载资源
                                print('📻 重新播放 - 加载新资源');
                                audioService.playStation(currentStation);
                                break;
                              case PlaybackState.loading:
                                // 正在加载，不做任何操作
                                break;
                              case PlaybackState.error:
                                // 重试播放
                                audioService.playStation(currentStation);
                                break;
                            }
                          });
                        },
                        child: _buildPlayButton(context, ref, currentStation, playbackAsync),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建电台封面（使用统一的图片构建工具）
  Widget _buildStationCover(BuildContext context, StationSimple station) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: StationImageBuilder.buildStationImage(
        station: station,
        width: 56,
        height: 56,
        fit: BoxFit.cover,
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  /// 构建电台信息
  Widget _buildStationInfo(
    BuildContext context,
    StationSimple station,
    AsyncValue<CurrentPlayback> playbackAsync,
  ) {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 电台名称 - 使用安全文本处理
          TextOverflowHandler.safeText(
            station.name,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.white,
              fontSize: 15,
            ),
            maxLines: 1,
          ),
          const SizedBox(height: 2),

          // 电台详细信息 - 使用Flexible包装整个Row防止溢出
          Flexible(
            child: Row(
              children: [
                // 国家信息
                if (station.country.isNotEmpty) ...[
                  Icon(
                    Icons.public,
                    size: 12,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Flexible(
                    flex: 1,
                    child: TextOverflowHandler.safeText(
                      station.country,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      maxLines: 1,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],

                // 语言信息（替代比特率信息，因为StationSimple没有bitrate属性）
                if (station.language.isNotEmpty) ...[
                  Icon(
                    Icons.language,
                    size: 12,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Flexible(
                    flex: 1,
                    child: TextOverflowHandler.safeText(
                      station.language,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      maxLines: 1,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 2),

          // 播放状态
          playbackAsync.when(
            data: (playback) => _buildStatusIndicator(context, playback, station),
            loading: () => _buildLoadingIndicator(context),
            error: (error, stack) => _buildErrorIndicator(context),
          ),
        ],
      ),
    );
  }

  /// 构建状态指示器
  Widget _buildStatusIndicator(
    BuildContext context,
    CurrentPlayback playback,
    StationSimple station,
  ) {
    String statusText;
    Color statusColor;
    IconData? statusIcon;
    bool showVisualizer = false;

    switch (playback.state) {
      case PlaybackState.loading:
        statusText = 'connecting'.tr();
        statusColor = Colors.orange;
        statusIcon = Icons.sync;
        break;
      case PlaybackState.playing:
        statusText = 'now_playing'.tr();
        showVisualizer = true;
        statusColor = Colors.green;
        statusIcon = Icons.play_circle_filled;
        break;
      case PlaybackState.paused:
        statusText = 'pause'.tr();
        statusColor = Colors.grey;
        statusIcon = Icons.pause_circle_filled;
        break;
      case PlaybackState.error:
        statusText = 'connection_error'.tr();
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case PlaybackState.stopped:
        statusText = station.country;
        statusColor = Colors.grey;
        statusIcon = null;
        break;
    }

    return Flexible(
      child: Row(
        children: [
          if (statusIcon != null) ...[
            Icon(
              statusIcon,
              size: 14,
              color: statusColor,
            ),
            const SizedBox(width: 4),
          ],
          Flexible(
            child: TextOverflowHandler.safeText(
              statusText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
            ),
          ),
          // 音律跳动效果（仅在播放时显示）
          if (showVisualizer) ...[
            const SizedBox(width: 8),
            AudioVisualizer(
              isPlaying: showVisualizer,
              barCount: 5,
              barWidth: 2.5,
              barSpacing: 1.5,
              maxHeight: 16.0,
              minHeight: 4.0,
              color: statusColor,
            ),
          ],
        ],
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 12,
          height: 12,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
        ),
        const SizedBox(width: 8),
        TextOverflowHandler.safeText(
          'loading'.tr(),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).primaryColor,
          ),
          maxLines: 1,
        ),
      ],
    );
  }

  /// 构建错误指示器
  Widget _buildErrorIndicator(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.error,
          size: 14,
          color: Colors.red[600],
        ),
        const SizedBox(width: 4),
        TextOverflowHandler.safeText(
          'error'.tr(),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.red[600],
          ),
          maxLines: 1,
        ),
      ],
    );
  }

  /// 构建收藏按钮
  Widget _buildFavoriteButton(
    BuildContext context,
    WidgetRef ref,
    StationSimple station,
    AsyncValue<List<StationSimple>> favoritesAsync,
  ) {
    return favoritesAsync.when(
      data: (favorites) {
        final isFavorite = favorites.any((fav) => fav.id == station.id);
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          child: IconButton(
            onPressed: () async {
              final storageService = ref.read(storageServiceProvider);
              if (isFavorite) {
                await storageService.removeFromFavorites(station.id);
              } else {
                await storageService.addToFavorites(station);
              }
              ref.invalidate(favoriteStationsProvider);
            },
            icon: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                key: ValueKey(isFavorite),
                color: isFavorite ? Colors.red : Colors.grey[600],
                size: 24,
              ),
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      },
      loading: () => IconButton(
        onPressed: null,
        icon: Icon(
          Icons.favorite_border,
          color: Colors.grey[400],
          size: 24,
        ),
      ),
      error: (error, stack) => IconButton(
        onPressed: null,
        icon: Icon(
          Icons.favorite_border,
          color: Colors.grey[400],
          size: 24,
        ),
      ),
    );
  }

  /// 构建播放按钮
  Widget _buildPlayButton(
    BuildContext context,
    WidgetRef ref,
    StationSimple station,
    AsyncValue<CurrentPlayback> playbackAsync,
  ) {
    return playbackAsync.when(
      data: (playback) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: IconButton(
            onPressed: () {
              final audioService = ref.read(audioServiceProvider);
              switch (playback.state) {
                case PlaybackState.playing:
                  audioService.pause();
                  break;
                case PlaybackState.paused:
                  // 暂停状态：快速恢复播放，无需重新加载资源
                  print('📻 恢复播放 - 使用缓存资源');
                  audioService.resume();
                  break;
                case PlaybackState.stopped:
                  // 停止状态：重新开始播放，需要重新加载资源
                  print('📻 重新播放 - 加载新资源');
                  audioService.playStation(station);
                  break;
                case PlaybackState.loading:
                  // 正在加载，不做任何操作
                  break;
                case PlaybackState.error:
                  // 重试播放
                  audioService.playStation(station);
                  break;
              }
            },
            icon: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: _getPlayButtonIcon(playback.state),
            ),
            iconSize: 28,
            style: IconButton.styleFrom(
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(12),
            ),
          ),
        );
      },
      loading: () => Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(20),
        ),
        child: IconButton(
          onPressed: null,
          icon: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
            ),
          ),
          iconSize: 28,
        ),
      ),
      error: (error, stack) => Container(
        decoration: BoxDecoration(
          color: Colors.red[400],
          borderRadius: BorderRadius.circular(20),
        ),
        child: IconButton(
          onPressed: () {
            ref.read(audioServiceProvider).playStation(station);
          },
          icon: const Icon(Icons.refresh, color: Colors.white),
          iconSize: 28,
        ),
      ),
    );
  }

  /// 根据播放状态获取播放按钮图标
  Widget _getPlayButtonIcon(PlaybackState state) {
    switch (state) {
      case PlaybackState.playing:
        return const Icon(
          Icons.pause,
          key: ValueKey('pause'),
          color: Colors.white,
        );
      case PlaybackState.loading:
        return SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        );
      case PlaybackState.error:
        return const Icon(
          Icons.refresh,
          key: ValueKey('refresh'),
          color: Colors.white,
        );
      case PlaybackState.stopped:
      case PlaybackState.paused:
        return const Icon(
          Icons.play_arrow,
          key: ValueKey('play'),
          color: Colors.white,
        );
    }
  }
}
