import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/radio_models.dart';
import '../providers/country_provider.dart';

/// 现代化的国家选择器组件
class CountrySelector extends ConsumerStatefulWidget {
  /// 是否在顶部导航栏中显示（影响样式）
  final bool isInAppBar;
  
  /// 自定义宽度
  final double? width;
  
  /// 选择回调
  final ValueChanged<Country>? onCountrySelected;

  const CountrySelector({
    super.key,
    this.isInAppBar = false,
    this.width,
    this.onCountrySelected,
  });

  @override
  ConsumerState<CountrySelector> createState() => _CountrySelectorState();
}

class _CountrySelectorState extends ConsumerState<CountrySelector> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(countrySelectionProvider);
    final selectedCountry = state.selectedCountry;
    
    if (state.isLoading && state.countries.isEmpty) {
      return _buildLoadingWidget();
    }
    
    if (state.error != null && state.countries.isEmpty) {
      return _buildErrorWidget(state.error!);
    }

    return Container(
      width: widget.width,
      child: widget.isInAppBar 
        ? _buildAppBarSelector(selectedCountry, state)
        : _buildStandardSelector(selectedCountry, state),
    );
  }

  /// 构建AppBar中的选择器（紧凑样式）
  Widget _buildAppBarSelector(Country? selectedCountry, CountrySelectionState state) {
    return GestureDetector(
      onTap: () => _showCountryBottomSheet(context, state.countries),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (selectedCountry != null) ...[
              _buildCountryFlag(selectedCountry.code),
              const SizedBox(width: 6),
              Text(
                selectedCountry.code,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
              ),
            ] else
              const Text(
                '选择',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 13,
                ),
              ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              color: Colors.white.withOpacity(0.8),
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标准选择器（完整样式）
  Widget _buildStandardSelector(Country? selectedCountry, CountrySelectionState state) {
    return Material(
      elevation: 2,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: () => _showCountryBottomSheet(context, state.countries),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).primaryColor.withOpacity(0.1),
                Theme.of(context).primaryColor.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).primaryColor.withOpacity(0.2),
              width: 1.5,
            ),
          ),
          child: Row(
            children: [
              if (selectedCountry != null) ...[
                _buildCountryFlag(selectedCountry.code),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    selectedCountry.name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ] else
                Expanded(
                  child: Text(
                    'choose country',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              Icon(
                Icons.keyboard_arrow_down,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建国家旗帜图标
  Widget _buildCountryFlag(String countryCode) {
    return Container(
      width: 28,
      height: 20,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 0.5,
        ),
      ),
      child: Center(
        child: Text(
          _getCountryFlag(countryCode),
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }

  /// 获取国家旗帜表情符号
  String _getCountryFlag(String countryCode) {
    if (countryCode.length != 2) return '🏳️';
    
    // 将国家代码转换为对应的旗帜表情符号
    final codePoints = countryCode.toUpperCase().codeUnits
        .map((code) => 0x1F1E6 + (code - 0x41))
        .toList();
    
    return String.fromCharCodes(codePoints);
  }

  /// 构建加载中组件
  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: widget.isInAppBar
        ? SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2, 
              color: Colors.white.withOpacity(0.8)
            ),
          )
        : const Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 8),
              Text('loading...'),
            ],
          ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget(String error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: widget.isInAppBar
        ? Icon(Icons.error, color: Colors.white.withOpacity(0.8), size: 20)
        : Row(
            children: [
              const Icon(Icons.error, color: Colors.red, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'load failed',
                  style: TextStyle(color: Colors.red.shade600),
                ),
              ),
            ],
          ),
    );
  }

  /// 显示现代化的国家选择底部弹窗
  void _showCountryBottomSheet(BuildContext context, List<Country> countries) {
    showModalBottomSheet<Country>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ModernCountryBottomSheet(
        countries: countries,
        onCountrySelected: _onCountrySelected,
      ),
    );
  }

  /// 国家选择回调
  void _onCountrySelected(Country country) {
    ref.read(countrySelectionProvider.notifier).selectCountry(country);
    widget.onCountrySelected?.call(country);
    
    // 清除搜索
    ref.read(countrySelectionProvider.notifier).clearSearchQuery();
  }
}

/// 现代化的国家选择底部弹窗
class ModernCountryBottomSheet extends ConsumerStatefulWidget {
  final List<Country> countries;
  final ValueChanged<Country> onCountrySelected;

  const ModernCountryBottomSheet({
    super.key,
    required this.countries,
    required this.onCountrySelected,
  });

  @override
  ConsumerState<ModernCountryBottomSheet> createState() => _ModernCountryBottomSheetState();
}

class _ModernCountryBottomSheetState extends ConsumerState<ModernCountryBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredCountries = ref.watch(filteredCountriesProvider);
    
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              // 拖拽指示器和标题栏
              _buildHeader(),
              
              // 搜索框
              _buildSearchSection(),
              
              // 国家列表
              Expanded(
                child: _buildCountryList(filteredCountries, scrollController),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建头部区域
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          
          // 标题和关闭按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'choose country',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: Colors.grey.shade600,
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey.shade100,
                    shape: const CircleBorder(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索区域
  Widget _buildSearchSection() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
        child: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'search country',
            hintStyle: TextStyle(color: Colors.grey.shade500),
            prefixIcon: Icon(
              Icons.search,
              color: Colors.grey.shade500,
            ),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      _searchController.clear();
                      ref.read(countrySelectionProvider.notifier)
                          .clearSearchQuery();
                    },
                    icon: Icon(
                      Icons.clear,
                      color: Colors.grey.shade500,
                    ),
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          onChanged: (value) {
            ref.read(countrySelectionProvider.notifier).updateSearchQuery(value);
            setState(() {}); // 更新suffixIcon
          },
        ),
      ),
    );
  }

  /// 构建国家列表
  Widget _buildCountryList(List<Country> countries, ScrollController scrollController) {
    if (countries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'no matching country',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'try other keywords',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: countries.length,
      itemBuilder: (context, index) {
        final country = countries[index];
        return _buildCountryItem(country);
      },
    );
  }

  /// 构建国家项
  Widget _buildCountryItem(Country country) {
    final selectedCountry = ref.watch(selectedCountryProvider);
    final isSelected = selectedCountry?.id == country.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected 
            ? Theme.of(context).primaryColor.withOpacity(0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected 
              ? Theme.of(context).primaryColor.withOpacity(0.3)
              : Colors.transparent,
          width: 1.5,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        leading: Container(
          width: 40,
          height: 28,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 0.5,
            ),
          ),
          child: Center(
            child: Text(
              _getCountryFlag(country.code),
              style: const TextStyle(fontSize: 20),
            ),
          ),
        ),
        title: Text(
          country.name,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected 
                ? Theme.of(context).primaryColor
                : null,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              country.code,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (isSelected) ...[
              const SizedBox(width: 8),
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ],
          ],
        ),
        onTap: () {
          widget.onCountrySelected(country);
          Navigator.of(context).pop();
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  String _getCountryFlag(String countryCode) {
    if (countryCode.length != 2) return '🏳️';
    
    final codePoints = countryCode.toUpperCase().codeUnits
        .map((code) => 0x1F1E6 + (code - 0x41))
        .toList();
    
    return String.fromCharCodes(codePoints);
  }
} 