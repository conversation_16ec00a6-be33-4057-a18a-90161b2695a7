import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:math';
import '../models/models.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';
import '../utils/text_overflow_handler.dart';
import '../utils/station_image_builder.dart';
import 'audio_visualizer.dart';
import 'player_banner_ad.dart';

/// 播放器Modal组件 - 从MiniPlayer展开的全屏播放器
class PlayerModal extends ConsumerStatefulWidget {
  const PlayerModal({
    super.key,
    required this.onClose,
    required this.animation,
  });

  final VoidCallback onClose;
  final Animation<double> animation;

  @override
  ConsumerState<PlayerModal> createState() => _PlayerModalState();
}

class _PlayerModalState extends ConsumerState<PlayerModal>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _heartEffectController; // 红心特效动画控制器
  late AnimationController _tutorialController; // 新手引导动画控制器
  late AnimationController _dragController; // 滑动跟随动画控制器
  late AnimationController _feedbackController; // 操作反馈动画控制器

  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _heartScaleAnimation; // 红心缩放动画
  late Animation<double> _heartOpacityAnimation; // 红心透明度动画
  late Animation<double> _tutorialOpacityAnimation; // 引导透明度动画

  late Animation<double> _sideIconScaleAnimation; // 侧边图标缩放动画

  bool _showHeartEffect = false; // 是否显示红心特效
  bool _showTutorial = false; // 是否显示新手引导
  bool _isDragging = false; // 是否正在拖拽
  double _dragOffset = 0.0; // 当前拖拽偏移
  double _dragProgress = 0.0; // 拖拽进度 (-1 to 1)
  bool _hasShownTutorial = false; // 是否已显示过引导

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeCarousel();
    _startEntryAnimation();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 红心特效动画控制器
    _heartEffectController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 新手引导动画控制器
    _tutorialController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // 滑动跟随动画控制器
    _dragController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 操作反馈动画控制器
    _feedbackController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // 红心缩放动画：从小到大再回到0
    _heartScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _heartEffectController,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));

    // 红心透明度动画：淡入然后淡出
    _heartOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _heartEffectController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeIn),
    ));

    // 新手引导透明度动画
    _tutorialOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _tutorialController,
      curve: Curves.easeInOut,
    ));

    // 侧边图标不透明度动画（替代缩放动画）
    _sideIconScaleAnimation = Tween<double>(
      begin: 0.3,  // 从30%不透明度开始
      end: 1.0,    // 到完全不透明
    ).animate(CurvedAnimation(
      parent: _feedbackController,
      curve: Curves.easeOut,  // 使用更平滑的曲线
    ));
  }

  void _startEntryAnimation() async {
    await Future<void>.delayed(const Duration(milliseconds: 100));
    if (mounted) {
      _fadeController.forward();
      _slideController.forward();
      await Future<void>.delayed(const Duration(milliseconds: 150));
      if (mounted) {
        _scaleController.forward();
        // 延迟启动新手引导
        _checkAndShowTutorial();
      }
    }
  }

  /// 检查并显示新手引导
  /// 
  /// 功能实现: 检查是否需要显示引导，立即启动引导动画
  /// 实现方案: 页面打开后立即检查雷达模式并显示引导
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _checkAndShowTutorial() async {
    // 立即显示引导，不再延迟
    if (mounted && !_hasShownTutorial) {
      final radarContext = ref.read(radarRecommendationContextProvider);
      if (radarContext != null) {
        _startTutorial();
      }
    }
  }

  /// 启动新手引导动画
  /// 
  /// 功能实现: 显示滑动引导覆盖层和提示动画
  /// 实现方案: 透明度渐变 + 循环脉冲动画
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _startTutorial() {
    if (!mounted) return;
    
    setState(() {
      _showTutorial = true;
      _hasShownTutorial = true;
    });
    
    _tutorialController.forward();
    
    // 5秒后自动隐藏
    Future<void>.delayed(const Duration(seconds: 5), () {
      if (mounted && _showTutorial) {
        _hideTutorial();
      }
    });
    
    print('🎯 Started tutorial animation for radar mode');
  }

  /// 隐藏新手引导
  /// 
  /// 功能实现: 淡出引导覆盖层
  /// 实现方案: 透明度动画 + 状态更新
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _hideTutorial() {
    if (!mounted) return;
    
    _tutorialController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showTutorial = false;
        });
      }
    });
    
    print('🎯 Hidden tutorial animation');
  }

  late PageController _pageController;
  int _currentCarouselIndex = 0; // 默认显示第一张图片
  bool _isChangingStation = false; // 防止重复切换

  /// 初始化轮播图控制器
  void _initializeCarousel() {
    _pageController = PageController(
      initialPage: 0, // 从第一张开始，具体位置在构建时确定
      viewportFraction: 0.75, // 显示左右两边的部分图片，增强滑动感
    );
  }



  /// 重置轮播图到当前电台位置
  /// 
  /// 功能实现: 根据实际电台列表，回弹到当前电台的正确位置
  /// 实现方案: 动态计算当前电台在实际列表中的索引
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _resetToCurrentCenter(List<StationSimple> actualStations, StationSimple currentStation) {
    if (mounted && _pageController.hasClients) {
      final currentStationIndex = actualStations.indexWhere((s) => s.id == currentStation.id);
      if (currentStationIndex != -1) {
        _currentCarouselIndex = currentStationIndex;
        _pageController.animateToPage(
          currentStationIndex,
          duration: const Duration(milliseconds: 150),
          curve: Curves.fastOutSlowIn,
        );
      }
    }
  }

  /// 播放上一首（直接播放版）
  /// 
  /// 功能实现: 在当前播放列表中切换到上一首电台，播放失败时自动验证
  /// 优化策略: 先播放，失败时AudioService自动验证和处理
  /// 实现方案: 调用AudioService的playPrevious方法
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Future<void> _playPrevious() async {
    print('⏮️ PlayerModal: 尝试播放上一首');
    
    final audioService = ref.read(audioServiceProvider);
    final success = await audioService.playPrevious();
    
    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Already the first station'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  /// 播放下一首（直接播放版）
  /// 
  /// 功能实现: 在当前播放列表中切换到下一首电台，播放失败时自动验证
  /// 优化策略: 先播放，失败时AudioService自动验证和处理
  /// 实现方案: 调用AudioService的playNext方法
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Future<void> _playNext() async {
    print('⏭️ PlayerModal: 尝试播放下一首');
    
    final audioService = ref.read(audioServiceProvider);
    final success = await audioService.playNext();
    
    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Already the last station'),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _heartEffectController.dispose();
    _tutorialController.dispose();
    _dragController.dispose();
    _feedbackController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentStation = ref.watch(currentStationProvider);
    final playbackAsync = ref.watch(currentPlaybackProvider);
    final favoritesAsync = ref.watch(favoriteStationsProvider);

    if (currentStation == null) {
      return Material(
        color: Colors.grey.shade50,
        child: SafeArea(
          child: Column(
            children: [
              _buildTopNavigation(context),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.radio,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      TextOverflowHandler.safeText(
                        'no_data'.tr(),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Material(
      color: Colors.transparent,
      child: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Stack(
              children: [
                // 背景
                _buildBackground(context, currentStation),
                
                // 主要内容
                SafeArea(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Column(
                      children: [
                        // 顶部导航
                        _buildTopNavigation(context),
                        
                        // 主要内容区域
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(24, 0, 24, 50), // 减少底部预留空间到50px
                            child: Column(
                            children: [
                              const SizedBox(height: 20),
                              
                              // 电台图片展示区域（根据播放源类型决定显示模式）- 增加flex权重
                              Expanded(
                                flex: 7, // 从6增加到7，给电台图片更多空间
                                child: ScaleTransition(
                                  scale: _scaleAnimation,
                                  child: _buildStationDisplayArea(context, ref, currentStation),
                                ),
                              ),
                                
                                const SizedBox(height: 24), // 减少间距
                                
                                // 电台信息
                                _buildStationInfo(context, currentStation),
                                
                                const SizedBox(height: 10), // 减少间距
                                
                                // 播放列表信息
                                _buildPlaylistInfo(context, ref),
                                
                                const SizedBox(height: 20), // 减少间距
                                
                                // 播放状态
                                playbackAsync.when(
                                  data: (playback) => _buildPlaybackStatus(context, playback),
                                  loading: () => _buildLoadingStatus(context),
                                  error: (error, stack) => _buildErrorStatus(context, error),
                                ),
                                
                                const SizedBox(height: 24), // 减少间距
                                
                                // 播放控制
                                _buildPlayControls(context, ref, currentStation, playbackAsync, favoritesAsync),
                                
                                const SizedBox(height: 12), // 减少底部间距
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                // 独立的底部广告banner - 不影响主要内容布局
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: SafeArea(
                    top: false,
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: const PlayerBannerAd(),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建现代化动态背景
  Widget _buildBackground(BuildContext context, StationSimple station) {
    return Stack(
      children: [
        // 主渐变背景
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF0F0F23), // 深蓝黑
                const Color(0xFF1A1A2E), // 深紫蓝
                const Color(0xFF16213E), // 深蓝
                const Color(0xFF0F3460), // 中蓝
              ],
              stops: const [0.0, 0.3, 0.7, 1.0],
            ),
          ),
        ),

        // 动态粒子背景
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: _ParticleBackgroundPainter(
                  animationValue: _fadeAnimation.value,
                  particleCount: 30,
                  particleColor: const Color(0xFF00FFFF).withOpacity(0.3),
                ),
              );
            },
          ),
        ),

        // 霓虹发光效果层
        Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.5,
              colors: [
                const Color(0xFF00FFFF).withOpacity(0.1),
                const Color(0xFFFF00FF).withOpacity(0.05),
                Colors.transparent,
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建顶部导航
  Widget _buildTopNavigation(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.keyboard_arrow_down, size: 28),
              color: Colors.grey.shade700,
              onPressed: () {
                print('🎵 PlayerModal close button tapped');
                widget.onClose();
              },
            ),
          ),
          // 可以在这里添加更多按钮，比如分享、更多选项等
          const SizedBox(width: 48), // 占位保持对称
        ],
      ),
    );
  }

  /// 构建电台展示区域（修复版 - 更严格的模式检测）
  /// 
  /// 功能实现: 严格检测播放源类型，确保只有真正的雷达推荐才使用手势模式
  /// 修复问题: 防止其他页面（探索/资料库）错误使用雷达推荐的手势模式
  /// 实现方案: 同时检查雷达上下文和播放列表上下文，优先处理明确的播放列表源
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Widget _buildStationDisplayArea(BuildContext context, WidgetRef ref, StationSimple currentStation) {
    final radarContext = ref.watch(radarRecommendationContextProvider);
    final playlistContext = ref.watch(currentPlaylistProvider);
    
    // 🔧 修复：优先检查播放列表上下文，如果来自非雷达推荐源，使用轮播图模式
    if (playlistContext != null) {
      switch (playlistContext.sourceType) {
        case PlaylistSourceType.favorites:
        case PlaylistSourceType.playHistory:
        case PlaylistSourceType.hotRecommendations:
        case PlaylistSourceType.mostClicked:
        case PlaylistSourceType.highQuality:
        case PlaylistSourceType.latestAdded:
        case PlaylistSourceType.categoryGrid:
          // print('🎵 使用传统轮播图模式 - 播放列表源: ${playlistContext.sourceType}');
          return _buildStationCarousel(context, ref, currentStation);
        case PlaylistSourceType.radarRecommendations:
          // 雷达推荐的播放列表，检查是否有有效的雷达上下文
          if (radarContext != null) {
            // print('🎵 使用雷达推荐手势模式 - 播放列表确认为雷达推荐');
            return _buildRadarGestureMode(context, ref, currentStation);
          }
          break;
      }
    }
    
    // 🔧 只有在没有其他播放列表上下文，且存在雷达推荐上下文时，才使用手势模式
    if (radarContext != null && playlistContext == null) {
      // print('🎵 使用雷达推荐手势模式 - 纯雷达推荐模式');
      return _buildRadarGestureMode(context, ref, currentStation);
    }
    
    // 默认使用传统轮播图模式
    // print('🎵 使用传统轮播图模式 - 默认模式');
    return _buildStationCarousel(context, ref, currentStation);
  }

  /// 构建雷达推荐手势模式
  /// 
  /// 功能实现: 显示单张电台图片，支持左右滑动手势控制
  /// 实现方案: 使用GestureDetector包装电台封面，实现左滑跳过、右滑收藏
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Widget _buildRadarGestureMode(BuildContext context, WidgetRef ref, StationSimple currentStation) {
    return Column(
      children: [
        // 带手势的电台封面和特效层
        Expanded(
          child: Stack(
            children: [
              // 背景渐变响应层
              if (_isDragging)
                Positioned.fill(
                  child: AnimatedBuilder(
                    animation: _feedbackController,
                    builder: (context, child) {
                      final progress = _dragProgress.abs();
                      final isLeft = _dragProgress < 0;
                      final color = isLeft 
                        ? Colors.red.withOpacity(0.1 * progress)
                        : Colors.pink.withOpacity(0.1 * progress);
                      
                      return Container(
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            center: isLeft ? Alignment.centerLeft : Alignment.centerRight,
                            radius: 1.5,
                            colors: [
                              color,
                              Colors.transparent,
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),

              // 电台封面（带拖拽跟随效果）
              Center(
                child: GestureDetector(
                  onPanStart: (details) => _onDragStart(details),
                  onPanUpdate: (details) => _onDragUpdate(details, context),
                  onPanEnd: (details) => _onDragEnd(details, ref, currentStation),
                  child: AnimatedBuilder(
                    animation: _dragController,
                    builder: (context, child) {
                      // 根据拖拽方向决定是否移动图片
                      // 右滑收藏时图片保持不动，左滑跳过时跟随移动
                      final shouldFollowDrag = _dragProgress < 0; // 只有左滑时跟随
                      final offsetX = shouldFollowDrag ? _dragOffset : 0.0;
                      final rotationAngle = shouldFollowDrag ? _dragOffset * 0.01 : 0.0;
                      
                      return Transform.translate(
                        offset: Offset(offsetX, 0),
                        child: Transform.rotate(
                          angle: rotationAngle,
                          child: Opacity(
                            opacity: 1.0 - (_dragProgress.abs() * 0.3), // 拖拽时半透明
                            child: _buildStationCover(context, currentStation),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // 左侧跳过图标（优化版 - 固定大小，不透明度变化）
              if (_isDragging && _dragProgress < -0.05) // 降低显示阈值，更早出现
                Positioned(
                  left: 30,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: AnimatedBuilder(
                      animation: _feedbackController,
                      builder: (context, child) {
                        // 使用不透明度变化替代缩放，基于拖拽进度
                        final progress = _dragProgress.abs().clamp(0.0, 1.0);
                        final baseOpacity = _sideIconScaleAnimation.value * 0.3; // 基础透明度30%
                        final progressOpacity = progress * 0.7; // 进度透明度70%
                        final totalOpacity = (baseOpacity + progressOpacity).clamp(0.0, 1.0);
                        
                        // 颜色饱和度也根据进度变化
                        final colorIntensity = (0.7 + progress * 0.3).clamp(0.7, 1.0);
                        
                        return Container(
                          width: 80, // 固定大尺寸
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(totalOpacity * colorIntensity),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(totalOpacity),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.red.withOpacity(totalOpacity * 0.6),
                                blurRadius: 25,
                                spreadRadius: 3,
                              ),
                              BoxShadow(
                                color: Colors.red.withOpacity(totalOpacity * 0.3),
                                blurRadius: 40,
                                spreadRadius: 8,
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.shuffle, // 随机播放图标，表示跳过到随机电台
                            color: Colors.white.withOpacity(totalOpacity),
                            size: 40, // 固定大图标
                          ),
                        );
                      },
                    ),
                  ),
                ),

              // 右侧收藏图标（优化版 - 固定大小，不透明度变化）
              if (_isDragging && _dragProgress > 0.05) // 降低显示阈值，更早出现
                Positioned(
                  right: 30,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: AnimatedBuilder(
                      animation: _feedbackController,
                      builder: (context, child) {
                        // 使用不透明度变化替代缩放，基于拖拽进度
                        final progress = _dragProgress.abs().clamp(0.0, 1.0);
                        final baseOpacity = _sideIconScaleAnimation.value * 0.3; // 基础透明度30%
                        final progressOpacity = progress * 0.7; // 进度透明度70%
                        final totalOpacity = (baseOpacity + progressOpacity).clamp(0.0, 1.0);
                        
                        // 颜色饱和度也根据进度变化
                        final colorIntensity = (0.7 + progress * 0.3).clamp(0.7, 1.0);
                        
                        return Container(
                          width: 80, // 固定大尺寸
                          height: 80,
                          decoration: BoxDecoration(
                            color: Colors.pink.withOpacity(totalOpacity * colorIntensity),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withOpacity(totalOpacity),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.pink.withOpacity(totalOpacity * 0.6),
                                blurRadius: 25,
                                spreadRadius: 3,
                              ),
                              BoxShadow(
                                color: Colors.pink.withOpacity(totalOpacity * 0.3),
                                blurRadius: 40,
                                spreadRadius: 8,
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.favorite,
                            color: Colors.white.withOpacity(totalOpacity),
                            size: 40, // 固定大图标
                          ),
                        );
                      },
                    ),
                  ),
                ),
              
              // 红心爆炸特效
              if (_showHeartEffect)
                Center(
                  child: AnimatedBuilder(
                    animation: _heartEffectController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _heartScaleAnimation.value,
                        child: Opacity(
                          opacity: _heartOpacityAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.transparent,
                            ),
                            child: const Icon(
                              Icons.favorite,
                              size: 80,
                              color: Colors.red,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),

              // 新手引导覆盖层
              if (_showTutorial)
                _buildTutorialOverlay(context),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 手势提示（移到电台图下方）
        if (!_isDragging && !_showTutorial)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: Text(
              'Swipe left to skip • Swipe right to favorite',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }

  /// 开始拖拽手势（优化版 - 立即震动反馈）
  /// 
  /// 功能实现: 初始化拖拽状态，隐藏引导，立即提供震动反馈
  /// 实现方案: 设置拖拽标志，启动反馈动画，触发轻微震动
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _onDragStart(DragStartDetails details) {
    if (!mounted) return;
    
    setState(() {
      _isDragging = true;
    });
    
    // 隐藏引导
    if (_showTutorial) {
      _hideTutorial();
    }
    
    // 启动反馈动画
    _feedbackController.forward();
    
    // 🎯 立即提供轻微震动反馈，让用户知道手势已开始
    HapticFeedback.selectionClick(); // 使用更轻微的震动类型
    
    print('🎯 Started drag gesture with immediate haptic feedback');
  }

  // 添加震动反馈状态追踪
  bool _hasTriggeredThresholdHaptic = false;
  
  /// 更新拖拽手势（优化版 - 改进震动反馈）
  /// 
  /// 功能实现: 实时更新拖拽位移和进度，提供分层震动反馈
  /// 实现方案: 增加拖拽距离，降低阻力，多阶段震动反馈
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _onDragUpdate(DragUpdateDetails details, BuildContext context) {
    if (!mounted || !_isDragging) return;
    
    final screenWidth = MediaQuery.of(context).size.width;
    final maxDragDistance = screenWidth * 0.5; // 增加到50%，让拖拽更容易
    
    // final previousProgress = _dragProgress.abs(); // 移除未使用的变量
    
    setState(() {
      // 增加灵敏度，让滑动更跟手
      _dragOffset += details.delta.dx * 1.2;
      _dragOffset = _dragOffset.clamp(-maxDragDistance, maxDragDistance);
      _dragProgress = _dragOffset / maxDragDistance;
    });
    
    final currentProgress = _dragProgress.abs();
    
    // 🎯 分层震动反馈系统
    // 1. 当达到操作阈值时触发确认震动（只触发一次）
    if (currentProgress > 0.4 && !_hasTriggeredThresholdHaptic) {
      _hasTriggeredThresholdHaptic = true;
      HapticFeedback.lightImpact(); // 确认将要触发操作
      print('🎯 Threshold haptic feedback triggered at ${(currentProgress * 100).toInt()}%');
    }
    
    // 2. 当从阈值回退时重置状态
    if (currentProgress < 0.3 && _hasTriggeredThresholdHaptic) {
      _hasTriggeredThresholdHaptic = false;
    }
  }

  /// 结束拖拽手势
  /// 
  /// 功能实现: 根据拖拽距离决定执行操作或回弹
  /// 实现方案: 降低阈值，让操作更容易触发
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _onDragEnd(DragEndDetails details, WidgetRef ref, StationSimple currentStation) {
    if (!mounted || !_isDragging) return;
    
    final threshold = 0.4; // 降低阈值到40%，让操作更容易
    final shouldExecuteAction = _dragProgress.abs() > threshold;
    
    if (shouldExecuteAction) {
      // 执行操作
      if (_dragProgress > 0) {
        // 右滑收藏
        _executeSwipeAction(true, ref, currentStation);
      } else {
        // 左滑跳过
        _executeSwipeAction(false, ref, currentStation);
      }
    } else {
      // 回弹
      _resetDragState();
    }
  }

  /// 执行滑动操作（优化版）
  /// 
  /// 功能实现: 执行收藏或跳过操作，右滑保持图片不动，左滑快速跳过
  /// 实现方案: 收藏时图片不动并播放红心特效，跳过时直接切换
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _executeSwipeAction(bool isFavorite, WidgetRef ref, StationSimple currentStation) {
    print('🎯 Executing swipe action: ${isFavorite ? 'favorite' : 'skip'}');
    
    if (isFavorite) {
      // 收藏操作：图片保持不动，直接执行收藏并播放红心特效
      _favoriteCurrentStation(ref, currentStation);
      // 立即重置拖拽状态，让图片回到原位
      _resetDragState();
    } else {
      // 跳过操作：直接执行，快速重置，避免卡顿
      _skipToRandomStation(ref, currentStation);
      // 立即重置状态，提供更流畅的体验
      _resetDragState();
    }
  }

  /// 重置拖拽状态（优化版 - 包含震动状态重置）
  /// 
  /// 功能实现: 重置所有拖拽相关状态和动画，包括震动反馈状态
  /// 实现方案: 快速重置状态，减少动画延迟
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _resetDragState() {
    if (!mounted) return;
    
    // 立即重置状态（包括震动反馈状态）
    setState(() {
      _isDragging = false;
      _dragOffset = 0.0;
      _dragProgress = 0.0;
    });
    
    // 🎯 重置震动反馈状态
    _hasTriggeredThresholdHaptic = false;
    
    // 快速回弹动画（缩短时间）
    _dragController.duration = const Duration(milliseconds: 150);
    _dragController.reverse().then((_) {
      if (mounted) {
        _feedbackController.reverse();
        // 恢复原有动画时长
        _dragController.duration = const Duration(milliseconds: 300);
      }
    });
  }



  /// 收藏当前电台（优化右滑体验）
  /// 
  /// 功能实现: 调用现有收藏功能，更新UI状态，播放红心特效
  /// 实现方案: 延迟播放红心特效，让图片先回到原位再显示特效
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Future<void> _favoriteCurrentStation(WidgetRef ref, StationSimple currentStation) async {
    try {
      final storageService = ref.read(storageServiceProvider);
      
      // 检查是否已经收藏
      final favorites = await ref.read(favoriteStationsProvider.future);
      final isFavorite = favorites.any((fav) => fav.id == currentStation.id);
      
      if (isFavorite) {
        // 如果已经收藏，取消收藏
        await storageService.removeFromFavorites(currentStation.id);
        print('💔 取消收藏电台: ${currentStation.name}');
        
        // 已移除收藏成功提示
      } else {
        // 如果未收藏，添加收藏并延迟播放红心特效
        await storageService.addToFavorites(currentStation);
        print('⭐ 收藏电台: ${currentStation.name}');
        
        // 延迟播放红心爆炸特效，让图片先回到原位
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            _playHeartEffect();
          }
        });
        
        // 已添加收藏成功提示
      }
      
      // 刷新收藏列表状态
      ref.invalidate(favoriteStationsProvider);
      
    } catch (e) {
      print('❌ 收藏操作失败: $e');
      // 已移除收藏失败提示
    }
  }

  /// 播放红心爆炸特效
  /// 
  /// 功能实现: 显示红心图标并播放缩放爆炸动画
  /// 实现方案: 使用AnimationController控制缩放和透明度
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _playHeartEffect() {
    if (!mounted) return;
    
    setState(() {
      _showHeartEffect = true;
    });
    
    // 重置并开始动画
    _heartEffectController.reset();
    _heartEffectController.forward();
    
    // 动画完成后隐藏特效
    _heartEffectController.addStatusListener((status) {
      if (status == AnimationStatus.completed && mounted) {
        setState(() {
          _showHeartEffect = false;
        });
      }
    });
    
    print('💖 播放红心爆炸特效');
  }

  /// 跳过到随机电台
  /// 
  /// 功能实现: 从雷达推荐上下文中随机选择下一个电台播放
  /// 实现方案: 使用RadarRecommendationContext的getNextRandomStation方法
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  void _skipToRandomStation(WidgetRef ref, StationSimple currentStation) {
    final radarContext = ref.read(radarRecommendationContextProvider);
    if (radarContext == null) {
      print('❌ 雷达推荐上下文为空，无法跳过');
      return;
    }
    
    // 如果只有1个电台，左滑无效
    if (radarContext.availableStations.length <= 1) {
      print('⚠️ 只有1个电台，左滑无效');
      // 已移除没有更多电台的提示
      return;
    }
    
    // 获取下一个随机电台
    final nextStation = radarContext.getNextRandomStation();
    if (nextStation == null) {
      print('❌ 没有找到下一个随机电台');
      return;
    }
    
    print('🔀 跳过到随机电台: ${nextStation.name}');
    
    // 更新已播放列表
    final updatedContext = radarContext.addPlayedStation(currentStation.id);
    ref.read(radarRecommendationContextProvider.notifier).state = updatedContext;
    
    // 播放下一个电台
    final audioService = ref.read(audioServiceProvider);
    audioService.playStation(nextStation);
    
    // 已移除跳过成功提示
  }

  /// 构建新手引导覆盖层（简化版）
  /// 
  /// 功能实现: 显示简洁的滑动引导，只保留核心信息
  /// 实现方案: 简化为主要提示文字 + 手势图标
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Widget _buildTutorialOverlay(BuildContext context) {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _tutorialOpacityAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _tutorialOpacityAnimation.value,
            child: GestureDetector(
              onTap: _hideTutorial,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 简化的手势图标
                      AnimatedBuilder(
                        animation: _tutorialController,
                        builder: (context, child) {
                          final pulseValue = (sin(_tutorialController.value * pi * 2) + 1) / 2;
                          
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // 左滑图标
                              Transform.translate(
                                offset: Offset(-30 * pulseValue, 0),
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(0.3),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.shuffle,
                                    color: Colors.white70,
                                    size: 24,
                                  ),
                                ),
                              ),
                              
                              const SizedBox(width: 80),
                              
                              // 右滑图标
                              Transform.translate(
                                offset: Offset(30 * pulseValue, 0),
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.pink.withOpacity(0.3),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.favorite,
                                    color: Colors.pink,
                                    size: 24,
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // 简洁提示文字
                      Text(
                        'Swipe to explore',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 8),
                      
                      Text(
                        'Skip ← • → Favorite',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }



  /// 构建轮播图式电台展示
  /// 
  /// 功能实现: 显示当前电台和前后电台的图片，支持滑动切换
  /// 实现方案: 使用PageView实现轮播图效果，显示3张图片（上一首、当前、下一首）
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
    Widget _buildStationCarousel(BuildContext context, WidgetRef ref, StationSimple currentStation) {
    final playlistContext = ref.watch(currentPlaylistProvider);
    
    // 如果没有播放列表，显示单张图片
    if (playlistContext == null) {
      return _buildStationCover(context, currentStation);
    }
    
    // 构建实际存在的电台列表（不包含null占位符）
    final List<StationSimple> actualStations = [];
    final List<int> stationMapping = []; // 映射到原始索引
    
    if (playlistContext.previousStation != null) {
      actualStations.add(playlistContext.previousStation!);
      stationMapping.add(-1); // 上一首
    }
    
    actualStations.add(currentStation);
    stationMapping.add(0); // 当前
    
    if (playlistContext.nextStation != null) {
      actualStations.add(playlistContext.nextStation!);
      stationMapping.add(1); // 下一首
    }
    
    // 如果只有一个电台，直接显示单张图片
    if (actualStations.length == 1) {
      return _buildStationCover(context, currentStation);
    }
    
    // 计算当前电台在实际列表中的索引
    final currentStationIndex = actualStations.indexWhere((s) => s.id == currentStation.id);
    
    // 确保PageController在正确位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _pageController.hasClients) {
        if (_currentCarouselIndex != currentStationIndex) {
          _currentCarouselIndex = currentStationIndex;
          _pageController.animateToPage(
            currentStationIndex,
            duration: const Duration(milliseconds: 50),
            curve: Curves.easeOut,
          );
        }
      }
    });
    
    // 🔧 优化：使用LayoutBuilder确保轮播图高度适应可用空间
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final maxWidth = screenWidth * 0.75;
        final maxHeight = constraints.maxHeight * 0.9;
        final carouselHeight = maxWidth < maxHeight ? maxWidth : maxHeight;
        
        return SizedBox(
          height: carouselHeight,
          child: PageView.builder(
        controller: _pageController,
        itemCount: actualStations.length,
        physics: const BouncingScrollPhysics(), // 使用iOS风格的弹性滑动物理效果
        pageSnapping: true, // 启用页面捕捉
        allowImplicitScrolling: false, // 禁用隐式滚动
        onPageChanged: (index) {
          print('🎵 轮播图页面切换到: $index, 映射: ${stationMapping[index]}');
          
          // 防止重复触发和正在切换时的干扰
          if (_currentCarouselIndex == index || _isChangingStation) return;
          
          _currentCarouselIndex = index;
          
          // 根据映射关系确定操作
          final mappedIndex = stationMapping[index];
          
          if (mappedIndex == -1) {
            // 滑动到上一首
            _isChangingStation = true;
            _playPrevious();
            // 立即回弹，提供丝滑体验
            Future.delayed(const Duration(milliseconds: 100), () {
              _isChangingStation = false;
              _resetToCurrentCenter(actualStations, currentStation);
            });
          } else if (mappedIndex == 1) {
            // 滑动到下一首
            _isChangingStation = true;
            _playNext();
            // 立即回弹，提供丝滑体验
            Future.delayed(const Duration(milliseconds: 100), () {
              _isChangingStation = false;
              _resetToCurrentCenter(actualStations, currentStation);
            });
          }
        },
        itemBuilder: (context, index) {
          final station = actualStations[index];
          final isCurrentStation = station.id == currentStation.id;
          
          return _buildCarouselStationCover(
            context, 
            station, 
            isCurrentStation,
            opacity: isCurrentStation ? 1.0 : 0.6,
          );
        },
      ),
        );
      },
    );
  }

  /// 构建轮播图中的电台封面
  Widget _buildCarouselStationCover(
    BuildContext context, 
    StationSimple station, 
    bool isCurrentStation,
    {double opacity = 1.0}
  ) {
    final size = MediaQuery.of(context).size.width * 0.75;
    final actualSize = isCurrentStation ? size : size * 0.85; // 非当前电台稍小一些
    
    return Center(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200), // 缩短动画时长
        curve: Curves.easeOutCubic, // 使用更平滑的动画曲线
        width: actualSize,
        height: actualSize,
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 200),
          opacity: opacity,
          child: Transform.scale(
            scale: isCurrentStation ? 1.0 : 0.95, // 添加缩放效果
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: isCurrentStation ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.25),
                    blurRadius: 25,
                    offset: const Offset(0, 12),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.purple.withOpacity(0.15),
                    blurRadius: 40,
                    offset: const Offset(0, 20),
                    spreadRadius: 3,
                  ),
                ] : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Stack(
                  children: [
                    // 电台图片
                    StationImageBuilder.buildStationImage(
                      station: station,
                      width: actualSize,
                      height: actualSize,
                      fit: BoxFit.cover,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    
                    // 渐变覆盖层
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.03),
                            Colors.black.withOpacity(0.08),
                          ],
                        ),
                      ),
                    ),
                    
                    // 非当前电台显示淡化指示
                    if (!isCurrentStation)
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: Colors.black.withOpacity(0.15),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }



  /// 构建电台封面（使用统一的图片构建工具）
  Widget _buildStationCover(BuildContext context, StationSimple station) {
    // 🔧 优化：使用LayoutBuilder获取实际可用空间，确保图片不被压缩
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算合适的图片大小，考虑可用空间
        final screenWidth = MediaQuery.of(context).size.width;
        final maxWidth = screenWidth * 0.75;
        final maxHeight = constraints.maxHeight * 0.9; // 使用90%的可用高度
        
        // 选择较小的尺寸，确保图片完整显示
        final size = maxWidth < maxHeight ? maxWidth : maxHeight;
        
        return Center(
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 30,
                  offset: const Offset(0, 15),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: Colors.purple.withOpacity(0.2),
                  blurRadius: 50,
                  offset: const Offset(0, 25),
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Stack(
              children: [
                // 电台图片（使用统一的图片构建工具）
                StationImageBuilder.buildStationImage(
                  station: station,
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(20),
                ),

                // 渐变覆盖层
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.05),
                        Colors.black.withOpacity(0.1),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建播放列表信息
  /// 
  /// 功能实现: 显示当前播放列表的状态信息
  /// 实现方案: 监听播放列表上下文Provider，显示列表标题和位置
  /// 影响范围: player_modal.dart
  /// 实现日期: 2025-01-27
  Widget _buildPlaylistInfo(BuildContext context, WidgetRef ref) {
    final playlistContext = ref.watch(currentPlaylistProvider);
    
    if (playlistContext == null) {
      return const SizedBox.shrink();
    }
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.queue_music,
                size: 16,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  playlistContext.sourceTitle,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${playlistContext.currentIndex + 1}/${playlistContext.stations.length}',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // 滑动提示
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.keyboard_arrow_left,
              size: 16,
              color: Colors.grey.shade500,
            ),
            const SizedBox(width: 4),
            Text(
              'Swipe to change stations',
              style: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 11,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_right,
              size: 16,
              color: Colors.grey.shade500,
            ),
          ],
        ),
      ],
    );
  }

  /// 构建电台信息
  Widget _buildStationInfo(BuildContext context, StationSimple station) {
    return Column(
      children: [
        // 电台名称
        TextOverflowHandler.safeText(
          station.name,
          style: TextStyle(
            color: Colors.white,
            fontSize: 22,
            fontWeight: FontWeight.bold,
            height: 1.2,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
        ),
        
        const SizedBox(height: 6),
        
        // 国家信息
        if (station.country.isNotEmpty)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.public,
                size: 14,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              TextOverflowHandler.safeText(
                station.country,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
              ),
            ],
          ),
      ],
    );
  }

  /// 构建播放状态
  Widget _buildPlaybackStatus(BuildContext context, CurrentPlayback playback) {
    String statusText;
    Color statusColor;
    IconData statusIcon;
    bool showVisualizer = false;

    switch (playback.state) {
      case PlaybackState.loading:
        statusText = 'connecting'.tr();
        statusColor = Colors.orange;
        statusIcon = Icons.sync;
        break;
      case PlaybackState.playing:
        statusText = 'now_playing'.tr();
        statusColor = Colors.green;
        statusIcon = Icons.play_circle_filled;
        showVisualizer = true;
        break;
      case PlaybackState.paused:
        statusText = 'pause'.tr();
        statusColor = Colors.grey;
        statusIcon = Icons.pause_circle_filled;
        break;
      case PlaybackState.error:
        statusText = 'connection_error'.tr();
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case PlaybackState.stopped:
        statusText = 'stopped'.tr();
        statusColor = Colors.grey;
        statusIcon = Icons.stop_circle;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: statusColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: 18,
          ),
          const SizedBox(width: 8),
          TextOverflowHandler.safeText(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            maxLines: 1,
          ),
          if (showVisualizer) ...[
            const SizedBox(width: 10),
            AudioVisualizer(
              isPlaying: showVisualizer,
              barCount: 4,
              barWidth: 2.0,
              barSpacing: 1.5,
              maxHeight: 14.0,
              minHeight: 3.0,
              color: statusColor,
            ),
          ],
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingStatus(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 14,
            height: 14,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
            ),
          ),
          const SizedBox(width: 8),
          TextOverflowHandler.safeText(
            'connecting'.tr(),
            style: const TextStyle(
              color: Colors.orange,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorStatus(BuildContext context, Object error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error,
            color: Colors.red,
            size: 18,
          ),
          const SizedBox(width: 8),
          TextOverflowHandler.safeText(
            'connection_error'.tr(),
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  /// 构建播放控制
  Widget _buildPlayControls(
    BuildContext context,
    WidgetRef ref,
    StationSimple station,
    AsyncValue<CurrentPlayback> playbackAsync,
    AsyncValue<List<StationSimple>> favoritesAsync,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 收藏按钮
          _buildFavoriteButton(context, ref, station, favoritesAsync),
          // 播放/暂停按钮
          _buildPlayButton(context, ref, station, playbackAsync),
          // 分享按钮
          _buildShareButton(context, station),
        ],
      ),
    );
  }

  /// 构建收藏按钮
  Widget _buildFavoriteButton(
    BuildContext context,
    WidgetRef ref,
    StationSimple station,
    AsyncValue<List<StationSimple>> favoritesAsync,
  ) {
    return favoritesAsync.when(
      data: (favorites) {
        final isFavorite = favorites.any((fav) => fav.id == station.id);
        return Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: () async {
                try {
                  final storageService = ref.read(storageServiceProvider);
                  if (isFavorite) {
                    await storageService.removeFromFavorites(station.id);
                  } else {
                    await storageService.addToFavorites(station);
                  }
                  ref.invalidate(favoriteStationsProvider);
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: TextOverflowHandler.safeText(
                          '操作失败: $e',
                          maxLines: 1,
                        ),
                        backgroundColor: Colors.red.shade600,
                      ),
                    );
                  }
                }
              },
              child: Center(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    key: ValueKey(isFavorite),
                    color: isFavorite ? Colors.red.shade400 : Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        );
      },
      loading: () => Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: const Center(
          child: SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      ),
      error: (error, stack) => Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.favorite_border,
          color: Colors.white.withOpacity(0.5),
          size: 20,
        ),
      ),
    );
  }

  /// 构建播放按钮
  Widget _buildPlayButton(
    BuildContext context,
    WidgetRef ref,
    StationSimple station,
    AsyncValue<CurrentPlayback> playbackAsync,
  ) {
    return playbackAsync.when(
      data: (playback) {
        return Container(
          width: 64,
          height: 64,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF00FFFF).withOpacity(0.3),
                const Color(0xFF00FFFF).withOpacity(0.1),
              ],
            ),
            shape: BoxShape.circle,
            border: Border.all(
              color: const Color(0xFF00FFFF).withOpacity(0.8),
              width: 2,
            ),
            boxShadow: [
              // 内发光
              BoxShadow(
                color: const Color(0xFF00FFFF).withOpacity(0.4),
                blurRadius: 8,
                spreadRadius: -2,
              ),
              // 外发光
              BoxShadow(
                color: const Color(0xFF00FFFF).withOpacity(0.6),
                blurRadius: 20,
                spreadRadius: 4,
              ),
              // 远距离发光
              BoxShadow(
                color: const Color(0xFF00FFFF).withOpacity(0.3),
                blurRadius: 40,
                spreadRadius: 8,
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(32),
              onTap: () {
                final audioService = ref.read(audioServiceProvider);
                switch (playback.state) {
                  case PlaybackState.playing:
                    audioService.pause();
                    break;
                  case PlaybackState.paused:
                    // 暂停状态：快速恢复播放，无需重新加载资源
                    print('📻 PlayerModal恢复播放 - 使用缓存资源');
                    audioService.resume();
                    break;
                  case PlaybackState.stopped:
                    // 停止状态：重新开始播放，需要重新加载资源
                    print('📻 PlayerModal重新播放 - 加载新资源');
                    audioService.playStation(station);
                    break;
                  case PlaybackState.loading:
                    break;
                  case PlaybackState.error:
                    audioService.playStation(station);
                    break;
                }
              },
              child: Center(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: _getPlayIcon(playback.state),
                ),
              ),
            ),
          ),
        );
      },
      loading: () => Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.3),
          shape: BoxShape.circle,
        ),
        child: const Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      ),
      error: (error, stack) => Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.8),
          shape: BoxShape.circle,
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(32),
            onTap: () {
              ref.read(audioServiceProvider).playStation(station);
            },
            child: const Center(
              child: Icon(
                Icons.refresh,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建分享按钮
  Widget _buildShareButton(BuildContext context, StationSimple station) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
                  child: InkWell(
            borderRadius: BorderRadius.circular(24),
            onTap: () => _showShareOptions(context, station),
            child: Center(
            child: Icon(
              Icons.share,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  /// 显示分享选项菜单
  void _showShareOptions(BuildContext context, StationSimple station) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部拖拽指示器
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // 标题
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  'Share "${station.name}"',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 20),
              // 分享选项
              _buildShareOption(
                context,
                Icons.share,
                'Share as Text',
                'Share with formatted text',
                () => _shareAsText(context, station),
              ),
              _buildShareOption(
                context,
                Icons.content_copy,
                'Copy Link',
                'Copy share link only',
                () => _copyToClipboard(context, station),
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  /// 构建分享选项项目
  Widget _buildShareOption(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }



  /// 使用纯文本方式分享
  Future<void> _shareAsText(BuildContext context, StationSimple station) async {
    try {
      final String shareUrl = 'https://wwww.share.worldtune.top/radio_share.html?s=${station.id}';
      final String shareText = _buildShareContent(station, shareUrl);

      HapticFeedback.lightImpact();

      await Share.share(
        shareText,
        subject: '🎵 ${station.name} - WorldTune Radio Station',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                const Text('Station shared as text!'),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      await _copyToClipboard(context, station);
    }
  }



  /// 构建兼容性分享内容（移除ASCII装饰字符）
  String _buildShareContent(StationSimple station, String shareUrl) {
    final StringBuffer content = StringBuffer();
    
    // 使用简单字符构建标题区
    content.writeln('🌍 WorldTune Radio');
    content.writeln('');
    
    // 电台标题
    content.writeln('🎵 Now Playing: ${station.name}');
    content.writeln('');
    
    // 电台详情信息
    if (station.country.isNotEmpty || station.language.isNotEmpty || station.tags.isNotEmpty) {
      content.writeln('📻 Station Details:');
      
      if (station.country.isNotEmpty) {
        content.writeln('📍 Broadcasting from ${station.country}');
      }
      
      if (station.language.isNotEmpty) {
        content.writeln('🗣️ Language: ${station.language}');
      }
      
      if (station.tags.isNotEmpty) {
        final tags = station.tags.take(3).join(' • ');
        content.writeln('🏷️ Tags: $tags');
      }
      
      content.writeln('');
    }
    
    // 使用星号分隔线替代Unicode字符
    content.writeln('⭐ ⭐ ⭐ ⭐ ⭐ ⭐ ⭐ ⭐ ⭐ ⭐');
    content.writeln('');
    
    // 应用推广
    content.writeln('🌟 Discover 40,000+ radio stations worldwide');
    content.writeln('🎧 Stream music from every corner of the globe');
    content.writeln('📱 Download WorldTune today');
    content.writeln('');
    
    // 链接区域
    content.writeln('🎧 Listen now:');
    content.writeln(shareUrl);
    content.writeln('');
    
    // 标签和推广
    content.writeln('🔥 Join the global music community!');
    content.writeln('');
    if (station.country.isNotEmpty) {
      final countryTag = station.country.replaceAll(' ', '');
      content.writeln('#WorldTune #Radio #Music #$countryTag #GlobalMusic');
    } else {
      content.writeln('#WorldTune #Radio #Music #GlobalMusic');
    }
    
    return content.toString();
  }

  /// 复制链接到剪贴板
  Future<void> _copyToClipboard(BuildContext context, StationSimple station) async {
    try {
      final String shareUrl = 'https://wwww.share.worldtune.top/radio_share.html?s=${station.id}';
      
      // 只复制链接，不包含其他文案
      await Clipboard.setData(ClipboardData(text: shareUrl));
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.content_copy, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Flexible(
                  child: Text('Share content copied to clipboard!'),
                ),
              ],
            ),
            backgroundColor: Colors.blue.shade600,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white, size: 16),
                const SizedBox(width: 8),
                Text('Failed to share station'),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  /// 获取播放图标
  Widget _getPlayIcon(PlaybackState state) {
    switch (state) {
      case PlaybackState.playing:
        return const Icon(
          Icons.pause,
          color: Colors.black87,
          size: 28,
          key: ValueKey('pause'),
        );
      case PlaybackState.paused:
      case PlaybackState.stopped:
        return const Icon(
          Icons.play_arrow,
          color: Colors.black87,
          size: 28,
          key: ValueKey('play'),
        );
      case PlaybackState.loading:
        return const SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.black87),
          ),
        );
      case PlaybackState.error:
        return const Icon(
          Icons.refresh,
          color: Colors.black87,
          size: 28,
          key: ValueKey('error'),
        );
    }
  }
}

/// 粒子背景绘制器
class _ParticleBackgroundPainter extends CustomPainter {
  final double animationValue;
  final int particleCount;
  final Color particleColor;

  _ParticleBackgroundPainter({
    required this.animationValue,
    required this.particleCount,
    required this.particleColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = particleColor
      ..style = PaintingStyle.fill;

    // 生成粒子位置
    for (int i = 0; i < particleCount; i++) {
      final x = (i * 37.0 + animationValue * 100) % size.width;
      final y = (i * 23.0 + animationValue * 50) % size.height;
      final radius = 1.0 + (i % 3) * 0.5;

      // 添加闪烁效果
      final opacity = (0.3 + 0.7 * ((animationValue + i * 0.1) % 1.0));
      paint.color = particleColor.withOpacity(opacity);

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}