import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

/// 高端现代化设置菜单组件
///
/// 提供用户协议、隐私协议和版本信息的快速访问入口
/// 采用现代化的glassmorphism设计风格
class SettingsMenu extends StatefulWidget {
  const SettingsMenu({super.key});

  @override
  State<SettingsMenu> createState() => _SettingsMenuState();
}

class _SettingsMenuState extends State<SettingsMenu> {
  String _version = '1.0.0';
  String _buildNumber = '1';

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  /// 加载应用包信息
  Future<void> _loadPackageInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _version = packageInfo.version;
          _buildNumber = packageInfo.buildNumber;
        });
      }
    } catch (e) {
      debugPrint('获取包信息失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return PopupMenuButton<String>(
      icon: _buildModernIcon(context, colorScheme),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 20,
      shadowColor: colorScheme.shadow.withValues(alpha: 0.3),
      color: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
      constraints: const BoxConstraints(
        minWidth: 240,
        maxWidth: 280,
      ),
      itemBuilder: (context) => _buildMenuItems(context, colorScheme),
      onSelected: _handleMenuSelection,
    );
  }

  /// 构建现代化的设置图标
  Widget _buildModernIcon(BuildContext context, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.surfaceContainerHighest.withValues(alpha: 0.8),
            colorScheme.surfaceContainer.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: colorScheme.surface.withValues(alpha: 0.8),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Icon(
        Icons.settings,
        color: colorScheme.onSurfaceVariant,
        size: 20,
      ),
    );
  }

  /// 构建菜单项列表
  List<PopupMenuEntry<String>> _buildMenuItems(
    BuildContext context,
    ColorScheme colorScheme,
  ) {
    return [
      // 菜单标题
      PopupMenuItem<String>(
        enabled: false,
        child: _buildMenuHeader(context, colorScheme),
      ),
      const PopupMenuDivider(height: 1),

      // 隐私协议
      PopupMenuItem(
        value: 'privacy',
        child: _buildMenuItem(
          context,
          colorScheme,
          icon: Icons.privacy_tip_rounded,
          title: 'privacy_policy'.tr(),
          subtitle: 'privacy_policy_desc'.tr(),
        ),
      ),

      // 用户协议
      PopupMenuItem(
        value: 'terms',
        child: _buildMenuItem(
          context,
          colorScheme,
          icon: Icons.description_rounded,
          title: 'terms_of_service'.tr(),
          subtitle: 'terms_of_service_desc'.tr(),
        ),
      ),

      const PopupMenuDivider(height: 1),

      // 版本信息
      PopupMenuItem<String>(
        enabled: false,
        child: _buildVersionInfo(context, colorScheme),
      ),
    ];
  }

  /// 构建菜单头部
  Widget _buildMenuHeader(BuildContext context, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primary.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.settings_rounded,
              size: 16,
              color: colorScheme.onPrimary,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'settings'.tr(),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个菜单项
  Widget _buildMenuItem(
    BuildContext context,
    ColorScheme colorScheme, {
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              size: 18,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.open_in_new_rounded,
            size: 14,
            color: colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
          ),
        ],
      ),
    );
  }

  /// 构建版本信息
  Widget _buildVersionInfo(BuildContext context, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.info_outline_rounded,
              size: 14,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'app_name'.tr(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: colorScheme.onSurfaceVariant,
                    fontSize: 11,
                  ),
                ),
                Text(
                  '${'version'.tr()} $_version ($_buildNumber)',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 处理菜单选择
  Future<void> _handleMenuSelection(String value) async {
    String url;
    switch (value) {
      case 'privacy':
        url = 'https://sites.google.com/view/worldtune-privacypolicy';
        break;
      case 'terms':
        url = 'https://sites.google.com/view/worldtune-terms-of-user';
        break;
      default:
        return;
    }
    
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        debugPrint('无法打开链接: $url');
      }
    } catch (e) {
      debugPrint('打开链接时出错: $e');
    }
  }
}
