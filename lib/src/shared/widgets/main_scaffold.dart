import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import 'dart:ui' show ImageFilter;
import '../router/app_router.dart';
import '../utils/text_overflow_checker.dart';
import 'mini_player.dart';
import 'player_modal.dart';
import 'app_top_bar.dart';
import 'global_banner_ad.dart';
import '../services/audio_service.dart';
import '../../features/home/<USER>';
import '../../features/explore/explore_page.dart';
import '../../features/library/library_page.dart';

/// 应用程序主要脚手架
class MainScaffold extends ConsumerStatefulWidget {
  const MainScaffold({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  ConsumerState<MainScaffold> createState() => _MainScaffoldState();
}

class _MainScaffoldState extends ConsumerState<MainScaffold> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const HomePage(),
    const ExplorePage(),
    const LibraryPage(),
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateCurrentIndex();
  }

  void _updateCurrentIndex() {
    final location = GoRouterState.of(context).location;
    for (int i = 0; i < BottomNavItem.items.length; i++) {
      if (BottomNavItem.items[i].route == location) {
        if (_currentIndex != i) {
          setState(() {
            _currentIndex = i;
          });
        }
        break;
      }
    }
  }

  void _onItemTapped(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });
      final item = BottomNavItem.items[index];
      context.go(item.route);
    }
  }

  void _onMiniPlayerTapped() {
    print('🎵 MiniPlayer tapped - expanding player modal');
    _showPlayerModal();
  }
  
  void _showPlayerModal() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9, // 初始高度为屏幕90%
        minChildSize: 0.5,     // 最小高度为屏幕50%
        maxChildSize: 0.95,    // 最大高度为屏幕95%
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: PlayerModal(
            onClose: () => Navigator.of(context).pop(),
            animation: kAlwaysCompleteAnimation,
          ),
        ),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    final location = GoRouterState.of(context).location;
    final isMainPage = [
      AppRouter.home,
      AppRouter.explore,
      AppRouter.library,
    ].contains(location);

    // 如果不是主要页面，直接返回child
    if (!isMainPage) {
      return widget.child;
    }

    return TextOverflowChecker.checkAndWrap(
      Container(
        // 统一的背景渐变色，覆盖整个屏幕
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: const [
              Color(0xFF0F0F23), // 深蓝黑
              Color(0xFF1A1A2E), // 深紫蓝
              Color(0xFF16213E), // 深蓝
              Color(0xFF0F3460), // 中蓝
            ],
            stops: const [0.0, 0.3, 0.6, 1.0],
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent, // 透明背景，使用Container的渐变
          appBar: const AppTopBar(),
          body: Stack(
            children: [
              // 主要内容区域 - 移除顶部广告，内容区域完整显示
              TextOverflowChecker.autoFixTree(
                IndexedStack(
                  index: _currentIndex,
                  children: _pages,
                ),
              ),

              // 底部悬浮元素区域
              _buildFloatingElements(),
            ],
          ),
          bottomNavigationBar: _buildModernBottomNavBar(),
        ),
      ),
      debugLabel: 'MainScaffold',
    );
  }

  /// 构建底部悬浮元素
  Widget _buildFloatingElements() {
    return Consumer(
      builder: (context, ref, child) {
        final currentStation = ref.watch(currentStationProvider);
        
        // 计算是否显示 MiniPlayer
        final showMiniPlayer = currentStation != null;
        
        return Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 🎯 悬浮Banner广告 - 动态计算位置偏移
                GlobalBannerAd(
                  bottomOffset: showMiniPlayer ? 80.0 : 0.0, // 根据MiniPlayer状态动态调整
                ),
                
                // MiniPlayer - 只有在有播放电台时才显示
                if (showMiniPlayer)
                  MiniPlayer(
                    onTap: _onMiniPlayerTapped,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }



  /// 构建现代化浮动底部导航栏 - 统一背景色系和增强玻璃态效果（响应式优化）
  Widget _buildModernBottomNavBar() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // BUG修复: 溢出问题修复 - 使用响应式设计适配不同屏幕尺寸
        // 修复策略: 动态计算高度和边距，避免固定尺寸导致的溢出
        // 影响范围: lib/src/shared/widgets/main_scaffold.dart:168-229
        // 修复日期: 2025-01-27
        
        final screenWidth = constraints.maxWidth;
        // BUG修复: 重新调整屏幕分类和尺寸，解决实际设备上的溢出
        // 修复策略: 更激进的小屏优化，减少边距和padding累积
        // 影响范围: lib/src/shared/widgets/main_scaffold.dart:176-184
        // 修复日期: 2025-01-27
        final isVerySmallScreen = screenWidth < 375; // iPhone SE, iPhone 12 mini
        final isSmallScreen = screenWidth < 390; // iPhone 12/13/14 标准版
        final isMediumScreen = screenWidth < 430; // iPhone 12/13/14 Pro
        
        // 根据屏幕尺寸动态调整参数 - 更激进的空间优化
        final horizontalMargin = isVerySmallScreen ? 8.0 : (isSmallScreen ? 12.0 : 16.0);
        final bottomMargin = isVerySmallScreen ? 12.0 : (isSmallScreen ? 16.0 : 24.0);
        final navBarHeight = isVerySmallScreen ? 56.0 : (isSmallScreen ? 60.0 : 72.0);
        final borderRadius = isVerySmallScreen ? 24.0 : (isSmallScreen ? 28.0 : 35.0);
        
        return Container(
          margin: EdgeInsets.fromLTRB(horizontalMargin, 0, horizontalMargin, bottomMargin),
          height: navBarHeight,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFF0F0F23).withOpacity(0.85), // 深蓝黑
                      const Color(0xFF1A1A2E).withOpacity(0.80), // 深紫蓝
                      const Color(0xFF16213E).withOpacity(0.75), // 深蓝
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                  borderRadius: BorderRadius.circular(borderRadius),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                  boxShadow: [
                    // 主阴影
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 30,
                      offset: const Offset(0, 12),
                      spreadRadius: 0,
                    ),
                    // 高光效果
                    BoxShadow(
                      color: Colors.white.withOpacity(0.08),
                      blurRadius: 20,
                      offset: const Offset(0, -6),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: BottomNavItem.items.asMap().entries.map((entry) {
                    final index = entry.key;
                    final item = entry.value;
                    final isSelected = index == _currentIndex;

                    return _buildNavItem(
                      icon: item.icon,
                      label: item.label.tr(),
                      isSelected: isSelected,
                      onTap: () => _onItemTapped(index),
                      isVerySmallScreen: isVerySmallScreen,
                      isSmallScreen: isSmallScreen,
                      isMediumScreen: isMediumScreen,
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建单个导航项 - 彻底优化空间利用和响应式布局
  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    bool isVerySmallScreen = false,
    bool isSmallScreen = false,
    bool isMediumScreen = false,
  }) {
    // BUG修复: 彻底重构尺寸系统，解决padding累积导致的溢出
    // 修复策略: 单层padding + 极简尺寸 + 智能文本处理
    // 影响范围: lib/src/shared/widgets/main_scaffold.dart:257-330
    // 修复日期: 2025-01-27
    
    // 根据屏幕尺寸动态调整尺寸 - 更激进的空间节省
    final iconSize = isVerySmallScreen ? 18.0 : (isSmallScreen ? 20.0 : 24.0);
    final fontSize = isVerySmallScreen ? 9.0 : (isSmallScreen ? 10.0 : 12.0);
    final verticalPadding = isVerySmallScreen ? 4.0 : (isSmallScreen ? 6.0 : 8.0);
    final horizontalPadding = isVerySmallScreen ? 2.0 : (isSmallScreen ? 3.0 : 4.0);
    final spacingHeight = isVerySmallScreen ? 1.0 : (isSmallScreen ? 2.0 : 3.0);
    final borderRadius = isVerySmallScreen ? 8.0 : (isSmallScreen ? 10.0 : 12.0);
    
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque, // 确保整个区域都可点击
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
          // 使用单层padding，避免多层累积
          padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding, 
            vertical: verticalPadding,
          ),
          decoration: isSelected ? BoxDecoration(
            // 优化后的选中状态背景 - 更加subtle和现代
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.white.withOpacity(0.12),
                Colors.white.withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(borderRadius),
            // 移除突兀的边框，使用更subtle的阴影效果
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.1),
                blurRadius: 6,
                offset: const Offset(0, 1),
                spreadRadius: 0,
              ),
            ],
          ) : null,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedScale(
                scale: isSelected ? 1.05 : 1.0, // 减小缩放避免挤压
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOutCubic,
                child: Icon(
                  icon,
                  color: isSelected
                      ? Colors.white
                      : Colors.white.withOpacity(0.6),
                  size: iconSize,
                ),
              ),
              SizedBox(height: spacingHeight),
              // 使用约束布局避免文本溢出
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: isVerySmallScreen ? 60 : (isSmallScreen ? 70 : 80),
                ),
                child: AnimatedDefaultTextStyle(
                  duration: const Duration(milliseconds: 300),
                  style: TextStyle(
                    fontSize: fontSize,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withOpacity(0.6),
                    letterSpacing: isVerySmallScreen ? 0.1 : 0.2,
                    height: 1.0, // 紧凑行高
                  ),
                  child: FittedBox(
                    fit: BoxFit.scaleDown, // 防止文本溢出，自动缩放
                    child: Text(
                      label,
                      maxLines: 1,
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
