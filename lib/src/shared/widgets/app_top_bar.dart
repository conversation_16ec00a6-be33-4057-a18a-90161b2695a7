import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'country_selector.dart';
import 'settings_menu.dart';
import '../providers/radio_providers.dart';
import '../models/radio_models.dart';

/// 现代化的顶部导航栏组件
/// 
/// 提供精美的品牌展示和统一的导航栏设计
class AppTopBar extends ConsumerWidget implements PreferredSizeWidget {
  const AppTopBar({
    super.key,
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
    this.backgroundColor,
    this.elevation = 0,
    this.centerTitle = false,
    this.title,
    this.showUserAvatar = true,
    this.showCountrySelector = true,
  });

  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final double elevation;
  final bool centerTitle;
  final Widget? title;
  final bool showUserAvatar;
  final bool showCountrySelector;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 8);

  /// 国家切换时的数据刷新回调
  static void _onCountryChanged(BuildContext context, WidgetRef ref, Country country) {
    // 强制刷新所有电台数据提供者
    _forceRefreshAllRadioData(ref, country);
  }

  /// 强制刷新所有电台数据
  static void _forceRefreshAllRadioData(WidgetRef ref, Country country) {
    try {
      // 刷新热门电台
      ref.read(popularRadiosProvider.notifier).loadPopularRadios(
        refresh: true,
        countryId: country.id,
      );
      
      // 刷新最新电台
      ref.read(latestRadiosProvider.notifier).loadLatestRadios(
        refresh: true,
        countryId: country.id,
      );
      
      // 刷新分类电台（重置状态）
      ref.read(categoryRadiosProvider.notifier).reset();
      
      // 重置首页Tab专用的分类电台
      ref.read(homeTabRadiosProvider.notifier).reset();
      
      // 刷新搜索结果（清空当前搜索）
      ref.read(searchRadiosProvider.notifier).reset();
      
      // 无效化推荐电台缓存，触发重新获取
      ref.invalidate(recommendedRadiosProvider);
      
      // 触发全局数据刷新通知，让首页等页面感知到国家切换
      ref.read(dataRefreshNotifierProvider.notifier).triggerRefresh();
      
    } catch (e) {
      // 静默处理错误，避免影响用户体验
      debugPrint('刷新电台数据时出错: $e');
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        // 使用与主页面统一的背景色系
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF0F0F23).withOpacity(0.95), // 深蓝黑
            const Color(0xFF1A1A2E).withOpacity(0.90), // 深紫蓝
            const Color(0xFF16213E).withOpacity(0.85), // 深蓝
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        centerTitle: centerTitle,
        leading: showBackButton ? _buildBackButton(context) : null,
        title: title ?? _buildBrandTitle(context),
        titleSpacing: showBackButton ? 0 : 16,
        actions: [
          // 国家选择器
          if (showCountrySelector)
            Container(
              margin: const EdgeInsets.only(right: 8),
              child: CountrySelector(
                isInAppBar: true,
                onCountrySelected: (country) => _onCountryChanged(context, ref, country),
              ),
            ),
          // 设置菜单
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: const SettingsMenu(),
          ),
        ],
      ),
    );
  }

  /// 构建品牌标题
  Widget _buildBrandTitle(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // BUG修复: 图标因为BlendMode.srcIn导致不显示
        // 修复策略: 去除颜色混合模式，保持原图显示，添加圆角容器
        // 影响范围: lib/src/shared/widgets/app_top_bar.dart:141-156行
        // 修复日期: 2025-01-08
        Container(
          width: 28,
          height: 28,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.asset(
              'assets/app_icon.png',
              width: 28,
              height: 28,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                // 如果图标加载失败，回退到默认图标
                return Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Icon(
                    Icons.radio,
                    color: Colors.white,
                    size: 20,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(width: 12),
        // 功能实现: 简化应用名显示，去除渐变效果
        // 实现方案: 直接显示白色文字，使用简洁样式
        // 影响范围: 仅修改导航栏应用名显示样式
        // 实现日期: 2025-01-08
        Text(
          'app_name'.tr(),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  /// 构建返回按钮
  Widget _buildBackButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
        icon: Icon(
          Icons.arrow_back_ios_new,
          color: Colors.grey.shade700,
          size: 18,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }
}

/// 简化版顶部导航栏
/// 
/// 用于不需要用户信息的页面
class SimpleAppTopBar extends ConsumerWidget implements PreferredSizeWidget {
  const SimpleAppTopBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.onBackPressed,
    this.actions,
    this.backgroundColor,
    this.elevation = 0,
  });

  final String title;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final double elevation;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      backgroundColor: backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      elevation: elevation,
      surfaceTintColor: Colors.transparent,
      leading: showBackButton
          ? IconButton(
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back_ios),
            )
          : null,
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: actions,
    );
  }
}
