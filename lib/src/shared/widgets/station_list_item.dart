import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import '../models/models.dart';
import '../services/audio_service.dart';
import '../services/storage_service.dart';
import '../utils/station_image_builder.dart';
import '../utils/text_overflow_handler.dart';

/// 可复用的电台列表项组件
/// 
/// 支持多种布局和功能配置，适用于不同的列表场景
class StationListItem extends ConsumerWidget {
  const StationListItem({
    super.key,
    required this.station,
    this.showFavoriteButton = true,
    this.showPlayButton = true,
    this.showCountry = true,
    this.showTags = false,
    this.margin = const EdgeInsets.only(bottom: 12),
    this.padding = const EdgeInsets.all(12),
    this.imageSize = 56,
    this.borderRadius = 12,
    this.onTap,
    this.style = StationListItemStyle.modern,
  });

  final StationSimple station;
  final bool showFavoriteButton;
  final bool showPlayButton;
  final bool showCountry;
  final bool showTags;
  final EdgeInsets margin;
  final EdgeInsets padding;
  final double imageSize;
  final double borderRadius;
  final VoidCallback? onTap;
  final StationListItemStyle style;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final favoritesAsync = ref.watch(favoriteStationsProvider);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      margin: margin,
      decoration: _buildDecoration(context),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap ?? () => _playStation(ref, context),
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: padding,
            child: Row(
              children: [
                // 电台图片
                _buildStationImage(),
                const SizedBox(width: 12),

                // 电台信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStationTitle(context),
                      const SizedBox(height: 4),
                      _buildStationSubtitle(context),
                    ],
                  ),
                ),

                // 操作按钮
                _buildTrailingActions(context, ref, favoritesAsync),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建容器装饰
  BoxDecoration _buildDecoration(BuildContext context) {
    switch (style) {
      case StationListItemStyle.modern:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        );
      case StationListItemStyle.classic:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        );
      case StationListItemStyle.minimal:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(borderRadius),
        );
      case StationListItemStyle.glassmorphism:
        return BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.05),
              Theme.of(context).cardColor.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color: Colors.white.withOpacity(0.15),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        );
    }
  }

  /// 构建电台图片（使用统一的图片构建工具）
  Widget _buildStationImage() {
    return Container(
      width: imageSize,
      height: imageSize,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius * 0.6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: StationImageBuilder.buildStationImage(
        station: station,
        width: imageSize,
        height: imageSize,
        fit: BoxFit.cover,
        borderRadius: BorderRadius.circular(borderRadius * 0.6),
      ),
    );
  }

  /// 构建电台标题
  Widget _buildStationTitle(BuildContext context) {
    return TextOverflowHandler.safeText(
      station.name,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
      maxLines: 1,
    );
  }

  /// 构建电台副标题
  Widget _buildStationSubtitle(BuildContext context) {
    List<String> subtitleParts = [];
    
    if (showCountry && station.country.isNotEmpty) {
      subtitleParts.add(station.country);
    }
    
    if (showTags && station.tags.isNotEmpty) {
      final tags = station.tags.take(2).toList();
      subtitleParts.addAll(tags);
    }

    if (subtitleParts.isEmpty) {
      return const SizedBox.shrink();
    }

    return TextOverflowHandler.safeText(
      subtitleParts.join(' • '),
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Colors.grey[600],
      ),
      maxLines: 1,
    );
  }

  /// 构建尾部操作按钮
  Widget _buildTrailingActions(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<StationSimple>> favoritesAsync,
  ) {
    List<Widget> actions = [];

    // 收藏按钮
    if (showFavoriteButton) {
      actions.add(_buildFavoriteButton(ref, favoritesAsync));
    }

    // 播放按钮
    if (showPlayButton) {
      actions.add(_buildPlayButton(ref, context));
    }

    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: actions,
    );
  }

  /// 构建收藏按钮
  Widget _buildFavoriteButton(
    WidgetRef ref,
    AsyncValue<List<StationSimple>> favoritesAsync,
  ) {
    return favoritesAsync.when(
      data: (favorites) {
        final isFavorite = favorites.any((fav) => fav.id == station.id);
        return IconButton(
          onPressed: () async {
            final storageService = ref.read(storageServiceProvider);
            if (isFavorite) {
              await storageService.removeFromFavorites(station.id);
            } else {
              await storageService.addToFavorites(station);
            }
            ref.invalidate(favoriteStationsProvider);
          },
          icon: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              key: ValueKey(isFavorite),
              color: isFavorite ? Colors.red : Colors.grey[600],
              size: 20,
            ),
          ),
        );
      },
      loading: () => IconButton(
        onPressed: null,
        icon: Icon(
          Icons.favorite_border,
          color: Colors.grey[400],
          size: 20,
        ),
      ),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  /// 构建播放按钮
  Widget _buildPlayButton(WidgetRef ref, BuildContext context) {
    return IconButton(
      onPressed: () => _playStation(ref, context),
      icon: const Icon(Icons.play_arrow),
      iconSize: 24,
      style: IconButton.styleFrom(
        backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
        foregroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  /// 播放电台
  void _playStation(WidgetRef ref, BuildContext context) async {
    final audioService = ref.read(audioServiceProvider);
    
    try {
      await audioService.playStation(station);
      audioService.refreshPlaybackState();
      
      // 去掉播放成功的弹出信息
      // if (context.mounted) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     SnackBar(
      //       content: Text('${'now_playing'.tr()}: ${station.name}'),
      //       duration: const Duration(seconds: 2),
      //       behavior: SnackBarBehavior.floating,
      //       shape: RoundedRectangleBorder(
      //         borderRadius: BorderRadius.circular(10),
      //       ),
      //     ),
      //   );
      // }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: TextOverflowHandler.safeText(
              '${'playback_error'.tr()}: ${station.name}',
              maxLines: 1,
            ),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
}

/// 电台列表项样式枚举
enum StationListItemStyle {
  /// 现代风格 - 圆角、阴影、边框
  modern,
  /// 经典风格 - 简单圆角和阴影
  classic,
  /// 极简风格 - 仅圆角
  minimal,
  /// 玻璃态风格 - 渐变、透明、模糊
  glassmorphism,
}
