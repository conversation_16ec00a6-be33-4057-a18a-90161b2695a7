import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 音频可视化样式
enum AudioVisualizerStyle {
  bars,      // 条形图样式
  wave,      // 波浪样式
  pulse,     // 脉冲样式
  spectrum,  // 频谱样式
}

/// 音频可视化组件
///
/// 提供音律跳动的视觉效果，用于播放器界面
class AudioVisualizer extends StatefulWidget {
  const AudioVisualizer({
    super.key,
    this.isPlaying = false,
    this.barCount = 5,
    this.barWidth = 3.0,
    this.barSpacing = 2.0,
    this.maxHeight = 20.0,
    this.minHeight = 4.0,
    this.color,
    this.animationDuration = const Duration(milliseconds: 300),
    this.style = AudioVisualizerStyle.bars,
  });

  final bool isPlaying;
  final int barCount;
  final double barWidth;
  final double barSpacing;
  final double maxHeight;
  final double minHeight;
  final Color? color;
  final Duration animationDuration;
  final AudioVisualizerStyle style;

  @override
  State<AudioVisualizer> createState() => _AudioVisualizerState();
}

class _AudioVisualizerState extends State<AudioVisualizer>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  final math.Random _random = math.Random();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.barCount,
      (index) => AnimationController(
        duration: Duration(
          milliseconds: 300 + (_random.nextInt(200)), // 300-500ms 随机
        ),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: widget.minHeight,
        end: widget.maxHeight,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ));
    }).toList();

    // 如果正在播放，启动动画
    if (widget.isPlaying) {
      _startAnimations();
    }
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      // 为每个条形图添加不同的延迟
      Future.delayed(Duration(milliseconds: i * 50), () {
        if (mounted && widget.isPlaying) {
          _animateBar(i);
        }
      });
    }
  }

  void _animateBar(int index) {
    if (!mounted || !widget.isPlaying) return;

    final controller = _controllers[index];

    // 更频繁的动画，模拟真实音频频谱
    Future<void> animationCycle() async {
      if (!mounted || !widget.isPlaying) return;

      // 随机目标值，模拟音频强度变化
      final targetValue = 0.3 + _random.nextDouble() * 0.7;

      await controller.animateTo(
        targetValue,
        duration: Duration(milliseconds: 100 + _random.nextInt(150)),
        curve: Curves.easeInOut,
      );

      if (mounted && widget.isPlaying) {
        // 短暂停留后继续下一个周期
        await Future.delayed(Duration(milliseconds: 50 + _random.nextInt(100)));
        animationCycle();
      }
    }

    animationCycle();
  }

  void _stopAnimations() {
    for (final controller in _controllers) {
      controller.stop();
      controller.animateTo(0.0); // 回到最小高度
    }
  }

  @override
  void didUpdateWidget(AudioVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? Theme.of(context).primaryColor;

    return SizedBox(
      width: (widget.barWidth + widget.barSpacing) * widget.barCount - widget.barSpacing,
      height: widget.maxHeight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(widget.barCount, (index) {
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              final height = widget.isPlaying
                  ? _animations[index].value
                  : widget.minHeight;

              // 根据高度计算透明度和发光强度
              final intensity = height / widget.maxHeight;

              return Container(
                width: widget.barWidth,
                height: height,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      color,
                      color.withOpacity(0.7),
                      color.withOpacity(0.4),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(widget.barWidth / 2),
                  boxShadow: widget.isPlaying ? [
                    BoxShadow(
                      color: color.withOpacity(0.3 * intensity),
                      blurRadius: 4 * intensity,
                      spreadRadius: 1 * intensity,
                    ),
                  ] : null,
                ),
              );
            },
          );
        }),
      ),
    );
  }
}

/// 圆形音频可视化组件
/// 
/// 提供圆形的音律跳动效果
class CircularAudioVisualizer extends StatefulWidget {
  const CircularAudioVisualizer({
    super.key,
    this.isPlaying = false,
    this.size = 40.0,
    this.barCount = 12,
    this.color,
  });

  final bool isPlaying;
  final double size;
  final int barCount;
  final Color? color;

  @override
  State<CircularAudioVisualizer> createState() => _CircularAudioVisualizerState();
}

class _CircularAudioVisualizerState extends State<CircularAudioVisualizer>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late List<AnimationController> _barControllers;
  late List<Animation<double>> _barAnimations;
  final math.Random _random = math.Random();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // 旋转动画
    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    // 条形图动画
    _barControllers = List.generate(
      widget.barCount,
      (index) => AnimationController(
        duration: Duration(milliseconds: 200 + _random.nextInt(300)),
        vsync: this,
      ),
    );

    _barAnimations = _barControllers.map((controller) {
      return Tween<double>(begin: 0.3, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    if (widget.isPlaying) {
      _startAnimations();
    }
  }

  void _startAnimations() {
    _rotationController.repeat();
    
    for (int i = 0; i < _barControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 30), () {
        if (mounted && widget.isPlaying) {
          _animateBar(i);
        }
      });
    }
  }

  void _animateBar(int index) {
    if (!mounted || !widget.isPlaying) return;

    final controller = _barControllers[index];
    
    controller.forward().then((_) {
      if (mounted && widget.isPlaying) {
        controller.reverse().then((_) {
          if (mounted && widget.isPlaying) {
            Future.delayed(
              Duration(milliseconds: _random.nextInt(300)),
              () => _animateBar(index),
            );
          }
        });
      }
    });
  }

  void _stopAnimations() {
    _rotationController.stop();
    for (final controller in _barControllers) {
      controller.stop();
      controller.reset();
    }
  }

  @override
  void didUpdateWidget(CircularAudioVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    for (final controller in _barControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? Theme.of(context).primaryColor;
    
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _rotationController,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotationController.value * 2 * math.pi,
            child: CustomPaint(
              painter: CircularVisualizerPainter(
                barAnimations: _barAnimations,
                color: color,
                isPlaying: widget.isPlaying,
              ),
            ),
          );
        },
      ),
    );
  }
}

/// 背景音频可视化组件
///
/// 用于播放器背景的大型音律跳动效果
class BackgroundAudioVisualizer extends StatefulWidget {
  const BackgroundAudioVisualizer({
    super.key,
    this.isPlaying = false,
    this.color,
    this.opacity = 0.1,
  });

  final bool isPlaying;
  final Color? color;
  final double opacity;

  @override
  State<BackgroundAudioVisualizer> createState() => _BackgroundAudioVisualizerState();
}

class _BackgroundAudioVisualizerState extends State<BackgroundAudioVisualizer>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  final math.Random _random = math.Random();
  final int _barCount = 20;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      _barCount,
      (index) => AnimationController(
        duration: Duration(milliseconds: 200 + _random.nextInt(300)),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.1, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    if (widget.isPlaying) {
      _startAnimations();
    }
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 20), () {
        if (mounted && widget.isPlaying) {
          _animateBar(i);
        }
      });
    }
  }

  void _animateBar(int index) {
    if (!mounted || !widget.isPlaying) return;

    final controller = _controllers[index];

    Future<void> animationCycle() async {
      if (!mounted || !widget.isPlaying) return;

      final targetValue = 0.2 + _random.nextDouble() * 0.8;

      await controller.animateTo(
        targetValue,
        duration: Duration(milliseconds: 150 + _random.nextInt(200)),
        curve: Curves.easeInOut,
      );

      if (mounted && widget.isPlaying) {
        await Future.delayed(Duration(milliseconds: 50 + _random.nextInt(150)));
        animationCycle();
      }
    }

    animationCycle();
  }

  void _stopAnimations() {
    for (final controller in _controllers) {
      controller.stop();
      controller.animateTo(0.1);
    }
  }

  @override
  void didUpdateWidget(BackgroundAudioVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? Theme.of(context).primaryColor;

    return Positioned.fill(
      child: CustomPaint(
        painter: BackgroundVisualizerPainter(
          animations: _animations,
          color: color,
          opacity: widget.opacity,
          isPlaying: widget.isPlaying,
        ),
      ),
    );
  }
}

/// 背景可视化绘制器
class BackgroundVisualizerPainter extends CustomPainter {
  const BackgroundVisualizerPainter({
    required this.animations,
    required this.color,
    required this.opacity,
    required this.isPlaying,
  });

  final List<Animation<double>> animations;
  final Color color;
  final double opacity;
  final bool isPlaying;

  @override
  void paint(Canvas canvas, Size size) {
    if (!isPlaying || animations.isEmpty) return;

    final paint = Paint()
      ..style = PaintingStyle.fill;

    final barWidth = size.width / animations.length;

    for (int i = 0; i < animations.length; i++) {
      final barHeight = size.height * animations[i].value;
      final x = i * barWidth;

      // 创建渐变效果
      final gradient = LinearGradient(
        begin: Alignment.bottomCenter,
        end: Alignment.topCenter,
        colors: [
          color.withOpacity(opacity * animations[i].value),
          color.withOpacity(opacity * animations[i].value * 0.5),
          Colors.transparent,
        ],
      );

      paint.shader = gradient.createShader(
        Rect.fromLTWH(x, size.height - barHeight, barWidth, barHeight),
      );

      canvas.drawRect(
        Rect.fromLTWH(x, size.height - barHeight, barWidth, barHeight),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(BackgroundVisualizerPainter oldDelegate) {
    return true;
  }
}

/// 圆形可视化绘制器
class CircularVisualizerPainter extends CustomPainter {
  const CircularVisualizerPainter({
    required this.barAnimations,
    required this.color,
    required this.isPlaying,
  });

  final List<Animation<double>> barAnimations;
  final Color color;
  final bool isPlaying;

  @override
  void paint(Canvas canvas, Size size) {
    if (!isPlaying) return;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 3;
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    for (int i = 0; i < barAnimations.length; i++) {
      final angle = (2 * math.pi / barAnimations.length) * i;
      final barHeight = 8 * barAnimations[i].value;
      
      final startPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );
      
      final endPoint = Offset(
        center.dx + (radius + barHeight) * math.cos(angle),
        center.dy + (radius + barHeight) * math.sin(angle),
      );
      
      canvas.drawLine(startPoint, endPoint, paint);
    }
  }

  @override
  bool shouldRepaint(CircularVisualizerPainter oldDelegate) {
    return true;
  }
}
