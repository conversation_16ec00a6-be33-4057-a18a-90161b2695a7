import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import '../services/storage_service.dart';
import '../models/models.dart';

/// 导入收藏对话框
class ImportFavoritesDialog extends ConsumerStatefulWidget {
  const ImportFavoritesDialog({super.key});

  @override
  ConsumerState<ImportFavoritesDialog> createState() => _ImportFavoritesDialogState();
}

class _ImportFavoritesDialogState extends ConsumerState<ImportFavoritesDialog> {
  final TextEditingController _textController = TextEditingController();
  List<String>? _parsedStationIds;
  bool _isImporting = false;
  String? _errorMessage;

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  /// 解析输入文本
  void _parseText() {
    setState(() {
      _errorMessage = null;
      _parsedStationIds = null;
    });

    final text = _textController.text.trim();
    if (text.isEmpty) {
      return;
    }

    final storageService = ref.read(storageServiceProvider);
    final stationIds = storageService.parseImportText(text);

    setState(() {
      if (stationIds == null) {
        _errorMessage = 'import_format_error'.tr();
      } else if (stationIds.isEmpty) {
        _errorMessage = 'no_stations_found'.tr();
      } else {
        _parsedStationIds = stationIds;
      }
    });
  }

  /// 执行导入
  Future<void> _performImport() async {
    if (_parsedStationIds == null || _parsedStationIds!.isEmpty) {
      return;
    }

    setState(() {
      _isImporting = true;
      _errorMessage = null;
    });

    try {
      final storageService = ref.read(storageServiceProvider);
      
      final result = await storageService.importFavorites(
        _parsedStationIds!,
        onProgress: (current, total, stationId) {
          // 这里可以显示进度，暂时保持简单
        },
      );

      // 刷新收藏列表
      ref.invalidate(favoriteStationsProvider);

      // 显示结果
      _showImportResult(result);
      
      Navigator.of(context).pop();
    } catch (e) {
      setState(() {
        _errorMessage = 'import_failed'.tr();
        _isImporting = false;
      });
    }
  }

  /// 显示导入结果
  void _showImportResult(ImportResult result) {
    final message = 'import_result'.tr()
        .replaceAll('{success}', result.successCount.toString())
        .replaceAll('{duplicate}', result.duplicateCount.toString())
        .replaceAll('{failed}', result.failedCount.toString());

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF1A1A2E),
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.75,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF0F0F23),
              const Color(0xFF1A1A2E),
              const Color(0xFF16213E),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: const Color(0xFF00FFFF).withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF00FFFF).withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 0),
              spreadRadius: 5,
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: const Color(0xFF00FFFF).withOpacity(0.2),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.download_rounded,
                    color: const Color(0xFF00FFFF),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'import_favorites'.tr(),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close_rounded,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),

            // 内容区域
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // 说明文本
                    Text(
                      'import_instruction'.tr(),
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // 输入框
                    Expanded(
                      flex: 3,
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFF1A1A2E).withOpacity(0.8),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _errorMessage != null
                                ? Colors.red.withOpacity(0.5)
                                : const Color(0xFF00FFFF).withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: TextField(
                          controller: _textController,
                          maxLines: null,
                          expands: true,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                          decoration: InputDecoration(
                            hintText: 'paste_import_text_here'.tr(),
                            hintStyle: TextStyle(
                              color: Colors.white38,
                              fontSize: 14,
                            ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.all(16),
                          ),
                          onChanged: (_) => _parseText(),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 预览区域
                    if (_parsedStationIds != null) ...[
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFF00FFFF).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(0xFF00FFFF).withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle_rounded,
                              color: const Color(0xFF00FFFF),
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'stations_to_import'.tr().replaceAll('{count}', _parsedStationIds!.length.toString()),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    // 错误消息
                    if (_errorMessage != null) ...[
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.red.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline_rounded,
                              color: Colors.red[400],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(
                                  color: Colors.red[300],
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    // 导入进度
                    if (_isImporting) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFF1A1A2E).withOpacity(0.8),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            const LinearProgressIndicator(
                              backgroundColor: Color(0xFF16213E),
                              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00FFFF)),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'importing_stations'.tr(),
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    const SizedBox(height: 20),

                    // 操作按钮
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: _isImporting ? null : () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(
                                  color: Colors.white24,
                                  width: 1,
                                ),
                              ),
                            ),
                            child: Text(
                              'cancel'.tr(),
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: (_parsedStationIds != null && !_isImporting) 
                                ? _performImport 
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF00FFFF),
                              foregroundColor: Colors.black,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 0,
                            ),
                            child: _isImporting
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                                    ),
                                  )
                                : Text(
                                    'import'.tr(),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}