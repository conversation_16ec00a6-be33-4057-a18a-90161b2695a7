import 'dart:async';
import 'package:flutter/material.dart';
import 'package:ad_sdk/ad_sdk.dart';

/// 播放器Modal专用Banner广告组件
/// 
/// 显示在播放器Modal底部，提供沉浸式广告体验
class PlayerBannerAd extends StatefulWidget {
  const PlayerBannerAd({super.key});

  @override
  State<PlayerBannerAd> createState() => _PlayerBannerAdState();
}

class _PlayerBannerAdState extends State<PlayerBannerAd> {
  bool _isAdLoaded = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  /// 加载播放器Banner广告
  Future<void> _loadBannerAd() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      print('🎯 开始加载播放器Banner广告...');
      
      // 使用Completer来等待广告真正加载完成
      final completer = Completer<bool>();
      bool hasCompleted = false;
      
      await AdSDK.load(
        AdType.banner,
        pageId: 'player_banner',
        callback: AdCallback(
          onAdLoaded: (source) {
            print('🎯 播放器Banner广告加载成功: $source');
            if (!hasCompleted) {
              hasCompleted = true;
              completer.complete(true);
            }
          },
          onAdFailedToLoad: (error) {
            print('❌ 播放器Banner广告加载失败: $error');
            if (!hasCompleted) {
              hasCompleted = true;
              completer.complete(false);
            }
            // 尝试加载备用广告
            _loadBackupBannerAd();
          },
        ),
      );
      
      // 等待广告加载完成，最多等待5秒
      final success = await completer.future.timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          print('⏰ 播放器Banner广告加载超时');
          if (!hasCompleted) {
            hasCompleted = true;
          }
          return false;
        },
      );

      if (mounted) {
        setState(() {
          _isAdLoaded = success;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ 播放器Banner广告加载异常: $e');
      if (mounted) {
        setState(() {
          _isAdLoaded = false;
          _isLoading = false;
        });
      }
    }
  }

  /// 加载备用Banner广告
  Future<void> _loadBackupBannerAd() async {
    try {
      print('🎯 尝试加载备用播放器Banner广告...');

      await AdSDK.loadBackup(
        AdType.banner,
        pageId: 'player_banner',
        callback: AdCallback(
          onAdLoaded: (source) {
            print('🎯 备用播放器Banner广告加载成功: $source');
            if (mounted) {
              setState(() {
                _isAdLoaded = true;
                _isLoading = false;
              });
            }
          },
          onAdFailedToLoad: (error) {
            print('❌ 备用播放器Banner广告也加载失败: $error');
            if (mounted) {
              setState(() {
                _isAdLoaded = false;
                _isLoading = false;
              });
            }
          },
        ),
      );
    } catch (e) {
      print('❌ 备用播放器Banner广告加载异常: $e');
      if (mounted) {
        setState(() {
          _isAdLoaded = false;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 只有在广告加载成功时才显示
    if (!_isAdLoaded) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.purple.withOpacity(0.1),
            offset: const Offset(0, 8),
            blurRadius: 24,
            spreadRadius: 2,
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.1),
              Colors.white.withOpacity(0.05),
            ],
          ),
          border: Border.all(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: AdSDK.getBannerWidget(
            pageId: 'player_banner',
            backup: false,
          ),
        ),
      ),
    );
  }
}