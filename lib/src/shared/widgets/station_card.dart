import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'dart:ui' show ImageFilter;
import '../models/models.dart';
import '../services/audio_service.dart';
import '../utils/station_image_builder.dart';
import '../utils/text_overflow_handler.dart';

/// 可复用的电台卡片组件
///
/// 支持多种尺寸和样式配置，适用于不同的UI场景
class StationCard extends ConsumerStatefulWidget {
  const StationCard({
    super.key,
    required this.station,
    this.width = 140,
    this.imageHeight = 100,
    this.borderRadius = 16,
    this.margin = const EdgeInsets.only(right: 12),
    this.padding = const EdgeInsets.all(10),
    this.showCountry = true,
    this.showActions = true,
    this.maxNameLines = 2,
    this.onTap,
    this.style = StationCardStyle.frosted3D,
  });

  final StationSimple station;
  final double width;
  final double imageHeight;
  final double borderRadius;
  final EdgeInsets margin;
  final EdgeInsets padding;
  final bool showCountry;
  final bool showActions;
  final int maxNameLines;
  final VoidCallback? onTap;
  final StationCardStyle style;

  @override
  ConsumerState<StationCard> createState() => _StationCardState();
}

class _StationCardState extends ConsumerState<StationCard> {

  @override
  Widget build(BuildContext context) {
    // 3D毛玻璃风格需要特殊处理
    if (widget.style == StationCardStyle.frosted3D) {
      return _buildFrosted3DCard(context);
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap ?? () => _playStation(context),
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: Container(
            width: widget.width,
            margin: widget.margin,
            decoration: _buildDecoration(context),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 电台图片
                _buildStationImage(context),

                // 电台信息
                Padding(
                  padding: widget.padding,
                  child: _buildStationInfo(context),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建3D毛玻璃卡片
  Widget _buildFrosted3DCard(BuildContext context) {
    return
      Transform(
        // 3D变换效果
        transform: Matrix4.identity()
          ..setEntry(3, 2, 0.0008) // 透视效果
          ..rotateX(0.12) // X轴旋转
          ..rotateY(-0.08) // Y轴旋转
          ..translate(0.0, -5.0, 8.0), // 移动
        child: Container(
          width: widget.width,
          margin: widget.margin,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: widget.onTap ?? () => _playStation(context),
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(widget.borderRadius),
                      child: BackdropFilter(
                // 毛玻璃模糊效果
                filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
                        child: Container(
                          decoration: BoxDecoration(
                    // 渐变背景
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        const Color(0xFF1A1A2E).withOpacity(0.95), // 深紫蓝
                        const Color(0xFF16213E).withOpacity(0.90), // 深蓝
                        const Color(0xFF0F3460).withOpacity(0.85), // 中蓝
                        const Color(0xFF00FFFF).withOpacity(0.08), // 霓虹青色高光
                        Colors.white.withOpacity(0.12), // 白色高光
                      ],
                      stops: const [0.0, 0.3, 0.6, 0.8, 1.0],
                    ),
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    border: Border.all(
                      color: const Color(0xFF00FFFF).withOpacity(0.25), // 霓虹边框
                      width: 2.0,
                    ),
                    boxShadow: [
                      // 主要3D阴影
                      BoxShadow(
                        color: Colors.black.withOpacity(0.6),
                        blurRadius: 40,
                        offset: const Offset(12, 20), // 3D阴影偏移
                        spreadRadius: 2,
                      ),
                      // 次要阴影
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 25,
                        offset: const Offset(6, 12),
                        spreadRadius: 1,
                      ),
                      // 霓虹发光效果
                      BoxShadow(
                        color: const Color(0xFF00FFFF).withOpacity(0.15),
                        blurRadius: 25,
                        offset: const Offset(0, 0),
                        spreadRadius: 3,
                      ),
                      // 高光效果
                      BoxShadow(
                        color: Colors.white.withOpacity(0.15),
                        blurRadius: 30,
                        offset: const Offset(-6, -10),
                        spreadRadius: 0,
                      ),
                    ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 电台图片
                        _buildStationImage(context),
                        // 电台信息
                        Padding(
                          padding: widget.padding,
                          child: _buildStationInfo(context),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
    );
  }

  /// 构建容器装饰
  BoxDecoration _buildDecoration(BuildContext context) {
    switch (widget.style) {
      case StationCardStyle.modern:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        );
      case StationCardStyle.classic:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        );
      case StationCardStyle.minimal:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
        );
      case StationCardStyle.compact:
        return BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.06),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
          border: Border.all(
            color: Colors.grey.shade200,
            width: 0.5,
          ),
        );
      case StationCardStyle.glassmorphism:
        return BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.15),
              Colors.white.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            // 主阴影
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 20,
              offset: const Offset(0, 8),
              spreadRadius: 0,
            ),
            // 高光效果
            BoxShadow(
              color: Colors.white.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
              spreadRadius: 0,
            ),
          ],
        );
      case StationCardStyle.frosted3D:
        // frosted3D样式在_buildFrosted3DCard中处理，这里返回默认装饰
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(widget.borderRadius),
        );
    }
  }

  /// 构建电台图片（使用统一的图片构建工具）
  Widget _buildStationImage(BuildContext context) {
    return Container(
      height: widget.imageHeight,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(widget.borderRadius),
        ),
      ),
      child: StationImageBuilder.buildStationImage(
        station: widget.station,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(widget.borderRadius),
        ),
      ),
    );
  }

  /// 构建电台信息
  Widget _buildStationInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextOverflowHandler.safeText(
          widget.station.name,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.white,
            fontSize: widget.style == StationCardStyle.compact ? 12 : 13,
          ),
          maxLines: widget.maxNameLines,
        ),
        if (widget.showCountry) ...[
          const SizedBox(height: 2),
          TextOverflowHandler.safeText(
            widget.station.country,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontSize: widget.style == StationCardStyle.compact ? 10 : 11,
            ),
            maxLines: 1,
          ),
        ],
      ],
    );
  }

  /// 播放电台
  void _playStation(BuildContext context) async {
    final audioService = ref.read(audioServiceProvider);

    try {
      await audioService.playStation(widget.station);
      audioService.refreshPlaybackState();

      // 去掉播放成功的弹出信息
      // if (context.mounted) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     SnackBar(
      //       content: Text('${'now_playing'.tr()}: ${widget.station.name}'),
      //       duration: const Duration(seconds: 2),
      //       behavior: SnackBarBehavior.floating,
      //       shape: RoundedRectangleBorder(
      //         borderRadius: BorderRadius.circular(10),
      //       ),
      //     ),
      //   );
      // }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: TextOverflowHandler.safeText(
              '${'playback_error'.tr()}: ${widget.station.name}',
              maxLines: 1,
            ),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
}

/// 电台卡片样式枚举
enum StationCardStyle {
  /// 现代风格 - 圆角、阴影、边框
  modern,
  /// 经典风格 - 简单圆角和阴影
  classic,
  /// 极简风格 - 仅圆角
  minimal,
  /// 紧凑风格 - 小尺寸、轻阴影
  compact,
  /// 玻璃态风格 - 渐变、透明、模糊
  glassmorphism,
  /// 3D毛玻璃风格 - 3D变换 + 毛玻璃效果
  frosted3D,
}
