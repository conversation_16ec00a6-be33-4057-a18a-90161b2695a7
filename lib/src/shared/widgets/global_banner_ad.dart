import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:ad_sdk/ad_sdk.dart';

/// 全局悬浮Banner广告组件
/// 
/// 悬浮显示在底部，采用玻璃态设计，类似MiniPlayer风格
class GlobalBannerAd extends StatefulWidget {
  const GlobalBannerAd({
    super.key,
    this.bottomOffset = 0.0,
  });

  /// 距离底部的偏移量，用于适配MiniPlayer等其他悬浮元素
  final double bottomOffset;

  @override
  State<GlobalBannerAd> createState() => _GlobalBannerAdState();
}

class _GlobalBannerAdState extends State<GlobalBannerAd> {
  bool _isAdLoaded = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadBannerAd();
  }

  /// 加载Banner广告
  Future<void> _loadBannerAd() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      print('🎯 开始加载全局Banner广告...');

      // 使用Completer来等待广告真正加载完成
      final completer = Completer<bool>();

      await AdSDK.load(
        AdType.banner,
        pageId: 'global_banner',
        callback: AdCallback(
          onAdLoaded: (source) {
            print('🎯 全局Banner广告加载成功: $source');
            if (!completer.isCompleted) {
              completer.complete(true);
            }
          },
          onAdFailedToLoad: (error) {
            print('❌ 全局Banner广告加载失败: $error');
            if (!completer.isCompleted) {
              completer.complete(false);
            }
            // // 尝试加载备用广告
            // _loadBackupBannerAd();
          },
        ),
      );

      // 等待广告加载完成，最多等待8秒
      final success = await completer.future.timeout(
        const Duration(seconds: 8),
        onTimeout: () {
          print('⏰ 全局Banner广告加载超时');
          return false;
        },
      );

      print('🎯 全局Banner广告加载结果: success=$success');

      if (mounted) {
        setState(() {
          _isAdLoaded = success;
          _isLoading = false;
        });
        print('🎯 全局Banner广告状态更新: _isAdLoaded=$_isAdLoaded');
      }
    } catch (e) {
      print('❌ 全局Banner广告加载异常: $e');
      if (mounted) {
        setState(() {
          _isAdLoaded = false;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    print('🎯 FloatingBannerAd build: _isAdLoaded=$_isAdLoaded, _isLoading=$_isLoading');

    // 边界检测：检查是否应该显示悬浮广告
    if (!_shouldShowFloatingAd(context)) {
      print('🎯 FloatingBannerAd: 边界检测失败，不显示广告');
      return const SizedBox.shrink();
    }

    // 只有在广告加载成功时才显示
    if (!_isAdLoaded) {
      print('🎯 FloatingBannerAd: 广告未加载，返回空Widget');
      return const SizedBox.shrink();
    }

    print('🎯 FloatingBannerAd: 广告已加载，显示悬浮Banner');

    // 适合Banner广告内容的尺寸，避免上下黑边
    return Container(
      height: 60, // 减小高度以贴合广告内容，避免上下空白
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8), // 保持与MiniPlayer相同的边距
      child: Material(
        color: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            // 与MiniPlayer统一的玻璃态背景
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF0F0F23).withOpacity(0.85), // 深蓝黑
                const Color(0xFF1A1A2E).withOpacity(0.80), // 深紫蓝
                const Color(0xFF16213E).withOpacity(0.75), // 深蓝
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            borderRadius: BorderRadius.circular(20), // 与MiniPlayer相同的圆角
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              // 主阴影
              BoxShadow(
                color: Colors.black.withOpacity(0.25),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
              // 高光效果
              BoxShadow(
                color: Colors.white.withOpacity(0.08),
                blurRadius: 12,
                offset: const Offset(0, -2),
                spreadRadius: 0,
              ),
              // 广告特有的金色光晕
              BoxShadow(
                color: const Color(0xFFFFD700).withOpacity(0.1),
                blurRadius: 16,
                spreadRadius: 1,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20), // 与MiniPlayer相同的圆角
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: _buildAdContent(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建广告内容
  Widget _buildAdContent() {
    // 获取Banner Widget
    final bannerWidget = AdSDK.getBannerWidget(
      pageId: 'global_banner',
      backup: false,
    );
    print('🎯 FloatingBannerAd: Banner Widget类型: ${bannerWidget.runtimeType}');

    return ClipRRect(
      borderRadius: BorderRadius.circular(16), // 内容区域稍小的圆角，与MiniPlayer内容风格一致
      child: bannerWidget,
    );
  }

  /// 边界检测：检查是否应该显示悬浮广告
  bool _shouldShowFloatingAd(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenSize = mediaQuery.size;
    final bottomInset = mediaQuery.viewInsets.bottom;
    final bottomPadding = mediaQuery.padding.bottom;
    final orientation = mediaQuery.orientation;

    // 1. 检查屏幕尺寸 - 最小高度要求
    final minHeightRequired = orientation == Orientation.portrait ? 600.0 : 400.0;
    if (screenSize.height < minHeightRequired) {
      print('🎯 边界检测: 屏幕高度不足 ${screenSize.height} (最小需要 $minHeightRequired)');
      return false;
    }

    // 2. 检查键盘状态 - 键盘弹出时隐藏
    if (bottomInset > 0) {
      print('🎯 边界检测: 键盘已弹出 $bottomInset');
      return false;
    }

    // 3. 检查屏幕方向 - 横屏时更严格的限制
    if (orientation == Orientation.landscape) {
      // 横屏时检查宽度是否足够
      if (screenSize.width < 600) {
        print('🎯 边界检测: 横屏宽度不足 ${screenSize.width}');
        return false;
      }
      
      // 横屏时减少悬浮元素高度要求
      final reservedSpace = 120 + widget.bottomOffset + bottomPadding; // 横屏预留空间更少
      final availableContentSpace = screenSize.height - reservedSpace;
      if (availableContentSpace < 200) {
        print('🎯 边界检测: 横屏可用内容空间不足 $availableContentSpace');
        return false;
      }
    } else {
      // 竖屏检查可用空间
      final reservedSpace = 200 + widget.bottomOffset + bottomPadding + 100; // 预留导航栏空间
      final availableContentSpace = screenSize.height - reservedSpace;
      if (availableContentSpace < 400) {
        print('🎯 边界检测: 竖屏可用内容空间不足 $availableContentSpace');
        return false;
      }
    }

    // 4. 检查设备类型适配
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    if (devicePixelRatio > 3.0) {
      // 高分辨率设备，可能是小屏设备
      if (screenSize.height / devicePixelRatio < 200) {
        print('🎯 边界检测: 高分辨率小屏设备，高度不足');
        return false;
      }
    }

    // 5. 检查悬浮元素总高度限制
    final totalFloatingHeight = 60 + widget.bottomOffset + 32; // 广告高度 + 偏移 + 边距
    final maxFloatingRatio = orientation == Orientation.portrait ? 0.25 : 0.35; // 最大占用屏幕比例
    if (totalFloatingHeight > screenSize.height * maxFloatingRatio) {
      print('🎯 边界检测: 悬浮元素高度超出限制 $totalFloatingHeight > ${screenSize.height * maxFloatingRatio}');
      return false;
    }

    print('🎯 边界检测: 通过所有检查，可以显示悬浮广告');
    return true;
  }
}
