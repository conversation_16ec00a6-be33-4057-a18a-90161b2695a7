import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import '../models/radio_models.dart';
import '../providers/state_provider.dart';
import '../providers/country_provider.dart';

/// 省州选择器组件
/// 
/// 功能特点：
/// - 只有选择了具体国家后才显示
/// - 下拉列表形式，支持大量数据
/// - 显示电台数量，过滤掉数量为0的省州
/// - 遵循Material Design 3设计规范
class StateSelector extends ConsumerStatefulWidget {
  /// 是否在搜索栏区域显示（影响样式）
  final bool isInSearchArea;
  
  /// 自定义宽度
  final double? width;
  
  /// 选择回调
  final ValueChanged<CountryState?>? onStateSelected;

  const StateSelector({
    super.key,
    this.isInSearchArea = true,
    this.width,
    this.onStateSelected,
  });

  @override
  ConsumerState<StateSelector> createState() => _StateSelectorState();
}

class _StateSelectorState extends ConsumerState<StateSelector> {
  @override
  Widget build(BuildContext context) {
    final selectedCountry = ref.watch(selectedCountryProvider);
    final stateSelectionState = ref.watch(stateSelectionProvider);
    // 🔧 修复：使用统一的状态源 selectedStateProvider，确保与 ExplorePage 一致
    final selectedState = ref.watch(selectedStateProvider);

    print('🏗️ StateSelector: build 调用');
    print('🏗️   - selectedCountry: ${selectedCountry?.name}');
    print('🏗️   - selectedState (via selectedStateProvider): ${selectedState?.stateName ?? 'null'}');
    print('🏗️   - selectedState (via stateSelectionState): ${stateSelectionState.selectedState?.stateName ?? 'null'}');
    print('🏗️   - 状态一致性检查: ${selectedState == stateSelectionState.selectedState ? '✅ 一致' : '❌ 不一致'}');
    print('🏗️   - availableStates count: ${stateSelectionState.availableStates.length}');
    
    // 只有选择了具体国家后才显示省州选择器
    if (selectedCountry == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      width: widget.width ?? 180,
      child: widget.isInSearchArea 
        ? _buildSearchAreaSelector(selectedCountry, stateSelectionState, selectedState)
        : _buildStandardSelector(selectedCountry, stateSelectionState, selectedState),
    );
  }

  /// 构建搜索区域的选择器（紧凑样式）
  Widget _buildSearchAreaSelector(
    Country selectedCountry, 
    StateSelectionState stateSelectionState, 
    CountryState? selectedState
  ) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(24),
      ),
      child: _buildDropdownButton(stateSelectionState, selectedState),
    );
  }

  /// 构建标准选择器
  Widget _buildStandardSelector(
    Country selectedCountry, 
    StateSelectionState stateSelectionState, 
    CountryState? selectedState
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: _buildDropdownButton(stateSelectionState, selectedState),
    );
  }

  /// 构建下拉按钮
  Widget _buildDropdownButton(StateSelectionState stateSelectionState, CountryState? selectedState) {
    final availableStates = stateSelectionState.availableStates;
    
    if (stateSelectionState.isLoading) {
      return _buildLoadingWidget();
    }
    
    if (stateSelectionState.error != null) {
      return _buildErrorWidget(stateSelectionState.error!);
    }
    
    if (availableStates.isEmpty) {
      return _buildEmptyWidget();
    }

    return DropdownButtonHideUnderline(
      child: DropdownButton<CountryState?>(
        value: selectedState,
        hint: _buildHint(),
        isExpanded: true,
        icon: Icon(
          Icons.keyboard_arrow_down_rounded,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
          size: 20,
        ),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurface,
          fontWeight: FontWeight.w500,
        ),
        dropdownColor: Theme.of(context).colorScheme.surface,
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        menuMaxHeight: 300, // 限制最大高度，支持滚动
        items: _buildDropdownItems(availableStates),
        onChanged: (CountryState? newState) {
          print('🏗️ StateSelector: onChanged 被调用');
          print('🏗️   - 旧状态: ${selectedState?.stateName ?? 'null'}');
          print('🏗️   - 新状态: ${newState?.stateName ?? 'null'}');

          // 调用状态更新
          ref.read(stateSelectionProvider.notifier).selectState(newState);

          // 立即验证状态是否更新
          Future.delayed(const Duration(milliseconds: 50), () {
            final updatedState = ref.read(selectedStateProvider);
            print('🏗️ StateSelector: 状态更新验证');
            print('🏗️   - 期望状态: ${newState?.stateName ?? 'null'}');
            print('🏗️   - 实际状态: ${updatedState?.stateName ?? 'null'}');
            print('🏗️   - 更新成功: ${updatedState == newState ? '✅' : '❌'}');
          });

          // 调用回调
          widget.onStateSelected?.call(newState);

          print('🏗️ StateSelector: selectState 和 onStateSelected 调用完成');
        },
      ),
    );
  }

  /// 构建下拉菜单项
  List<DropdownMenuItem<CountryState?>> _buildDropdownItems(List<CountryState> availableStates) {
    final items = <DropdownMenuItem<CountryState?>>[];
    
    // 添加"全部"选项
    items.add(
      DropdownMenuItem<CountryState?>(
        value: null,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Icon(
                Icons.public_rounded,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'all_states'.tr(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (availableStates.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      Container(
                        height: 1,
                        width: double.infinity,
                        margin: const EdgeInsets.only(top: 4),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
    
    // 注释掉分隔线，避免重复的null值导致DropdownButton错误
    // if (availableStates.isNotEmpty) {
    //   items.add(
    //     DropdownMenuItem<CountryState?>(
    //       enabled: false,
    //       value: null,
    //       child: Container(
    //         height: 1,
    //         margin: const EdgeInsets.symmetric(vertical: 4),
    //         color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
    //       ),
    //     ),
    //   );
    // }
    
    // 添加省州选项
    for (final state in availableStates) {
      items.add(
        DropdownMenuItem<CountryState?>(
          value: state,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              children: [
                Icon(
                  Icons.location_on_rounded,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    state.stateName,
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    state.stationCount.toString(),
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    
    return items;
  }

  /// 构建提示文本
  Widget _buildHint() {
    return Row(
      children: [
        Icon(
          Icons.tune_rounded,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Text(
          'select_state'.tr(),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// 构建加载状态组件
  Widget _buildLoadingWidget() {
    return Row(
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'loading_states'.tr(),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// 构建错误状态组件
  Widget _buildErrorWidget(String error) {
    return Row(
      children: [
        Icon(
          Icons.error_outline_rounded,
          size: 16,
          color: Theme.of(context).colorScheme.error,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'load_states_failed'.tr(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// 构建空状态组件
  Widget _buildEmptyWidget() {
    return Row(
      children: [
        Icon(
          Icons.location_off_rounded,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Text(
          'no_states_available'.tr(),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}