# 主界面组件 (Main Scaffold)

## 概述
该模块提供应用的主界面脚手架组件，包含底部导航栏、全局播放器等核心UI元素。

## 核心组件

### MainScaffold
主脚手架组件，负责应用的整体布局和导航管理。

#### 主要功能：
- **底部导航栏管理**：根据当前路由自动更新选中状态
- **全局播放器集成**：提供固定在底部的迷你播放器
- **响应式布局**：为迷你播放器预留空间，避免内容遮挡
- **路由同步**：监听路由变化并同步导航栏状态

#### 关键特性：
- 使用 `GoRouter` 进行路由管理
- 支持多语言（通过 `easy_localization`）
- 自动路由状态同步
- 响应式UI设计

## 技术实现

### 路由状态管理
```dart
void _updateCurrentIndex() {
  final String location = GoRouterState.of(context).location;
  // 根据当前路由更新底部导航栏选中状态
}
```

### 布局结构
- **AppTopBar**：顶部应用栏
- **主内容区域**：可滚动的页面内容
- **MiniPlayer**：固定在底部的迷你播放器
- **BottomNavigationBar**：底部导航栏

## 最近更新
- **2024-01-XX**：修复了GoRouter 6.x兼容性问题
  - 将 `matchedLocation` 替换为 `location` 属性
  - 解决了构建错误问题

## 依赖项
- `go_router: ^6.0.3`
- `easy_localization: ^3.0.7+1`
- `flutter/material.dart`

## 使用示例
```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: AppRouter.router,
      builder: (context, child) {
        return MainScaffold(child: child!);
      },
    );
  }
}
```

## 注意事项
- 确保所有导航项都在 `BottomNavItem.items` 中定义
- MiniPlayer的高度为96px，主内容区域会自动适配
- 路由路径必须与导航项的路径完全匹配

## 相关文件
- `lib/src/shared/widgets/mini_player.dart` - 迷你播放器组件
- `lib/src/shared/widgets/app_top_bar.dart` - 顶部应用栏组件
- `lib/src/shared/router/app_router.dart` - 路由配置
