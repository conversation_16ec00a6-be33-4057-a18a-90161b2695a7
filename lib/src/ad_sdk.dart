import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart' as gma;
import 'models/ad_type.dart';
import 'models/ad_config.dart';
import 'models/ad_callback.dart';
import 'models/ad_events.dart';
import 'core/ad_manager.dart';

/// 事件上报函数类型定义
typedef EventReporter = void Function(String eventName, Map<String, dynamic> parameters);

/// 价格事件上报器类型定义
///
/// [scene] 广告场景
/// [posid] 广告ID
/// [platform] 广告平台来源
/// [price] 广告价格（美元）
/// [currency] 货币代码
/// [paytype] 精确度类型
typedef EventPriceReporter = void Function(
  int scene,
  String posid,
  String platform,
  double price,
  String currency,
  int paytype,
);

/// 广告SDK主入口类
/// 
/// 提供简洁的API来管理广告的加载和展示
/// 支持主广告和备用广告，自动处理缓存策略
class AdSDK {
  static AdManager? _manager;
  static bool _initialized = false;

  /// 全局事件上报器
  static EventReporter? _eventReporter;

  /// 全局价格事件上报器
  static EventPriceReporter? _priceReporter;

  /// 初始化广告SDK
  ///
  /// [config] 广告配置，包含所有广告类型的主广告和备用广告ID
  ///
  /// 示例：
  /// ```dart
  /// await AdSDK.initialize(AdConfig(
  ///   ads: {
  ///     AdType.banner: AdIds(
  ///       main: 'ca-app-pub-xxx/banner-main',
  ///       backup: 'ca-app-pub-xxx/banner-backup',
  ///     ),
  ///     AdType.interstitial: AdIds(
  ///       main: 'ca-app-pub-xxx/interstitial-main',
  ///       backup: 'ca-app-pub-xxx/interstitial-backup',
  ///     ),
  ///   },
  ///   enableAdInspector: true, // 启用广告检查器（仅调试模式）
  /// ));
  /// ```
  static Future<void> initialize(AdConfig config) async {
    if (_initialized) {
      throw StateError('AdSDK已经初始化，请勿重复初始化');
    }

    _manager = AdManager(config);
    await _manager!.initialize();

    // 如果已经设置了事件上报器，同步到AdManager
    if (_eventReporter != null) {
      _manager!.setEventReporter(_eventReporter!);
    }

    _initialized = true;
  }

  /// 加载主广告到缓存
  ///
  /// [type] 广告类型
  /// [callback] 可选的回调函数
  /// [bannerSize] Banner广告尺寸（仅Banner广告有效）
  /// [pageId] 页面标识（仅Banner广告需要，用于区分不同页面的相同广告位）
  ///
  /// 注意：Banner广告不支持缓存，每次都是实时加载
  ///
  /// 示例：
  /// ```dart
  /// // 非Banner广告
  /// await AdSDK.load(AdType.rewarded);
  ///
  /// // Banner广告
  /// await AdSDK.load(AdType.banner, pageId: 'home_page');
  /// ```
  static Future<void> load(
    AdType type, {
    AdCallback? callback,
    gma.AdSize? bannerSize,
    String? pageId,
  }) async {
    _ensureInitialized();

    // 检查是否有缓存，只有没有缓存时才上报请求事件
    final hasCache = _manager!.isReady(type, false);
    if (!hasCache) {
      // 上报广告请求事件（只有真实网络请求才上报）
      _reportEvent(AdEvents.eventNameAdSdk, AdEvents.createAdRequestParams(
        placementId: _getAdId(type, false),
      ));
    }

    // 获取广告ID和平台信息
    final adId = _getAdId(type, false);
    final platform = _manager!.getCachedAdPlatform(adId) ?? 'unknown';

    // 创建带事件上报的回调包装器
    final reportingCallback = _eventReporter != null
      ? AdCallback.withReporting(
          placementId: adId,
          eventReporter: _reportEvent,
          originalCallback: callback,
          startTime: hasCache ? null : DateTime.now(), // 只有实时请求才记录开始时间
          isBanner: type == AdType.banner, // Banner广告标记
          scene: null, // load方法不传递场景参数
          platform: platform, // 传递平台信息
          platformGetter: _manager!.getCachedAdPlatform, // 传递获取平台信息的函数
          priceReporter: _priceReporter, // 传递价格上报器
        )
      : callback;

    return _manager!.load(type, backup: false, callback: reportingCallback, bannerSize: bannerSize, pageId: pageId);
  }

  /// 展示主广告
  ///
  /// 优先使用缓存中的广告，如果缓存中没有则实时加载
  ///
  /// [type] 广告类型
  /// [callback] 可选的回调函数
  /// [bannerSize] Banner广告尺寸（仅Banner广告有效）
  /// [pageId] 页面标识（仅Banner广告需要，用于区分不同页面的相同广告位）
  /// [scene] 广告场景参数（可选），用于价格事件上报
  ///
  /// 示例：
  /// ```dart
  /// // 非Banner广告
  /// await AdSDK.show(AdType.interstitial,
  ///   callback: AdCallback(
  ///     onAdShowed: (source) => print('广告展示，来源：$source'),
  ///   ),
  /// );
  ///
  /// // Banner广告
  /// await AdSDK.show(AdType.banner,
  ///   pageId: 'home_page',
  ///   bannerSize: gma.AdSize.largeBanner,
  /// );
  /// ```
  static Future<void> show(
    AdType type, {
    AdCallback? callback,
    gma.AdSize? bannerSize,
    String? pageId,
    int? scene, // 新增场景参数，用于价格事件上报
  }) async {
    _ensureInitialized();

    // 获取广告ID和平台信息
    final adId = _getAdId(type, false);
    final platform = _manager!.getCachedAdPlatform(adId) ?? 'unknown';

    // 创建带事件上报的回调包装器
    final reportingCallback = _eventReporter != null
      ? AdCallback.withReporting(
          placementId: adId,
          eventReporter: _reportEvent,
          originalCallback: callback,
          isBanner: type == AdType.banner, // Banner广告标记
          scene: scene, // 传递场景参数
          platform: platform, // 传递平台信息
          platformGetter: _manager!.getCachedAdPlatform, // 传递获取平台信息的函数
          priceReporter: _priceReporter, // 传递价格上报器
        )
      : callback;

    return _manager!.show(type, backup: false, callback: reportingCallback, bannerSize: bannerSize, pageId: pageId);
  }

  /// 加载备用广告到缓存
  ///
  /// [type] 广告类型
  /// [callback] 可选的回调函数
  /// [bannerSize] Banner广告尺寸（仅Banner广告有效）
  /// [pageId] 页面标识（仅Banner广告需要，用于区分不同页面的相同广告位）
  ///
  /// 示例：
  /// ```dart
  /// // 非Banner广告
  /// await AdSDK.loadBackup(AdType.rewarded);
  ///
  /// // Banner广告
  /// await AdSDK.loadBackup(AdType.banner, pageId: 'home_page');
  /// ```
  static Future<void> loadBackup(
    AdType type, {
    AdCallback? callback,
    gma.AdSize? bannerSize,
    String? pageId,
  }) async {
    _ensureInitialized();

    // 检查是否有备用缓存，只有没有缓存时才上报请求事件
    final hasCache = _manager!.isReady(type, true);
    if (!hasCache) {
      // 上报备用广告请求事件（只有真实网络请求才上报）
      _reportEvent(AdEvents.eventNameAdSdk, AdEvents.createAdRequestParams(
        placementId: _getAdId(type, true),
      ));
    }

    // 获取广告ID和平台信息
    final adId = _getAdId(type, true);
    final platform = _manager!.getCachedAdPlatform(adId) ?? 'unknown';

    // 创建带事件上报的回调包装器
    final reportingCallback = _eventReporter != null
      ? AdCallback.withReporting(
          placementId: adId,
          eventReporter: _reportEvent,
          originalCallback: callback,
          startTime: hasCache ? null : DateTime.now(), // 只有实时请求才记录开始时间
          isBanner: type == AdType.banner, // Banner广告标记
          scene: null, // loadBackup方法不传递场景参数
          platform: platform, // 传递平台信息
          platformGetter: _manager!.getCachedAdPlatform, // 传递获取平台信息的函数
          priceReporter: _priceReporter, // 传递价格上报器
        )
      : callback;

    return _manager!.load(type, backup: true, callback: reportingCallback, bannerSize: bannerSize, pageId: pageId);
  }

  /// 展示备用广告
  ///
  /// 优先使用缓存中的备用广告，如果缓存中没有则实时加载备用广告
  ///
  /// [type] 广告类型
  /// [callback] 可选的回调函数
  /// [bannerSize] Banner广告尺寸（仅Banner广告有效）
  /// [pageId] 页面标识（仅Banner广告需要，用于区分不同页面的相同广告位）
  /// [scene] 广告场景参数（可选），用于价格事件上报
  ///
  /// 示例：
  /// ```dart
  /// // 非Banner广告
  /// await AdSDK.showBackup(AdType.interstitial);
  ///
  /// // Banner广告
  /// await AdSDK.showBackup(AdType.banner, pageId: 'home_page');
  /// ```
  static Future<void> showBackup(
    AdType type, {
    AdCallback? callback,
    gma.AdSize? bannerSize,
    String? pageId,
    int? scene, // 广告场景参数，用于价格事件上报
  }) async {
    _ensureInitialized();

    // 获取广告ID和平台信息
    final adId = _getAdId(type, true);
    final platform = _manager!.getCachedAdPlatform(adId) ?? 'unknown';

    // 创建带事件上报的回调包装器
    final reportingCallback = _eventReporter != null
      ? AdCallback.withReporting(
          placementId: adId,
          eventReporter: _reportEvent,
          originalCallback: callback,
          isBanner: type == AdType.banner, // Banner广告标记
          scene: scene, // 传递场景参数
          platform: platform, // 传递平台信息
          platformGetter: _manager!.getCachedAdPlatform, // 传递获取平台信息的函数
          priceReporter: _priceReporter, // 传递价格上报器
        )
      : callback;

    return _manager!.show(type, backup: true, callback: reportingCallback, bannerSize: bannerSize, pageId: pageId);
  }

  /// 检查广告是否已准备好
  ///
  /// [type] 广告类型
  /// [backup] 是否检查备用广告，默认为false
  ///
  /// 返回true表示广告已缓存可以立即展示
  /// 注意：Banner广告不支持缓存，总是返回false
  ///
  /// 示例：
  /// ```dart
  /// bool mainReady = AdSDK.isReady(AdType.rewarded);
  /// bool backupReady = AdSDK.isReady(AdType.rewarded, backup: true);
  /// ```
  static bool isReady(
    AdType type, {
    bool backup = false,
  }) {
    _ensureInitialized();
    return _manager!.isReady(type, backup);
  }

  /// 获取Banner广告Widget（仅Banner广告有效）
  ///
  /// [pageId] 页面标识，用于区分不同页面的Banner广告
  /// [backup] 是否获取备用广告，默认为false
  ///
  /// 返回Banner广告Widget，可以直接在页面中使用
  ///
  /// 示例：
  /// ```dart
  /// // 先加载Banner广告
  /// await AdSDK.load(AdType.banner, pageId: 'home_page');
  ///
  /// // 获取Widget用于显示
  /// Widget bannerWidget = AdSDK.getBannerWidget(pageId: 'home_page');
  /// ```
  static Widget getBannerWidget({
    required String pageId,
    bool backup = false,
  }) {
    _ensureInitialized();
    return _manager!.getBannerWidget(pageId, backup);
  }

  /// 释放指定页面的Banner广告资源
  ///
  /// [pageId] 页面标识
  /// [backup] 是否释放备用广告，默认为false
  ///
  /// 示例：
  /// ```dart
  /// // 页面销毁时释放Banner广告
  /// AdSDK.disposeBanner(pageId: 'home_page');
  /// ```
  static void disposeBanner({
    required String pageId,
    bool backup = false,
  }) {
    _ensureInitialized();
    _manager!.disposeBanner(pageId, backup);
  }









  /// 预加载原生广告（不绑定样式）
  ///
  /// [type] 广告类型，必须是AdType.native
  /// [backup] 是否使用备用广告，默认false
  /// [callback] 广告回调
  ///
  /// 示例：
  /// ```dart
  /// await AdSDK.preLoad(AdType.native);
  /// ```
  static Future<void> preLoad(
    AdType type, {
    bool backup = false,
    AdCallback? callback,
  }) async {
    _ensureInitialized();
    if (type != AdType.native) {
      callback?.onAdFailedToLoad?.call('preLoad只支持原生广告');
      return;
    }

    // 检查是否有缓存，只有没有缓存时才上报请求事件
    final hasCache = _manager!.isReady(type, backup);
    if (!hasCache) {
      // 上报广告请求事件（只有真实网络请求才上报）
      _reportEvent(AdEvents.eventNameAdSdk, AdEvents.createAdRequestParams(
        placementId: _getAdId(type, backup),
      ));
    }

    // 获取广告ID和平台信息
    final adId = _getAdId(type, backup);
    final platform = _manager!.getCachedAdPlatform(adId) ?? 'unknown';

    // 创建带事件上报的回调包装器
    final reportingCallback = _eventReporter != null
      ? AdCallback.withReporting(
          placementId: adId,
          eventReporter: _reportEvent,
          originalCallback: callback,
          startTime: hasCache ? null : DateTime.now(), // 只有实时请求才记录开始时间
          isBanner: false, // 原生广告不是Banner
          scene: null, // preLoad方法不传递场景参数
          platform: platform, // 传递平台信息
          platformGetter: _manager!.getCachedAdPlatform, // 传递获取平台信息的函数
          priceReporter: _priceReporter, // 传递价格上报器
        )
      : callback;

    // 预加载原生广告
    await _manager!.load(type, backup: backup, callback: reportingCallback);
  }

  /// 绑定Template样式并展示原生广告
  ///
  /// [type] 广告类型，必须是AdType.native
  /// [templateType] 模板类型
  /// [backup] 是否使用备用广告，默认false
  /// [callback] 广告回调
  /// [scene] 广告场景参数（可选），用于价格事件上报
  ///
  /// 示例：
  /// ```dart
  /// await AdSDK.bindViewByTemplateStyle(AdType.native, templateType: 'medium');
  /// ```
  static Future<void> bindViewByTemplateStyle(
    AdType type, {
    String templateType = 'medium',
    bool backup = false,
    AdCallback? callback,
    int? scene, // 广告场景参数，用于价格事件上报
  }) async {
    _ensureInitialized();
    if (type != AdType.native) {
      callback?.onAdFailedToShow?.call('bindViewByTemplateStyle只支持原生广告');
      return;
    }

    // 获取广告ID和平台信息
    final adId = _getAdId(type, backup);
    final hasCache = _manager!.isReady(type, backup);
    final platform = _manager!.getCachedAdPlatform(adId) ?? 'unknown';

    // 创建带事件上报的回调包装器
    final reportingCallback = _eventReporter != null
      ? AdCallback.withReporting(
          placementId: adId,
          eventReporter: _reportEvent,
          originalCallback: callback,
          startTime: hasCache ? null : DateTime.now(), // 只有实时请求才记录开始时间
          isBanner: false, // 原生广告不是Banner
          scene: scene, // 传递场景参数
          platform: platform, // 传递平台信息
          platformGetter: _manager!.getCachedAdPlatform, // 传递获取平台信息的函数
          priceReporter: _priceReporter, // 传递价格上报器
        )
      : callback;

    // 绑定Template样式并展示原生广告
    await _manager!.bindViewByTemplateStyle(templateType, backup, reportingCallback);
  }

  /// 绑定Factory ID并展示原生广告
  ///
  /// [type] 广告类型，必须是AdType.native
  /// [factoryId] Factory ID
  /// [backup] 是否使用备用广告，默认false
  /// [callback] 广告回调
  /// [scene] 广告场景参数（可选），用于价格事件上报
  ///
  /// 示例：
  /// ```dart
  /// await AdSDK.bindViewByFactoryId(AdType.native, factoryId: 'adFactoryExample');
  /// ```
  static Future<void> bindViewByFactoryId(
    AdType type, {
    String factoryId = 'adFactoryExample',
    bool backup = false,
    AdCallback? callback,
    int? scene, // 广告场景参数，用于价格事件上报
  }) async {
    _ensureInitialized();
    if (type != AdType.native) {
      callback?.onAdFailedToShow?.call('bindViewByFactoryId只支持原生广告');
      return;
    }

    // 获取广告ID和平台信息
    final adId = _getAdId(type, backup);
    final hasCache = _manager!.isReady(type, backup);
    final platform = _manager!.getCachedAdPlatform(adId) ?? 'unknown';

    // 创建带事件上报的回调包装器
    final reportingCallback = _eventReporter != null
      ? AdCallback.withReporting(
          placementId: adId,
          eventReporter: _reportEvent,
          originalCallback: callback,
          startTime: hasCache ? null : DateTime.now(), // 只有实时请求才记录开始时间
          isBanner: false, // 原生广告不是Banner
          scene: scene, // 传递场景参数
          platform: platform, // 传递平台信息
          platformGetter: _manager!.getCachedAdPlatform, // 传递获取平台信息的函数
          priceReporter: _priceReporter, // 传递价格上报器
        )
      : callback;

    // 绑定Factory ID并展示原生广告
    await _manager!.bindViewByFactoryId(factoryId, backup, reportingCallback);
  }

  /// 获取原生广告Widget
  ///
  /// [placement] 广告位标识
  /// [backup] 是否使用备用广告，默认false
  ///
  /// 示例：
  /// ```dart
  /// Widget nativeAdWidget = AdSDK.getNativeWidget('native_ad');
  /// ```
  static Widget getNativeWidget({
    bool backup = false,
  }) {
    _ensureInitialized();
    return _manager!.getNativeWidget(backup);
  }

  /// 清理所有缓存的广告
  ///
  /// 通常在应用退出时调用
  static void dispose() {
    if (_initialized) {
      _manager?.dispose();
      _manager = null;
      _initialized = false;
    }
  }

  /// 打开广告检查器
  ///
  /// 用于调试广告配置和状态，仅在调试模式下有效
  ///
  /// 示例：
  /// ```dart
  /// // 手动打开广告检查器
  /// AdSDK.openAdInspector();
  /// ```
  static void openAdInspector() {
    if (kDebugMode) {
      gma.MobileAds.instance.openAdInspector((error) {
        if (error != null) {
          print('广告检查器打开失败:');
          print('  错误代码: ${error.code}');
          print('  错误消息: ${error.message}');
          print('  错误域: ${error.domain}');
          print('  可能原因: 1) 测试设备未注册 2) 广告ID配置错误 3) 网络问题 4) AdMob账户未设置');
        } else {
          print('广告检查器已成功打开');
        }
      });
    } else {
      print('广告检查器仅在调试模式下可用');
    }
  }

  /// 设置全局事件上报器
  ///
  /// [reporter] 事件上报函数，接收事件名和参数
  ///
  /// 示例：
  /// ```dart
  /// AdSDK.setEventReporter((eventName, parameters) {
  ///   MyAnalytics.track(eventName, parameters);
  /// });
  /// ```
  static void setEventReporter(EventReporter reporter) {
    _eventReporter = reporter;
    // 同时设置到AdManager，用于自动预加载的事件上报
    _manager?.setEventReporter(reporter);
  }

  /// 设置全局价格事件上报器
  ///
  /// [reporter] 价格事件上报函数，接收场景、广告ID、平台、价格等参数
  ///
  /// 示例：
  /// ```dart
  /// AdSDK.setEventPriceReporter((scene, posid, platform, price, currency, paytype) {
  ///   AdPriceReport().reportAdPrice(scene, AdEventData(
  ///     id: posid,
  ///     platform: platform,
  ///     price: (price * 1000000).toInt(),
  ///     currency: currency,
  ///     precision: getPrecisionTypeFromInt(paytype),
  ///   ));
  /// });
  /// ```
  static void setEventPriceReporter(EventPriceReporter reporter) {
    _priceReporter = reporter;
  }

  /// 内部上报事件的方法
  static void _reportEvent(String eventName, Map<String, dynamic> parameters) {
    _eventReporter?.call(eventName, parameters);
  }

  /// 获取广告ID
  static String _getAdId(AdType adType, bool backup) {
    _ensureInitialized();
    return _manager!.getAdId(adType, backup);
  }

  /// 确保SDK已初始化
  static void _ensureInitialized() {
    if (!_initialized || _manager == null) {
      throw StateError('AdSDK未初始化，请先调用AdSDK.initialize()');
    }
  }
}
