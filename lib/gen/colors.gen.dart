/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal

import 'package:flutter/painting.dart';
import 'package:flutter/material.dart';

class ColorName {
  ColorName._();

  /// Color: #212121
  static const Color black = Color(0xFF212121);

  /// Color: #D80000
  static const Color error = Color(0xFFD80000);

  /// Color: #CC9233
  static const Color inProgressYellow = Color(0xFFCC9233);

  /// Color: #F7FAF9
  static const Color lightGrey = Color(0xFFF7FAF9);

  /// Color: #75787b
  static const Color mlDarkGrey = Color(0xFF75787B);

  /// Color: #a7a8aa
  static const Color mlMediumGrey = Color(0xFFA7A8AA);

  /// Color: #139960
  static const Color primaryColor = Color(0xFF139960);
}
