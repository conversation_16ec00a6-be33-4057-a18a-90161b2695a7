import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:world_tune/start.dart';

Future<void> main() async {
  // 明确加载生产环境配置
  await dotenv.load(fileName: 'assets/env/.env.production');
  print('✅ 生产环境配置加载完成: assets/env/.env.production');
  
  // 打印API配置信息（生产环境）
  final apiUrl = dotenv.env['API_BASE_URL'] ?? 'https://api.knogwidapp.com';
  print('🔗 生产环境API地址: $apiUrl');

  await start();
}
