import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter_plugin_tools/appsflyer/config/appsFlyer_config.dart';
import 'package:flutter_plugin_tools/other/log/LogUtil.dart';


/// AppsFlyer 工具类
/// 提供 AppsFlyer SDK 的初始化和管理功能
class AFUtil {
  static AppsflyerSdk? _appsflyerSdk;
  static AppsFlyerConfig? _config;

  /// 初始化 AppsFlyer 配置
  static void initConfig(AppsFlyerConfig config) {
    _config = config;
    _appsflyerSdk = null; // 重置 SDK 实例
  }

  /// 获取 AppsFlyer 配置选项
  static AppsFlyerOptions getAppsFlyerOptions() {
    if (_config == null) {
      throw Exception('AppsFlyer config not initialized. Please call AFUtil.initConfig() first.');
    }

    LogManager.d("AppsFlyer DevKey = ${_config!.afDevKey}", tag: "AFUtil");

    const bool isReleaseMode = bool.fromEnvironment("dart.vm.product");

    AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
      afDevKey: _config!.afDevKey,
      appId: _config!.iosAppId, // for iOS
      showDebug: _config!.showDebug && !isReleaseMode,
      timeToWaitForATTUserAuthorization: _config!.timeToWaitForATTUserAuthorization.toDouble(),
      disableAdvertisingIdentifier: _config!.disableAdvertisingIdentifier,
      disableCollectASA: _config!.disableCollectASA,
    );

    return appsFlyerOptions;
  }

  /// 获取 AppsFlyer SDK 实例（单例模式）
  static AppsflyerSdk getAppsflyerSdk() {
    if (_appsflyerSdk != null) {
      return _appsflyerSdk!;
    } else {
      var appsflyerSdk = AppsflyerSdk(AFUtil.getAppsFlyerOptions());
      _appsflyerSdk = appsflyerSdk;
      return appsflyerSdk;
    }
  }

  /// 检查是否已初始化
  static bool get isInitialized => _config != null;
}
