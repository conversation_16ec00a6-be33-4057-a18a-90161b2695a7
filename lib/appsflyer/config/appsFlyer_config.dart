/// AppsFlyer 配置类
/// 用于存储 AppsFlyer 相关的配置信息
class AppsFlyerConfig {
  /// AppsFlyer Dev Key
  final String afDevKey;

  /// iOS App ID
  final String iosAppId;

  /// 是否显示调试信息
  final bool showDebug;

  /// 等待 ATT 用户授权的时间（毫秒）
  final int timeToWaitForATTUserAuthorization;

  /// 是否禁用广告标识符
  final bool disableAdvertisingIdentifier;

  /// 是否禁用 ASA 收集
  final bool disableCollectASA;

  const AppsFlyerConfig({
    required this.afDevKey,
    required this.iosAppId,
    this.showDebug = false,
    this.timeToWaitForATTUserAuthorization = 50,
    this.disableAdvertisingIdentifier = false,
    this.disableCollectASA = false,
  });

  /// 验证配置是否有效
  bool isValid() {
    return afDevKey.isNotEmpty && iosAppId.isNotEmpty;
  }
}
