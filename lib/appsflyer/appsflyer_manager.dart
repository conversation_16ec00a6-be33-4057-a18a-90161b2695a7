import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter_plugin_tools/appsflyer/af_util.dart';
import 'package:flutter_plugin_tools/appsflyer/config/appsFlyer_config.dart';
import 'package:flutter_plugin_tools/other/log/LogUtil.dart';


/// AppsFlyer 管理器
/// 提供 AppsFlyer 的初始化、事件上报等功能
class AppsFlyerManager {
  static AppsflyerSdk? _sdk;
  static bool _isInitialized = false;

  /// 初始化 AppsFlyer
  /// [config] AppsFlyer 配置信息
  /// [onInstallConversionData] 安装转化数据回调
  /// [onAppOpenAttribution] 应用打开归因回调
  /// [onDeepLinking] 深度链接回调
  static Future<void> initialize(
    AppsFlyerConfig config, {
    Function(Map<String, dynamic>)? onInstallConversionData,
    Function(Map<String, dynamic>)? onAppOpenAttribution,
    Function(DeepLinkResult)? onDeepLinking,
  }) async {
    if (_isInitialized) {
      LogManager.w("AppsFlyer already initialized", tag: "AppsFlyerManager");
      return;
    }

    if (!config.isValid()) {
      throw Exception('Invalid AppsFlyer config');
    }

    try {
      // 初始化配置
      AFUtil.initConfig(config);
      
      // 获取 SDK 实例
      _sdk = AFUtil.getAppsflyerSdk();
      
      // 设置回调
      if (onInstallConversionData != null) {
        _sdk!.onInstallConversionData(onInstallConversionData);
      }
      
      if (onAppOpenAttribution != null) {
        _sdk!.onAppOpenAttribution(onAppOpenAttribution);
      }
      
      if (onDeepLinking != null) {
        _sdk!.onDeepLinking(onDeepLinking);
      }

      // 初始化 SDK
      await _sdk!.initSdk(
        registerConversionDataCallback: onInstallConversionData != null,
        registerOnAppOpenAttributionCallback: onAppOpenAttribution != null,
        registerOnDeepLinkingCallback: onDeepLinking != null,
      );

      _isInitialized = true;
      LogManager.d("AppsFlyer initialized successfully", tag: "AppsFlyerManager");
    } catch (e) {
      LogManager.e("Failed to initialize AppsFlyer: $e", tag: "AppsFlyerManager", error: e);
      rethrow;
    }
  }

  /// 上报事件
  /// [eventName] 事件名称
  /// [eventValues] 事件参数
  static Future<void> logEvent(String eventName, Map<String, dynamic> eventValues) async {
    if (!_isInitialized || _sdk == null) {
      LogManager.e("AppsFlyer not initialized", tag: "AppsFlyerManager");
      return;
    }

    try {
      await _sdk!.logEvent(eventName, eventValues);
      LogManager.d("Event logged: $eventName with values: $eventValues", tag: "AppsFlyerManager");
    } catch (e) {
      LogManager.e("Failed to log event $eventName: $e", tag: "AppsFlyerManager", error: e);
    }
  }

  /// 获取 AppsFlyer ID
  static Future<String?> getAppsFlyerId() async {
    if (!_isInitialized || _sdk == null) {
      LogManager.e("AppsFlyer not initialized", tag: "AppsFlyerManager");
      return null;
    }

    try {
      return await _sdk!.getAppsFlyerUID();
    } catch (e) {
      LogManager.e("Failed to get AppsFlyer ID: $e", tag: "AppsFlyerManager", error: e);
      return null;
    }
  }

  /// 检查是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 获取 SDK 实例（仅供内部使用）
  static AppsflyerSdk? get sdk => _sdk;
}
