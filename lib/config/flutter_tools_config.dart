import 'package:flutter_plugin_tools/appsflyer/config/appsFlyer_config.dart';
import 'package:flutter_plugin_tools/firebase/config/firebase_config.dart';
import 'package:flutter_plugin_tools/att/config/att_config.dart';
import 'package:flutter_plugin_tools/other/log/LogUtil.dart';

/// Flutter Tools 统一配置类
/// 用于管理所有模块的配置信息
class FlutterPluginToolsConfig {
  /// AppsFlyer 配置
  final AppsFlyerConfig? appsFlyerConfig;

  /// Firebase 配置
  final FirebaseConfig? firebaseConfig;

  /// ATT (App Tracking Transparency) 配置
  final ATTConfig? attConfig;

  /// 日志配置
  final LogConfig logConfig;

  const FlutterPluginToolsConfig({
    this.appsFlyerConfig,
    this.firebaseConfig,
    this.attConfig,
    this.logConfig = const LogConfig(),
  });

  /// 验证配置是否有效
  bool isValid() {
    // 如果配置了 AppsFlyer，则需要验证其有效性
    if (appsFlyerConfig != null && !appsFlyerConfig!.isValid()) {
      return false;
    }

    // 如果配置了 Firebase，则需要验证其有效性
    if (firebaseConfig != null && !firebaseConfig!.isValid()) {
      return false;
    }

    // 如果配置了 ATT，则需要验证其有效性
    if (attConfig != null && !attConfig!.isValid()) {
      return false;
    }

    return true;
  }
}

/// 日志配置类
class LogConfig {
  /// 日志实现
  /// 如果为 null，则不输出任何日志
  /// 如果需要日志，可以传入 ConsoleLogger() 或自定义实现
  final ILogger? logger;

  /// 默认日志标签
  final String defaultTag;

  const LogConfig({this.logger, this.defaultTag = "FlutterTools"});

  /// 创建一个启用控制台日志的配置
  factory LogConfig.console({String defaultTag = "FlutterTools"}) {
    return LogConfig(logger: ConsoleLogger(), defaultTag: defaultTag);
  }

  /// 创建一个禁用日志的配置
  factory LogConfig.disabled() {
    return const LogConfig(logger: null);
  }
}
