import 'dart:async';
import 'dart:io';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter/foundation.dart';
import '../other/log/LogUtil.dart';
import 'config/att_config.dart';

/// ATT (App Tracking Transparency) 管理器
/// 提供 ATT 的初始化和状态管理功能
class ATTManager {
  static ATTConfig? _config;
  static bool _isInitialized = false;
  static bool _isIosInit = false;
  static TrackingStatus? _currentStatus;
  static Timer? _delayTimer;

  /// 初始化 ATT
  /// [config] ATT 配置信息
  static Future<void> initialize(ATTConfig config) async {
    if (_isInitialized) {
      LogManager.w("ATT already initialized", tag: "ATTManager");
      return;
    }

    if (!config.isValid()) {
      throw Exception('Invalid ATT config');
    }

    try {
      _config = config;
      _isInitialized = true;

      LogManager.i("ATT initialized successfully", tag: "ATTManager");

      // 如果是 iOS 平台且启用了 ATT
      if (Platform.isIOS && config.shouldEnableOnIOS) {
        if (config.autoRequestOnStartup) {
          // 延迟请求权限
          _schedulePermissionRequest();
        }
      } else {
        LogManager.d("ATT skipped: not iOS platform or disabled", tag: "ATTManager");
      }
    } catch (e) {
      LogManager.e("Failed to initialize ATT: $e", tag: "ATTManager", error: e);
      rethrow;
    }
  }

  /// 安排权限请求
  static void _schedulePermissionRequest() {
    if (_config == null) return;

    _delayTimer?.cancel();
    _delayTimer = Timer(_config!.requestDelay, () {
      _requestPermissionInternal();
    });

    LogManager.d("ATT permission request scheduled in ${_config!.requestDelayMs}ms", tag: "ATTManager");
  }

  /// 内部权限请求方法
  static Future<void> _requestPermissionInternal() async {
    if (!Platform.isIOS || _isIosInit) {
      return;
    }

    try {
      _isIosInit = true;

      // 获取当前状态
      final currentStatus = await AppTrackingTransparency.trackingAuthorizationStatus;
      _currentStatus = currentStatus;
      
      LogManager.d("Current ATT status: $currentStatus", tag: "ATTManager");

      // 获取广告标识符（如果已授权）
      String? advertisingId;
      if (currentStatus == TrackingStatus.authorized) {
        try {
          advertisingId = await AppTrackingTransparency.getAdvertisingIdentifier();
          LogManager.d("IDFA obtained: ${advertisingId?.substring(0, 8)}...", tag: "ATTManager");
        } catch (e) {
          LogManager.w("Failed to get IDFA: $e", tag: "ATTManager");
        }
      }

      // 通知状态变化
      _config?.onStatusChanged?.call(currentStatus, advertisingId);

      bool isFirstTime = false;

      // 如果状态是未确定，则请求权限
      if (currentStatus == TrackingStatus.notDetermined) {
        LogManager.i("Requesting ATT permission...", tag: "ATTManager");
        
        final newStatus = await AppTrackingTransparency.requestTrackingAuthorization();
        _currentStatus = newStatus;
        isFirstTime = true;

        LogManager.i("ATT permission result: $newStatus", tag: "ATTManager");

        // 根据用户选择进行不同的处理
        if (newStatus == TrackingStatus.authorized) {
          LogManager.i("✅ User granted ATT permission", tag: "ATTManager");
          // 获取新的广告标识符
          try {
            advertisingId = await AppTrackingTransparency.getAdvertisingIdentifier();
            LogManager.i("IDFA obtained after authorization: ${advertisingId?.substring(0, 8)}...", tag: "ATTManager");
          } catch (e) {
            LogManager.w("Failed to get IDFA after authorization: $e", tag: "ATTManager");
          }
        } else {
          LogManager.w("⚠️ User denied ATT permission: $newStatus", tag: "ATTManager");
          advertisingId = null; // 确保清空 IDFA
        }

        // 通知状态变化
        _config?.onStatusChanged?.call(newStatus, advertisingId);
      }

      // 通知请求完成
      _config?.onRequestCompleted?.call(_currentStatus!, isFirstTime, advertisingId);

    } catch (e) {
      LogManager.e("Failed to request ATT permission: $e", tag: "ATTManager", error: e);
    }
  }

  /// 手动请求 ATT 权限
  /// 返回权限状态
  static Future<TrackingStatus?> requestPermission() async {
    if (!_isInitialized) {
      LogManager.e("ATT not initialized", tag: "ATTManager");
      return null;
    }

    if (!Platform.isIOS) {
      LogManager.w("ATT is only available on iOS", tag: "ATTManager");
      return null;
    }

    try {
      // 取消自动请求的定时器
      _delayTimer?.cancel();
      
      // 执行权限请求
      await _requestPermissionInternal();
      return _currentStatus;
    } catch (e) {
      LogManager.e("Failed to manually request ATT permission: $e", tag: "ATTManager", error: e);
      return null;
    }
  }

  /// 获取当前 ATT 状态
  static Future<TrackingStatus?> getCurrentStatus() async {
    if (!Platform.isIOS) {
      return null;
    }

    try {
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      _currentStatus = status;
      return status;
    } catch (e) {
      LogManager.e("Failed to get ATT status: $e", tag: "ATTManager", error: e);
      return null;
    }
  }

  /// 获取广告标识符 (IDFA)
  static Future<String?> getAdvertisingIdentifier() async {
    if (!Platform.isIOS) {
      return null;
    }

    try {
      final status = await getCurrentStatus();
      if (status == TrackingStatus.authorized) {
        return await AppTrackingTransparency.getAdvertisingIdentifier();
      } else {
        LogManager.w("Cannot get IDFA: ATT not authorized", tag: "ATTManager");
        return null;
      }
    } catch (e) {
      LogManager.e("Failed to get advertising identifier: $e", tag: "ATTManager", error: e);
      return null;
    }
  }

  /// 检查是否已授权追踪
  static Future<bool> isTrackingAuthorized() async {
    final status = await getCurrentStatus();
    return status == TrackingStatus.authorized;
  }

  /// 检查是否可以请求权限
  static Future<bool> canRequestPermission() async {
    final status = await getCurrentStatus();
    return status == TrackingStatus.notDetermined;
  }

  /// 获取状态描述
  static String getStatusDescription(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.authorized:
        return "已授权";
      case TrackingStatus.denied:
        return "已拒绝";
      case TrackingStatus.notDetermined:
        return "未确定";
      case TrackingStatus.restricted:
        return "受限制";
      case TrackingStatus.notSupported:
        throw UnimplementedError();
    }
  }

  /// 检查是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 获取配置信息
  static ATTConfig? get config => _config;

  /// 获取当前缓存的状态
  static TrackingStatus? get cachedStatus => _currentStatus;

  /// 重置（用于测试）
  static void reset() {
    _config = null;
    _isInitialized = false;
    _isIosInit = false;
    _currentStatus = null;
    _delayTimer?.cancel();
    _delayTimer = null;
  }
}
