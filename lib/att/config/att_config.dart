import 'package:app_tracking_transparency/app_tracking_transparency.dart';

/// ATT (App Tracking Transparency) 配置类
/// 用于存储 ATT 相关的配置信息
class ATTConfig {
  /// 是否启用 ATT
  final bool enabled;
  
  /// 是否在应用启动时自动请求权限
  final bool autoRequestOnStartup;
  
  /// 权限请求的延迟时间（毫秒）
  /// 有些情况下需要延迟请求，让用户先熟悉应用
  final int requestDelayMs;
  
  /// 自定义权限请求提示信息
  /// 如果为 null，则使用系统默认提示
  final String? customMessage;
  
  /// 权限状态变化回调
  /// [status] 权限状态
  /// [advertisingId] 广告标识符（仅在已授权时有值）
  final Function(TrackingStatus status, String? advertisingId)? onStatusChanged;

  /// 权限请求完成回调
  /// [status] 权限状态
  /// [isFirstTime] 是否首次请求
  /// [advertisingId] 广告标识符（仅在已授权时有值）
  final Function(TrackingStatus status, bool isFirstTime, String? advertisingId)? onRequestCompleted;

  const ATTConfig({
    this.enabled = true,
    this.autoRequestOnStartup = true,
    this.requestDelayMs = 1000,
    this.customMessage,
    this.onStatusChanged,
    this.onRequestCompleted,
  });

  /// 创建一个禁用 ATT 的配置
  factory ATTConfig.disabled() {
    return const ATTConfig(enabled: false);
  }
  
  /// 创建一个延迟请求的配置
  /// [delaySeconds] 延迟秒数
  /// [onStatusChanged] 状态变化回调
  /// [onRequestCompleted] 请求完成回调
  factory ATTConfig.delayed({
    required int delaySeconds,
    Function(TrackingStatus status, String? advertisingId)? onStatusChanged,
    Function(TrackingStatus status, bool isFirstTime, String? advertisingId)? onRequestCompleted,
  }) {
    return ATTConfig(
      enabled: true,
      autoRequestOnStartup: true,
      requestDelayMs: delaySeconds * 1000,
      onStatusChanged: onStatusChanged,
      onRequestCompleted: onRequestCompleted,
    );
  }
  
  /// 创建一个手动请求的配置
  /// 不会在启动时自动请求，需要手动调用
  /// [onStatusChanged] 状态变化回调
  /// [onRequestCompleted] 请求完成回调
  factory ATTConfig.manual({
    Function(TrackingStatus status, String? advertisingId)? onStatusChanged,
    Function(TrackingStatus status, bool isFirstTime, String? advertisingId)? onRequestCompleted,
  }) {
    return ATTConfig(
      enabled: true,
      autoRequestOnStartup: false,
      onStatusChanged: onStatusChanged,
      onRequestCompleted: onRequestCompleted,
    );
  }

  /// 验证配置是否有效
  bool isValid() {
    return enabled;
  }
  
  /// 是否应该在 iOS 平台上启用
  bool get shouldEnableOnIOS => enabled;
  
  /// 获取请求延迟时长
  Duration get requestDelay => Duration(milliseconds: requestDelayMs);
}
