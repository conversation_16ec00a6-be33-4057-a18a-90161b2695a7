import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:world_tune/src/shared/providers/startup_cache_provider.dart';
import 'package:world_tune/src/shared/services/startup_cache_service.dart';
import 'package:world_tune/src/shared/services/storage_service.dart';
import 'package:world_tune/src/shared/utils/logger.dart';
import 'package:world_tune/src/shared/utils/platform_type.dart';

// SDK初始化服务
import 'package:world_tune/src/shared/services/sdk_initialization_service.dart';

import 'package:world_tune/app.dart';

Future<void> start() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();

  FlutterError.onError = (details) {
    log(details.exceptionAsString(), stackTrace: details.stack);
  };

  // 🚀 初始化所有SDK服务（Firebase、AppsFlyer、ATT）
  print('🔧 开始SDK初始化流程...');
  
  // 初始化所有模块，互相独立，任何一个失败都不影响其他模块
  await SDKInitializationService.initialize(
    // 如果你有真实的AppsFlyer配置，可以在这里填写：
    // appsFlyerDevKey: "你的真实DevKey",
    // appsFlyerIOSAppId: "1234567890",  // 纯数字格式的App Store ID
  );
  
  print('✅ SDK初始化流程完成');

  final platformType = detectPlatformType();

  // 初始化StorageService
  final storageService = await StorageService.create();

  // 初始化StartupCacheService
  final startupCacheService = StartupCacheService();
  await startupCacheService.initialize();

  runApp(EasyLocalization(
    supportedLocales: const [Locale('en'), Locale('zh')],
    path: 'assets/lang',
    fallbackLocale: const Locale('en'),
    child: ProviderScope(
      overrides: [
        platformTypeProvider.overrideWithValue(platformType),
        storageServiceProvider.overrideWithValue(storageService),
        startupCacheServiceProvider.overrideWithValue(startupCacheService),
      ],
      observers: [
        Logger(),
      ],
      child: const App(),
    ),
  ));
}
