import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:world_tune/gen/assets.gen.dart';
import 'package:world_tune/start.dart';
import 'src/shared/utils/text_overflow_checker.dart';
import 'src/shared/utils/startup_performance_monitor.dart';

Future<void> main() async {
  // 记录应用启动时间
  PerformanceTracker.start();

  // 在调试模式下启用文本溢出检查
  if (kDebugMode) {
    TextOverflowDebugger.enable();
    print('📊 Text overflow debugging enabled');
  }

  PerformanceTracker.mark('env_config_start');

  // 默认加载staging环境配置
  await dotenv.load(fileName: Assets.env.envStaging);
  print('✅ 默认环境(Staging)配置加载完成: ${Assets.env.envStaging}');

  // 打印API配置信息
  final apiUrl = dotenv.env['API_BASE_URL'] ?? 'http://localhost:8300';
  print('🔗 Staging环境API地址: $apiUrl');

  PerformanceTracker.mark('env_config_complete');
  PerformanceTracker.mark('app_start');

  await start();
}
