import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:world_tune/start.dart';

Future<void> main() async {
  // 明确加载开发环境配置
  await dotenv.load(fileName: 'assets/env/.env.development');
  print('✅ 开发环境配置加载完成: assets/env/.env.development');
  
  // 打印API配置信息（开发环境专用）
  final apiUrl = dotenv.env['API_BASE_URL'] ?? 'http://localhost:8300';
  print('🔗 开发环境API地址: $apiUrl');

  await start();
}
