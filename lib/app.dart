import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:world_tune/src/shared/router/app_router.dart';
import 'package:world_tune/src/shared/services/audio_service.dart';
import 'package:world_tune/src/shared/services/storage_service.dart';
import 'package:world_tune/src/shared/services/ad_config_service.dart';
import 'package:world_tune/src/shared/services/ad_analytics_service.dart';
import 'package:world_tune/src/shared/providers/country_provider.dart';
import 'package:world_tune/src/shared/providers/startup_cache_provider.dart';
import 'package:world_tune/src/shared/utils/startup_performance_monitor.dart';

/// 服务初始化状态Provider
final servicesInitializedProvider = StateProvider<bool>((ref) => false);

class App extends ConsumerStatefulWidget {
  const App({super.key});

  @override
  ConsumerState<App> createState() => _AppState();
}

class _AppState extends ConsumerState<App> {
  bool _servicesInitialized = false;
  String _initializationStatus = '正在初始化...';
  bool _showCachedContent = false;
  final DateTime _startTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeServicesParallel();
  }

  /// 并行初始化应用服务（优化版）
  Future<void> _initializeServicesParallel() async {
    final initStartTime = DateTime.now();
    print('🚀 开始并行初始化应用服务...');
    PerformanceTracker.mark('services_init_start');

    try {
      // 第一步：立即检查启动缓存
      setState(() {
        _initializationStatus = '正在检查启动缓存...';
      });

      PerformanceTracker.mark('cache_check_start');
      final cacheNotifier = ref.read(startupCacheProvider.notifier);
      final hasCachedData = cacheNotifier.isCacheValid();
      PerformanceTracker.mark('cache_check_complete');

      if (hasCachedData) {
        print('✅ 发现有效的启动缓存，准备显示缓存内容');
        setState(() {
          _showCachedContent = true;
          _initializationStatus = '正在加载缓存数据...';
        });

        // 延迟服务初始化完成通知，确保SplashPage有足够显示时间
        Future.delayed(const Duration(milliseconds: 2000), () {
          if (mounted) {
            setState(() {
              _servicesInitialized = true;
            });
            ref.read(servicesInitializedProvider.notifier).state = true;
            PerformanceTracker.mark('first_content_visible');
          }
        });
      }

      // 第二步：并行初始化核心服务
      setState(() {
        _initializationStatus = '正在初始化核心服务...';
      });

      final storageService = ref.read(storageServiceProvider);
      final audioService = ref.read(audioServiceProvider);

      // 并行执行：音频服务初始化 + 国家选择初始化 + 广告SDK初始化
      final futures = <Future>[
        // 音频服务初始化 + 锁屏播放器初始化
        _initializeAudioServices(audioService, storageService, ref),
        // 国家选择初始化（异步，不阻塞）
        _initializeCountrySelectionAsync(),
        // 广告SDK初始化和事件回调设置（异步，不阻塞）
        _initializeAdServices(),
      ];

      // 等待核心服务初始化完成
      await Future.wait(futures);

      PerformanceTracker.mark('services_init_complete');
      final initDuration = DateTime.now().difference(initStartTime);
      print('✅ 应用服务初始化完成，耗时: ${initDuration.inMilliseconds}ms');

      // 如果没有缓存数据，延迟通知服务初始化完成
      if (!_showCachedContent) {
        // 确保至少等待2秒，让SplashPage有足够显示时间
        await Future.delayed(const Duration(milliseconds: 2000));
        if (mounted) {
          setState(() {
            _servicesInitialized = true;
          });
          ref.read(servicesInitializedProvider.notifier).state = true;
          PerformanceTracker.mark('first_content_visible');
        }
      }

    } catch (e) {
      print('❌ 应用服务初始化失败: $e');
      // 即使初始化失败，也要显示应用
      setState(() {
        _servicesInitialized = true;
      });
      ref.read(servicesInitializedProvider.notifier).state = true;
    }
  }

  /// 初始化音频服务（包括锁屏播放器）
  /// 
  /// 功能实现: 统一初始化音频服务和锁屏播放器
  /// 实现方案: 先初始化AudioService，再初始化锁屏播放器
  /// 边界处理: 锁屏播放器初始化失败不影响核心播放功能
  /// 实现日期: 2025-01-27
  Future<void> _initializeAudioServices(
    AudioService audioService,
    StorageService storageService,
    dynamic ref,
  ) async {
    try {
      print('🎵 开始初始化音频服务...');
      
      // 第一步：初始化核心音频服务
      await audioService.initialize(storageService, ref);
      print('✅ 核心音频服务初始化完成');
      
      // 第二步：初始化锁屏播放器
      print('📱 开始初始化锁屏播放器...');
      await audioService.initializeLockScreenPlayer();
      print('✅ 锁屏播放器初始化完成');
      
    } catch (e) {
      print('❌ 音频服务初始化失败: $e');
      // 重新抛出异常，让上层处理
      rethrow;
    }
  }

  /// 初始化广告服务（广告SDK + 事件回调）
  /// 
  /// 功能实现: 先初始化广告SDK，再设置AF事件回调
  /// 实现方案: AdConfigService.initialize() -> AdAnalyticsService.setupAdCallbacks()
  /// 边界处理: 广告服务初始化失败不阻塞应用启动
  /// 实现日期: 2025-01-27
  Future<void> _initializeAdServices() async {
    try {
      print('🎯 开始初始化广告服务...');
      
      // 第一步：初始化广告SDK
      await AdConfigService.initialize();
      print('✅ 广告SDK初始化完成');
      
      // 第二步：设置广告事件回调（AF上报）
      await AdAnalyticsService.setupAdCallbacks();
      print('✅ 广告事件回调设置完成');
      
    } catch (e) {
      print('❌ 广告服务初始化失败: $e');
      // 广告服务初始化失败不阻塞应用启动
    }
  }

  /// 异步初始化国家选择（优化版）
  Future<void> _initializeCountrySelectionAsync() async {
    try {
      print('🌍 开始异步初始化国家选择...');

      // 减少等待时间：从5秒减少到1秒
      int attempts = 0;
      const maxAttempts = 10; // 最多等待1秒 (10 * 100ms)

      while (attempts < maxAttempts) {
        final state = ref.read(countrySelectionProvider);

        // 如果国家已经选择完成且不在加载中，则初始化完成
        if (state.selectedCountry != null && !state.isLoading) {
          print('🌍 国家选择异步初始化完成: ${state.selectedCountry!.name}');
          return;
        }

        // 如果有错误但国家列表不为空，使用默认国家
        if (state.error != null && state.countries.isNotEmpty) {
          print('🌍 国家选择初始化出错，使用默认国家');
          return;
        }

        await Future.delayed(const Duration(milliseconds: 100));
        attempts++;
      }

      print('⚠️ 国家选择异步初始化超时，但不阻塞应用启动');
    } catch (e) {
      print('❌ 异步初始化国家选择失败: $e');
      // 不阻塞应用启动
    }
  }



  /// 记录启动性能指标
  void _logStartupPerformance() {
    final totalStartupTime = DateTime.now().difference(_startTime);
    print('📊 应用启动性能指标:');
    print('   - 总启动时间: ${totalStartupTime.inMilliseconds}ms');
    print('   - 是否使用缓存: $_showCachedContent');

    if (_showCachedContent) {
      print('   - 缓存加速效果: 显著提升用户体验');
    }

    // 打印详细性能报告
    PerformanceTracker.report();
  }

  @override
  Widget build(BuildContext context) {
    // 记录启动性能（仅在首次构建时）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _logStartupPerformance();
    });

    // 直接显示路由器，让SplashPage处理启动流程
    // 在后台异步初始化服务，不阻塞UI显示

    return MaterialApp.router(
      title: 'WorldTune',
      debugShowCheckedModeBanner: false,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      theme: _buildModernLightTheme(),
      darkTheme: _buildModernDarkTheme(),
      routerConfig: AppRouter.router,
    );
  }

  /// 构建现代化亮色主题
  ThemeData _buildModernLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF00FFFF), // 霓虹青色
        brightness: Brightness.light,
      ),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      cardTheme: CardThemeData(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        color: Colors.white.withOpacity(0.1),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// 构建现代化深色主题（基于demo.png的深色渐变风格）
  ThemeData _buildModernDarkTheme() {
    const primaryColor = Color(0xFF00FFFF); // 霓虹青色
    const backgroundColor = Color(0xFF0F0F23); // 深蓝黑背景
    const surfaceColor = Color(0xFF1A1A2E); // 深紫蓝表面

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      colorScheme: ColorScheme.dark(
        primary: primaryColor,
        secondary: const Color(0xFFFF00FF), // 霓虹洋红
        surface: surfaceColor,
        background: backgroundColor,
        onPrimary: Colors.black,
        onSecondary: Colors.black,
        onSurface: Colors.white,
        onBackground: Colors.white,
      ),
      visualDensity: VisualDensity.adaptivePlatformDensity,

      // 现代化卡片主题
      cardTheme: CardThemeData(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        color: surfaceColor.withOpacity(0.3),
        shadowColor: primaryColor.withOpacity(0.2),
      ),

      // 现代化按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: primaryColor.withOpacity(0.2),
          foregroundColor: primaryColor,
        ),
      ),

      // 现代化应用栏主题
      appBarTheme: AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
      ),

      // 现代化底部导航主题
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: surfaceColor.withOpacity(0.8),
        selectedItemColor: primaryColor,
        unselectedItemColor: Colors.white.withOpacity(0.6),
        elevation: 0,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}