import 'package:firebase_core/firebase_core.dart';

/// Firebase 配置类
/// 用于存储 Firebase 相关的配置信息
class FirebaseConfig {
  /// iOS 平台配置
  final FirebaseOptions iosOptions;

  /// Android 平台配置
  final FirebaseOptions androidOptions;

  /// 是否启用 Analytics
  final bool enableAnalytics;

  /// 是否启用 Crashlytics
  final bool enableCrashlytics;

  /// 是否启用 Remote Config
  final bool enableRemoteConfig;

  /// Remote Config 获取间隔（毫秒）
  final int remoteConfigFetchInterval;

  /// Remote Config 默认值
  final Map<String, dynamic> remoteConfigDefaults;

  const FirebaseConfig({
    required this.iosOptions,
    required this.androidOptions,
    this.enableAnalytics = true,
    this.enableCrashlytics = true,
    this.enableRemoteConfig = true,
    this.remoteConfigFetchInterval = 2 * 60 * 60 * 1000, // 2小时
    this.remoteConfigDefaults = const {},
  });

  /// 验证配置是否有效
  bool isValid() {
    return iosOptions.projectId.isNotEmpty &&
           androidOptions.projectId.isNotEmpty;
  }

  /// 创建一个简化的配置（只需要基本参数）
  /// 至少需要配置 iOS 或 Android 平台的参数
  factory FirebaseConfig.simple({
    required String projectId,
    String? iosApiKey,
    String? iosAppId,
    String? androidApiKey,
    String? androidAppId,
    required String messagingSenderId,
    String? storageBucket,
    String? iosClientId,
    String? iosBundleId,
    String? androidClientId,
    String? androidPackageName,
    bool enableAnalytics = true,
    bool enableCrashlytics = true,
    bool enableRemoteConfig = true,
    Map<String, dynamic> remoteConfigDefaults = const {},
  }) {
    // 验证至少配置了一个平台
    final hasIosConfig = iosApiKey != null && iosAppId != null;
    final hasAndroidConfig = androidApiKey != null && androidAppId != null;
    
    if (!hasIosConfig && !hasAndroidConfig) {
      throw ArgumentError('At least one platform (iOS or Android) API Key and App ID must be configured');
    }

    return FirebaseConfig(
      iosOptions: hasIosConfig ? FirebaseOptions(
        apiKey: iosApiKey,
        appId: iosAppId,
        messagingSenderId: messagingSenderId,
        projectId: projectId,
        storageBucket: storageBucket,
        iosClientId: iosClientId,
        iosBundleId: iosBundleId,
      ) : FirebaseOptions(
        apiKey: 'dummy',
        appId: 'dummy',
        messagingSenderId: messagingSenderId,
        projectId: projectId,
      ),
      androidOptions: hasAndroidConfig ? FirebaseOptions(
        apiKey: androidApiKey,
        appId: androidAppId,
        messagingSenderId: messagingSenderId,
        projectId: projectId,
        storageBucket: storageBucket,
        androidClientId: androidClientId,
      ) : FirebaseOptions(
        apiKey: 'dummy',
        appId: 'dummy',
        messagingSenderId: messagingSenderId,
        projectId: projectId,
      ),
      enableAnalytics: enableAnalytics,
      enableCrashlytics: enableCrashlytics,
      enableRemoteConfig: enableRemoteConfig,
      remoteConfigDefaults: remoteConfigDefaults,
    );
  }
}
