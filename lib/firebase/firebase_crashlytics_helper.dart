import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import '../other/log/LogUtil.dart';
import 'firebase_manager.dart';

/// Firebase Crashlytics 辅助类
/// 提供 Crashlytics 的便捷操作方法
class FirebaseCrashlyticsHelper {
  
  /// 获取 Crashlytics 实例
  static FirebaseCrashlytics? get _crashlytics => FirebaseManager.crashlytics;

  /// 记录错误
  /// [exception] 异常对象
  /// [stackTrace] 堆栈跟踪
  /// [reason] 错误原因描述
  /// [information] 附加信息
  /// [printDetails] 是否打印详细信息到控制台
  static Future<void> recordError({
    required dynamic exception,
    StackTrace? stackTrace,
    String? reason,
    Iterable<Object> information = const [],
    bool printDetails = false,
  }) async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    try {
      await _crashlytics!.recordError(
        exception,
        stackTrace,
        reason: reason,
        information: information,
        printDetails: printDetails,
      );
      LogManager.d("Error recorded to Crashlytics: $exception", tag: "FirebaseCrashlyticsHelper");
    } catch (e) {
      LogManager.e("Failed to record error to Crashlytics: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }

  /// 记录 Flutter 错误
  /// [details] Flutter 错误详情
  static Future<void> recordFlutterError(FlutterErrorDetails details) async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    try {
      await _crashlytics!.recordFlutterError(details);
      LogManager.d("Flutter error recorded to Crashlytics", tag: "FirebaseCrashlyticsHelper");
    } catch (e) {
      LogManager.e("Failed to record Flutter error to Crashlytics: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }

  /// 记录自定义日志
  /// [message] 日志消息
  static Future<void> log(String message) async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    try {
      await _crashlytics!.log(message);
      LogManager.d("Log recorded to Crashlytics: $message", tag: "FirebaseCrashlyticsHelper");
    } catch (e) {
      LogManager.e("Failed to record log to Crashlytics: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }

  /// 设置用户标识符
  /// [identifier] 用户标识符
  static Future<void> setUserIdentifier(String identifier) async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    try {
      await _crashlytics!.setUserIdentifier(identifier);
      LogManager.d("User identifier set: $identifier", tag: "FirebaseCrashlyticsHelper");
    } catch (e) {
      LogManager.e("Failed to set user identifier: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }

  /// 设置自定义键值对
  /// [key] 键名
  /// [value] 值
  static Future<void> setCustomKey(String key, Object value) async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    try {
      await _crashlytics!.setCustomKey(key, value);
      LogManager.d("Custom key set: $key = $value", tag: "FirebaseCrashlyticsHelper");
    } catch (e) {
      LogManager.e("Failed to set custom key: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }

  /// 强制发送崩溃报告（仅用于测试）
  static void crash() {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    if (kDebugMode) {
      LogManager.w("Crash() called in debug mode - this will not send a report", tag: "FirebaseCrashlyticsHelper");
    }

    try {
      _crashlytics!.crash();
    } catch (e) {
      LogManager.e("Failed to trigger crash: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }

  /// 启用/禁用 Crashlytics 收集
  /// [enabled] 是否启用
  static Future<void> setCrashlyticsCollectionEnabled(bool enabled) async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    try {
      await _crashlytics!.setCrashlyticsCollectionEnabled(enabled);
      LogManager.d("Crashlytics collection enabled: $enabled", tag: "FirebaseCrashlyticsHelper");
    } catch (e) {
      LogManager.e("Failed to set Crashlytics collection enabled: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }

  /// 检查 Crashlytics 是否启用
  static Future<bool> isCrashlyticsCollectionEnabled() async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return false;
    }

    try {
      return _crashlytics!.isCrashlyticsCollectionEnabled;
    } catch (e) {
      LogManager.e("Failed to check Crashlytics collection status: $e", tag: "FirebaseCrashlyticsHelper", error: e);
      return false;
    }
  }

  /// 发送未发送的报告
  static Future<void> sendUnsentReports() async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    try {
      await _crashlytics!.sendUnsentReports();
      LogManager.d("Unsent reports sent", tag: "FirebaseCrashlyticsHelper");
    } catch (e) {
      LogManager.e("Failed to send unsent reports: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }

  /// 删除未发送的报告
  static Future<void> deleteUnsentReports() async {
    if (!FirebaseManager.isInitialized || _crashlytics == null) {
      LogManager.w("Firebase Crashlytics not initialized", tag: "FirebaseCrashlyticsHelper");
      return;
    }

    try {
      await _crashlytics!.deleteUnsentReports();
      LogManager.d("Unsent reports deleted", tag: "FirebaseCrashlyticsHelper");
    } catch (e) {
      LogManager.e("Failed to delete unsent reports: $e", tag: "FirebaseCrashlyticsHelper", error: e);
    }
  }
}
