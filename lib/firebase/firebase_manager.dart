import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_plugin_tools/firebase/config/firebase_config.dart';
import '../other/log/LogUtil.dart';

/// Firebase 管理器
/// 提供 Firebase 的初始化和统一管理功能
class FirebaseManager {
  static FirebaseConfig? _config;
  static bool _isInitialized = false;
  static FirebaseAnalytics? _analytics;
  static FirebaseCrashlytics? _crashlytics;
  static FirebaseRemoteConfig? _remoteConfig;

  /// 初始化 Firebase
  /// [config] Firebase 配置信息
  static Future<void> initialize(FirebaseConfig config) async {
    if (_isInitialized) {
      LogManager.w("Firebase already initialized", tag: "FirebaseManager");
      return;
    }

    if (!config.isValid()) {
      throw Exception('Invalid Firebase config');
    }

    try {
      _config = config;

      // 初始化 Firebase Core
      await _initializeCore(config);

      // 初始化各个服务
      if (config.enableAnalytics) {
        await _initializeAnalytics();
      }

      if (config.enableCrashlytics) {
        await _initializeCrashlytics();
      }

      if (config.enableRemoteConfig) {
        await _initializeRemoteConfig(config);
      }

      _isInitialized = true;
      LogManager.i("Firebase initialized successfully", tag: "FirebaseManager");
    } catch (e) {
      LogManager.e("Failed to initialize Firebase: $e", tag: "FirebaseManager", error: e);
      rethrow;
    }
  }

  /// 初始化 Firebase Core
  static Future<void> _initializeCore(FirebaseConfig config) async {
    LogManager.d("Initializing Firebase Core...", tag: "FirebaseManager");
    
    FirebaseOptions options;
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      options = config.iosOptions;
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      options = config.androidOptions;
    } else {
      throw UnsupportedError('Firebase is not supported on this platform');
    }

    await Firebase.initializeApp(options: options);
    LogManager.d("Firebase Core initialized", tag: "FirebaseManager");
  }

  /// 初始化 Analytics
  static Future<void> _initializeAnalytics() async {
    LogManager.d("Initializing Firebase Analytics...", tag: "FirebaseManager");
    _analytics = FirebaseAnalytics.instance;
    
    // 设置默认参数
    await _analytics!.setDefaultEventParameters({
      'app_version': '1.0.0', // 可以从 package_info 获取
      'platform': defaultTargetPlatform.name,
    });
    
    LogManager.d("Firebase Analytics initialized", tag: "FirebaseManager");
  }

  /// 初始化 Crashlytics
  static Future<void> _initializeCrashlytics() async {
    LogManager.d("Initializing Firebase Crashlytics...", tag: "FirebaseManager");
    _crashlytics = FirebaseCrashlytics.instance;
    
    // 在 debug 模式下禁用 Crashlytics
    if (kDebugMode) {
      await _crashlytics!.setCrashlyticsCollectionEnabled(false);
    }
    
    LogManager.d("Firebase Crashlytics initialized", tag: "FirebaseManager");
  }

  /// 初始化 Remote Config
  static Future<void> _initializeRemoteConfig(FirebaseConfig config) async {
    LogManager.d("Initializing Firebase Remote Config...", tag: "FirebaseManager");
    _remoteConfig = FirebaseRemoteConfig.instance;
    
    // 设置配置
    await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: Duration(milliseconds: config.remoteConfigFetchInterval),
    ));
    
    // 设置默认值
    if (config.remoteConfigDefaults.isNotEmpty) {
      await _remoteConfig!.setDefaults(config.remoteConfigDefaults);
    }
    
    // 首次获取配置
    try {
      await _remoteConfig!.fetchAndActivate();
      LogManager.d("Remote Config fetched successfully", tag: "FirebaseManager");
    } catch (e) {
      LogManager.w("Failed to fetch Remote Config: $e", tag: "FirebaseManager");
    }
    
    LogManager.d("Firebase Remote Config initialized", tag: "FirebaseManager");
  }

  /// 获取 Analytics 实例
  static FirebaseAnalytics? get analytics => _analytics;

  /// 获取 Crashlytics 实例
  static FirebaseCrashlytics? get crashlytics => _crashlytics;

  /// 获取 Remote Config 实例
  static FirebaseRemoteConfig? get remoteConfig => _remoteConfig;

  /// 检查是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 获取配置信息
  static FirebaseConfig? get config => _config;

  /// 重置（用于测试）
  static void reset() {
    _config = null;
    _isInitialized = false;
    _analytics = null;
    _crashlytics = null;
    _remoteConfig = null;
  }
}
