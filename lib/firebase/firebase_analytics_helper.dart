import 'package:firebase_analytics/firebase_analytics.dart';
import '../other/log/LogUtil.dart';
import 'firebase_manager.dart';

/// Firebase Analytics 辅助类
/// 提供常用的 Analytics 事件上报方法
class FirebaseAnalyticsHelper {
  
  /// 上报登录事件
  /// [method] 登录方式（如：email, google, facebook 等）
  static Future<void> logLogin({String? method}) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.logLogin(loginMethod: method);
      LogManager.d("Login event logged: method=$method", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to log login event: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }

  /// 上报注册事件
  /// [method] 注册方式
  static Future<void> logSignUp({String? method}) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.logSignUp(signUpMethod: method ?? "unknown");
      LogManager.d("SignUp event logged: method=$method", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to log signup event: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }

  /// 上报购买事件
  /// [transactionId] 交易ID
  /// [value] 价格
  /// [currency] 币种
  /// [items] 购买项目列表
  static Future<void> logPurchase({
    String? transactionId,
    required double value,
    required String currency,
    List<AnalyticsEventItem>? items,
  }) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.logPurchase(
        transactionId: transactionId,
        value: value,
        currency: currency,
        items: items,
      );
      LogManager.d("Purchase event logged: value=$value, currency=$currency", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to log purchase event: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }

  /// 上报屏幕浏览事件
  /// [screenName] 屏幕名称
  /// [screenClass] 屏幕类别
  static Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
      );
      LogManager.d("Screen view logged: $screenName", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to log screen view: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }

  /// 上报搜索事件
  /// [searchTerm] 搜索词
  static Future<void> logSearch({required String searchTerm}) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.logSearch(searchTerm: searchTerm);
      LogManager.d("Search event logged: $searchTerm", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to log search event: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }

  /// 上报分享事件
  /// [contentType] 内容类型
  /// [itemId] 项目ID
  /// [method] 分享方式
  static Future<void> logShare({
    required String contentType,
    required String itemId,
    String? method,
  }) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.logShare(
        contentType: contentType,
        itemId: itemId,
        method: method ?? "unknown",
      );
      LogManager.d("Share event logged: $contentType, $itemId", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to log share event: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }

  /// 设置用户属性
  /// [name] 属性名
  /// [value] 属性值
  static Future<void> setUserProperty({
    required String name,
    required String value,
  }) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.setUserProperty(name: name, value: value);
      LogManager.d("User property set: $name=$value", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to set user property: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }

  /// 设置用户ID
  /// [userId] 用户ID
  static Future<void> setUserId({required String userId}) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.setUserId(id: userId);
      LogManager.d("User ID set: $userId", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to set user ID: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }

  /// 上报自定义事件
  /// [name] 事件名称
  /// [parameters] 事件参数
  static Future<void> logCustomEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    if (!FirebaseManager.isInitialized || FirebaseManager.analytics == null) {
      LogManager.w("Firebase Analytics not initialized", tag: "FirebaseAnalyticsHelper");
      return;
    }

    try {
      await FirebaseManager.analytics!.logEvent(name: name, parameters: parameters);
      LogManager.d("Custom event logged: $name", tag: "FirebaseAnalyticsHelper");
    } catch (e) {
      LogManager.e("Failed to log custom event: $e", tag: "FirebaseAnalyticsHelper", error: e);
    }
  }
}
