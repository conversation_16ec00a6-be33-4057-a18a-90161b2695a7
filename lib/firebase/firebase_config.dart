import 'package:firebase_remote_config/firebase_remote_config.dart';
import '../other/log/LogUtil.dart';
import 'firebase_manager.dart';

/// Firebase Remote Config 辅助类
/// 提供 Remote Config 的便捷操作方法
class FirebaseRemoteConfigHelper {

  /// 常用的 Remote Config 键名
  static const String keyPushMaxCount = "push_max_count";
  static const String keyUrgentPushSwitch = "urgentpush_switch";

  /// 获取 Remote Config 实例
  static FirebaseRemoteConfig? get _remoteConfig => FirebaseManager.remoteConfig;

  /// 强制获取最新配置
  /// [force] 是否强制获取，忽略缓存间隔
  static Future<bool> fetchConfig({bool force = false}) async {
    if (!FirebaseManager.isInitialized || _remoteConfig == null) {
      LogManager.w("Firebase Remote Config not initialized", tag: "FirebaseRemoteConfigHelper");
      return false;
    }

    try {
      if (force) {
        // 强制获取时，临时设置最小间隔为0
        await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: Duration.zero,
        ));
      }

      final success = await _remoteConfig!.fetchAndActivate();
      LogManager.d("Remote Config fetch result: $success", tag: "FirebaseRemoteConfigHelper");
      return success;
    } catch (e) {
      LogManager.e("Failed to fetch Remote Config: $e", tag: "FirebaseRemoteConfigHelper", error: e);
      return false;
    }
  }

  /// 获取布尔值
  /// [key] 配置键名
  /// [defaultValue] 默认值
  static bool getBool(String key, {bool defaultValue = false}) {
    if (!FirebaseManager.isInitialized || _remoteConfig == null) {
      LogManager.w("Firebase Remote Config not initialized, returning default value", tag: "FirebaseRemoteConfigHelper");
      return defaultValue;
    }

    try {
      final value = _remoteConfig!.getBool(key);
      LogManager.d("Remote Config getBool: $key = $value", tag: "FirebaseRemoteConfigHelper");
      return value;
    } catch (e) {
      LogManager.e("Failed to get bool value for $key: $e", tag: "FirebaseRemoteConfigHelper", error: e);
      return defaultValue;
    }
  }

  /// 获取整数值
  /// [key] 配置键名
  /// [defaultValue] 默认值
  static int getInt(String key, {int defaultValue = 0}) {
    if (!FirebaseManager.isInitialized || _remoteConfig == null) {
      LogManager.w("Firebase Remote Config not initialized, returning default value", tag: "FirebaseRemoteConfigHelper");
      return defaultValue;
    }

    try {
      final value = _remoteConfig!.getInt(key);
      LogManager.d("Remote Config getInt: $key = $value", tag: "FirebaseRemoteConfigHelper");
      return value == 0 ? defaultValue : value;
    } catch (e) {
      LogManager.e("Failed to get int value for $key: $e", tag: "FirebaseRemoteConfigHelper", error: e);
      return defaultValue;
    }
  }

  /// 获取双精度浮点数值
  /// [key] 配置键名
  /// [defaultValue] 默认值
  static double getDouble(String key, {double defaultValue = 0.0}) {
    if (!FirebaseManager.isInitialized || _remoteConfig == null) {
      LogManager.w("Firebase Remote Config not initialized, returning default value", tag: "FirebaseRemoteConfigHelper");
      return defaultValue;
    }

    try {
      final value = _remoteConfig!.getDouble(key);
      LogManager.d("Remote Config getDouble: $key = $value", tag: "FirebaseRemoteConfigHelper");
      return value == 0.0 ? defaultValue : value;
    } catch (e) {
      LogManager.e("Failed to get double value for $key: $e", tag: "FirebaseRemoteConfigHelper", error: e);
      return defaultValue;
    }
  }

  /// 获取字符串值
  /// [key] 配置键名
  /// [defaultValue] 默认值
  static String getString(String key, {String defaultValue = ""}) {
    if (!FirebaseManager.isInitialized || _remoteConfig == null) {
      LogManager.w("Firebase Remote Config not initialized, returning default value", tag: "FirebaseRemoteConfigHelper");
      return defaultValue;
    }

    try {
      final value = _remoteConfig!.getString(key);
      LogManager.d("Remote Config getString: $key = $value", tag: "FirebaseRemoteConfigHelper");
      return value.isEmpty ? defaultValue : value;
    } catch (e) {
      LogManager.e("Failed to get string value for $key: $e", tag: "FirebaseRemoteConfigHelper", error: e);
      return defaultValue;
    }
  }

  /// 获取所有配置
  static Map<String, RemoteConfigValue> getAll() {
    if (!FirebaseManager.isInitialized || _remoteConfig == null) {
      LogManager.w("Firebase Remote Config not initialized", tag: "FirebaseRemoteConfigHelper");
      return {};
    }

    try {
      return _remoteConfig!.getAll();
    } catch (e) {
      LogManager.e("Failed to get all Remote Config values: $e", tag: "FirebaseRemoteConfigHelper", error: e);
      return {};
    }
  }

  /// 获取配置的来源
  /// [key] 配置键名
  static ValueSource getValueSource(String key) {
    if (!FirebaseManager.isInitialized || _remoteConfig == null) {
      return ValueSource.valueStatic;
    }

    try {
      return _remoteConfig!.getValue(key).source;
    } catch (e) {
      LogManager.e("Failed to get value source for $key: $e", tag: "FirebaseRemoteConfigHelper", error: e);
      return ValueSource.valueStatic;
    }
  }

  /// 便捷方法：获取推送最大次数
  static int getPushMaxCount({int defaultValue = 3}) {
    return getInt(keyPushMaxCount, defaultValue: defaultValue);
  }

  /// 便捷方法：获取紧急推送开关
  static bool getUrgentPushSwitch({bool defaultValue = true}) {
    return getBool(keyUrgentPushSwitch, defaultValue: defaultValue);
  }
}