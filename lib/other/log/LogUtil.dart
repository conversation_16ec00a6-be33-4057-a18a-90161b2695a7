
/// 日志级别枚举
enum LogLevel {
  verbose,
  debug,
  info,
  warning,
  error,
  wtf,
}

/// 日志接口
/// 使用者可以实现这个接口来自定义日志处理方式
abstract class ILogger {
  /// 输出日志
  /// [level] 日志级别
  /// [tag] 日志标签
  /// [message] 日志消息
  /// [error] 错误对象（可选）
  /// [stackTrace] 堆栈跟踪（可选）
  void log(LogLevel level, String tag, String message, [Object? error, StackTrace? stackTrace]);
}

/// 默认的控制台日志实现（仅用于开发调试）
class ConsoleLogger implements ILogger {
  @override
  void log(LogLevel level, String tag, String message, [Object? error, StackTrace? stackTrace]) {
    final levelStr = _getLevelString(level);
    final timestamp = DateTime.now().toIso8601String();

    // ignore: avoid_print
    print("[$timestamp] $levelStr/$tag: $message");

    if (error != null) {
      // ignore: avoid_print
      print("Error: $error");
    }

    if (stackTrace != null) {
      // ignore: avoid_print
      print("StackTrace: $stackTrace");
    }
  }

  String _getLevelString(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return 'V';
      case LogLevel.debug:
        return 'D';
      case LogLevel.info:
        return 'I';
      case LogLevel.warning:
        return 'W';
      case LogLevel.error:
        return 'E';
      case LogLevel.wtf:
        return 'WTF';
    }
  }
}

/// 日志管理器
class LogManager {
  static ILogger? _logger;
  static String _defaultTag = "FlutterTools";

  /// 设置日志实现
  /// [logger] 日志实现，如果为 null 则禁用日志
  static void setLogger(ILogger? logger) {
    _logger = logger;
  }

  /// 设置默认标签
  static void setDefaultTag(String tag) {
    _defaultTag = tag;
  }

  /// 获取当前是否启用日志
  static bool get isEnabled => _logger != null;

  /// 输出 Verbose 级别日志
  static void v(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _logger?.log(LogLevel.verbose, tag ?? _defaultTag, message, error, stackTrace);
  }

  /// 输出 Debug 级别日志
  static void d(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _logger?.log(LogLevel.debug, tag ?? _defaultTag, message, error, stackTrace);
  }

  /// 输出 Info 级别日志
  static void i(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _logger?.log(LogLevel.info, tag ?? _defaultTag, message, error, stackTrace);
  }

  /// 输出 Warning 级别日志
  static void w(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _logger?.log(LogLevel.warning, tag ?? _defaultTag, message, error, stackTrace);
  }

  /// 输出 Error 级别日志
  static void e(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _logger?.log(LogLevel.error, tag ?? _defaultTag, message, error, stackTrace);
  }

  /// 输出 WTF 级别日志
  static void wtf(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _logger?.log(LogLevel.wtf, tag ?? _defaultTag, message, error, stackTrace);
  }
}


