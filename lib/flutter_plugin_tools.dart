import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter_plugin_tools/appsflyer/appsflyer_manager.dart';
import 'package:flutter_plugin_tools/appsflyer/config/appsFlyer_config.dart';
import 'package:flutter_plugin_tools/config/flutter_tools_config.dart';
import 'package:flutter_plugin_tools/firebase/config/firebase_config.dart';
import 'package:flutter_plugin_tools/firebase/firebase_manager.dart';
import 'package:flutter_plugin_tools/att/config/att_config.dart';
import 'package:flutter_plugin_tools/att/att_manager.dart';
import 'package:flutter_plugin_tools/other/log/LogUtil.dart';



/// Flutter Tools 主入口类
/// 提供统一的初始化和管理功能
class FlutterPluginTools {
  static FlutterPluginToolsConfig? _config;
  static bool _isInitialized = false;

  /// 初始化 Flutter Tools
  /// [config] 配置信息
  static Future<void> initialize(FlutterPluginToolsConfig config) async {
    if (_isInitialized) {
      LogManager.w("FlutterTools already initialized");
      return;
    }

    if (!config.isValid()) {
      throw Exception('Invalid FlutterTools config');
    }

    try {
      _config = config;

      // 初始化日志系统
      LogManager.setLogger(config.logConfig.logger);
      LogManager.setDefaultTag(config.logConfig.defaultTag);

      LogManager.i("Initializing FlutterTools...");

      // 初始化 AppsFlyer（如果配置了）
      if (config.appsFlyerConfig != null) {
        await _initializeAppsFlyer(config.appsFlyerConfig!);
      }

      // 初始化 Firebase（如果配置了）
      if (config.firebaseConfig != null) {
        await _initializeFirebase(config.firebaseConfig!);
      }

      // 初始化 ATT（如果配置了）
      if (config.attConfig != null) {
        await _initializeATT(config.attConfig!);
      }

      // TODO: 初始化其他模块（网络库等）

      _isInitialized = true;
      LogManager.i("FlutterTools initialized successfully");
    } catch (e) {
      LogManager.e("Failed to initialize FlutterTools: $e", error: e);
      rethrow;
    }
  }

  /// 初始化 AppsFlyer
  static Future<void> _initializeAppsFlyer(AppsFlyerConfig config) async {
    LogManager.i("Initializing AppsFlyer...");

    await AppsFlyerManager.initialize(
      config,
      onInstallConversionData: (data) {
        LogManager.d("AppsFlyer onInstallConversionData: $data");
      },
      onAppOpenAttribution: (data) {
        LogManager.d("AppsFlyer onAppOpenAttribution: $data");
      },
      onDeepLinking: (result) {
        switch (result.status) {
          case Status.FOUND:
            LogManager.d("AppsFlyer deep link found: ${result.deepLink?.toString()}");
            LogManager.d("AppsFlyer deep link value: ${result.deepLink?.deepLinkValue}");
            break;
          case Status.NOT_FOUND:
            LogManager.d("AppsFlyer deep link not found");
            break;
          case Status.ERROR:
            LogManager.e("AppsFlyer deep link error: ${result.error}");
            break;
          case Status.PARSE_ERROR:
            LogManager.e("AppsFlyer deep link parse error");
            break;
        }
      },
    );
  }

  /// 初始化 Firebase
  static Future<void> _initializeFirebase(FirebaseConfig config) async {
    LogManager.i("Initializing Firebase...");

    await FirebaseManager.initialize(config);
  }

  /// 初始化 ATT
  static Future<void> _initializeATT(ATTConfig config) async {
    LogManager.i("Initializing ATT...");

    await ATTManager.initialize(config);
  }

  /// 获取配置信息
  static FlutterPluginToolsConfig? get config => _config;

  /// 检查是否已初始化
  static bool get isInitialized => _isInitialized;

  /// 获取 AppsFlyer 管理器类（静态访问）
  static Type get appsFlyer {
    if (!_isInitialized) {
      throw Exception('FlutterTools not initialized. Please call FlutterTools.initialize() first.');
    }
    return AppsFlyerManager;
  }

  /// 获取 Firebase 管理器类（静态访问）
  static Type get firebase {
    if (!_isInitialized) {
      throw Exception('FlutterTools not initialized. Please call FlutterTools.initialize() first.');
    }
    return FirebaseManager;
  }

  /// 获取 ATT 管理器类（静态访问）
  static Type get att {
    if (!_isInitialized) {
      throw Exception('FlutterTools not initialized. Please call FlutterTools.initialize() first.');
    }
    return ATTManager;
  }

  /// 重置（用于测试）
  static void reset() {
    _config = null;
    _isInitialized = false;
  }
}
