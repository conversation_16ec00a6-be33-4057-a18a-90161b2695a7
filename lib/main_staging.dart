import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:world_tune/start.dart';

Future<void> main() async {
  // 明确加载staging环境配置
  await dotenv.load(fileName: 'assets/env/.env.staging');
  print('✅ Staging环境配置加载完成: assets/env/.env.staging');
  
  // 打印API配置信息（staging环境）
  final apiUrl = dotenv.env['API_BASE_URL'] ?? 'http://localhost:8300';
  print('🔗 Staging环境API地址: $apiUrl');

  await start();
}
