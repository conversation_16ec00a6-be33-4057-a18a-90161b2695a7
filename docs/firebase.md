# Firebase 模块使用文档

## 概述

Firebase 模块是 Flutter Tools 基础组件库的一部分，提供了简单易用的 Firebase 服务集成方案。支持 Firebase Analytics、Crashlytics、Remote Config 等核心功能。

## 功能特性

- 🚀 **统一初始化**: 一次配置，自动初始化所有 Firebase 服务
- 📊 **Analytics 集成**: 完整的用户行为分析和事件追踪
- 🐛 **Crashlytics 集成**: 自动崩溃报告和错误监控
- ⚙️ **Remote Config**: 远程配置管理，支持 A/B 测试
- 🔧 **易于配置**: 通过配置类统一管理 Firebase 参数
- 📱 **跨平台**: 支持 iOS 和 Android

## 快速开始

### 1. Firebase 项目设置

在使用前，请确保已在 Firebase 控制台创建项目并获取配置信息：

1. 访问 [Firebase 控制台](https://console.firebase.google.com/)
2. 创建新项目或选择现有项目
3. 添加 iOS 和 Android 应用
4. 获取配置参数（API Key、App ID 等）

### 2. 初始化配置

在您的 `main.dart` 中：

```dart
import 'package:flutter/material.dart';
import 'package:flutter_tools/flutter_tools.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await FlutterTools.initialize(
    FlutterToolsConfig(
      // Firebase 配置
      firebaseConfig: FirebaseConfig.simple(
        projectId: "your_project_id",
        iosApiKey: "your_ios_api_key",
        iosAppId: "your_ios_app_id",
        androidApiKey: "your_android_api_key",
        androidAppId: "your_android_app_id",
        messagingSenderId: "your_messaging_sender_id",
        // 可选配置
        enableAnalytics: true,
        enableCrashlytics: true,
        enableRemoteConfig: true,
        remoteConfigDefaults: {
          "welcome_message": "欢迎使用应用!",
          "feature_enabled": true,
          "max_retry_count": 3,
        },
      ),
    ),
  );
  
  runApp(MyApp());
}
```

### 3. 使用 Firebase 服务

#### Analytics 事件追踪

```dart
// 登录事件
await FirebaseAnalyticsHelper.logLogin(method: "email");

// 购买事件
await FirebaseAnalyticsHelper.logPurchase(
  transactionId: "txn_123",
  value: 9.99,
  currency: "USD",
);

// 自定义事件
await FirebaseAnalyticsHelper.logCustomEvent(
  name: "button_clicked",
  parameters: {"button_name": "subscribe"},
);

// 设置用户属性
await FirebaseAnalyticsHelper.setUserProperty(
  name: "user_type",
  value: "premium",
);
```

#### Remote Config 使用

```dart
// 获取字符串配置
String welcomeMessage = FirebaseRemoteConfigHelper.getString(
  "welcome_message",
  defaultValue: "默认欢迎消息",
);

// 获取布尔配置
bool featureEnabled = FirebaseRemoteConfigHelper.getBool(
  "feature_enabled",
  defaultValue: false,
);

// 获取数字配置
int maxRetryCount = FirebaseRemoteConfigHelper.getInt(
  "max_retry_count",
  defaultValue: 3,
);

// 强制刷新配置
bool success = await FirebaseRemoteConfigHelper.fetchConfig(force: true);
```

#### Crashlytics 错误报告

```dart
// 记录错误
await FirebaseCrashlyticsHelper.recordError(
  exception: Exception("Something went wrong"),
  stackTrace: StackTrace.current,
  reason: "User action failed",
);

// 记录自定义日志
await FirebaseCrashlyticsHelper.log("User performed action X");

// 设置用户标识
await FirebaseCrashlyticsHelper.setUserIdentifier("user_123");

// 设置自定义键值对
await FirebaseCrashlyticsHelper.setCustomKey("last_action", "subscribe");
```

## 配置参数

### FirebaseConfig

#### 使用 FirebaseConfig.simple() 创建（推荐）

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| projectId | String | ✅ | Firebase 项目 ID |
| iosApiKey | String | ✅ | iOS API Key |
| iosAppId | String | ✅ | iOS App ID |
| androidApiKey | String | ✅ | Android API Key |
| androidAppId | String | ✅ | Android App ID |
| messagingSenderId | String | ✅ | Messaging Sender ID |
| storageBucket | String | ❌ | Storage Bucket |
| iosClientId | String | ❌ | iOS Client ID |
| iosBundleId | String | ❌ | iOS Bundle ID |
| androidClientId | String | ❌ | Android Client ID |
| androidPackageName | String | ❌ | Android Package Name |
| enableAnalytics | bool | ❌ | 是否启用 Analytics（默认 true） |
| enableCrashlytics | bool | ❌ | 是否启用 Crashlytics（默认 true） |
| enableRemoteConfig | bool | ❌ | 是否启用 Remote Config（默认 true） |
| remoteConfigDefaults | Map | ❌ | Remote Config 默认值 |

#### 使用完整构造函数

```dart
FirebaseConfig(
  iosOptions: FirebaseOptions(
    apiKey: "ios_api_key",
    appId: "ios_app_id",
    messagingSenderId: "sender_id",
    projectId: "project_id",
    // ... 其他 iOS 配置
  ),
  androidOptions: FirebaseOptions(
    apiKey: "android_api_key",
    appId: "android_app_id",
    messagingSenderId: "sender_id",
    projectId: "project_id",
    // ... 其他 Android 配置
  ),
  enableAnalytics: true,
  enableCrashlytics: true,
  enableRemoteConfig: true,
  remoteConfigFetchInterval: 2 * 60 * 60 * 1000, // 2小时
  remoteConfigDefaults: {
    "key": "value",
  },
)
```

## API 参考

### FirebaseManager

#### 静态方法

- `initialize(FirebaseConfig config)` - 初始化 Firebase
- `get isInitialized` - 检查是否已初始化
- `get analytics` - 获取 Analytics 实例
- `get crashlytics` - 获取 Crashlytics 实例
- `get remoteConfig` - 获取 Remote Config 实例

### FirebaseAnalyticsHelper

#### 预定义事件

- `logLogin({String? method})` - 登录事件
- `logSignUp({String? method})` - 注册事件
- `logPurchase(...)` - 购买事件
- `logScreenView(...)` - 屏幕浏览事件
- `logSearch({required String searchTerm})` - 搜索事件
- `logShare(...)` - 分享事件

#### 用户管理

- `setUserId({required String userId})` - 设置用户 ID
- `setUserProperty(...)` - 设置用户属性

#### 自定义事件

- `logCustomEvent({required String name, Map<String, Object>? parameters})` - 自定义事件

### FirebaseRemoteConfigHelper

#### 获取配置值

- `getBool(String key, {bool defaultValue})` - 获取布尔值
- `getInt(String key, {int defaultValue})` - 获取整数值
- `getDouble(String key, {double defaultValue})` - 获取浮点数值
- `getString(String key, {String defaultValue})` - 获取字符串值

#### 配置管理

- `fetchConfig({bool force})` - 获取最新配置
- `getAll()` - 获取所有配置
- `getValueSource(String key)` - 获取配置来源

### FirebaseCrashlyticsHelper

#### 错误记录

- `recordError(...)` - 记录错误
- `recordFlutterError(FlutterErrorDetails details)` - 记录 Flutter 错误
- `log(String message)` - 记录日志

#### 用户信息

- `setUserIdentifier(String identifier)` - 设置用户标识
- `setCustomKey(String key, Object value)` - 设置自定义键值对

#### 报告管理

- `sendUnsentReports()` - 发送未发送的报告
- `deleteUnsentReports()` - 删除未发送的报告
- `setCrashlyticsCollectionEnabled(bool enabled)` - 启用/禁用收集

## 最佳实践

### 1. 环境配置

```dart
// 开发环境
final developmentConfig = FirebaseConfig.simple(
  projectId: "myapp-dev",
  // ... 开发环境配置
  enableAnalytics: false, // 开发环境可以禁用
  enableCrashlytics: false,
);

// 生产环境
final productionConfig = FirebaseConfig.simple(
  projectId: "myapp-prod",
  // ... 生产环境配置
  enableAnalytics: true,
  enableCrashlytics: true,
);

const isProduction = bool.fromEnvironment('dart.vm.product');
final config = isProduction ? productionConfig : developmentConfig;
```

### 2. 错误处理

```dart
// 全局错误处理
FlutterError.onError = (FlutterErrorDetails details) {
  FirebaseCrashlyticsHelper.recordFlutterError(details);
};

// 异步错误处理
PlatformDispatcher.instance.onError = (error, stack) {
  FirebaseCrashlyticsHelper.recordError(
    exception: error,
    stackTrace: stack,
  );
  return true;
};
```

### 3. Remote Config 缓存

```dart
class ConfigManager {
  static Timer? _refreshTimer;
  
  static void startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(Duration(hours: 1), (timer) {
      FirebaseRemoteConfigHelper.fetchConfig();
    });
  }
  
  static void stopPeriodicRefresh() {
    _refreshTimer?.cancel();
  }
}
```

## 注意事项

1. **初始化顺序**: 必须在使用任何 Firebase 功能前调用 `FlutterTools.initialize()`
2. **网络权限**: 确保应用有网络访问权限
3. **平台配置**: iOS 和 Android 需要分别配置对应的参数
4. **调试模式**: Crashlytics 在 debug 模式下默认禁用
5. **隐私合规**: 使用 Analytics 时注意隐私政策和用户同意

## 常见问题

### Q: 如何获取 Firebase 配置参数？
A: 在 Firebase 控制台的项目设置中可以找到所有配置参数。

### Q: 为什么 Crashlytics 在开发环境不工作？
A: Crashlytics 在 debug 模式下默认禁用，这是正常行为。

### Q: Remote Config 多久更新一次？
A: 默认间隔是 2 小时，可以通过 `remoteConfigFetchInterval` 参数调整。

### Q: 如何测试 Firebase 集成？
A: 可以使用示例应用中的测试按钮，或查看 Firebase 控制台的实时数据。
