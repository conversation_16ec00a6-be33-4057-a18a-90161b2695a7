# WorldTune 电台项目功能列表

## 项目概述

WorldTune 是一个全球电台播放器应用，允许用户浏览、搜索和收听来自世界各地的电台广播。本文档详细介绍了项目的各个页面及其功能，用于支持UI重新设计时保持现有功能。

## 页面功能列表

### 1. 探索页面 (ExplorePage)

探索页面是用户发现和搜索电台的主要入口。

#### 功能列表：

1. **国家与省/州筛选**
   - 选择国家
   - 基于选定国家显示省/州列表
   - 组合式地区筛选（国家+省/州）

2. **搜索功能**
   - 电台名称和标签的关键词搜索
   - 实时搜索结果更新（延迟500ms）

3. **标签筛选系统**
   - 可折叠的标签选择区域
   - 多标签同时选择功能
   - 基于选定标签显示相关电台

4. **排序控制**
   - 支持多种排序方式：
     - 热度排序（最多点击）
     - 最多投票
     - 按名称排序
     - 最新电台

5. **电台列表显示**
   - 滚动电台列表
   - 电台基本信息展示（名称、国家、标签）
   - 电台播放功能

6. **热门电台区域**
   - 独立显示热门电台列表
   - 当没有搜索条件时显示

7. **UI交互优化**
   - 筛选条件重置功能
   - 下拉刷新获取最新数据
   - 加载状态反馈
   - 错误状态处理与重试

### 2. 资料库页面 (LibraryPage)

资料库页面管理用户的收藏电台和收听历史。

#### 功能列表：

1. **标签式导航**
   - 收藏电台标签
   - 历史记录标签
   - 自定义Tab栏设计

2. **收藏电台管理**
   - 收藏电台列表展示
   - 电台信息显示（名称、国家等）
   - 收藏电台播放功能
   - 取消收藏功能
   - 空状态处理

3. **历史记录管理**
   - 播放历史列表展示
   - 历史电台信息显示（名称、国家、上次播放时间）
   - 历史电台播放功能
   - 播放时间本地化展示
   - 空状态处理

4. **播放列表上下文**
   - 播放收藏列表中的电台（带上下文）
   - 播放历史列表中的电台（带上下文）
   - 支持在播放器中前后切换电台

5. **UI状态处理**
   - 加载状态显示
   - 错误状态处理与重试
   - 优雅的空状态设计

### 3. 首页 (HomePage)

首页是应用的主要入口，展示分类和推荐电台。

#### 功能列表：

1. **分类选择栏**
   - 水平滚动的分类选择器
   - 基于不同分类显示相关电台
   - 分类标签支持本地化

2. **推荐区域**
   - 热门推荐电台展示
   - 点击率最高电台展示
   - 高音质电台展示
   - 最新上架电台展示
   - 推荐内容支持分类过滤

3. **电台网格展示**
   - 以网格形式展示分类下的电台
   - 多行展示，每行滚动浏览
   - 电台卡片显示（图标、名称）

4. **播放列表上下文**
   - 从网格区域播放电台（带上下文）
   - 从推荐区域播放电台（带上下文）
   - 支持在播放器中前后切换电台

5. **缓存与数据加载优化**
   - 启动缓存系统，加速首次加载
   - 后台更新最新数据
   - 预加载分类数据
   - 延迟保存缓存数据

6. **UI交互优化**
   - 下拉刷新更新所有数据
   - 动画效果（滑动、淡入淡出、缩放）
   - 空状态和加载状态处理

### 4. 全局播放功能

贯穿整个应用的音频播放功能。

#### 功能列表：

1. **迷你播放器**
   - 显示当前播放的电台信息
   - 播放/暂停控制
   - 展开为全屏播放器

2. **全屏播放器**
   - 大图电台展示
   - 播放控制（播放/暂停）
   - 音量控制
   - 播放列表上下文展示
   - 上一个/下一个电台切换

3. **播放列表功能**
   - 基于来源上下文显示播放列表
   - 从列表中选择电台播放
   - 来源信息展示

4. **后台播放**
   - 应用切换到后台时继续播放
   - 通知栏控制播放

5. **播放状态管理**
   - 播放状态持久化
   - 自动记录播放历史
   - 播放次数统计

### 5. 通用功能

贯穿整个应用的通用功能。

#### 功能列表：

1. **国际化支持**
   - 多语言界面支持
   - 本地化文本展示

2. **主题支持**
   - 明暗主题支持
   - Material Design 3设计规范

3. **数据缓存**
   - 电台数据本地缓存
   - 用户偏好设置缓存
   - 启动缓存系统

4. **性能优化**
   - 图片加载优化
   - 电台数据延迟加载
   - 页面状态保持

5. **错误处理**
   - 网络错误处理
   - 加载失败重试机制
   - 优雅降级策略

## 数据模型

### 1. 电台模型

```dart
class StationSimple {
  final String id;
  final String name;
  final String url;
  final String country;
  final String language;
  final String favicon;
  final String homepage;
  final List<String> tags;
  final int votes;
  final bool isFavorite;
  final int playCount;
  final DateTime? lastPlayed;
}
```

### 2. 分类模型

```dart
class RadioCategory {
  final int id;
  final String name;
  final String displayName;
  final IconData icon;
  final String color;
  final String description;
  final List<String> tags;
  final int sortOrder;
  final bool isVisible;
}
```

### 3. 播放列表上下文模型

```dart
enum PlaylistSourceType {
  hotRecommendations,
  mostClicked,
  highQuality,
  latestAdded,
  categoryGrid,
  playHistory,
  favorites,
}

class PlaylistContext {
  final PlaylistSourceType sourceType;
  final List<StationSimple> stations;
  final int currentIndex;
  final String sourceTitle;
  final Map<String, dynamic>? sourceMetadata;
}
```

### 4. 播放历史模型

```dart
class PlayHistory {
  final String stationId;
  final String stationName;
  final DateTime playedAt;
  final Duration duration;
  final String? stationUrl;
  final String? stationCountry;
  final String? stationFavicon;
  final String? stationLanguage;
}
```

### 5. 用户偏好模型

```dart
class UserPreferences {
  final List<String> favoriteStationIds;
  final List<PlayHistory> playHistory;
  final String language;
  final double volume;
  final bool autoPlay;
}
```