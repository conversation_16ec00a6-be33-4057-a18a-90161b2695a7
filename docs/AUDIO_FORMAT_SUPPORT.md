# 音频格式支持文档

> **文档版本**: v1.0  
> **更新时间**: 2025-01-27  
> **适用版本**: World Tune v1.0.0+

## 📋 概述

World Tune 基于 `just_audio` 音频播放器构建，提供广泛的音频格式和流媒体协议支持。本文档详细说明支持的格式、处理方式和平台兼容性。

## 🎵 支持的音频格式

### 1. 直播流媒体格式

#### 1.1 MP3 (MPEG Audio Layer 3)
- **MIME类型**: `audio/mpeg`, `audio/mp3`, `audio/mpeg3`, `audio/x-mpeg`
- **文件扩展名**: `.mp3`
- **比特率支持**: 32-320 kbps
- **支持平台**: ✅ iOS / ✅ Android
- **处理方式**: 直接播放，无需预处理
- **特殊检测**: 
  - Magic Bytes: `\xff\xfb`, `\xff\xf3`, `\xff\xf2`, `ID3`
  - 最小数据量: 1024 bytes
- **应用场景**: 最常见的电台流媒体格式

#### 1.2 AAC (Advanced Audio Coding)
- **MIME类型**: `audio/aac`, `audio/aacp`, `audio/mp4`, `audio/x-aac`
- **文件扩展名**: `.aac`, `.m4a`
- **变体支持**: AAC, AAC+, AACP (高质量流媒体)
- **支持平台**: ✅ iOS / ✅ Android
- **处理方式**: 直接播放，自动格式检测
- **特殊检测**:
  - Magic Bytes: `\xff\xf1`, `\xff\xf9`, `ftyp`
  - 最小数据量: 1024 bytes
- **应用场景**: 高质量电台流媒体

#### 1.3 OGG/Vorbis
- **MIME类型**: `audio/ogg`, `application/ogg`, `audio/vorbis`
- **文件扩展名**: `.ogg`, `.oga`
- **支持平台**: ✅ iOS / ✅ Android
- **处理方式**: 直接播放
- **特殊检测**:
  - Magic Bytes: `OggS`
  - 最小数据量: 1024 bytes
- **应用场景**: 开源流媒体格式

#### 1.4 FLAC (Free Lossless Audio Codec)
- **MIME类型**: `audio/flac`, `audio/x-flac`
- **文件扩展名**: `.flac`
- **支持平台**: ✅ iOS / ✅ Android
- **处理方式**: 直接播放（通常为文件而非流）
- **特殊检测**:
  - Magic Bytes: `fLaC`
  - 最小数据量: 2048 bytes
- **应用场景**: 无损音频文件播放

#### 1.5 WAV (Waveform Audio Format)
- **MIME类型**: `audio/wav`, `audio/wave`, `audio/x-wav`
- **文件扩展名**: `.wav`
- **支持平台**: ✅ iOS / ✅ Android
- **处理方式**: 直接播放
- **特殊检测**:
  - Magic Bytes: `RIFF`
  - 最小数据量: 2048 bytes
- **应用场景**: 无压缩音频文件

### 2. 流媒体协议

#### 2.1 HLS (HTTP Live Streaming)
- **文件扩展名**: `.m3u8`
- **MIME类型**: 
  - `application/vnd.apple.mpegurl`
  - `application/x-mpegurl`
  - `audio/x-mpegurl`
  - `audio/mpegurl`
  - `text/plain`
- **支持平台**: ✅ iOS / ✅ Android
- **处理方式**: **直接播放** (不解析播放列表)
- **特殊检测**:
  - Magic Bytes: `#EXTM3U`
  - 最小数据量: 100 bytes
- **技术特点**:
  - 自适应比特率流
  - 自动处理音频片段连接
  - 支持实时流媒体
- **应用场景**: 现代流媒体电台的主要格式

## 📂 播放列表格式

### 2.1 PLS (Playlist File)
- **文件扩展名**: `.pls`
- **MIME类型**: `audio/x-scpls`, `text/plain`
- **支持平台**: ✅ iOS / ✅ Android
- **处理方式**: **解析后播放实际流URL**
- **格式检测**:
  - Magic Bytes: `[playlist]`, `file1=`, `File1=`
  - 解析字段: `File1=`, `file1=`
- **解析逻辑**:
  ```
  [playlist]
  File1=http://stream.example.com/radio.mp3
  Title1=Example Radio
  Length1=-1
  ```

### 2.2 M3U (MP3 URL)
- **文件扩展名**: `.m3u`
- **MIME类型**: `audio/x-mpegurl`, `audio/mpegurl`, `text/plain`
- **支持平台**: ✅ iOS / ✅ Android
- **处理方式**: **解析后播放第一个有效URL**
- **格式检测**:
  - Magic Bytes: `#EXTM3U`, `http`
  - 自动检测HTTP/HTTPS URL
- **解析逻辑**:
  ```
  #EXTM3U
  #EXTINF:0,Example Radio
  http://stream.example.com/radio.mp3
  ```

## 🔧 技术实现

### 1. 格式检测机制

#### 1.1 URL扩展名检测
```dart
bool _isPlaylistFile(String url) {
  final path = uri.path.toLowerCase();
  
  // M3U8 HLS流直接播放
  if (path.endsWith('.m3u8')) {
    return false; // 不解析，直接播放
  }
  
  // 需要解析的播放列表格式
  return path.endsWith('.pls') || path.endsWith('.m3u');
}
```

#### 1.2 内容类型验证
- HTTP Header `Content-Type` 检查
- Magic Bytes 二进制签名检测
- 内容格式自动识别

#### 1.3 流可用性验证
- HEAD请求检测
- GET请求前1024字节采样
- 音频数据格式验证

### 2. 播放处理流程

#### 2.1 HLS流处理 (M3U8)
```
电台URL (*.m3u8) → 直接传递给just_audio → 自动处理HLS片段
```

#### 2.2 播放列表处理 (PLS/M3U)
```
播放列表URL → 下载文件 → 解析获取流URL → 传递给just_audio
```

#### 2.3 直接流处理
```
流URL → 直接传递给just_audio → 开始播放
```

### 3. 错误处理机制

#### 3.1 网络错误
- **超时处理**: 连接和接收超时 (10秒)
- **重连机制**: 自动重试 (最多3次)
- **网络状态**: 连接失败、不可达等

#### 3.2 格式错误
- **不支持格式**: 自动识别和报告
- **编码问题**: Codec不兼容检测
- **播放列表错误**: 解析失败处理

#### 3.3 平台特定错误
- **Android网络安全策略**: HTTP连接限制
- **iOS App Transport Security**: HTTPS要求
- **SSL/TLS证书**: 证书验证失败

## 🔒 平台兼容性

### 1. Android平台

#### 1.1 支持情况
- ✅ 所有主要音频格式
- ✅ HLS流媒体
- ✅ HTTP/HTTPS协议
- ⚠️ 网络安全策略限制

#### 1.2 特殊配置
```xml
<!-- android/app/src/main/res/xml/network_security_config.xml -->
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">example-radio-domain.com</domain>
    </domain-config>
</network-security-config>
```

#### 1.3 已知限制
- HTTP明文传输可能被阻止
- 需要网络安全配置白名单
- 部分低版本设备对AAC+支持有限

### 2. iOS平台

#### 2.1 支持情况
- ✅ 所有主要音频格式
- ✅ HLS流媒体 (原生支持)
- ⚠️ App Transport Security限制

#### 2.2 特殊配置
```xml
<!-- ios/Runner/Info.plist -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

#### 2.3 已知限制
- 默认只允许HTTPS连接
- 需要ATS配置允许HTTP
- HLS是首选的流媒体格式

## ⚡ 性能优化

### 1. 播放启动优化
- **预连接**: 提前建立网络连接
- **缓存策略**: 播放列表文件缓存
- **超时控制**: 合理的连接超时设置

### 2. 内存优化
- **流式播放**: 不下载完整文件
- **缓冲控制**: 适当的缓冲区大小
- **资源释放**: 及时清理播放器资源

### 3. 网络优化
- **并发限制**: 限制同时连接数
- **重试策略**: 渐进式重试间隔
- **错误恢复**: 智能错误恢复机制

## 🧪 测试验证

### 1. 格式兼容性测试
项目包含完整的格式验证工具:
- `docs/cmd/data_import.py` - 批量电台验证
- `docs/cmd/station_data_updater.py` - 高性能验证器

### 2. 验证指标
- **响应时间**: < 5秒连接建立
- **成功率**: > 95% 播放成功率
- **错误恢复**: < 2秒自动重试

### 3. 质量检查
```python
# 支持的格式验证
supported_audio_formats = [
    'audio/mpeg',           # MP3
    'audio/aac',            # AAC
    'audio/ogg',            # OGG
    'video/mp2t',           # HLS/M3U8
    'application/vnd.apple.mpegurl',  # M3U8
    'audio/flac',           # FLAC
    'audio/wav',            # WAV
]
```

## 📊 使用统计

### 1. 格式分布 (基于Radio Browser数据)
- **MP3**: ~65% 的电台
- **AAC/AAC+**: ~20% 的电台  
- **HLS (M3U8)**: ~10% 的电台
- **OGG**: ~3% 的电台
- **其他**: ~2% 的电台

### 2. 平台表现
- **iOS**: HLS格式表现最佳
- **Android**: MP3格式兼容性最好
- **跨平台**: AAC格式平衡性较好

## 🔮 未来规划

### 1. 格式支持扩展
- **OPUS**: 新一代音频编码格式
- **WebM**: Web优化格式
- **DASH**: 动态自适应流

### 2. 功能增强
- **自适应码率**: 根据网络状况调整
- **离线缓存**: 支持电台内容缓存
- **音质选择**: 用户自定义音质偏好

### 3. 性能提升
- **预加载优化**: 智能预加载机制
- **CDN支持**: 多CDN源支持
- **P2P技术**: 点对点流媒体分发

---

## 📝 更新历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2025-01-27 | 初始版本，完整格式支持文档 |

---

> **注意**: 本文档基于当前版本的技术实现，随着项目发展可能会有更新。如有疑问或建议，请参考项目的 Issues 页面。 