# 📱 World Tune iOS发布准备 - README

## 🎯 当前项目状态总结

### ✅ 已完成的配置
- **Flutter项目结构**: 完整且规范
- **iOS基础配置**: Info.plist、图标、启动屏幕已配置
- **多环境支持**: 生产、开发、测试环境已配置
- **音频播放权限**: 后台音频播放权限已设置
- **网络权限**: NSAppTransportSecurity已配置
- **国际化支持**: 英文和西班牙语支持
- **构建测试**: iOS Release版本构建成功 ✅

### ⚠️ 需要立即修改的关键问题

#### 1. Bundle Identifier（必须修改）
**当前状态**: `com.example.verygoodcore.flutter-boilerplate`
**需要修改为**: `com.worldtune.radio`（或你的域名）

**修改位置**:
- `ios/Runner.xcodeproj/project.pbxproj` 中的所有PRODUCT_BUNDLE_IDENTIFIER
- 生产环境: `com.worldtune.radio`
- 开发环境: `com.worldtune.radio.dev`
- 测试环境: `com.worldtune.radio.stg`

#### 2. Apple Developer账户
**状态**: 未注册
**费用**: $99/年
**注册地址**: https://developer.apple.com/programs/

#### 3. 隐私政策网页
**状态**: 需要创建
**要求**: App Store审核必需项
**内容**: 说明数据收集和使用方式

## 🚀 快速开始步骤

### 第1步：注册Apple Developer账户（1-2天）
1. 访问 https://developer.apple.com/programs/
2. 支付$99年费
3. 等待审核通过

### 第2步：修改Bundle ID（30分钟）
```bash
# 1. 打开Xcode项目
open ios/Runner.xcworkspace

# 2. 在Xcode中修改Bundle Identifier
# Runner项目 → Runner target → General → Bundle Identifier
# 修改为: com.worldtune.radio
```

### 第3步：创建App Store应用（1小时）
1. 登录 https://appstoreconnect.apple.com/
2. 创建新应用
3. 填写基本信息

### 第4步：准备应用资源（2-3小时）
- **应用图标**: 1024×1024px PNG
- **截图**: 至少3张不同尺寸
- **应用描述**: 详细功能介绍
- **隐私政策**: 创建网页链接

### 第5步：构建和上传（1小时）
```bash
# 构建Release版本
flutter build ios --release --no-codesign --flavor production

# 在Xcode中创建Archive并上传
```

## 📋 详细操作指南

完整的操作指南请查看：`docs/ios_release_guide.md`

## 🔧 项目特定配置

### 音频播放配置
已在Info.plist中配置：
```xml
<key>UIBackgroundModes</key>
<array>
    <string>audio</string>
    <string>background-processing</string>
</array>
```

### 网络安全配置
已配置允许HTTP连接（生产环境建议使用HTTPS）：
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

### 国际化配置
支持语言：
- 英文 (en)
- 西班牙语 (es)

## 🚨 重要注意事项

### 全球电台应用特殊要求
1. **内容合规**: 确保电台内容符合各地区法律
2. **版权问题**: 避免包含版权争议内容
3. **网络安全**: 生产环境建议使用HTTPS
4. **隐私保护**: 明确说明数据收集用途

### 常见问题解决
1. **构建失败**: 使用 `--flavor production` 参数
2. **签名问题**: 配置Apple Developer证书
3. **Bundle ID冲突**: 确保使用唯一ID

## ⏰ 预计时间安排

| 任务 | 预计时间 | 状态 |
|------|----------|------|
| Apple Developer注册 | 1-2天 | ⏳ 待完成 |
| 修改Bundle ID | 30分钟 | ⏳ 待完成 |
| 创建App Store应用 | 1小时 | ⏳ 待完成 |
| 准备应用资源 | 2-3小时 | ⏳ 待完成 |
| 构建和上传 | 1小时 | ⏳ 待完成 |
| 等待审核 | 1-3天 | ⏳ 待完成 |

**总计**: 约5个工作日准备 + 1-3天审核

## 📞 获取帮助

### 官方资源
- [App Store审核指南](https://developer.apple.com/app-store/review/guidelines/)
- [Flutter iOS部署文档](https://docs.flutter.dev/deployment/ios)
- [Apple Developer支持](https://developer.apple.com/support/)

### 项目文档
- `docs/ios_release_guide.md` - 完整发布指南
- `docs/ios_setup_guide.md` - 开发环境配置
- `PUBLISH_CHECKLIST.md` - 发布检查清单

## 🎉 成功标志

当你看到以下内容时，说明发布成功：
- [ ] Apple Developer账户激活
- [ ] Bundle ID修改完成
- [ ] App Store Connect应用创建
- [ ] 应用资源准备完成
- [ ] Archive构建成功
- [ ] 应用上传成功
- [ ] 审核通过并上线

---

**下一步**: 开始注册Apple Developer账户，这是发布的第一步！

**需要帮助?** 查看 `docs/ios_release_guide.md` 获取详细操作步骤。
