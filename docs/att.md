# ATT (App Tracking Transparency) 模块使用文档

## 概述

ATT (App Tracking Transparency) 模块是 Flutter Tools 基础组件库的一部分，提供了简单易用的 iOS App Tracking Transparency 集成方案。从 iOS 14.5 开始，苹果要求应用在追踪用户数据前必须获得用户明确同意。

## 功能特性

- 🍎 **iOS 专用**: 专为 iOS 14.5+ 的 ATT 要求设计
- 🚀 **自动初始化**: 支持应用启动时自动请求权限
- ⏰ **延迟请求**: 可配置延迟时间，让用户先熟悉应用
- 🎯 **手动控制**: 支持手动触发权限请求
- 📊 **状态监控**: 实时监控权限状态变化
- 🔗 **回调支持**: 提供丰富的回调函数
- 🆔 **IDFA 获取**: 安全获取广告标识符

## 快速开始

### 1. iOS 配置

在使用前，请确保在 iOS 项目中添加了必要的配置：

#### Info.plist 配置

在 `ios/Runner/Info.plist` 中添加：

```xml
<key>NSUserTrackingUsageDescription</key>
<string>此应用需要获取您的设备标识符来为您提供个性化的广告体验</string>
```

### 2. 初始化配置

在您的 `main.dart` 中：

```dart
import 'package:flutter/material.dart';
import 'package:flutter_plugin_tools/flutter_plugin_tools.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await FlutterPluginTools.initialize(
    FlutterPluginToolsConfig(
      // ATT 配置
      attConfig: ATTConfig.delayed(
        delaySeconds: 2, // 延迟2秒请求权限
        onStatusChanged: (status, advertisingId) {
          print("ATT 状态变化: ${ATTManager.getStatusDescription(status)}");
          if (advertisingId != null) {
            print("IDFA: $advertisingId");
          }
        },
        onRequestCompleted: (status, isFirstTime, advertisingId) {
          print("ATT 请求完成: ${ATTManager.getStatusDescription(status)}");
          if (status == TrackingStatus.authorized && advertisingId != null) {
            // 用户同意追踪，可以启用相关功能
            print("✅ 用户同意 ATT 权限，IDFA: $advertisingId");
            _enableTrackingWithIDFA(advertisingId);
          } else {
            // 用户拒绝追踪，需要禁用相关功能
            print("⚠️ 用户拒绝 ATT 权限");
            _enableAnonymousMode();
          }
        },
      ),
    ),
  );
  
  runApp(MyApp());
}
```

### 3. 使用 ATT 功能

#### 检查权限状态

```dart
// 获取当前权限状态
TrackingStatus? status = await ATTManager.getCurrentStatus();
print("当前 ATT 状态: ${ATTManager.getStatusDescription(status!)}");

// 检查是否已授权
bool isAuthorized = await ATTManager.isTrackingAuthorized();
print("是否已授权: $isAuthorized");

// 检查是否可以请求权限
bool canRequest = await ATTManager.canRequestPermission();
print("是否可以请求权限: $canRequest");
```

#### 手动请求权限

```dart
// 手动请求 ATT 权限
TrackingStatus? status = await ATTManager.requestPermission();
if (status == TrackingStatus.authorized) {
  print("用户同意了追踪权限");
} else {
  print("用户拒绝了追踪权限");
}
```

#### 获取 IDFA

```dart
// 获取广告标识符（仅在已授权时可用）
String? idfa = await ATTManager.getAdvertisingIdentifier();
if (idfa != null) {
  print("IDFA: $idfa");
} else {
  print("无法获取 IDFA（可能未授权或不是 iOS 平台）");
}
```

## 配置参数

### ATTConfig

#### 基本构造函数

```dart
ATTConfig({
  bool enabled = true,                                                        // 是否启用 ATT
  bool autoRequestOnStartup = true,                                           // 是否自动请求权限
  int requestDelayMs = 1000,                                                 // 请求延迟（毫秒）
  String? customMessage,                                                      // 自定义提示信息
  Function(TrackingStatus, String?)? onStatusChanged,                        // 状态变化回调
  Function(TrackingStatus, bool, String?)? onRequestCompleted,               // 请求完成回调
})
```

#### 回调参数说明

- **onStatusChanged**: `(TrackingStatus status, String? advertisingId)`
  - `status`: 当前权限状态
  - `advertisingId`: 广告标识符（仅在已授权时有值）

- **onRequestCompleted**: `(TrackingStatus status, bool isFirstTime, String? advertisingId)`
  - `status`: 最终权限状态
  - `isFirstTime`: 是否首次请求权限
  - `advertisingId`: 广告标识符（仅在已授权时有值）

#### 便捷工厂方法

##### ATTConfig.disabled()
创建一个禁用 ATT 的配置

```dart
attConfig: ATTConfig.disabled()
```

##### ATTConfig.delayed()
创建一个延迟请求的配置

```dart
attConfig: ATTConfig.delayed(
  delaySeconds: 3,
  onStatusChanged: (status, advertisingId) {
    // 状态变化处理
    if (advertisingId != null) {
      print("获得 IDFA: $advertisingId");
    }
  },
  onRequestCompleted: (status, isFirstTime, advertisingId) {
    // 请求完成处理
    if (status == TrackingStatus.authorized && advertisingId != null) {
      _configureTrackingWithIDFA(advertisingId);
    }
  },
)
```

##### ATTConfig.manual()
创建一个手动请求的配置

```dart
attConfig: ATTConfig.manual(
  onStatusChanged: (status, advertisingId) {
    // 状态变化处理
    print("ATT 状态: ${ATTManager.getStatusDescription(status)}");
    if (advertisingId != null) {
      print("IDFA: $advertisingId");
    }
  },
  onRequestCompleted: (status, isFirstTime, advertisingId) {
    // 请求完成处理
    _handleATTResult(status, advertisingId);
  },
)
```

## API 参考

### ATTManager

#### 静态方法

##### 初始化和状态
- `initialize(ATTConfig config)` - 初始化 ATT
- `get isInitialized` - 检查是否已初始化
- `getCurrentStatus()` - 获取当前权限状态
- `get cachedStatus` - 获取缓存的权限状态

##### 权限管理
- `requestPermission()` - 手动请求权限
- `isTrackingAuthorized()` - 检查是否已授权追踪
- `canRequestPermission()` - 检查是否可以请求权限

##### IDFA 管理
- `getAdvertisingIdentifier()` - 获取广告标识符

##### 工具方法
- `getStatusDescription(TrackingStatus)` - 获取状态描述

### TrackingStatus 枚举

- `TrackingStatus.authorized` - 已授权
- `TrackingStatus.denied` - 已拒绝
- `TrackingStatus.notDetermined` - 未确定
- `TrackingStatus.restricted` - 受限制

## 最佳实践

### 1. 合适的请求时机

```dart
// 不要在应用启动立即请求，给用户一些时间熟悉应用
attConfig: ATTConfig.delayed(
  delaySeconds: 3, // 延迟3秒
  onRequestCompleted: (status, isFirstTime) {
    if (isFirstTime) {
      // 首次请求，记录用户选择
      _recordATTChoice(status);
    }
  },
)
```

### 2. 根据权限状态调整功能

```dart
void _handleATTStatus(TrackingStatus status) {
  switch (status) {
    case TrackingStatus.authorized:
      // 启用个性化广告
      _enablePersonalizedAds();
      // 启用精确的分析追踪
      _enableDetailedAnalytics();
      break;
    case TrackingStatus.denied:
    case TrackingStatus.restricted:
      // 禁用个性化广告
      _disablePersonalizedAds();
      // 使用匿名分析
      _enableAnonymousAnalytics();
      break;
    case TrackingStatus.notDetermined:
      // 可以考虑稍后再次请求
      break;
  }
}
```

### 3. 与其他 SDK 集成

```dart
// 在 ATT 权限确定后初始化其他 SDK
attConfig: ATTConfig.delayed(
  delaySeconds: 2,
  onRequestCompleted: (status, isFirstTime, advertisingId) async {
    // 根据 ATT 状态配置 AppsFlyer
    if (status == TrackingStatus.authorized && advertisingId != null) {
      // 启用 AppsFlyer 的详细追踪，传入 IDFA
      await _configureAppsFlyerWithTracking(advertisingId);

      // 配置其他需要 IDFA 的 SDK
      await _configureFacebookSDK(advertisingId);
      await _configureGoogleAds(advertisingId);
    } else {
      // 配置匿名模式
      await _configureAppsFlyerAnonymous();
      await _configureFacebookSDKAnonymous();
      await _configureGoogleAdsAnonymous();
    }
  },
)

// 配置 AppsFlyer 使用 IDFA
Future<void> _configureAppsFlyerWithTracking(String idfa) async {
  // 设置 AppsFlyer 使用 IDFA 进行精确追踪
  print("配置 AppsFlyer 使用 IDFA: $idfa");
  // 这里可以调用 AppsFlyer 的相关 API
}

// 配置 Facebook SDK 使用 IDFA
Future<void> _configureFacebookSDK(String idfa) async {
  // 设置 Facebook SDK 使用 IDFA
  print("配置 Facebook SDK 使用 IDFA: $idfa");
}
```

### 4. 用户教育

```dart
// 在请求权限前，可以显示教育界面
Future<void> _showATTEducation() async {
  await showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('个性化体验'),
      content: Text('为了为您提供更好的个性化体验，我们需要获取您的设备标识符。'),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            ATTManager.requestPermission();
          },
          child: Text('了解并继续'),
        ),
      ],
    ),
  );
}
```

## 注意事项

1. **iOS 专用**: ATT 仅在 iOS 14.5+ 上可用，Android 平台会被忽略
2. **权限时机**: 每个应用只能请求一次 ATT 权限，用户选择后无法再次弹出系统对话框
3. **Info.plist**: 必须在 Info.plist 中添加 `NSUserTrackingUsageDescription`
4. **用户体验**: 建议在用户熟悉应用后再请求权限，提高同意率
5. **功能适配**: 根据权限状态调整应用功能，确保在拒绝权限时仍能正常使用

## 常见问题

### Q: 为什么在 Android 上没有效果？
A: ATT 是 iOS 专用功能，Android 平台会自动跳过相关逻辑。

### Q: 用户拒绝权限后如何再次请求？
A: 系统不允许再次弹出权限对话框，只能引导用户到设置中手动开启。

### Q: 如何提高用户同意率？
A: 
- 在合适的时机请求（用户熟悉应用后）
- 提供清晰的说明为什么需要这个权限
- 展示权限带来的好处

### Q: 获取不到 IDFA 怎么办？
A: 确保：
- 用户已授权 ATT 权限
- 在 iOS 设备上运行
- 设备没有限制广告追踪

### Q: 如何测试 ATT 功能？
A: 
- 在 iOS 模拟器或真机上测试
- 可以在设置 > 隐私与安全性 > 追踪中重置权限状态
- 使用不同的 Bundle ID 可以重新触发权限请求
