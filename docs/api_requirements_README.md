# API需求文档更新说明

## 📋 更新概述

本次更新根据新的数据库架构调整了API响应格式和查询参数，增加了新的API端点，优化了数据结构以提供更丰富的功能。

## 🔄 主要变更

### 1. 电台列表API (/stations)

#### 新增查询参数
- `countrycode`: 国家代码过滤（ISO 3166-1 alpha-2）
- `languagecodes`: 语言代码过滤（支持多个，逗号分隔）
- `codec`: 音频编码格式过滤
- `bitrate_min/bitrate_max`: 比特率范围过滤
- `is_hls`: HLS流过滤
- `last_check_ok`: 检查状态过滤

#### 响应格式调整
- 新增 `server_uuid`、`change_uuid` 字段
- 国家信息：从对象改为直接字段 + 关联数组
- 语言信息：从对象改为直接字段
- 新增地理位置相关字段
- 新增检查状态相关字段

### 2. 电台详情API (/stations/{id})

#### 响应格式增强
- 完整的radio-browser.info数据结构
- `countries`: 关联国家数组
- `tag_details`: 标签详细信息数组
- 所有时间戳字段
- 地理位置和距离信息

### 3. 新增API端点

#### 按国家获取电台
```
GET /stations/by-country/{country_code}
```
- 支持按国家代码快速查询
- 完整的分页和排序支持

#### 按标签获取电台
```
GET /stations/by-tag/{tag_name}
```
- 支持按标签名称快速查询
- 完整的分页和排序支持

#### 搜索电台
```
GET /stations/search
```
- 全文搜索功能
- 多条件组合过滤
- 智能排序算法

#### 相似电台推荐
```
GET /stations/{station_id}/similar
```
- 基于标签的相似度算法
- 基于地理位置的推荐
- 混合推荐算法

#### 电台统计信息
```
GET /stations/{station_id}/stats
```
- 播放统计数据
- 时间段统计
- 趋势分析

### 4. 基础数据API优化

#### 国家列表API
- 新增 `sort_order` 排序支持
- 新增 `station_count` 统计信息
- 支持显示状态过滤

#### 标签列表API
- 简化数据结构
- 新增排序权重
- 新增电台数量统计

#### 语言统计API
- 从独立表改为统计查询
- 提供语言使用统计
- 支持多语言代码

### 5. 统计API增强

#### 热门电台API
- 新增时间周期支持
- 新增地区和标签过滤
- 优化排序算法

#### 推荐电台API
- 新增用户位置考虑
- 新增语言偏好支持
- 个性化推荐算法

## 🎯 API设计原则

### 1. 向后兼容
- 保持原有API结构
- 新增字段不影响现有客户端
- 渐进式升级支持

### 2. 性能优化
- 减少不必要的关联查询
- 支持字段选择（未来扩展）
- 智能缓存策略

### 3. 数据完整性
- 提供完整的radio-browser.info数据
- 保持数据同步和一致性
- 支持增量更新

### 4. 用户体验
- 丰富的过滤和排序选项
- 智能搜索和推荐
- 快速响应时间

## 📊 查询参数标准化

### 1. 分页参数
- `page`: 页码（从1开始）
- `limit`: 每页数量（默认20，最大100）

### 2. 排序参数
- `sort`: 排序字段
- `order`: 排序方向（asc/desc）

### 3. 过滤参数
- 单值过滤：直接传递值
- 多值过滤：逗号分隔
- 范围过滤：min/max后缀

### 4. 搜索参数
- `search`/`q`: 搜索关键词
- 支持多字段搜索
- 智能匹配算法

## 🔍 响应格式规范

### 1. 统一响应结构
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-01-08T10:30:00Z"
}
```

### 2. 分页响应
```json
{
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total": 1500,
    "total_pages": 75
  }
}
```

### 3. 错误响应
```json
{
  "code": 400,
  "message": "Invalid parameters",
  "error": "详细错误信息",
  "timestamp": "2025-01-08T10:30:00Z"
}
```

## 🚀 性能优化策略

### 1. 缓存策略
- 热门数据Redis缓存
- CDN静态资源缓存
- 查询结果缓存

### 2. 数据库优化
- 索引优化
- 查询优化
- 连接池管理

### 3. API限流
- 用户级别限流
- IP级别限流
- 接口级别限流

## 📝 开发注意事项

### 1. 数据验证
- 严格的参数验证
- 数据类型检查
- 安全性验证

### 2. 错误处理
- 统一错误码
- 详细错误信息
- 日志记录

### 3. 监控告警
- API响应时间监控
- 错误率监控
- 资源使用监控

## 🔮 未来扩展

### 1. 功能扩展
- GraphQL支持
- 实时推送
- 批量操作API

### 2. 性能扩展
- 分布式缓存
- 读写分离
- 微服务架构

### 3. 数据扩展
- 更多元数据支持
- 用户行为分析
- 智能推荐算法
