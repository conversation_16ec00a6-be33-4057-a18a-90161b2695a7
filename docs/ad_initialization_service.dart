import 'package:ad_sdk/ad_sdk.dart';
import 'package:flutter/material.dart';
import 'package:pdf_toolkit/services/app_usage_tracker.dart';
import 'package:pdf_toolkit/services/attribution_manager.dart';
import 'package:pdf_toolkit/services/report_service.dart';

/// 广告初始化服务类
class AdInitializationService {
 
  /// 执行广告SDK初始化
  static Future<void> initializeAdSDK() async {

    // 设置事件上报回调
    AdSDK.setEventReporter((eventName, parameters) {
      debugPrint('📊 广告事件上报: $eventName');
      debugPrint('   参数: $parameters');
      ReportService.instance.logEvent(name: eventName, parameters: parameters.cast<String, Object>());
    });

    // 设置价格事件上报回调
    AdSDK.setEventPriceReporter((scene, posid, platform, price, currency, paytype) {
      debugPrint('💰 价格事件上报:');
      debugPrint('   scene: $scene, posid: $posid');
      debugPrint('   platform: $platform, price: $price, currency: $currency, paytype: $paytype');
      ReportService.instance.logEvent(name: "ad_scene_price",parameters: {
        "scene": scene,
        "ad_posid": posid,
        "ad_platform": platform,
        "ad_price": price,
        "ad_currency": currency,
        "ad_paytype": paytype,
        "ad_mediation":"admob",
        "lifetime": AppUsageTracker.shared.getRealtimeUsageDays(),
      });
      AttributionManager.getSharedInstance().logEvent("ad_impression_revenue", {
        "currencyCode": currency,
        "af_revenue": price,
      });
    });
  
    // 初始化广告SDK
    await AdSDK.initialize(const AdConfig(
      ads: {
        // 插屏广告
        AdType.interstitial: AdIds(
          main: 'ca-app-pub-3940256099942544/4411468910', 
          backup:'ca-app-pub-3940256099942544/4411468910', 
        ),
        // 开屏广告
        AdType.appOpen: AdIds(
          main: 'ca-app-pub-3940256099942544/5575463023', 
          backup: 'ca-app-pub-3940256099942544/5575463023', 
        ),

        // 原生广告
        AdType.native: AdIds(
          main: 'ca-app-pub-3940256099942544/3986624511',
          backup:'ca-app-pub-3940256099942544/3986624511',
        ),
      },
    ));
  }

}
