# UI/UX 设计规范

本文档定义了 World Tune 项目的 UI/UX 设计规范，确保用户界面的一致性、可用性和美观性。

## 📋 目录

- [设计系统](#设计系统)
- [组件设计规范](#组件设计规范)
- [响应式设计](#响应式设计)
- [交互设计](#交互设计)
- [无障碍设计](#无障碍设计)

## 🎨 设计系统

### 色彩系统

#### 主色调
```dart
// 主色调 - 蓝色系
static const Color primary50 = Color(0xFFE3F2FD);
static const Color primary100 = Color(0xFFBBDEFB);
static const Color primary200 = Color(0xFF90CAF9);
static const Color primary300 = Color(0xFF64B5F6);
static const Color primary400 = Color(0xFF42A5F5);
static const Color primary500 = Color(0xFF2196F3); // 主色
static const Color primary600 = Color(0xFF1E88E5);
static const Color primary700 = Color(0xFF1976D2);
static const Color primary800 = Color(0xFF1565C0);
static const Color primary900 = Color(0xFF0D47A1);
```

#### 辅助色调
```dart
// 成功色 - 绿色
static const Color success = Color(0xFF4CAF50);
static const Color successLight = Color(0xFF81C784);
static const Color successDark = Color(0xFF388E3C);

// 警告色 - 橙色
static const Color warning = Color(0xFFFF9800);
static const Color warningLight = Color(0xFFFFB74D);
static const Color warningDark = Color(0xFFF57C00);

// 错误色 - 红色
static const Color error = Color(0xFFF44336);
static const Color errorLight = Color(0xFFE57373);
static const Color errorDark = Color(0xFFD32F2F);

// 信息色 - 青色
static const Color info = Color(0xFF00BCD4);
static const Color infoLight = Color(0xFF4DD0E1);
static const Color infoDark = Color(0xFF0097A7);
```

#### 中性色调
```dart
// 灰色系
static const Color neutral50 = Color(0xFFFAFAFA);
static const Color neutral100 = Color(0xFFF5F5F5);
static const Color neutral200 = Color(0xFFEEEEEE);
static const Color neutral300 = Color(0xFFE0E0E0);
static const Color neutral400 = Color(0xFFBDBDBD);
static const Color neutral500 = Color(0xFF9E9E9E);
static const Color neutral600 = Color(0xFF757575);
static const Color neutral700 = Color(0xFF616161);
static const Color neutral800 = Color(0xFF424242);
static const Color neutral900 = Color(0xFF212121);
```

### 字体系统

#### 字体族
```dart
// 主字体 - 系统默认
static const String primaryFontFamily = 'SF Pro Display'; // iOS
static const String primaryFontFamilyAndroid = 'Roboto'; // Android

// 辅助字体 - 数字显示
static const String monospaceFontFamily = 'SF Mono'; // iOS
static const String monospaceFontFamilyAndroid = 'Roboto Mono'; // Android
```

#### 字体大小和权重
```dart
class AppTextStyles {
  // 标题样式
  static const TextStyle displayLarge = TextStyle(
    fontSize: 57,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    height: 1.12,
  );

  static const TextStyle displayMedium = TextStyle(
    fontSize: 45,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.16,
  );

  static const TextStyle displaySmall = TextStyle(
    fontSize: 36,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.22,
  );

  // 标题样式
  static const TextStyle headlineLarge = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.25,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.29,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.33,
  );

  // 正文样式
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.43,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.33,
  );

  // 标签样式
  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.43,
  );

  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.33,
  );

  static const TextStyle labelSmall = TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    height: 1.45,
  );
}
```

### 间距系统

#### 基础间距
```dart
class AppSpacing {
  // 基础间距单位 (4px)
  static const double unit = 4.0;
  
  // 预定义间距
  static const double xs = unit;      // 4px
  static const double sm = unit * 2;  // 8px
  static const double md = unit * 4;  // 16px
  static const double lg = unit * 6;  // 24px
  static const double xl = unit * 8;  // 32px
  static const double xxl = unit * 12; // 48px
  static const double xxxl = unit * 16; // 64px

  // 组件特定间距
  static const double cardPadding = md;
  static const double listItemPadding = md;
  static const double buttonPadding = sm;
  static const double iconSpacing = sm;
  static const double sectionSpacing = lg;
}
```

#### 圆角系统
```dart
class AppRadius {
  static const double xs = 2.0;
  static const double sm = 4.0;
  static const double md = 8.0;
  static const double lg = 12.0;
  static const double xl = 16.0;
  static const double xxl = 24.0;
  static const double full = 999.0; // 完全圆角

  // 组件特定圆角
  static const double button = md;
  static const double card = lg;
  static const double dialog = xl;
  static const double bottomSheet = xxl;
}
```

### 阴影系统

#### 阴影级别
```dart
class AppShadows {
  // 轻微阴影 - 悬浮卡片
  static const List<BoxShadow> level1 = [
    BoxShadow(
      color: Color(0x0A000000),
      blurRadius: 2,
      offset: Offset(0, 1),
    ),
    BoxShadow(
      color: Color(0x14000000),
      blurRadius: 3,
      offset: Offset(0, 1),
    ),
  ];

  // 中等阴影 - 按钮、卡片
  static const List<BoxShadow> level2 = [
    BoxShadow(
      color: Color(0x0A000000),
      blurRadius: 4,
      offset: Offset(0, 1),
    ),
    BoxShadow(
      color: Color(0x1F000000),
      blurRadius: 6,
      offset: Offset(0, 2),
    ),
  ];

  // 较强阴影 - 浮动按钮
  static const List<BoxShadow> level3 = [
    BoxShadow(
      color: Color(0x0A000000),
      blurRadius: 8,
      offset: Offset(0, 4),
    ),
    BoxShadow(
      color: Color(0x29000000),
      blurRadius: 12,
      offset: Offset(0, 6),
    ),
  ];

  // 强阴影 - 对话框、底部表单
  static const List<BoxShadow> level4 = [
    BoxShadow(
      color: Color(0x14000000),
      blurRadius: 16,
      offset: Offset(0, 8),
    ),
    BoxShadow(
      color: Color(0x29000000),
      blurRadius: 24,
      offset: Offset(0, 12),
    ),
  ];
}
```

## 🧩 组件设计规范

### 按钮设计

#### 主要按钮
```dart
class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.icon,
    this.size = ButtonSize.medium,
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final IconData? icon;
  final ButtonSize size;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary500,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        padding: _getPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
        minimumSize: _getMinimumSize(),
      ),
      child: isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  Icon(icon, size: _getIconSize()),
                  SizedBox(width: AppSpacing.sm),
                ],
                Text(text, style: _getTextStyle()),
              ],
            ),
    );
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case ButtonSize.medium:
        return EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case ButtonSize.large:
        return EdgeInsets.symmetric(horizontal: 24, vertical: 16);
    }
  }

  Size _getMinimumSize() {
    switch (size) {
      case ButtonSize.small:
        return Size(64, 32);
      case ButtonSize.medium:
        return Size(80, 40);
      case ButtonSize.large:
        return Size(96, 48);
    }
  }

  double _getIconSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 18;
      case ButtonSize.large:
        return 20;
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case ButtonSize.small:
        return AppTextStyles.labelSmall;
      case ButtonSize.medium:
        return AppTextStyles.labelMedium;
      case ButtonSize.large:
        return AppTextStyles.labelLarge;
    }
  }
}

enum ButtonSize { small, medium, large }
```

### 卡片设计

#### 标准卡片
```dart
class AppCard extends StatelessWidget {
  const AppCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation = CardElevation.level1,
    this.borderRadius,
    this.backgroundColor,
    this.onTap,
  });

  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final CardElevation elevation;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.cardColor,
        borderRadius: borderRadius ?? BorderRadius.circular(AppRadius.card),
        boxShadow: _getShadow(),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(AppRadius.card),
          child: Padding(
            padding: padding ?? EdgeInsets.all(AppSpacing.md),
            child: child,
          ),
        ),
      ),
    );
  }

  List<BoxShadow> _getShadow() {
    switch (elevation) {
      case CardElevation.none:
        return [];
      case CardElevation.level1:
        return AppShadows.level1;
      case CardElevation.level2:
        return AppShadows.level2;
      case CardElevation.level3:
        return AppShadows.level3;
    }
  }
}

enum CardElevation { none, level1, level2, level3 }
```

### 输入框设计

#### 标准输入框
```dart
class AppTextField extends StatelessWidget {
  const AppTextField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.onChanged,
    this.obscureText = false,
    this.keyboardType,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLines = 1,
    this.enabled = true,
  });

  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool obscureText;
  final TextInputType? keyboardType;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final int maxLines;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.labelMedium.copyWith(
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: AppSpacing.xs),
        TextFormField(
          controller: controller,
          validator: validator,
          onChanged: onChanged,
          obscureText: obscureText,
          keyboardType: keyboardType,
          maxLines: maxLines,
          enabled: enabled,
          style: AppTextStyles.bodyMedium,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: AppTextStyles.bodyMedium.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: theme.colorScheme.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.md),
              borderSide: BorderSide(
                color: theme.colorScheme.outline,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.md),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withOpacity(0.5),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.md),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppRadius.md),
              borderSide: BorderSide(
                color: theme.colorScheme.error,
                width: 1,
              ),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.sm,
            ),
          ),
        ),
      ],
    );
  }
}
```

## 📱 响应式设计

### 断点系统
```dart
class AppBreakpoints {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;
  static const double largeDesktop = 1600;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }

  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= largeDesktop;
  }
}
```

### 响应式布局
```dart
class ResponsiveLayout extends StatelessWidget {
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= AppBreakpoints.desktop) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= AppBreakpoints.mobile) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}
```

### 响应式网格
```dart
class ResponsiveGrid extends StatelessWidget {
  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 16.0,
  });

  final List<Widget> children;
  final double spacing;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = _getCrossAxisCount(constraints.maxWidth);
        
        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: _getChildAspectRatio(constraints.maxWidth),
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }

  int _getCrossAxisCount(double width) {
    if (width >= AppBreakpoints.largeDesktop) return 6;
    if (width >= AppBreakpoints.desktop) return 4;
    if (width >= AppBreakpoints.tablet) return 3;
    return 2;
  }

  double _getChildAspectRatio(double width) {
    if (width >= AppBreakpoints.desktop) return 1.2;
    if (width >= AppBreakpoints.tablet) return 1.1;
    return 1.0;
  }
}
```

---

**注意**: 这些设计规范应该与设计团队协作制定，确保视觉一致性和用户体验的连贯性。定期审查和更新以适应设计趋势和用户反馈。
