# World Tune 开发规范

本文档定义了 World Tune 项目的开发规范，旨在确保代码质量、团队协作效率和项目的长期可维护性。

## 📋 目录

- [通用开发规范](#通用开发规范)
- [代码规范](#代码规范)
- [Git 工作流规范](#git-工作流规范)
- [文档规范](#文档规范)
- [安全规范](#安全规范)

## 🔧 通用开发规范

### 开发环境要求

#### 必需工具版本
```yaml
Flutter: >=3.16.0 <4.0.0
Dart: >=3.2.0 <4.0.0
Android Studio: >=2023.1.1
Xcode: >=15.0 (macOS only)
VS Code: >=1.85.0 (可选)
```

#### 推荐插件
- **Flutter**: 官方 Flutter 插件
- **Dart**: 官方 Dart 插件
- **Flutter Intl**: 国际化支持
- **Riverpod Snippets**: 状态管理代码片段
- **Error Lens**: 实时错误显示

### 项目配置

#### pubspec.yaml 规范
```yaml
# ✅ 正确的依赖版本管理
dependencies:
  flutter:
    sdk: flutter
  # 使用精确的版本范围
  riverpod: ^2.4.9
  go_router: ^12.1.3
  
# ❌ 避免使用
dependencies:
  some_package: any  # 不要使用 any
  other_package: ^1.0.0  # 避免过于宽泛的版本范围
```

#### 环境配置
```dart
// ✅ 使用环境变量管理配置
class AppConfig {
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://api.worldtune.com',
  );
  
  static const bool enableDebugMode = bool.fromEnvironment(
    'DEBUG_MODE',
    defaultValue: false,
  );
}
```

## 📝 代码规范

### 命名规范

#### 文件和目录命名
```
✅ 正确命名
lib/src/features/home/<USER>
lib/src/shared/widgets/station_card.dart
lib/src/shared/models/radio_station.dart

❌ 错误命名
lib/HomePage.dart
lib/stationCard.dart
lib/RadioStation.dart
```

#### 类和变量命名
```dart
// ✅ 类名使用 PascalCase
class RadioStationService {}
class AudioPlayerController {}

// ✅ 变量和方法使用 camelCase
String stationName = '';
void playStation() {}

// ✅ 常量使用 lowerCamelCase
const String defaultLanguage = 'en';
const Duration animationDuration = Duration(milliseconds: 300);

// ✅ 私有成员使用下划线前缀
class _PrivateWidget extends StatelessWidget {}
String _privateField = '';
```

### 代码结构规范

#### 导入顺序
```dart
// 1. Dart 核心库
import 'dart:async';
import 'dart:convert';

// 2. Flutter 框架
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. 第三方包
import 'package:riverpod/riverpod.dart';
import 'package:go_router/go_router.dart';

// 4. 项目内部导入
import '../models/radio_station.dart';
import '../services/audio_service.dart';
```

#### 类结构顺序
```dart
class ExampleWidget extends ConsumerStatefulWidget {
  // 1. 构造函数
  const ExampleWidget({
    super.key,
    required this.title,
  });

  // 2. 公共字段
  final String title;

  // 3. 静态方法
  static ExampleWidget create() => const ExampleWidget(title: 'Default');

  // 4. 重写方法
  @override
  ConsumerState<ExampleWidget> createState() => _ExampleWidgetState();
}

class _ExampleWidgetState extends ConsumerState<ExampleWidget> {
  // 1. 私有字段
  late final TextEditingController _controller;
  bool _isLoading = false;

  // 2. 生命周期方法
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // 3. 构建方法
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  // 4. 私有辅助方法
  Widget _buildBody() {
    return Container();
  }

  void _handleTap() {
    // 处理点击
  }
}
```

### 错误处理规范

#### 异常处理
```dart
// ✅ 正确的异常处理
Future<List<RadioStation>> fetchStations() async {
  try {
    final response = await _apiClient.get('/stations');
    return response.data.map((json) => RadioStation.fromJson(json)).toList();
  } on DioException catch (e) {
    // 具体的网络异常处理
    throw NetworkException('Failed to fetch stations: ${e.message}');
  } catch (e) {
    // 通用异常处理
    throw UnknownException('Unexpected error: $e');
  }
}

// ❌ 避免的做法
Future<List<RadioStation>> fetchStations() async {
  try {
    final response = await _apiClient.get('/stations');
    return response.data.map((json) => RadioStation.fromJson(json)).toList();
  } catch (e) {
    print('Error: $e'); // 不要使用 print
    return []; // 不要静默失败
  }
}
```

#### 日志记录
```dart
// ✅ 使用结构化日志
import 'package:logger/logger.dart';

final logger = Logger();

void playStation(RadioStation station) {
  logger.i('Playing station', {
    'stationId': station.id,
    'stationName': station.name,
    'timestamp': DateTime.now().toIso8601String(),
  });
}

// ❌ 避免使用 print
void playStation(RadioStation station) {
  print('Playing ${station.name}'); // 不推荐
}
```

## 🔄 Git 工作流规范

### 分支命名规范
```
main                    # 主分支
develop                 # 开发分支
feature/audio-player    # 功能分支
bugfix/player-crash     # 修复分支
hotfix/critical-bug     # 热修复分支
release/v1.2.0          # 发布分支
```

### 提交信息规范
```
feat: 添加音频播放器组件
fix: 修复播放器崩溃问题
docs: 更新 API 文档
style: 格式化代码
refactor: 重构音频服务
test: 添加播放器单元测试
chore: 更新依赖版本
```

### 提交信息模板
```
<type>(<scope>): <subject>

<body>

<footer>
```

示例：
```
feat(player): 添加音频可视化效果

- 实现实时频谱分析
- 添加多种可视化样式
- 优化性能和内存使用

Closes #123
```

## 📚 文档规范

### 代码注释规范
```dart
/// 音频播放服务
/// 
/// 提供音频播放、暂停、停止等核心功能。
/// 支持网络流媒体和本地文件播放。
/// 
/// 使用示例：
/// ```dart
/// final audioService = AudioService();
/// await audioService.playStation(station);
/// ```
class AudioService {
  /// 播放指定电台
  /// 
  /// [station] 要播放的电台信息
  /// 
  /// 抛出 [NetworkException] 当网络连接失败时
  /// 抛出 [AudioException] 当音频格式不支持时
  Future<void> playStation(RadioStation station) async {
    // 实现代码
  }
}
```

### README 文档规范
每个模块都应该包含 README.md 文件：
```markdown
# 模块名称

简短描述模块的功能和用途。

## 功能特性

- 功能1
- 功能2

## 使用方法

```dart
// 代码示例
```

## API 文档

### 类名
描述类的功能

#### 方法名
- 参数说明
- 返回值说明
- 异常说明
```

## 🔒 安全规范

### API 密钥管理
```dart
// ✅ 正确的密钥管理
class ApiConfig {
  static const String _apiKey = String.fromEnvironment('API_KEY');
  
  static String get apiKey {
    if (_apiKey.isEmpty) {
      throw ConfigurationException('API_KEY not configured');
    }
    return _apiKey;
  }
}

// ❌ 避免硬编码
class ApiConfig {
  static const String apiKey = 'sk-1234567890abcdef'; // 不要这样做
}
```

### 数据验证
```dart
// ✅ 输入验证
class StationValidator {
  static bool isValidUrl(String url) {
    final uri = Uri.tryParse(url);
    return uri != null && (uri.scheme == 'http' || uri.scheme == 'https');
  }
  
  static bool isValidStationName(String name) {
    return name.trim().isNotEmpty && name.length <= 100;
  }
}
```

### 权限管理
```dart
// ✅ 权限检查
Future<bool> requestAudioPermission() async {
  final permission = Permission.microphone;
  final status = await permission.status;
  
  if (status.isDenied) {
    final result = await permission.request();
    return result.isGranted;
  }
  
  return status.isGranted;
}
```

## ⚡ 性能规范

### 内存管理
```dart
// ✅ 正确的资源管理
class AudioPlayerWidget extends StatefulWidget {
  @override
  _AudioPlayerWidgetState createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget> {
  late AudioPlayer _player;
  StreamSubscription? _subscription;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    _subscription = _player.onStateChanged.listen(_handleStateChange);
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _player.dispose();
    super.dispose();
  }
}
```

### 异步操作规范
```dart
// ✅ 正确的异步处理
Future<void> loadStations() async {
  if (!mounted) return;
  
  setState(() => _isLoading = true);
  
  try {
    final stations = await _stationService.fetchStations();
    if (mounted) {
      setState(() {
        _stations = stations;
        _isLoading = false;
      });
    }
  } catch (e) {
    if (mounted) {
      setState(() => _isLoading = false);
      _showErrorDialog(e.toString());
    }
  }
}
```

## 🧪 测试规范

### 单元测试
```dart
// ✅ 测试文件命名：原文件名_test.dart
// test/services/audio_service_test.dart

void main() {
  group('AudioService', () {
    late AudioService audioService;
    
    setUp(() {
      audioService = AudioService();
    });
    
    tearDown(() {
      audioService.dispose();
    });
    
    test('should play station successfully', () async {
      // Arrange
      final station = RadioStation.test();
      
      // Act
      await audioService.playStation(station);
      
      // Assert
      expect(audioService.isPlaying, isTrue);
      expect(audioService.currentStation, equals(station));
    });
  });
}
```

### Widget 测试
```dart
void main() {
  testWidgets('StationCard should display station info', (tester) async {
    // Arrange
    const station = RadioStation.test();
    
    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: StationCard(station: station),
      ),
    );
    
    // Assert
    expect(find.text(station.name), findsOneWidget);
    expect(find.text(station.country), findsOneWidget);
  });
}
```

## 📱 平台特定规范

### iOS 特定
```dart
// iOS 特定配置
if (Platform.isIOS) {
  await AudioSession.instance.then((session) {
    return session.configure(AudioSessionConfiguration.music());
  });
}
```

### Android 特定
```dart
// Android 特定配置
if (Platform.isAndroid) {
  await AndroidAudioManager.requestAudioFocus(
    AndroidAudioFocusRequest(
      focusGain: AndroidAudioFocus.gain,
      audioAttributes: const AndroidAudioAttributes(
        contentType: AndroidAudioContentType.music,
      ),
    ),
  );
}
```

## 🔍 代码审查清单

### 提交前检查
- [ ] 代码格式化 (`dart format .`)
- [ ] 静态分析 (`dart analyze`)
- [ ] 测试通过 (`flutter test`)
- [ ] 文档更新
- [ ] 性能影响评估
- [ ] 安全性检查
- [ ] 兼容性验证

### 审查要点
- [ ] 代码逻辑正确性
- [ ] 错误处理完整性
- [ ] 性能优化合理性
- [ ] 代码可读性和可维护性
- [ ] 测试覆盖率
- [ ] 文档完整性

---

**注意**: 本规范是活文档，会根据项目发展和团队反馈持续更新。所有团队成员都应该遵循这些规范，并积极提出改进建议。
