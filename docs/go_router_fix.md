# Go Router 6.x 兼容性修复

## 问题描述

在使用 `go_router: ^6.0.3` 版本时，出现以下编译错误：

```
lib/src/shared/widgets/main_scaffold.dart:31:55: Error: The getter 'matchedLocation' isn't defined for the class 'GoRouterState'.
```

## 问题原因

在 go_router 6.x 版本中，`GoRouterState` 的 API 发生了变化：
- `GoRouterState.matchedLocation` 属性已被移除
- 需要使用 `GoRouterState.location` 替代

## 修复方案

### 1. 修复 main_scaffold.dart

**修复前：**
```dart
void _updateCurrentIndex() {
  final String location = GoRouterState.of(context).matchedLocation;
  final items = BottomNavItem.items;
  // ...
}
```

**修复后：**
```dart
void _updateCurrentIndex() {
  final String location = GoRouterState.of(context).location;
  final items = BottomNavItem.items;
  // ...
}
```

### 2. 修复 router_notifier.dart

**修复前：**
```dart
final isSplash = state.matchedLocation == SplashRoute.path;
// ...
final isLoggingIn = state.matchedLocation == LoginRoute.path;
```

**修复后：**
```dart
final isSplash = state.location == SplashRoute.path;
// ...
final isLoggingIn = state.location == LoginRoute.path;
```

## API 变更说明

### go_router 6.x 中的主要变更

1. **matchedLocation → location**
   - `state.matchedLocation` → `state.location`
   - 用于获取当前匹配的路由路径

2. **其他可用属性**
   - `state.uri`: 完整的URI对象
   - `state.uri.toString()`: URI的字符串表示
   - `state.fullPath`: 完整路径（包括查询参数）

### 选择 location 的原因

- `location` 返回当前匹配的路由路径，不包括查询参数
- 是 go_router 6.x 版本中推荐的标准API
- 适合用于底部导航栏的路由匹配逻辑

## 验证方法

1. 运行 `flutter analyze` 确保没有编译错误
2. 启动应用验证底部导航栏功能正常
3. 测试路由跳转和状态更新

## 相关文件

- `lib/src/shared/widgets/main_scaffold.dart`
- `lib/shared/route/router_notifier.dart`

## 参考资料

- [go_router 官方文档](https://pub.dev/packages/go_router)
- [go_router 迁移指南](https://flutter.dev/docs/development/ui/navigation/url-strategies)
