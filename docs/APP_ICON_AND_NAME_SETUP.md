# WorldTune 应用图标和名称配置指南

## 概述

本文档记录了 WorldTune 应用的图标和名称配置过程，确保应用在 Android 和 iOS 平台上都能正确显示自定义图标和应用名称。

## 配置内容

### 1. 应用名称配置

#### Android 配置
在 `android/app/build.gradle` 中配置了不同构建版本的应用名称：

```gradle
productFlavors { 
    production {
        dimension "default"
        applicationIdSuffix ""
        manifestPlaceholders = [appName: "WorldTune"]
    }
    staging {
        dimension "default"
        applicationIdSuffix ".stg"
        manifestPlaceholders = [appName: "[STG] World Tune"]
    }        
    development {
        dimension "default"
        applicationIdSuffix ".dev"
        manifestPlaceholders = [appName: "[DEV] World Tune"]
    }
}
```

#### iOS 配置
在 `ios/Runner/Info.plist` 中配置：

```xml
<key>CFBundleName</key>
<string>World Tune</string>
<key>CFBundleDisplayName</key>
<string>$(FLAVOR_APP_NAME)</string>
```

### 2. 应用图标配置

#### 使用 flutter_launcher_icons 包

1. **添加依赖**
   在 `pubspec.yaml` 的 `dev_dependencies` 中添加：
   ```yaml
   flutter_launcher_icons: ^0.13.1
   ```

2. **配置图标生成**
   在 `pubspec.yaml` 末尾添加配置：
   ```yaml
   flutter_launcher_icons:
     android: "launcher_icon"
     ios: true
     image_path: "assets/app_icon.png"
     min_sdk_android: 21
     remove_alpha_ios: true
     web:
       generate: true
       image_path: "assets/app_icon.png"
       background_color: "#hexcode"
       theme_color: "#hexcode"
     windows:
       generate: true
       image_path: "assets/app_icon.png"
       icon_size: 48
     macos:
       generate: true
       image_path: "assets/app_icon.png"
   ```

3. **生成图标**
   ```bash
   flutter pub get
   flutter pub run flutter_launcher_icons
   ```

## 生成的文件

### Android 图标文件
- `android/app/src/main/res/mipmap-hdpi/launcher_icon.png`
- `android/app/src/main/res/mipmap-mdpi/launcher_icon.png`
- `android/app/src/main/res/mipmap-xhdpi/launcher_icon.png`
- `android/app/src/main/res/mipmap-xxhdpi/launcher_icon.png`
- `android/app/src/main/res/mipmap-xxxhdpi/launcher_icon.png`

### iOS 图标文件
在 `ios/Runner/Assets.xcassets/AppIcon.appiconset/` 目录下生成了多个尺寸的图标文件。

## 构建和测试

### 构建命令

1. **Android Production 版本**
   ```bash
   flutter build apk --flavor production --release
   ```

2. **Android Development 版本**
   ```bash
   flutter build apk --flavor development --debug
   ```

3. **安装到设备**
   ```bash
   flutter install --flavor production
   ```

### 验证脚本

创建了 `scripts/verify_app_config.sh` 脚本来验证配置：

```bash
chmod +x scripts/verify_app_config.sh
./scripts/verify_app_config.sh
```

## 注意事项

1. **iOS 透明度警告**：iOS App Store 不允许带有透明通道的图标，已通过 `remove_alpha_ios: true` 配置解决。

2. **构建版本选择**：
   - Production 版本显示 "World Tune"
   - Staging 版本显示 "[STG] World Tune"
   - Development 版本显示 "[DEV] World Tune"

3. **图标要求**：
   - 源图标文件：`assets/app_icon.png`
   - 建议尺寸：1024x1024 像素
   - 格式：PNG

4. **清理缓存**：修改图标后建议执行 `flutter clean` 清理缓存。

## 故障排除

1. **图标未更新**：
   - 执行 `flutter clean`
   - 重新生成图标：`flutter pub run flutter_launcher_icons`
   - 重新构建应用

2. **应用名称错误**：
   - 检查构建版本（production/staging/development）
   - 确认 `android/app/build.gradle` 中的配置

3. **iOS 构建问题**：
   - 需要在 Xcode 中配置 Release 构建配置
   - 参考 Flutter 官方文档进行 iOS 项目配置

## 相关文件

- `pubspec.yaml` - 依赖和图标配置
- `android/app/build.gradle` - Android 应用名称配置
- `ios/Runner/Info.plist` - iOS 应用名称配置
- `assets/app_icon.png` - 源图标文件
- `scripts/verify_app_config.sh` - 配置验证脚本
