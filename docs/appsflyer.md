# AppsFlyer 模块使用文档

## 概述

AppsFlyer 模块是 Flutter Tools 基础组件库的一部分，提供了简单易用的 AppsFlyer SDK 集成方案。通过统一的配置和 API，您可以快速在项目中集成 AppsFlyer 功能。

## 功能特性

- 🚀 **简单配置**: 通过配置类统一管理 AppsFlyer 参数
- 📊 **事件上报**: 提供常用事件上报方法和自定义事件支持
- 🔧 **易于集成**: 一行代码完成初始化
- 📱 **跨平台**: 支持 iOS 和 Android
- 🐛 **调试友好**: 内置日志系统，方便调试

## 快速开始

### 1. 添加依赖

在您的项目 `pubspec.yaml` 中添加：
 
### 2. 初始化配置

在您的 `main.dart` 中：

```dart
import 'package:flutter/material.dart';
import 'package:flutter_tools/flutter_tools.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化 FlutterTools
  await FlutterTools.initialize(
    FlutterToolsConfig(
      // AppsFlyer 配置
      appsFlyerConfig: AppsFlyerConfig(
        afDevKey: "your_appsflyer_dev_key_here", // 替换为你的 AppsFlyer Dev Key
        iosAppId: "your_ios_app_id_here", // 替换为你的 iOS App ID
        showDebug: true, // 开发环境显示调试信息
      ),
      // 日志配置
      logConfig: LogConfig(
        showLog: true, // 开发环境显示日志
      ),
    ),
  );
  
  runApp(MyApp());
}
```

### 3. 使用事件上报

```dart
import 'package:flutter_tools/flutter_tools.dart';

// 上报订阅事件
await AppFlyerReport.reportSubscribe(
  orderId: "order_123",
  revenue: 9.99,
  productId: "premium_subscription",
);

// 上报购买事件
await AppFlyerReport.reportPurchase(
  orderId: "purchase_456",
  currency: "USD",
  revenue: 19.99,
  productId: "premium_product",
);

// 上报注册事件
await AppFlyerReport.reportRegistration(method: "email");

// 上报自定义事件
await AppFlyerReport.reportCustomEvent("custom_event", {
  "param1": "value1",
  "param2": "value2",
});
```

## 配置参数

### AppsFlyerConfig

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| afDevKey | String | ✅ | - | AppsFlyer Dev Key |
| iosAppId | String | ✅ | - | iOS App ID |
| showDebug | bool | ❌ | false | 是否显示调试信息 |
| timeToWaitForATTUserAuthorization | int | ❌ | 50 | 等待 ATT 用户授权时间（毫秒） |
| disableAdvertisingIdentifier | bool | ❌ | false | 是否禁用广告标识符 |
| disableCollectASA | bool | ❌ | false | 是否禁用 ASA 收集 |

### LogConfig

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| showLog | bool | ❌ | false | 是否显示日志 |
| tag | String | ❌ | "FlutterTools" | 日志标签 |

## API 参考

### AppFlyerReport 类

#### reportSubscribe()
上报订阅事件

```dart
static Future<void> reportSubscribe({
  required String orderId,     // 订单ID
  String currency = "CNY",     // 币种
  required double revenue,     // 价格
  String? productId,          // 产品ID（可选）
})
```

#### reportPurchase()
上报购买事件

```dart
static Future<void> reportPurchase({
  required String orderId,     // 订单ID
  required String currency,    // 币种
  required double revenue,     // 价格
  required String productId,   // 产品ID
  int quantity = 1,           // 数量
})
```

#### reportRegistration()
上报注册事件

```dart
static Future<void> reportRegistration({
  String? method,             // 注册方式（可选）
})
```

#### reportLogin()
上报登录事件

```dart
static Future<void> reportLogin({
  String? method,             // 登录方式（可选）
})
```

#### reportCustomEvent()
上报自定义事件

```dart
static Future<void> reportCustomEvent(
  String eventName,                    // 事件名称
  Map<String, dynamic> eventValues    // 事件参数
)
```

### AppsFlyerManager 类

#### getAppsFlyerId()
获取 AppsFlyer ID

```dart
static Future<String?> getAppsFlyerId()
```

#### isInitialized
检查是否已初始化

```dart
static bool get isInitialized
```

## 注意事项

1. **配置验证**: 确保 `afDevKey` 和 `iosAppId` 不为空
2. **初始化顺序**: 必须在使用任何 AppsFlyer 功能前调用 `FlutterTools.initialize()`
3. **调试模式**: 生产环境建议关闭 `showDebug` 和 `showLog`
4. **错误处理**: 所有 API 调用都应该包装在 try-catch 中

## 常见问题

### Q: 如何获取 AppsFlyer Dev Key？
A: 登录 AppsFlyer 控制台，在应用设置中可以找到 Dev Key。

### Q: iOS App ID 是什么？
A: iOS App ID 是您在 App Store Connect 中的应用 ID，格式通常为数字。

### Q: 为什么事件上报没有生效？
A: 请检查：
1. 是否正确初始化了 FlutterTools
2. 配置参数是否正确
3. 网络连接是否正常
4. 查看日志输出是否有错误信息

### Q: 如何在生产环境中使用？
A: 生产环境配置示例：

```dart
appsFlyerConfig: AppsFlyerConfig(
  afDevKey: "your_production_dev_key",
  iosAppId: "your_production_ios_app_id",
  showDebug: false, // 关闭调试信息
),
logConfig: LogConfig(
  showLog: false, // 关闭日志
),
```

## 更新日志

### v0.0.1
- 初始版本
- 支持基础的 AppsFlyer 集成
- 提供常用事件上报方法
- 统一配置管理
