# 📱 iOS App Store 发布完整指南

> 适合iOS发布新手的详细操作流程

## 🎯 发布前准备检查清单

### ✅ 当前项目状态
- [x] Flutter项目结构完整
- [x] iOS配置基本完成
- [x] 应用图标已生成
- [x] 启动屏幕已配置
- [x] 音频播放权限已设置
- [x] 国际化支持已配置

### ⚠️ 需要立即修改的问题
- [ ] **Bundle Identifier**: 当前为示例ID，必须修改
- [ ] **Apple Developer账户**: 需要注册
- [ ] **代码签名**: 需要配置证书
- [ ] **隐私政策**: 需要创建网页

## 📋 第一阶段：Apple Developer账户准备

### 1.1 注册Apple Developer账户
**时间**: 1-2天  
**费用**: $99/年

1. 访问 https://developer.apple.com/programs/
2. 点击 "Enroll" 开始注册
3. 选择账户类型：
   - **个人账户**: 适合个人开发者
   - **公司账户**: 需要公司注册文件
4. 填写个人/公司信息
5. 支付$99年费
6. 等待Apple审核（通常1-2个工作日）

### 1.2 配置开发环境
```bash
# 检查Xcode版本（需要最新版本）
xcode-select --version

# 检查Flutter环境
flutter doctor

# 确保iOS工具链正常
flutter doctor --verbose
```

## 📋 第二阶段：项目配置修改

### 2.1 修改Bundle Identifier
**重要**: 这是发布的关键步骤！

1. 打开Xcode项目：
```bash
cd /Users/<USER>/work_space/world_tune
open ios/Runner.xcworkspace
```

2. 在Xcode中修改Bundle ID：
   - 选择 `Runner` 项目
   - 选择 `Runner` target
   - 在 `General` 标签页找到 `Bundle Identifier`
   - 修改为: `com.worldtune.radio`（或你的域名）

3. 同时修改所有环境的Bundle ID：
   - Production: `com.worldtune.radio`
   - Development: `com.worldtune.radio.dev`
   - Staging: `com.worldtune.radio.stg`

### 2.2 配置App ID
在Apple Developer Console中：

1. 登录 https://developer.apple.com/account/
2. 进入 "Certificates, Identifiers & Profiles"
3. 点击 "Identifiers" → "+"
4. 选择 "App IDs" → "App"
5. 填写信息：
   - **Description**: World Tune Radio
   - **Bundle ID**: `com.worldtune.radio`
6. 配置Capabilities：
   - ✅ Background Modes
   - ✅ Network Extensions
   - ✅ Push Notifications（如果需要）

### 2.3 生成证书
#### 开发证书（Development Certificate）
1. 在Keychain Access中生成CSR：
   - 打开 "钥匙串访问"
   - 菜单：证书助理 → 从证书颁发机构请求证书
   - 填写邮箱地址，选择"存储到磁盘"
   
2. 在Developer Console中：
   - 进入 "Certificates" → "+"
   - 选择 "iOS App Development"
   - 上传CSR文件
   - 下载证书并双击安装

#### 发布证书（Distribution Certificate）
1. 重复上述步骤，但选择 "iOS Distribution"
2. 这个证书用于App Store发布

### 2.4 创建Provisioning Profile
#### 开发Profile
1. 在Developer Console中：
   - 进入 "Profiles" → "+"
   - 选择 "iOS App Development"
   - 选择刚创建的App ID
   - 选择开发证书
   - 选择测试设备
   - 命名并生成

#### 发布Profile
1. 选择 "App Store" 类型
2. 选择App ID和发布证书
3. 下载并双击安装

## 📋 第三阶段：应用商店准备

### 3.1 创建App Store Connect应用
1. 登录 https://appstoreconnect.apple.com/
2. 点击 "我的App" → "+"
3. 填写应用信息：
   - **名称**: World Tune
   - **Bundle ID**: 选择之前创建的ID
   - **SKU**: 唯一标识符（如：worldtune-radio-2024）
   - **用户访问权限**: 完全访问权限

### 3.2 准备应用元数据
#### 必需信息：
- **应用名称**: World Tune
- **副标题**: 全球电台聚合平台
- **描述**: 详细介绍应用功能
- **关键词**: radio, music, global, streaming
- **支持URL**: 你的网站或GitHub页面
- **隐私政策URL**: 必需！

#### 应用图标要求：
- **尺寸**: 1024×1024像素
- **格式**: PNG（无透明度）
- **内容**: 不能包含文字或UI元素

#### 截图要求：
- **iPhone 6.7"**: 1290×2796像素（至少3张）
- **iPhone 6.5"**: 1242×2688像素
- **iPhone 5.5"**: 1242×2208像素
- **iPad Pro**: 2048×2732像素（如果支持iPad）

### 3.3 创建隐私政策
**这是必需的！** 创建一个网页包含：

```markdown
# World Tune 隐私政策

## 数据收集
- 我们收集设备信息用于应用正常运行
- 收集使用统计以改善用户体验
- 不收集个人身份信息

## 数据使用
- 仅用于应用功能实现
- 不与第三方分享
- 不用于广告目的

## 用户权利
- 用户可以随时删除应用数据
- 联系方式：<EMAIL>
```

## 📋 第四阶段：构建和上传

### 4.1 配置Xcode签名
1. 在Xcode中选择 `Runner` target
2. 进入 "Signing & Capabilities"
3. 取消勾选 "Automatically manage signing"
4. 手动选择：
   - **Team**: 你的开发团队
   - **Provisioning Profile**: 选择对应的Profile

### 4.2 构建Archive
```bash
# 清理项目
flutter clean
cd ios && pod install && cd ..

# 构建iOS Release版本
flutter build ios --release --no-codesign

# 在Xcode中创建Archive
# Product → Archive
```

### 4.3 上传到App Store
1. Archive成功后，Xcode会打开Organizer
2. 选择刚创建的Archive
3. 点击 "Distribute App"
4. 选择 "App Store Connect"
5. 选择 "Upload"
6. 等待上传完成

## 📋 第五阶段：App Store审核

### 5.1 完善应用信息
在App Store Connect中：
1. 上传截图和图标
2. 填写应用描述
3. 设置价格（免费）
4. 选择发布地区
5. 完成年龄分级

### 5.2 提交审核
1. 在 "App Store" 标签页
2. 点击 "提交以供审核"
3. 回答审核问题
4. 确认提交

### 5.3 审核时间
- **通常**: 1-3个工作日
- **节假日**: 可能延长到7天
- **首次提交**: 可能需要更长时间

## 🚨 常见问题解决

### 问题1: 签名失败
```
Error: Code signing failed
```
**解决方案**:
1. 检查证书是否过期
2. 确认Provisioning Profile正确
3. 清理Derived Data

### 问题2: Bundle ID冲突
```
Error: Bundle identifier already exists
```
**解决方案**:
1. 修改Bundle ID为唯一值
2. 在Developer Console中创建新的App ID

### 问题3: 审核被拒
**常见原因**:
- 缺少隐私政策
- 应用崩溃或功能不完整
- 违反App Store审核指南

**解决方案**:
1. 仔细阅读拒绝原因
2. 修复问题后重新提交
3. 在Resolution Center中回复

## 📞 获取帮助

### 官方资源
- [App Store审核指南](https://developer.apple.com/app-store/review/guidelines/)
- [Flutter iOS部署文档](https://docs.flutter.dev/deployment/ios)
- [Apple Developer支持](https://developer.apple.com/support/)

### 社区资源
- Flutter中文社区
- Stack Overflow
- GitHub Issues

## ⏰ 预计时间安排

| 阶段 | 时间 | 主要任务 |
|------|------|----------|
| 第1天 | 2-4小时 | 注册Apple Developer账户 |
| 第2天 | 3-5小时 | 配置证书和Provisioning Profile |
| 第3天 | 2-3小时 | 修改项目配置，创建App Store应用 |
| 第4天 | 1-2小时 | 准备截图和描述 |
| 第5天 | 1-2小时 | 构建和上传应用 |
| 第6-8天 | - | 等待审核 |

**总计**: 约5个工作日准备 + 1-3天审核

## 📋 第六阶段：全球电台应用特定配置

### 6.1 隐私权限配置检查
基于你的应用功能，需要在 `ios/Runner/Info.plist` 中添加以下隐私权限说明：

```xml
<!-- 网络使用说明 -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
    <key>NSExceptionDomains</key>
    <dict>
        <!-- 为生产环境建议配置具体域名 -->
        <key>radio-browser.info</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <true/>
        </dict>
    </dict>
</dict>

<!-- 后台音频播放 -->
<key>UIBackgroundModes</key>
<array>
    <string>audio</string>
    <string>background-processing</string>
</array>
```

### 6.2 App Store审核注意事项
**全球电台应用的特殊要求**：

1. **内容合规性**：
   - 确保电台内容符合各地区法律法规
   - 避免包含版权争议内容
   - 不包含政治敏感内容

2. **网络安全**：
   - 生产版本建议使用HTTPS
   - 配置具体的网络安全域名
   - 避免使用 `NSAllowsArbitraryLoads = true`

3. **用户隐私**：
   - 明确说明收集的设备信息用途
   - 说明国家检测功能的必要性
   - 提供用户数据控制选项

### 6.3 隐私政策模板
为全球电台应用创建的隐私政策应包含：

```markdown
# World Tune 隐私政策

## 数据收集
我们收集以下信息以提供服务：
- **设备信息**: 用于优化应用性能
- **国家/地区信息**: 用于提供本地化电台内容
- **使用统计**: 用于改善用户体验
- **网络状态**: 用于确保音频播放质量

## 数据使用
- 仅用于应用功能实现
- 不与第三方分享个人信息
- 不用于广告或营销目的
- 不存储个人身份信息

## 第三方服务
- 使用 radio-browser.info API 获取电台信息
- 使用系统音频服务播放内容
- 不集成广告或分析SDK

## 用户权利
- 可随时清除应用数据
- 可关闭位置检测功能
- 联系方式：<EMAIL>

最后更新：2024年1月
```

### 6.4 App Store元数据建议
**应用描述**：
```
World Tune - 全球电台聚合平台

🌍 发现世界各地的精彩电台
📻 支持全球数万个电台
🎵 高品质音频流播放
🌐 智能国家检测和推荐
📱 简洁优雅的用户界面

主要功能：
• 按国家浏览电台
• 实时音频播放
• 后台播放支持
• 收藏喜爱电台
• 多语言界面支持

无广告，完全免费使用！
```

**关键词**：
`radio, music, global, streaming, international, broadcast, audio, world, tune, station`

### 6.5 截图建议
准备以下类型的截图：
1. **主界面**: 显示国家选择和电台列表
2. **播放界面**: 显示正在播放的电台
3. **搜索功能**: 展示搜索和筛选功能
4. **设置界面**: 显示应用设置选项

### 6.6 构建前最终检查
```bash
# 1. 检查Bundle ID是否已修改
grep -r "com.example.verygoodcore" ios/

# 2. 检查隐私权限配置
grep -A 10 "NSAppTransportSecurity" ios/Runner/Info.plist

# 3. 测试音频播放功能
flutter run -d ios --release

# 4. 检查国际化配置
grep -A 5 "CFBundleLocalizations" ios/Runner/Info.plist
```

---

**重要提醒**:
1. 备份所有证书和密钥文件
2. Bundle ID一旦确定不要随意更改
3. 隐私政策是必需的，不能跳过
4. 首次发布建议先进行内部测试
5. **全球电台应用需要特别注意内容合规性**

**成功发布后记得**:
- 定期更新应用
- 回复用户评论
- 监控应用性能
- 收集用户反馈
- **关注各地区的内容政策变化**
