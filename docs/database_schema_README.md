# 数据库架构文档更新说明

## 📋 更新概述

本次更新将数据库架构从原有的规范化设计调整为更贴近radio-browser.info API数据结构的设计，以提高数据导入效率和查询性能。

## 🔄 主要变更

### 1. 电台基础信息表 (world_tune_radio_stations)

#### 新增字段
- `server_uuid`: 服务器UUID
- `change_uuid`: 变更UUID  
- `country`: 国家名称（直接存储）
- `countrycode`: 国家代码
- `iso_3166_2`: ISO 3166-2代码
- `language`: 语言名称（直接存储）
- `languagecodes`: 语言代码（逗号分隔）
- `tags`: 标签（逗号分隔，冗余存储）
- `geo_distance`: 地理距离
- `has_extended_info`: 是否有扩展信息
- `last_local_check_time`: 最后本地检查时间

#### 字段类型调整
- `name`: varchar(255) → varchar(1255) - 支持更长的电台名称
- `homepage`: varchar(500) → text - 支持更长的URL
- `favicon`: varchar(500) → text - 支持更长的URL
- `id`: bigint(20) → bigint - 移除长度限制
- 所有字段添加了明确的COLLATE设置

#### 移除字段
- `country_id`: 外键关联改为直接存储+关联表
- `language_id`: 外键关联改为直接存储

### 2. 国家表 (world_tune_countries)

#### 新增字段
- `sort_order`: 排序权重
- `is_show`: 显示状态

#### 索引调整
- 新增复合唯一索引 `uk_name_code`
- 新增排序和状态相关索引

### 3. 标签表 (world_tune_tags)

#### 移除字段
- `tag`: 标签分类字段（简化设计）

#### 新增字段
- `sort_order`: 排序权重
- `is_show`: 显示状态

### 4. 新增关联表

#### 电台国家关联表 (world_tune_station_countries)
- 支持一个电台属于多个国家的场景
- 替代原有的外键关联方式

### 5. 移除表
- `world_tune_languages`: 语言信息直接存储在电台表中

## 🎯 设计优势

### 1. 数据导入效率
- 减少关联查询，直接存储常用字段
- 支持批量导入radio-browser.info数据
- 减少数据转换和映射工作

### 2. 查询性能
- 常用字段直接查询，无需JOIN
- 保留关联表支持复杂查询
- 优化的索引结构

### 3. 数据完整性
- 保留原始API数据结构
- 支持增量更新和同步
- 冗余存储提高查询效率

### 4. 扩展性
- 支持多国家、多语言场景
- 灵活的标签系统
- 预留扩展字段

## 📊 数据迁移策略

### 1. 迁移步骤
1. 备份现有数据
2. 创建新表结构
3. 数据转换和导入
4. 验证数据完整性
5. 更新应用代码
6. 切换到新架构

### 2. 数据转换规则
- 国家关联：从外键转为关联表+直接存储
- 语言信息：从关联表转为直接存储
- 标签信息：保持关联表+增加冗余存储

### 3. 兼容性考虑
- API响应格式保持向后兼容
- 逐步迁移，支持平滑过渡
- 保留原有查询逻辑的支持

## 🔍 查询优化

### 1. 常用查询模式
- 按国家查询：直接使用countrycode字段
- 按语言查询：直接使用languagecodes字段
- 按标签查询：使用关联表或tags字段
- 全文搜索：使用name、country、language字段

### 2. 索引策略
- 单字段索引：支持基础查询
- 复合索引：支持复杂查询条件
- 全文索引：支持搜索功能（可选）

### 3. 性能监控
- 查询执行计划分析
- 慢查询日志监控
- 索引使用率统计

## 📝 注意事项

1. **数据一致性**：冗余存储需要保证数据同步
2. **存储空间**：直接存储会增加存储空间使用
3. **更新策略**：需要同时更新关联表和冗余字段
4. **查询选择**：根据场景选择直接查询或关联查询

## 🚀 后续优化

1. **缓存策略**：热门数据缓存
2. **分区表**：大数据量分区存储
3. **读写分离**：查询性能优化
4. **数据归档**：历史数据管理
