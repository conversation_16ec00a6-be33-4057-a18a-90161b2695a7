# World Tune 数据入库需求说明

## 📊 数据源说明

### 源数据来源
- **数据源**: radio-browser.info API
- **数据格式**: JSON格式
- **更新频率**: 建议每日同步一次
- **数据量**: 预计数万条电台记录

### 示例数据结构
```json
{
  "changeuuid": "7fc40bca-aac4-4a31-8dd8-209669a162ec",
  "stationuuid": "63151721-3f42-4515-89e1-51b4ff35aad5",
  "serveruuid": "18b788f4-4693-4d67-af55-c78e5c1d2d3e",
  "name": "黑龙江都市·女性广播",
  "url": "https://lhttp-hw.qtfm.cn/live/4968/64k.mp3",
  "url_resolved": "https://lhttp-hw.qtfm.cn/live/4968/64k.mp3",
  "homepage": "https://www.hljtv.com/",
  "favicon": "https://pic.qtfm.cn/2014/0330/20140330112742795.jpg",
  "tags": "lifestyle",
  "country": "China",
  "countrycode": "CN",
  "iso_3166_2": "",
  "state": "",
  "language": "chinese",
  "languagecodes": "zh",
  "votes": 29,
  "codec": "MP3",
  "bitrate": 0,
  "hls": 0,
  "lastcheckok": 1,
  "ssl_error": 0,
  "geo_lat": null,
  "geo_long": null,
  "clickcount": 29,
  "clicktrend": -2,
  "lastchangetime": "2025-06-13 00:54:54",
  "lastchecktime": "2025-07-07 03:50:14",
  "lastcheckoktime": "2025-07-07 03:50:14",
  "clicktimestamp": "2025-07-05 14:32:30"
}
```

## 🔄 数据入库流程

### 1. 数据预处理阶段

#### 1.1 数据清洗规则
- **必填字段验证**: stationuuid, name, url, country, language
- **URL有效性检查**: 验证播放URL是否可访问
- **重复数据处理**: 基于stationuuid去重
- **数据格式标准化**: 统一时间格式、编码格式等

#### 1.2 数据转换规则
```
源字段 -> 目标字段
stationuuid -> station_uuid
serveruuid -> server_uuid
changeuuid -> change_uuid
countrycode -> country_id (需要关联country表)
languagecodes -> language_id (需要关联language表)
tags -> 需要拆分并关联tag表
hls -> is_hls (0/1转换为boolean)
lastcheckok -> last_check_ok (0/1转换为boolean)
```

### 2. 基础数据入库

#### 2.1 国家数据入库 (world_tune_countries)
```sql
-- 处理逻辑：先检查是否存在，不存在则插入
INSERT IGNORE INTO world_tune_countries (name, code, iso_3166_2)
VALUES ('China', 'CN', '');

-- 获取country_id用于关联
SELECT id FROM world_tune_countries WHERE code = 'CN';
```

#### 2.2 语言数据入库 (world_tune_languages)
```sql
-- 处理逻辑：先检查是否存在，不存在则插入
INSERT IGNORE INTO world_tune_languages (name, code)
VALUES ('chinese', 'zh');

-- 获取language_id用于关联
SELECT id FROM world_tune_languages WHERE code = 'zh';
```

#### 2.3 标签数据入库 (world_tune_tags)
```sql
-- 标签处理逻辑：
-- 1. 按逗号分割tags字段："lifestyle,music,news" -> ["lifestyle", "music", "news"]
-- 2. 每个标签检查是否存在，不存在则插入
-- 3. 根据标签名称推断category分类

INSERT IGNORE INTO world_tune_tags (name, category, sort_order)
VALUES 
('lifestyle', 'lifestyle', 0),
('music', 'music', 0),
('news', 'news', 0);
```

### 3. 电台数据入库

#### 3.1 主表数据入库 (world_tune_radio_stations)
```sql
-- 使用ON DUPLICATE KEY UPDATE处理重复数据
INSERT INTO world_tune_radio_stations (
  station_uuid, server_uuid, change_uuid, name, url, url_resolved,
  homepage, favicon, country_id, state, language_id, codec, bitrate,
  is_hls, votes, click_count, click_trend, ssl_error, geo_lat, geo_long,
  last_check_ok, last_change_time, last_check_time, last_check_ok_time,
  click_timestamp, status
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
ON DUPLICATE KEY UPDATE
  name = VALUES(name),
  url = VALUES(url),
  url_resolved = VALUES(url_resolved),
  homepage = VALUES(homepage),
  favicon = VALUES(favicon),
  votes = VALUES(votes),
  click_count = VALUES(click_count),
  click_trend = VALUES(click_trend),
  last_check_ok = VALUES(last_check_ok),
  last_change_time = VALUES(last_change_time),
  last_check_time = VALUES(last_check_time),
  last_check_ok_time = VALUES(last_check_ok_time),
  click_timestamp = VALUES(click_timestamp),
  updated_at = CURRENT_TIMESTAMP;
```

#### 3.2 标签关联数据入库 (world_tune_station_tags)
```sql
-- 先删除该电台的所有标签关联，再重新插入
DELETE FROM world_tune_station_tags WHERE station_id = ?;

-- 批量插入新的标签关联
INSERT INTO world_tune_station_tags (station_id, tag_id)
VALUES (?, ?), (?, ?), (?, ?);
```

## 📋 入库业务逻辑

### 1. 数据同步策略

#### 1.1 增量同步
- **基于时间戳**: 使用lastchangetime字段判断数据是否更新
- **变更检测**: 比较change_uuid是否发生变化
- **状态检查**: 根据lastcheckok字段判断电台是否可用

#### 1.2 全量同步
- **定期执行**: 每天执行一次全量同步
- **数据校验**: 检查数据完整性和一致性
- **清理无效数据**: 删除长期不可用的电台

### 2. 数据质量控制

#### 2.1 必填字段检查
```python
required_fields = ['stationuuid', 'name', 'url', 'country', 'language']
for field in required_fields:
    if not data.get(field):
        raise ValueError(f"Missing required field: {field}")
```

#### 2.2 数据格式验证
```python
# URL格式验证
import re
url_pattern = r'^https?://.+'
if not re.match(url_pattern, data['url']):
    raise ValueError("Invalid URL format")

# UUID格式验证
uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
if not re.match(uuid_pattern, data['stationuuid']):
    raise ValueError("Invalid UUID format")
```

#### 2.3 数据去重策略
- **主键去重**: 基于station_uuid进行去重
- **相似度检测**: 检查name和url的相似度，避免重复电台
- **质量优先**: 保留votes和click_count更高的记录

### 3. 错误处理机制

#### 3.1 数据异常处理
```python
try:
    # 数据入库操作
    insert_station_data(data)
except IntegrityError as e:
    # 处理外键约束错误
    logger.error(f"Foreign key constraint error: {e}")
    # 尝试创建缺失的关联数据
    create_missing_references(data)
    retry_insert(data)
except Exception as e:
    # 记录错误日志
    logger.error(f"Data import error: {e}")
    # 将错误数据记录到错误表
    log_import_error(data, str(e))
```

#### 3.2 事务处理
```python
def import_station_batch(stations_data):
    with database.transaction():
        for station_data in stations_data:
            try:
                import_single_station(station_data)
            except Exception as e:
                # 记录单条数据错误，继续处理其他数据
                log_error(station_data, e)
                continue
```

## 🔧 技术实现建议

### 1. 批量处理
- **批次大小**: 建议每批处理1000条记录
- **并发控制**: 使用多线程/多进程提高处理效率
- **内存管理**: 及时释放已处理数据的内存

### 2. 性能优化
- **索引优化**: 在导入前临时禁用非必要索引
- **批量插入**: 使用批量INSERT语句提高效率
- **连接池**: 使用数据库连接池管理连接

### 3. 监控和日志
- **进度监控**: 实时显示导入进度
- **错误统计**: 统计各类错误的数量和比例
- **性能指标**: 记录导入速度和资源使用情况

## 📊 数据统计和验证

### 1. 导入后验证
```sql
-- 检查数据完整性
SELECT 
  COUNT(*) as total_stations,
  COUNT(DISTINCT country_id) as unique_countries,
  COUNT(DISTINCT language_id) as unique_languages,
  SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_stations
FROM world_tune_radio_stations;

-- 检查标签分布
SELECT 
  t.category,
  COUNT(*) as station_count
FROM world_tune_tags t
JOIN world_tune_station_tags st ON t.id = st.tag_id
GROUP BY t.category
ORDER BY station_count DESC;
```

### 2. 数据质量报告
- **成功率**: 成功导入的记录数 / 总记录数
- **错误分析**: 各类错误的统计和分析
- **数据分布**: 按国家、语言、标签的数据分布情况
