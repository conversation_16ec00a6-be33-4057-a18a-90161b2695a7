# World Tune 项目文档

欢迎来到 World Tune 项目文档中心。这里包含了项目开发所需的所有规范和指南。

## 📚 文档概览

### 🔧 开发规范
- **[DEVELOPMENT_RULES.md](./DEVELOPMENT_RULES.md)** - 通用开发规范
  - 开发环境要求
  - 代码规范和命名约定
  - Git 工作流规范
  - 文档和注释规范
  - 安全和性能规范

### 📱 Flutter 开发规范
- **[FLUTTER_RULES.md](./FLUTTER_RULES.md)** - Flutter 特定开发规范
  - Widget 设计规范
  - 状态管理规范 (Riverpod)
  - 性能优化规范
  - 主题和样式规范
  - 国际化规范
  - 调试和开发工具

### 🎨 UI/UX 设计规范
- **[UI_UX_RULES.md](./UI_UX_RULES.md)** - 用户界面设计规范
  - 设计系统 (色彩、字体、间距)
  - 组件设计规范
  - 响应式设计规范
  - 交互设计规范
  - 无障碍设计规范

### 🏗️ 架构规范
- **[ARCHITECTURE_RULES.md](./ARCHITECTURE_RULES.md)** - 项目架构规范
  - 项目结构和目录规范
  - Clean Architecture 实现
  - 模块划分和依赖管理
  - 数据流架构
  - 错误处理架构

### 🔍 兼容性和测试规范
- **[COMPATIBILITY_RULES.md](./COMPATIBILITY_RULES.md)** - 兼容性和测试规范
  - 设备兼容性要求
  - 系统版本兼容性
  - 网络兼容性
  - 性能要求和监控
  - 自动化测试规范

### 🎵 音频技术规范
- **[AUDIO_FORMAT_SUPPORT.md](./AUDIO_FORMAT_SUPPORT.md)** - 音频格式支持文档
  - 支持的音频格式和编码
  - 流媒体协议和播放列表格式
  - 平台兼容性和技术实现
  - 性能优化和错误处理
  - 格式验证和测试机制

## 🚀 快速开始

### 新团队成员入门
1. **阅读顺序建议**:
   ```
   1. DEVELOPMENT_RULES.md    (了解基础开发规范)
   2. ARCHITECTURE_RULES.md   (理解项目架构)
   3. FLUTTER_RULES.md        (掌握 Flutter 开发规范)
   4. UI_UX_RULES.md          (了解设计规范)
   5. COMPATIBILITY_RULES.md  (了解兼容性要求)
   ```

2. **开发环境设置**:
   ```bash
   # 1. 克隆项目
   git clone <repository-url>
   cd world_tune

   # 2. 安装依赖
   flutter pub get

   # 3. 生成代码
   flutter packages pub run build_runner build

   # 4. 运行应用
   flutter run --flavor development -t lib/main_development.dart
   ```

3. **代码质量检查**:
   ```bash
   # 代码格式化
   dart format .

   # 静态分析
   dart analyze

   # 运行测试
   flutter test

   # 测试覆盖率
   flutter test --coverage
   ```

### 开发工作流

#### 功能开发流程
```mermaid
graph TD
    A[创建功能分支] --> B[阅读相关规范]
    B --> C[设计架构]
    C --> D[编写代码]
    D --> E[编写测试]
    E --> F[代码审查]
    F --> G[合并到主分支]
    G --> H[部署测试]
```

#### 代码审查清单
- [ ] 遵循 [DEVELOPMENT_RULES.md](./DEVELOPMENT_RULES.md) 中的代码规范
- [ ] 符合 [FLUTTER_RULES.md](./FLUTTER_RULES.md) 中的 Flutter 规范
- [ ] 实现 [UI_UX_RULES.md](./UI_UX_RULES.md) 中的设计要求
- [ ] 遵循 [ARCHITECTURE_RULES.md](./ARCHITECTURE_RULES.md) 中的架构模式
- [ ] 满足 [COMPATIBILITY_RULES.md](./COMPATIBILITY_RULES.md) 中的兼容性要求
- [ ] 包含充分的单元测试和 Widget 测试
- [ ] 更新相关文档

## 📋 规范要点总结

### 🔑 核心原则
1. **一致性优先** - 所有代码应遵循统一的规范和模式
2. **可维护性** - 代码应易于理解、修改和扩展
3. **性能优化** - 始终考虑性能影响，优化用户体验
4. **用户体验** - 以用户为中心，提供流畅的交互体验
5. **兼容性** - 确保在不同设备和系统版本上的稳定运行

### 📱 技术栈
```yaml
Framework: Flutter 3.16+
Language: Dart 3.2+
State Management: Riverpod 2.4+
Routing: GoRouter 12.1+
Audio: just_audio 0.9+
Storage: SharedPreferences + Hive
Network: Dio 5.4+
Testing: flutter_test + mockito
```

### 🎯 质量标准
```yaml
Code Coverage: > 80%
Performance: 
  - App Start: < 3s
  - Memory Usage: < 100MB
  - Frame Rate: 60 FPS
Compatibility:
  - iOS: 12.0+
  - Android: API 23+ (6.0)
```

## 🔄 文档维护

### 更新频率
- **每月审查**: 检查规范的适用性和完整性
- **版本发布时**: 更新兼容性要求和技术栈版本
- **重大变更时**: 及时更新相关规范文档

### 贡献指南
1. **发现问题**: 通过 Issue 报告文档问题或改进建议
2. **提出修改**: 创建 Pull Request 提交文档修改
3. **团队讨论**: 重大规范变更需要团队讨论和同意
4. **版本控制**: 重要的规范变更需要记录版本历史

### 反馈渠道
- **GitHub Issues**: 用于报告问题和建议
- **团队会议**: 定期讨论规范的执行情况
- **代码审查**: 在审查过程中发现和改进规范问题

## 📞 联系方式

如果您对这些规范有任何疑问或建议，请通过以下方式联系：

- **项目负责人**: [项目负责人联系方式]
- **技术负责人**: [技术负责人联系方式]
- **GitHub Issues**: [项目 GitHub 地址]

## 📄 许可证

本项目文档遵循 [MIT License](../LICENSE)。

---

**最后更新**: 2025-01-09  
**文档版本**: 1.0.0  
**适用项目版本**: World Tune v1.0.0+

---

> 💡 **提示**: 这些规范是活文档，会根据项目发展和团队反馈持续更新。请定期查看最新版本，确保您的开发实践与团队标准保持一致。
