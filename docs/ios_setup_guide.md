# iOS 项目配置指南

## 📱 在Xcode中运行World Tune

### 1. 环境检查

首先确保您的开发环境已正确配置：

```bash
# 检查Flutter环境
flutter doctor

# 检查iOS模拟器
flutter devices

# 检查Xcode配置
xcode-select --print-path
```

### 2. 项目配置

#### 2.1 打开iOS项目
```bash
# 在项目根目录执行
cd /Users/<USER>/work_space/world_tune
open ios/Runner.xcworkspace
```

#### 2.2 配置Bundle Identifier
在Xcode中：
1. 选择 `Runner` 项目
2. 选择 `Runner` target
3. 在 `General` 标签页中修改 `Bundle Identifier`
   - 建议使用: `com.worldtune.app`

#### 2.3 配置Team和Signing
1. 在 `Signing & Capabilities` 标签页
2. 选择您的开发团队 (Team)
3. 确保 `Automatically manage signing` 已勾选

### 3. 音频播放权限配置

#### 3.1 修改Info.plist
在 `ios/Runner/Info.plist` 中添加音频播放权限：

```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access for audio recording features.</string>
<key>UIBackgroundModes</key>
<array>
    <string>audio</string>
    <string>background-processing</string>
</array>
```

#### 3.2 网络权限配置
由于需要播放网络电台，确保网络权限已配置：

```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

### 4. 依赖配置

#### 4.1 CocoaPods配置
```bash
# 进入iOS目录
cd ios

# 安装依赖
pod install

# 如果遇到问题，清理后重新安装
pod deintegrate
pod install
```

#### 4.2 最低iOS版本
确保 `ios/Podfile` 中的最低版本设置：

```ruby
platform :ios, '12.0'
```

### 5. 运行项目

#### 5.1 使用Flutter命令运行
```bash
# 运行在iOS模拟器
flutter run -d ios

# 运行在真机（需要连接设备）
flutter run -d [device-id]

# 运行开发版本
flutter run --flavor development --target lib/main_development.dart -d ios
```

#### 5.2 使用Xcode运行
1. 在Xcode中选择目标设备（模拟器或真机）
2. 点击 `Run` 按钮 (⌘+R)
3. 等待编译和安装完成

### 6. 常见问题解决

#### 6.1 签名问题
```
Error: No development team selected
```
**解决方案**:
1. 在Xcode中选择开发团队
2. 或者使用免费的Apple ID进行开发

#### 6.2 CocoaPods问题
```
Error: CocoaPods not installed
```
**解决方案**:
```bash
# 安装CocoaPods
sudo gem install cocoapods

# 或使用Homebrew
brew install cocoapods
```

#### 6.3 模拟器问题
```
Error: No iOS devices available
```
**解决方案**:
```bash
# 打开iOS模拟器
open -a Simulator

# 或在Xcode中: Window -> Devices and Simulators
```

#### 6.4 网络播放问题
如果在iOS上无法播放网络电台：

1. 检查Info.plist中的网络权限配置
2. 确保URL使用HTTPS（如果可能）
3. 检查防火墙设置

### 7. 调试技巧

#### 7.1 查看日志
```bash
# Flutter日志
flutter logs

# iOS系统日志
xcrun simctl spawn booted log stream --predicate 'process == "Runner"'
```

#### 7.2 性能分析
1. 在Xcode中使用Instruments进行性能分析
2. 使用Flutter DevTools进行调试

#### 7.3 音频调试
```dart
// 在AudioService中添加调试日志
print('Playing station: ${station.name}');
print('URL: ${station.url}');
```

### 8. 发布准备

#### 8.1 配置Release模式
```bash
# 构建Release版本
flutter build ios --release

# 构建Archive
flutter build ipa
```

#### 8.2 App Store配置
1. 配置App Store Connect
2. 上传应用图标和截图
3. 填写应用描述和关键词

### 9. 推荐的Xcode设置

#### 9.1 编辑器设置
- 启用行号显示
- 设置缩进为2个空格
- 启用自动保存

#### 9.2 调试设置
- 启用异常断点
- 配置符号断点
- 使用LLDB调试器

### 10. 性能优化建议

#### 10.1 启动优化
- 减少启动时的初始化操作
- 使用懒加载
- 优化资源加载

#### 10.2 内存优化
- 及时释放音频资源
- 优化图片缓存
- 监控内存使用

#### 10.3 电池优化
- 合理使用后台播放
- 优化网络请求频率
- 减少CPU密集型操作

## 🚀 快速开始命令

```bash
# 1. 检查环境
flutter doctor

# 2. 安装iOS依赖
cd ios && pod install && cd ..

# 3. 运行项目
flutter run -d ios

# 4. 如果需要在Xcode中调试
open ios/Runner.xcworkspace
```

## 📝 注意事项

1. **开发者账号**: 真机调试需要Apple开发者账号
2. **证书管理**: 定期更新开发证书
3. **版本兼容**: 确保Flutter和Xcode版本兼容
4. **网络安全**: 生产环境建议使用HTTPS
5. **权限申请**: 及时申请必要的系统权限
