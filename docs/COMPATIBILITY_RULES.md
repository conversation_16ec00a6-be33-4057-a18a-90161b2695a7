# 兼容性和测试规范

本文档定义了 World Tune 项目的兼容性要求和测试规范，确保应用在不同设备、系统版本和网络环境下的稳定运行。

## 📋 目录

- [设备兼容性](#设备兼容性)
- [系统版本兼容性](#系统版本兼容性)
- [网络兼容性](#网络兼容性)
- [性能要求](#性能要求)
- [测试规范](#测试规范)

## 📱 设备兼容性

### 支持的设备类型

#### iOS 设备
```yaml
# 最低支持设备
iPhone:
  - iPhone 8 及以上 (iOS 12.0+)
  - iPhone SE (第2代) 及以上
  
iPad:
  - iPad (第6代) 及以上 (iOS 12.0+)
  - iPad Air (第2代) 及以上
  - iPad Pro (所有型号)
  - iPad mini (第4代) 及以上

# 屏幕尺寸支持
Screen Sizes:
  - 4.7" (iPhone 8/SE)
  - 5.4" (iPhone 12 mini)
  - 6.1" (iPhone 12/13/14)
  - 6.7" (iPhone 12/13/14 Pro Max)
  - 7.9" (iPad mini)
  - 10.2" (iPad)
  - 11" (iPad Pro)
  - 12.9" (iPad Pro)
```

#### Android 设备
```yaml
# 最低系统要求
Android Version: 6.0 (API Level 23)
RAM: 2GB 最低, 4GB 推荐
Storage: 100MB 可用空间

# 屏幕密度支持
Screen Densities:
  - ldpi (120dpi)
  - mdpi (160dpi)
  - hdpi (240dpi)
  - xhdpi (320dpi)
  - xxhdpi (480dpi)
  - xxxhdpi (640dpi)

# 屏幕尺寸支持
Screen Sizes:
  - Small (< 4.5")
  - Normal (4.5" - 6.5")
  - Large (6.5" - 8")
  - XLarge (> 8")
```

### 设备特性兼容性

#### 音频功能
```dart
// ✅ 音频功能检测
class AudioCapabilityChecker {
  static Future<AudioCapabilities> checkCapabilities() async {
    final capabilities = AudioCapabilities();
    
    // 检查音频输出设备
    capabilities.hasBuiltInSpeaker = await _checkBuiltInSpeaker();
    capabilities.hasHeadphoneJack = await _checkHeadphoneJack();
    capabilities.hasBluetoothAudio = await _checkBluetoothAudio();
    
    // 检查音频格式支持
    capabilities.supportedFormats = await _getSupportedFormats();
    
    return capabilities;
  }

  static Future<bool> _checkBuiltInSpeaker() async {
    // 检测内置扬声器
    return true; // 大部分设备都有
  }

  static Future<bool> _checkHeadphoneJack() async {
    // 检测耳机接口
    if (Platform.isIOS) {
      // iOS 设备检测逻辑
      return await _iosHeadphoneCheck();
    } else {
      // Android 设备检测逻辑
      return await _androidHeadphoneCheck();
    }
  }

  static Future<List<AudioFormat>> _getSupportedFormats() async {
    final supportedFormats = <AudioFormat>[];
    
    // 检测支持的音频格式
    if (await _supportsFormat('mp3')) {
      supportedFormats.add(AudioFormat.mp3);
    }
    if (await _supportsFormat('aac')) {
      supportedFormats.add(AudioFormat.aac);
    }
    if (await _supportsFormat('ogg')) {
      supportedFormats.add(AudioFormat.ogg);
    }
    
    return supportedFormats;
  }
}

class AudioCapabilities {
  bool hasBuiltInSpeaker = false;
  bool hasHeadphoneJack = false;
  bool hasBluetoothAudio = false;
  List<AudioFormat> supportedFormats = [];
}

enum AudioFormat { mp3, aac, ogg, flac, wav }
```

#### 网络功能
```dart
// ✅ 网络能力检测
class NetworkCapabilityChecker {
  static Future<NetworkCapabilities> checkCapabilities() async {
    final capabilities = NetworkCapabilities();
    
    // 检查网络连接类型
    final connectivity = await Connectivity().checkConnectivity();
    capabilities.connectionType = _mapConnectivityResult(connectivity);
    
    // 检查网络速度
    capabilities.downloadSpeed = await _measureDownloadSpeed();
    capabilities.uploadSpeed = await _measureUploadSpeed();
    
    // 检查延迟
    capabilities.latency = await _measureLatency();
    
    return capabilities;
  }

  static Future<double> _measureDownloadSpeed() async {
    try {
      final stopwatch = Stopwatch()..start();
      final response = await Dio().get(
        'https://httpbin.org/bytes/1048576', // 1MB 测试文件
        options: Options(
          receiveTimeout: Duration(seconds: 10),
        ),
      );
      stopwatch.stop();
      
      final bytes = response.data.length;
      final seconds = stopwatch.elapsedMilliseconds / 1000;
      return (bytes * 8) / (seconds * 1024 * 1024); // Mbps
    } catch (e) {
      return 0.0;
    }
  }

  static Future<int> _measureLatency() async {
    try {
      final stopwatch = Stopwatch()..start();
      await Dio().head('https://httpbin.org/status/200');
      stopwatch.stop();
      return stopwatch.elapsedMilliseconds;
    } catch (e) {
      return -1;
    }
  }
}

class NetworkCapabilities {
  ConnectionType connectionType = ConnectionType.unknown;
  double downloadSpeed = 0.0; // Mbps
  double uploadSpeed = 0.0; // Mbps
  int latency = -1; // ms
}

enum ConnectionType { wifi, mobile, ethernet, unknown }
```

## 🔄 系统版本兼容性

### Flutter 版本兼容性

#### 版本支持策略
```yaml
# 当前支持版本
Flutter: 3.16.0 - 3.19.x
Dart: 3.2.0 - 3.3.x

# 升级策略
Major Version: 每年评估一次
Minor Version: 每季度评估一次
Patch Version: 及时跟进安全更新

# 向后兼容性
Minimum Support: 2个主要版本
Deprecation Notice: 6个月提前通知
Migration Guide: 提供详细迁移指南
```

#### 版本检测和处理
```dart
// ✅ 版本兼容性检查
class VersionCompatibilityChecker {
  static const String minFlutterVersion = '3.16.0';
  static const String minDartVersion = '3.2.0';

  static bool isFlutterVersionSupported() {
    final currentVersion = _getCurrentFlutterVersion();
    return _compareVersions(currentVersion, minFlutterVersion) >= 0;
  }

  static bool isDartVersionSupported() {
    final currentVersion = _getCurrentDartVersion();
    return _compareVersions(currentVersion, minDartVersion) >= 0;
  }

  static String _getCurrentFlutterVersion() {
    // 获取当前 Flutter 版本
    return Platform.version; // 简化实现
  }

  static int _compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();

    for (int i = 0; i < 3; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;

      if (v1Part != v2Part) {
        return v1Part.compareTo(v2Part);
      }
    }
    return 0;
  }
}
```

### 操作系统兼容性

#### iOS 版本支持
```dart
// ✅ iOS 版本检测
class IOSCompatibilityChecker {
  static const String minIOSVersion = '12.0';

  static bool isIOSVersionSupported() {
    if (!Platform.isIOS) return true;

    final version = _getIOSVersion();
    return _compareVersions(version, minIOSVersion) >= 0;
  }

  static String _getIOSVersion() {
    // 获取 iOS 版本
    return Platform.operatingSystemVersion;
  }

  static Map<String, bool> checkIOSFeatures() {
    final version = _getIOSVersion();
    
    return {
      'darkMode': _compareVersions(version, '13.0') >= 0,
      'sceneDelegate': _compareVersions(version, '13.0') >= 0,
      'widgetKit': _compareVersions(version, '14.0') >= 0,
      'appTrackingTransparency': _compareVersions(version, '14.5') >= 0,
    };
  }
}
```

#### Android 版本支持
```dart
// ✅ Android 版本检测
class AndroidCompatibilityChecker {
  static const int minSDKVersion = 23; // Android 6.0

  static bool isAndroidVersionSupported() {
    if (!Platform.isAndroid) return true;

    final sdkVersion = _getAndroidSDKVersion();
    return sdkVersion >= minSDKVersion;
  }

  static int _getAndroidSDKVersion() {
    // 获取 Android SDK 版本
    return 30; // 简化实现，实际应该从系统获取
  }

  static Map<String, bool> checkAndroidFeatures() {
    final sdkVersion = _getAndroidSDKVersion();
    
    return {
      'runtimePermissions': sdkVersion >= 23, // Android 6.0
      'adaptiveIcons': sdkVersion >= 26, // Android 8.0
      'notificationChannels': sdkVersion >= 26, // Android 8.0
      'scoped_storage': sdkVersion >= 29, // Android 10
      'material_you': sdkVersion >= 31, // Android 12
    };
  }
}
```

## 🌐 网络兼容性

### 网络环境适配

#### 弱网络环境优化
```dart
// ✅ 网络适配策略
class NetworkAdaptationService {
  static const Duration slowNetworkThreshold = Duration(seconds: 3);
  static const Duration fastNetworkThreshold = Duration(milliseconds: 500);

  Future<T> adaptiveRequest<T>(
    Future<T> Function() request, {
    Duration? timeout,
    int maxRetries = 3,
  }) async {
    final networkSpeed = await _measureNetworkSpeed();
    final adaptedTimeout = _calculateTimeout(networkSpeed, timeout);
    
    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await request().timeout(adaptedTimeout);
      } catch (e) {
        if (attempt == maxRetries - 1) rethrow;
        
        // 指数退避
        await Future.delayed(Duration(seconds: math.pow(2, attempt).toInt()));
      }
    }
    
    throw Exception('Max retries exceeded');
  }

  Duration _calculateTimeout(NetworkSpeed speed, Duration? baseTimeout) {
    final base = baseTimeout ?? Duration(seconds: 10);
    
    switch (speed) {
      case NetworkSpeed.slow:
        return base * 3;
      case NetworkSpeed.medium:
        return base * 2;
      case NetworkSpeed.fast:
        return base;
    }
  }

  Future<NetworkSpeed> _measureNetworkSpeed() async {
    final latency = await NetworkCapabilityChecker._measureLatency();
    
    if (latency > 1000) return NetworkSpeed.slow;
    if (latency > 300) return NetworkSpeed.medium;
    return NetworkSpeed.fast;
  }
}

enum NetworkSpeed { slow, medium, fast }
```

#### 离线模式支持
```dart
// ✅ 离线模式实现
class OfflineModeService {
  static const String cacheKey = 'offline_data';

  Future<List<RadioStation>> getStationsWithOfflineSupport() async {
    try {
      // 尝试从网络获取
      final stations = await _fetchStationsFromNetwork();
      
      // 缓存到本地
      await _cacheStations(stations);
      
      return stations;
    } catch (e) {
      // 网络失败，从缓存获取
      return await _getStationsFromCache();
    }
  }

  Future<void> _cacheStations(List<RadioStation> stations) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonData = stations.map((s) => s.toJson()).toList();
    await prefs.setString(cacheKey, jsonEncode(jsonData));
  }

  Future<List<RadioStation>> _getStationsFromCache() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(cacheKey);
    
    if (jsonString == null) {
      throw Exception('No cached data available');
    }
    
    final jsonData = jsonDecode(jsonString) as List;
    return jsonData.map((json) => RadioStation.fromJson(json)).toList();
  }

  Future<bool> isOfflineDataAvailable() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(cacheKey);
  }
}
```

## ⚡ 性能要求

### 性能基准

#### 启动性能
```yaml
# 冷启动时间
Target: < 3 seconds
Acceptable: < 5 seconds
Poor: > 5 seconds

# 热启动时间
Target: < 1 second
Acceptable: < 2 seconds
Poor: > 2 seconds

# 首屏渲染时间
Target: < 1 second
Acceptable: < 2 seconds
Poor: > 2 seconds
```

#### 运行时性能
```yaml
# 内存使用
Target: < 100MB
Acceptable: < 150MB
Poor: > 200MB

# CPU 使用率
Target: < 20% (空闲时)
Acceptable: < 40% (播放时)
Poor: > 60%

# 帧率
Target: 60 FPS
Acceptable: 50+ FPS
Poor: < 50 FPS

# 电池消耗
Target: < 5% per hour (播放时)
Acceptable: < 10% per hour
Poor: > 15% per hour
```

### 性能监控

#### 性能指标收集
```dart
// ✅ 性能监控实现
class PerformanceMonitor {
  static final _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final List<PerformanceMetric> _metrics = [];

  void recordAppStart() {
    _metrics.add(PerformanceMetric(
      name: 'app_start',
      timestamp: DateTime.now(),
      value: 0,
    ));
  }

  void recordFirstFrame() {
    final startMetric = _metrics.firstWhere(
      (m) => m.name == 'app_start',
      orElse: () => PerformanceMetric(
        name: 'app_start',
        timestamp: DateTime.now(),
        value: 0,
      ),
    );

    final duration = DateTime.now().difference(startMetric.timestamp);
    
    _metrics.add(PerformanceMetric(
      name: 'time_to_first_frame',
      timestamp: DateTime.now(),
      value: duration.inMilliseconds.toDouble(),
    ));
  }

  void recordMemoryUsage() {
    // 记录内存使用情况
    _metrics.add(PerformanceMetric(
      name: 'memory_usage',
      timestamp: DateTime.now(),
      value: _getCurrentMemoryUsage(),
    ));
  }

  double _getCurrentMemoryUsage() {
    // 获取当前内存使用量（简化实现）
    return 50.0; // MB
  }

  List<PerformanceMetric> getMetrics() => List.unmodifiable(_metrics);

  void clearMetrics() => _metrics.clear();
}

class PerformanceMetric {
  final String name;
  final DateTime timestamp;
  final double value;

  PerformanceMetric({
    required this.name,
    required this.timestamp,
    required this.value,
  });
}
```

#### 性能测试工具
```dart
// ✅ 性能测试工具
class PerformanceTester {
  static Future<void> runPerformanceTests() async {
    await _testAppStartup();
    await _testMemoryUsage();
    await _testScrollPerformance();
    await _testAudioPlayback();
  }

  static Future<void> _testAppStartup() async {
    final stopwatch = Stopwatch()..start();
    
    // 模拟应用启动
    await Future.delayed(Duration(milliseconds: 500));
    
    stopwatch.stop();
    final startupTime = stopwatch.elapsedMilliseconds;
    
    assert(startupTime < 3000, 'Startup time too slow: ${startupTime}ms');
    print('✅ Startup test passed: ${startupTime}ms');
  }

  static Future<void> _testMemoryUsage() async {
    final initialMemory = _getCurrentMemoryUsage();
    
    // 执行一些内存密集操作
    final largeList = List.generate(10000, (i) => 'Item $i');
    
    final peakMemory = _getCurrentMemoryUsage();
    final memoryIncrease = peakMemory - initialMemory;
    
    assert(memoryIncrease < 50, 'Memory usage too high: ${memoryIncrease}MB');
    print('✅ Memory test passed: ${memoryIncrease}MB increase');
  }

  static double _getCurrentMemoryUsage() {
    // 获取当前内存使用量
    return 50.0; // 简化实现
  }
}
```

## 🧪 测试规范

### 测试策略

#### 测试金字塔
```
        /\
       /  \
      / UI \     ← 10% (端到端测试)
     /______\
    /        \
   / Widget   \   ← 20% (Widget 测试)
  /____________\
 /              \
/  Unit Tests    \ ← 70% (单元测试)
/__________________\
```

#### 测试覆盖率要求
```yaml
# 代码覆盖率目标
Overall: > 80%
Business Logic: > 90%
UI Components: > 70%
Services: > 85%
Models: > 95%

# 测试类型分布
Unit Tests: 70%
Widget Tests: 20%
Integration Tests: 10%
```

### 自动化测试

#### 单元测试示例
```dart
// ✅ 单元测试实现
void main() {
  group('StationRepository Tests', () {
    late StationRepository repository;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      repository = ApiStationRepository(mockApiService);
    });

    test('should return stations when API call is successful', () async {
      // Arrange
      final mockStations = [
        RadioStation(id: '1', name: 'Test Station'),
      ];
      when(mockApiService.getStations()).thenAnswer(
        (_) async => mockStations,
      );

      // Act
      final result = await repository.getStations();

      // Assert
      expect(result, equals(mockStations));
      verify(mockApiService.getStations()).called(1);
    });

    test('should throw exception when API call fails', () async {
      // Arrange
      when(mockApiService.getStations()).thenThrow(
        Exception('Network error'),
      );

      // Act & Assert
      expect(
        () => repository.getStations(),
        throwsA(isA<Exception>()),
      );
    });
  });
}
```

#### Widget 测试示例
```dart
// ✅ Widget 测试实现
void main() {
  group('StationCard Widget Tests', () {
    testWidgets('should display station information', (tester) async {
      // Arrange
      const station = RadioStation(
        id: '1',
        name: 'Test Station',
        country: 'Test Country',
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: StationCard(station: station),
        ),
      );

      // Assert
      expect(find.text('Test Station'), findsOneWidget);
      expect(find.text('Test Country'), findsOneWidget);
    });

    testWidgets('should handle tap events', (tester) async {
      // Arrange
      bool tapped = false;
      const station = RadioStation(id: '1', name: 'Test');

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: StationCard(
            station: station,
            onTap: () => tapped = true,
          ),
        ),
      );

      await tester.tap(find.byType(StationCard));
      await tester.pump();

      // Assert
      expect(tapped, isTrue);
    });
  });
}
```

#### 集成测试示例
```dart
// ✅ 集成测试实现
void main() {
  group('App Integration Tests', () {
    testWidgets('complete user flow test', (tester) async {
      // 启动应用
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();

      // 验证首页加载
      expect(find.text('World Tune'), findsOneWidget);

      // 点击探索页面
      await tester.tap(find.text('explore'));
      await tester.pumpAndSettle();

      // 验证探索页面
      expect(find.text('explore'), findsOneWidget);

      // 选择一个电台
      await tester.tap(find.byType(StationCard).first);
      await tester.pumpAndSettle();

      // 验证播放器页面
      expect(find.byType(PlayerPage), findsOneWidget);
    });
  });
}
```

### 设备测试矩阵

#### 测试设备配置
```yaml
# iOS 测试设备
iOS_Devices:
  - iPhone 8 (iOS 12.0)
  - iPhone 12 (iOS 15.0)
  - iPhone 14 (iOS 16.0)
  - iPad (9th gen, iOS 15.0)
  - iPad Pro 12.9" (iOS 16.0)

# Android 测试设备
Android_Devices:
  - Samsung Galaxy S10 (Android 9.0, API 28)
  - Google Pixel 5 (Android 11.0, API 30)
  - OnePlus 9 (Android 12.0, API 31)
  - Samsung Galaxy Tab S7 (Android 11.0)

# 测试场景
Test_Scenarios:
  - 不同屏幕尺寸
  - 不同屏幕密度
  - 不同网络条件
  - 不同系统版本
  - 不同硬件配置
```

---

**注意**: 这些兼容性和测试规范应该根据项目的实际需求和目标用户群体进行调整。定期更新支持的设备列表和系统版本，确保应用的兼容性和稳定性。
