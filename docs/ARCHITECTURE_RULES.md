# 项目架构规范

本文档定义了 World Tune 项目的架构规范，包括目录结构、模块划分、依赖管理和设计模式。

## 📋 目录

- [项目结构](#项目结构)
- [架构模式](#架构模式)
- [模块划分](#模块划分)
- [依赖管理](#依赖管理)
- [数据流架构](#数据流架构)

## 📁 项目结构

### 目录结构规范
```
world_tune/
├── android/                    # Android 平台特定代码
├── ios/                        # iOS 平台特定代码
├── lib/                        # Flutter 应用代码
│   ├── app/                    # 应用级配置和初始化
│   │   ├── app.dart           # 应用主入口
│   │   ├── config/            # 应用配置
│   │   └── providers/         # 全局 Provider
│   ├── src/                   # 源代码主目录
│   │   ├── features/          # 功能模块
│   │   │   ├── home/          # 首页模块
│   │   │   ├── player/        # 播放器模块
│   │   │   ├── explore/       # 探索模块
│   │   │   ├── library/       # 音乐库模块
│   │   │   └── search/        # 搜索模块
│   │   └── shared/            # 共享代码
│   │       ├── models/        # 数据模型
│   │       ├── services/      # 业务服务
│   │       ├── widgets/       # 通用组件
│   │       ├── utils/         # 工具类
│   │       ├── constants/     # 常量定义
│   │       └── router/        # 路由配置
│   ├── main.dart              # 应用入口点
│   ├── main_development.dart  # 开发环境入口
│   └── main_production.dart   # 生产环境入口
├── test/                      # 测试代码
│   ├── unit/                  # 单元测试
│   ├── widget/                # Widget 测试
│   └── integration/           # 集成测试
├── assets/                    # 静态资源
│   ├── images/                # 图片资源
│   ├── icons/                 # 图标资源
│   ├── fonts/                 # 字体文件
│   └── lang/                  # 国际化文件
├── docs/                      # 项目文档
│   ├── DEVELOPMENT_RULES.md   # 开发规范
│   ├── FLUTTER_RULES.md       # Flutter 规范
│   ├── UI_UX_RULES.md         # UI/UX 规范
│   ├── ARCHITECTURE_RULES.md  # 架构规范
│   └── COMPATIBILITY_RULES.md # 兼容性规范
└── scripts/                   # 构建和部署脚本
    ├── build.sh               # 构建脚本
    ├── deploy.sh              # 部署脚本
    └── test.sh                # 测试脚本
```

### 功能模块结构
```
features/home/
├── models/                    # 模块特定模型
│   ├── home_state.dart
│   └── home_models.dart
├── providers/                 # 模块特定 Provider
│   ├── home_provider.dart
│   └── home_notifier.dart
├── services/                  # 模块特定服务
│   └── home_service.dart
├── widgets/                   # 模块特定组件
│   ├── home_header.dart
│   ├── featured_stations.dart
│   └── recent_stations.dart
├── home_page.dart            # 主页面
└── README.md                 # 模块文档
```

## 🏗️ 架构模式

### Clean Architecture 实现

#### 层次划分
```
┌─────────────────────────────────────┐
│           Presentation Layer        │  ← UI/Widgets/Pages
├─────────────────────────────────────┤
│           Application Layer         │  ← Providers/Notifiers/UseCases
├─────────────────────────────────────┤
│             Domain Layer            │  ← Models/Entities/Repositories
├─────────────────────────────────────┤
│         Infrastructure Layer        │  ← Services/APIs/Storage
└─────────────────────────────────────┘
```

#### 依赖规则
```dart
// ✅ 正确的依赖方向
// Presentation → Application → Domain ← Infrastructure

// Presentation Layer (UI)
class HomePage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeState = ref.watch(homeProvider);
    // UI 依赖 Application Layer
  }
}

// Application Layer (Business Logic)
class HomeNotifier extends StateNotifier<HomeState> {
  HomeNotifier(this._stationRepository) : super(HomeState.initial());
  
  final StationRepository _stationRepository; // 依赖 Domain Layer
  
  Future<void> loadFeaturedStations() async {
    final stations = await _stationRepository.getFeaturedStations();
    // 业务逻辑处理
  }
}

// Domain Layer (Business Rules)
abstract class StationRepository {
  Future<List<RadioStation>> getFeaturedStations();
  Future<List<RadioStation>> searchStations(String query);
}

// Infrastructure Layer (External Concerns)
class ApiStationRepository implements StationRepository {
  ApiStationRepository(this._apiClient);
  
  final ApiClient _apiClient;
  
  @override
  Future<List<RadioStation>> getFeaturedStations() async {
    // API 调用实现
  }
}
```

### MVVM 模式实现

#### Model-View-ViewModel 结构
```dart
// Model (数据模型)
@freezed
class RadioStation with _$RadioStation {
  const factory RadioStation({
    required String id,
    required String name,
    required String streamUrl,
    required String country,
    required String language,
    String? imageUrl,
    List<String>? tags,
  }) = _RadioStation;

  factory RadioStation.fromJson(Map<String, dynamic> json) =>
      _$RadioStationFromJson(json);
}

// ViewModel (业务逻辑)
class StationListViewModel extends StateNotifier<StationListState> {
  StationListViewModel(this._repository) : super(StationListState.initial());

  final StationRepository _repository;

  Future<void> loadStations() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final stations = await _repository.getStations();
      state = state.copyWith(
        stations: stations,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  void selectStation(RadioStation station) {
    state = state.copyWith(selectedStation: station);
  }
}

// View (UI 组件)
class StationListView extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(stationListProvider);
    final viewModel = ref.read(stationListProvider.notifier);

    return state.when(
      loading: () => CircularProgressIndicator(),
      error: (error) => ErrorWidget(error: error),
      data: (stations) => ListView.builder(
        itemCount: stations.length,
        itemBuilder: (context, index) {
          return StationListItem(
            station: stations[index],
            onTap: () => viewModel.selectStation(stations[index]),
          );
        },
      ),
    );
  }
}
```

## 🧩 模块划分

### 功能模块设计原则

#### 单一职责原则
```dart
// ✅ 正确：每个模块只负责一个功能领域
// features/player/ - 只负责音频播放相关功能
// features/library/ - 只负责音乐库管理
// features/search/ - 只负责搜索功能

// ❌ 错误：模块职责混乱
// features/player_and_library/ - 职责不清晰
```

#### 模块间通信规范
```dart
// ✅ 通过 Provider 进行模块间通信
class PlayerNotifier extends StateNotifier<PlayerState> {
  PlayerNotifier(this._ref) : super(PlayerState.initial());
  
  final Ref _ref;

  void playStation(RadioStation station) {
    // 播放逻辑
    state = PlayerState.playing(station);
    
    // 通知其他模块
    _ref.read(libraryProvider.notifier).addToRecentlyPlayed(station);
    _ref.read(analyticsProvider.notifier).trackPlay(station);
  }
}

// ✅ 通过事件总线进行松耦合通信
class EventBus {
  static final _instance = EventBus._internal();
  factory EventBus() => _instance;
  EventBus._internal();

  final _controller = StreamController<AppEvent>.broadcast();
  
  Stream<T> on<T extends AppEvent>() {
    return _controller.stream.where((event) => event is T).cast<T>();
  }
  
  void emit(AppEvent event) {
    _controller.add(event);
  }
}

// 事件定义
abstract class AppEvent {}

class StationPlayedEvent extends AppEvent {
  final RadioStation station;
  StationPlayedEvent(this.station);
}

class StationFavoritedEvent extends AppEvent {
  final RadioStation station;
  StationFavoritedEvent(this.station);
}
```

### 共享模块设计

#### 服务层设计
```dart
// ✅ 服务接口定义
abstract class AudioService {
  Future<void> play(String url);
  Future<void> pause();
  Future<void> stop();
  Stream<PlayerState> get stateStream;
  Stream<Duration> get positionStream;
}

// ✅ 服务实现
class JustAudioService implements AudioService {
  JustAudioService() {
    _player = AudioPlayer();
  }

  late final AudioPlayer _player;
  final _stateController = StreamController<PlayerState>.broadcast();

  @override
  Future<void> play(String url) async {
    await _player.setUrl(url);
    await _player.play();
    _stateController.add(PlayerState.playing);
  }

  @override
  Stream<PlayerState> get stateStream => _stateController.stream;

  @override
  Stream<Duration> get positionStream => _player.positionStream;
}

// ✅ 服务注册
final audioServiceProvider = Provider<AudioService>((ref) {
  return JustAudioService();
});
```

#### 工具类设计
```dart
// ✅ 静态工具类
class DateUtils {
  static String formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  static String formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }
}

// ✅ 扩展方法
extension StringExtensions on String {
  bool get isValidUrl {
    return Uri.tryParse(this) != null;
  }

  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }
}

extension DurationExtensions on Duration {
  String get formatted {
    return DateUtils.formatDuration(this);
  }
}
```

## 📦 依赖管理

### 依赖注入规范

#### Provider 层次结构
```dart
// ✅ 分层的 Provider 结构
// 1. 基础设施层 Provider
final httpClientProvider = Provider<Dio>((ref) {
  final dio = Dio();
  dio.interceptors.add(LogInterceptor());
  return dio;
});

final storageProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be overridden');
});

// 2. 服务层 Provider
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService(ref.watch(httpClientProvider));
});

final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService(ref.watch(storageProvider));
});

// 3. 仓库层 Provider
final stationRepositoryProvider = Provider<StationRepository>((ref) {
  return ApiStationRepository(
    apiService: ref.watch(apiServiceProvider),
    storageService: ref.watch(storageServiceProvider),
  );
});

// 4. 业务逻辑层 Provider
final homeProvider = StateNotifierProvider<HomeNotifier, HomeState>((ref) {
  return HomeNotifier(ref.watch(stationRepositoryProvider));
});
```

#### 依赖覆盖 (用于测试)
```dart
// ✅ 测试中的依赖覆盖
void main() {
  testWidgets('HomePage should display stations', (tester) async {
    final container = ProviderContainer(
      overrides: [
        stationRepositoryProvider.overrideWithValue(
          MockStationRepository(),
        ),
      ],
    );

    await tester.pumpWidget(
      UncontrolledProviderScope(
        container: container,
        child: MaterialApp(home: HomePage()),
      ),
    );

    // 测试逻辑
  });
}
```

### 包管理规范

#### pubspec.yaml 组织
```yaml
name: world_tune
description: A sophisticated radio streaming application
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # 状态管理
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3

  # 路由
  go_router: ^12.1.3

  # 网络
  dio: ^5.4.0
  connectivity_plus: ^5.0.2

  # 音频
  just_audio: ^0.9.36
  audio_session: ^0.1.18

  # 本地存储
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # UI 组件
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0

  # 国际化
  easy_localization: ^3.0.3
  intl: ^0.19.0

  # 工具
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  logger: ^2.0.2+1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码生成
  build_runner: ^2.4.7
  freezed: ^2.4.6
  json_serializable: ^6.7.1
  riverpod_generator: ^2.3.9

  # 代码质量
  flutter_lints: ^3.0.1
  very_good_analysis: ^5.1.0

  # 测试
  mockito: ^5.4.4
  mocktail: ^1.0.2

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/lang/

  fonts:
    - family: CustomFont
      fonts:
        - asset: assets/fonts/CustomFont-Regular.ttf
        - asset: assets/fonts/CustomFont-Bold.ttf
          weight: 700
```

## 🔄 数据流架构

### 单向数据流
```dart
// ✅ 单向数据流实现
class StationListPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 1. 读取状态
    final state = ref.watch(stationListProvider);
    
    return Scaffold(
      body: state.when(
        loading: () => LoadingWidget(),
        error: (error, stack) => ErrorWidget(error: error),
        data: (stations) => ListView.builder(
          itemCount: stations.length,
          itemBuilder: (context, index) {
            return StationListItem(
              station: stations[index],
              onTap: () {
                // 2. 触发动作
                ref.read(stationListProvider.notifier)
                   .selectStation(stations[index]);
              },
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // 3. 触发状态更新
          ref.read(stationListProvider.notifier).refreshStations();
        },
        child: Icon(Icons.refresh),
      ),
    );
  }
}
```

### 状态管理模式
```dart
// ✅ 状态类定义
@freezed
class StationListState with _$StationListState {
  const factory StationListState({
    @Default([]) List<RadioStation> stations,
    @Default(false) bool isLoading,
    @Default(false) bool isRefreshing,
    String? error,
    RadioStation? selectedStation,
    @Default(1) int currentPage,
    @Default(false) bool hasMore,
  }) = _StationListState;
}

// ✅ 状态管理器
class StationListNotifier extends StateNotifier<StationListState> {
  StationListNotifier(this._repository) : super(StationListState());

  final StationRepository _repository;

  Future<void> loadStations() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final stations = await _repository.getStations(page: 1);
      state = state.copyWith(
        stations: stations,
        isLoading: false,
        currentPage: 1,
        hasMore: stations.length >= 20,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadMoreStations() async {
    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(isLoading: true);

    try {
      final newStations = await _repository.getStations(
        page: state.currentPage + 1,
      );
      
      state = state.copyWith(
        stations: [...state.stations, ...newStations],
        isLoading: false,
        currentPage: state.currentPage + 1,
        hasMore: newStations.length >= 20,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void selectStation(RadioStation station) {
    state = state.copyWith(selectedStation: station);
  }

  Future<void> refreshStations() async {
    state = state.copyWith(isRefreshing: true);
    await loadStations();
    state = state.copyWith(isRefreshing: false);
  }
}
```

### 错误处理架构
```dart
// ✅ 统一错误处理
abstract class AppException implements Exception {
  const AppException(this.message);
  final String message;
}

class NetworkException extends AppException {
  const NetworkException(super.message);
}

class ValidationException extends AppException {
  const ValidationException(super.message);
}

class UnknownException extends AppException {
  const UnknownException(super.message);
}

// ✅ 错误处理中间件
class ErrorHandler {
  static AppException handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
          return NetworkException('Connection timeout');
        case DioExceptionType.badResponse:
          return NetworkException('Server error: ${error.response?.statusCode}');
        default:
          return NetworkException('Network error');
      }
    }
    
    return UnknownException(error.toString());
  }
}

// ✅ 在 Notifier 中使用
class StationListNotifier extends StateNotifier<StationListState> {
  Future<void> loadStations() async {
    try {
      // 业务逻辑
    } catch (e) {
      final appException = ErrorHandler.handleError(e);
      state = state.copyWith(error: appException.message);
    }
  }
}
```

---

**注意**: 这些架构规范应该在项目开始时确定，并在整个开发过程中严格遵循。定期审查架构决策，确保它们仍然适合项目需求。
