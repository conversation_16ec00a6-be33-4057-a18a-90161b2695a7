# 插屏广告功能实现说明

## 功能概述

在首页电台卡片点击时显示插屏广告，实现以下特性：
- 1分钟冷却时间，期间不再弹广告
- 广告弹出期间电台不播放，广告关闭后再播放
- 广告加载失败时不影响正常播放功能
- 首页数据加载完成后预加载广告备用

## 实现方案

### 1. 插屏广告服务 (`InterstitialAdService`)

**文件位置**: `lib/src/shared/services/interstitial_ad_service.dart`

**核心功能**：
- 单例模式管理广告状态
- 1分钟冷却时间控制
- 广告预加载和显示管理
- 错误处理和降级逻辑

**关键方法**：
- `preloadAd()`: 预加载插屏广告
- `showAdIfAvailable()`: 显示广告（如果可用且不在冷却期）
- `isInCooldown`: 检查是否在冷却期内
- `isAdReady`: 检查广告是否已加载

### 2. 首页集成 (`home_page.dart`)

**集成点**：
- 导入插屏广告服务
- 在数据加载完成后预加载广告
- 修改所有电台播放方法，集成广告显示逻辑

**预加载时机**：
1. 首次数据加载完成后（2秒延迟）
2. 数据保存到缓存后

**播放流程**：
```
用户点击电台卡片
  ↓
检查冷却时间（1分钟内？）
  ↓ 否
检查插屏广告是否ready
  ↓ 是：显示广告 → 广告关闭后播放电台
  ↓ 否：加载广告 → 直接播放电台
  ↓
播放电台（原有逻辑）
```

### 3. 广告配置

**广告类型**: `AdType.interstitial`
**页面ID**: `'home_interstitial'`
**配置位置**: `lib/src/shared/services/ad_config_service.dart`

已配置的广告ID：
- iOS: `ca-app-pub-3940256099942544/4411468910`
- Android: `ca-app-pub-3940256099942544/1033173712`

## 技术细节

### 冷却时间管理

```dart
static const Duration cooldownDuration = Duration(minutes: 1);

bool get isInCooldown {
  if (_lastShowTime == null) return false;
  return DateTime.now().difference(_lastShowTime!) < cooldownDuration;
}
```

### 广告显示逻辑

```dart
void _playStationWithInterstitialAd(VoidCallback playCallback) {
  _interstitialAdService.showAdIfAvailable(
    onPlayDirectly: playCallback,
    onAdShown: () {
      print('🎯 插屏广告开始显示，暂停播放逻辑');
    },
    onAdClosed: () {
      print('🎯 插屏广告关闭，继续播放逻辑');
    },
  );
}
```

### 错误处理

- 广告加载失败：直接播放电台
- 广告显示失败：直接播放电台
- 网络问题：直接播放电台
- 确保广告问题不影响核心播放功能

## 影响的播放方法

所有首页电台卡片的点击都会经过插屏广告逻辑：

1. `_playGridStationWithPlaylist()` - 网格区域（雷达推荐）
2. `_playRecommendationStationWithPlaylist()` - 推荐区域
3. `_buildRecommendationStationCard()` 中的onTap - 推荐卡片

## 测试验证

### 功能测试

1. **首次点击**：应该显示插屏广告，关闭后播放电台
2. **冷却期内点击**：直接播放电台，不显示广告
3. **广告加载失败**：直接播放电台，不影响功能
4. **网络异常**：直接播放电台，不影响功能

### 调试信息

服务提供详细的调试信息：

```dart
// 打印服务状态
_interstitialAdService.printStatusInfo();

// 获取状态信息
final status = _interstitialAdService.getStatusInfo();
```

### 测试工具方法

```dart
// 重置冷却时间（测试用）
_interstitialAdService.resetCooldown();

// 强制重新加载广告（测试用）
await _interstitialAdService.forceReload();
```

## 性能考虑

- 广告预加载在后台进行，不影响UI响应
- 使用延迟加载避免与应用启动冲突
- 错误处理确保广告问题不阻塞播放功能
- 单例模式避免重复初始化

## 后续优化建议

1. **广告频率控制**: 可以根据用户行为调整冷却时间
2. **广告类型扩展**: 支持激励广告等其他类型
3. **用户偏好**: 允许用户关闭广告（付费功能）
4. **数据统计**: 记录广告显示率和用户互动数据

## 注意事项

- 广告SDK的配置ID当前使用测试ID，生产环境需要替换为真实ID
- 确保应用已正确初始化广告SDK (`AdConfigService.initialize()`)
- 广告显示依赖网络连接，离线环境下会直接播放电台
