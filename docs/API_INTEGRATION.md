# World Tune API 集成文档

## 概述

本文档详细说明了 World Tune 应用与后端 API 的集成方式，包括加密解密流程、接口使用方法、数据模型转换等。

## 后端服务信息

- **服务地址**: `http://10.60.81.105:8300`  （开发环境）
- **加密方式**: XOR 加密
- **加密密钥**: `gExXgO8x0OhkwHSV`
- **数据格式**: JSON

## 加密解密机制

### XOR 加密算法

后端使用 XOR 加密算法对请求和响应数据进行加密，确保数据传输安全。

#### 加密流程

1. 将 JSON 数据转换为 UTF-8 字节数组
2. 使用密钥对字节数组进行 XOR 运算
3. 将结果进行 Base64 编码

#### 解密流程

1. 对 Base64 字符串进行解码
2. 使用相同密钥进行 XOR 运算
3. 将结果转换为 UTF-8 字符串

### 前端实现

```dart
// 加密示例
final params = {'page': 1, 'page_size': 20};
final encryptedData = XorCrypto.encodeJson(params);
final requestBody = {'bsg': encryptedData};

// 解密示例
final responseData = {'header': {...}, 'data': 'encrypted_string'};
final decryptedData = XorCrypto.parseEncryptedResponse(responseData);
```

## API 接口列表

### 1. 获取电台列表

**接口路径**: `/api/v1/radio/list`  
**请求方式**: POST  
**功能**: 分页获取电台列表，支持多条件筛选和排序

#### 请求参数

```json
{
  "page": 1,
  "page_size": 20,
  "country_id": 1,
  "language_id": 1,
  "tag_ids": [1, 2, 3],
  "keyword": "BBC",
  "sort_by": "votes",
  "sort_order": "desc"
}
```

#### 响应数据

```json
{
  "list": [
    {
      "id": 1,
      "station_uuid": "uuid-123",
      "name": "BBC Radio 1",
      "url": "http://...",
      "url_resolved": "http://...",
      "favicon": "http://...",
      "country_id": 1,
      "language_id": 1,
      "votes": 1500,
      "click_count": 5000,
      "bitrate": 128,
      "codec": "MP3",
      "is_hls": 0,
      "status": 1
    }
  ],
  "page": {
    "page": 1,
    "page_size": 20,
    "total": 100
  }
}
```

### 2. 获取电台分类

**接口路径**: `/api/v1/radio/tags`  
**请求方式**: POST  
**功能**: 获取所有电台标签分类

#### 请求参数

```json
{
  "tag_category": "music",
  "is_show": 1
}
```

#### 响应数据

```json
{
  "list": [
    {
      "id": 1,
      "name": "Pop Music",
      "tag": "music",
      "sort_order": 1,
      "is_show": 1,
      "created_at": "2024-01-01 00:00:00",
      "updated_at": "2024-01-01 00:00:00"
    }
  ]
}
```

### 3. 获取国家列表

**接口路径**: `/api/v1/country/list`  
**请求方式**: POST  
**功能**: 获取所有国家列表

### 4. 获取语言列表

**接口路径**: `/api/v1/language/list`  
**请求方式**: POST  
**功能**: 获取所有语言列表

### 5. 获取电台详情

**接口路径**: `/api/v1/radio/detail`  
**请求方式**: POST  
**功能**: 获取指定电台的详细信息

## 数据模型

### RadioStation (API 模型)

```dart
class RadioStation {
  final int id;
  final String stationUuid;
  final String name;
  final String url;
  final String? urlResolved;
  final String? favicon;
  final String? homepage;
  final int? countryId;
  final int? languageId;
  final int votes;
  final int clickCount;
  final int bitrate;
  final String codec;
  final int isHls;
  final int status;
}
```

### StationSimple (UI 模型)

```dart
class StationSimple {
  final String id;
  final String name;
  final String url;
  final String country;
  final String language;
  final String favicon;
  final String homepage;
  final List<String> tags;
  final int votes;
  final bool isFavorite;
  final int playCount;
}
```

## 数据适配器

使用 `RadioDataAdapter` 类将 API 返回的数据模型转换为 UI 使用的数据模型：

```dart
// 转换单个电台
final stationSimple = RadioDataAdapter.radioStationToStationSimple(radioStation);

// 转换电台列表
final stationList = RadioDataAdapter.radioStationListToStationSimpleList(radioStations);
```

## Riverpod 状态管理

### Provider 列表

- `radioApiServiceProvider`: API 服务实例
- `popularRadiosProvider`: 热门电台列表状态
- `latestRadiosProvider`: 最新电台列表状态
- `searchRadiosProvider`: 搜索电台列表状态
- `categoryRadiosProvider`: 分类电台列表状态
- `radioTagsProvider`: 电台标签列表
- `countriesProvider`: 国家列表
- `languagesProvider`: 语言列表
- `recommendedRadiosProvider`: 推荐电台列表

### 使用示例

```dart
// 监听热门电台状态
final popularState = ref.watch(popularRadiosProvider);

// 加载更多热门电台
await ref.read(popularRadiosProvider.notifier).loadPopularRadios();

// 搜索电台
await ref.read(searchRadiosProvider.notifier).searchRadios('BBC');

// 按分类加载电台
await ref.read(categoryRadiosProvider.notifier).loadRadiosByCategory([1, 2, 3]);
```

## 错误处理

### API 异常类型

```dart
class ApiException implements Exception {
  final String message;
  final int? code;
}
```

### 常见错误处理

1. **网络连接错误**: 显示重试按钮
2. **加密解密错误**: 检查密钥配置
3. **服务器错误**: 显示错误信息和重试选项
4. **数据解析错误**: 记录日志并显示友好提示

## 性能优化

### 分页加载

- 每页默认加载 20 条数据
- 支持无限滚动加载更多
- 使用状态管理避免重复请求

### 缓存策略

- 使用 Riverpod 的缓存机制
- 标签和国家/语言列表缓存较长时间
- 电台列表根据参数缓存

### 图片加载

- 电台图标使用缓存网络图片
- 支持占位图和错误图片

## 调试和日志

### 开发模式日志

```dart
if (kDebugMode) {
  print('🚀 API Request: ${options.method} ${options.path}');
  print('📤 Request Data: ${options.data}');
  print('✅ API Response: ${response.statusCode}');
  print('📥 Response Data: ${response.data}');
}
```

### 错误日志

所有 API 错误都会被捕获并记录，包括：
- 请求参数
- 响应状态码
- 错误信息
- 堆栈跟踪

## 安全考虑

1. **加密传输**: 所有敏感数据使用 XOR 加密
2. **密钥管理**: 加密密钥硬编码在应用中
3. **HTTPS**: 生产环境建议使用 HTTPS
4. **输入验证**: 对用户输入进行验证和清理

## 部署配置

### 环境配置

```dart
// 开发环境
static const String baseUrl = 'http://10.60.81.105:8300';

// 生产环境
static const String baseUrl = 'https://api.worldtune.com';
```

### 超时配置

```dart
BaseOptions(
  connectTimeout: const Duration(seconds: 30),
  receiveTimeout: const Duration(seconds: 30),
  sendTimeout: const Duration(seconds: 30),
)
```

## 测试

### 单元测试

- 测试加密解密功能
- 测试数据模型转换
- 测试 API 服务方法

### 集成测试

- 测试完整的 API 调用流程
- 测试错误处理机制
- 测试状态管理

## 常见问题

### Q: 加密解密失败怎么办？

A: 检查密钥是否正确，确保前后端使用相同的密钥和算法。

### Q: 接口返回 400 错误？

A: 检查请求参数格式，确保必填参数都已提供。

### Q: 电台播放失败？

A: 检查电台 URL 是否有效，是否支持当前的音频格式。

### Q: 图片加载失败？

A: 检查图片 URL 是否有效，网络连接是否正常。

## 更新日志

### v1.0.0 (2025-01-10)

- 初始版本
- 实现基础 API 集成
- 支持电台列表、分类、搜索功能
- 实现 XOR 加密解密
- 完成数据模型适配
- 集成 Riverpod 状态管理
