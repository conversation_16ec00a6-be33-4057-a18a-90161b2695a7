# Flutter 开发规范

本文档专门针对 World Tune 项目的 Flutter 开发制定详细规范，确保代码质量、性能和可维护性。

## 📋 目录

- [Widget 设计规范](#widget-设计规范)
- [状态管理规范](#状态管理规范)
- [性能优化规范](#性能优化规范)
- [主题和样式规范](#主题和样式规范)
- [国际化规范](#国际化规范)

## 🎨 Widget 设计规范

### Widget 层次结构

#### StatelessWidget vs StatefulWidget
```dart
// ✅ 优先使用 StatelessWidget
class StationCard extends StatelessWidget {
  const StationCard({
    super.key,
    required this.station,
    this.onTap,
  });

  final RadioStation station;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text(station.name),
        onTap: onTap,
      ),
    );
  }
}

// ✅ 仅在需要状态时使用 StatefulWidget
class AudioPlayer extends StatefulWidget {
  const AudioPlayer({super.key});

  @override
  State<AudioPlayer> createState() => _AudioPlayerState();
}
```

#### Widget 组合原则
```dart
// ✅ 将复杂 Widget 拆分为小组件
class StationListItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            _buildStationImage(),
            const SizedBox(width: 16),
            Expanded(child: _buildStationInfo()),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildStationImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.network(
        station.imageUrl,
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 60,
            height: 60,
            color: Colors.grey[300],
            child: const Icon(Icons.radio),
          );
        },
      ),
    );
  }
}

// ❌ 避免在 build 方法中创建过于复杂的结构
class BadStationListItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                station.imageUrl,
                width: 60,
                height: 60,
                // ... 大量嵌套代码
              ),
            ),
            // ... 更多复杂嵌套
          ],
        ),
      ),
    );
  }
}
```

### 构造函数规范
```dart
// ✅ 正确的构造函数设计
class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.isLoading = false,
    this.icon,
  });

  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final bool isLoading;
  final IconData? icon;

  // 工厂构造函数用于常见配置
  factory CustomButton.primary({
    required String text,
    required VoidCallback? onPressed,
    bool isLoading = false,
  }) {
    return CustomButton(
      text: text,
      onPressed: onPressed,
      isLoading: isLoading,
      backgroundColor: Colors.blue,
      textColor: Colors.white,
    );
  }
}
```

### Widget 生命周期管理
```dart
class AudioPlayerWidget extends StatefulWidget {
  @override
  _AudioPlayerWidgetState createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AudioPlayer _audioPlayer;
  StreamSubscription<PlayerState>? _playerStateSubscription;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _setupAnimations();
  }

  @override
  void didUpdateWidget(AudioPlayerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 处理 widget 更新
    if (widget.station != oldWidget.station) {
      _loadNewStation();
    }
  }

  @override
  void dispose() {
    // ✅ 确保所有资源都被释放
    _playerStateSubscription?.cancel();
    _animationController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  void _initializePlayer() {
    _audioPlayer = AudioPlayer();
    _playerStateSubscription = _audioPlayer.playerStateStream.listen(
      (state) {
        if (mounted) {
          setState(() {
            // 更新 UI 状态
          });
        }
      },
    );
  }
}
```

## 🔄 状态管理规范

### Riverpod 使用规范

#### Provider 定义
```dart
// ✅ 正确的 Provider 定义
// providers/audio_providers.dart

// 简单状态 Provider
final currentStationProvider = StateProvider<RadioStation?>((ref) => null);

// 异步数据 Provider
final stationsProvider = FutureProvider<List<RadioStation>>((ref) async {
  final repository = ref.watch(stationRepositoryProvider);
  return repository.fetchStations();
});

// 复杂状态管理 Provider
final audioPlayerProvider = StateNotifierProvider<AudioPlayerNotifier, AudioPlayerState>(
  (ref) => AudioPlayerNotifier(ref),
);

// 依赖注入 Provider
final stationRepositoryProvider = Provider<StationRepository>((ref) {
  return StationRepositoryImpl(
    apiClient: ref.watch(apiClientProvider),
  );
});
```

#### StateNotifier 实现
```dart
// ✅ 正确的 StateNotifier 实现
class AudioPlayerNotifier extends StateNotifier<AudioPlayerState> {
  AudioPlayerNotifier(this._ref) : super(const AudioPlayerState.initial());

  final Ref _ref;
  AudioPlayer? _audioPlayer;

  Future<void> playStation(RadioStation station) async {
    state = AudioPlayerState.loading(station);
    
    try {
      _audioPlayer ??= AudioPlayer();
      await _audioPlayer!.setUrl(station.streamUrl);
      await _audioPlayer!.play();
      
      state = AudioPlayerState.playing(station);
      
      // 更新其他相关状态
      _ref.read(currentStationProvider.notifier).state = station;
      
    } catch (e) {
      state = AudioPlayerState.error(e.toString());
    }
  }

  Future<void> pause() async {
    if (_audioPlayer != null) {
      await _audioPlayer!.pause();
      state = state.copyWith(isPlaying: false);
    }
  }

  @override
  void dispose() {
    _audioPlayer?.dispose();
    super.dispose();
  }
}

// 状态类定义
@freezed
class AudioPlayerState with _$AudioPlayerState {
  const factory AudioPlayerState.initial() = _Initial;
  const factory AudioPlayerState.loading(RadioStation station) = _Loading;
  const factory AudioPlayerState.playing(RadioStation station) = _Playing;
  const factory AudioPlayerState.paused(RadioStation station) = _Paused;
  const factory AudioPlayerState.error(String message) = _Error;
}
```

#### Consumer 使用规范
```dart
// ✅ 正确的 Consumer 使用
class StationList extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stationsAsync = ref.watch(stationsProvider);
    
    return stationsAsync.when(
      data: (stations) => ListView.builder(
        itemCount: stations.length,
        itemBuilder: (context, index) {
          return StationListItem(
            station: stations[index],
            onTap: () => _playStation(ref, stations[index]),
          );
        },
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => ErrorWidget(error: error.toString()),
    );
  }

  void _playStation(WidgetRef ref, RadioStation station) {
    ref.read(audioPlayerProvider.notifier).playStation(station);
  }
}

// ✅ 使用 Consumer 进行局部更新
class PlayButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final playerState = ref.watch(audioPlayerProvider);
        
        return IconButton(
          onPressed: () {
            if (playerState.isPlaying) {
              ref.read(audioPlayerProvider.notifier).pause();
            } else {
              ref.read(audioPlayerProvider.notifier).resume();
            }
          },
          icon: Icon(
            playerState.isPlaying ? Icons.pause : Icons.play_arrow,
          ),
        );
      },
    );
  }
}
```

### 状态同步规范
```dart
// ✅ 正确的状态同步
class AudioService {
  static final _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final _stateController = StreamController<AudioPlayerState>.broadcast();
  Stream<AudioPlayerState> get stateStream => _stateController.stream;

  void updateState(AudioPlayerState state) {
    _stateController.add(state);
    // 同步到 Riverpod
    if (_ref != null) {
      _ref!.read(audioPlayerProvider.notifier).updateState(state);
    }
  }
}
```

## ⚡ 性能优化规范

### 构建优化

#### const 构造函数使用
```dart
// ✅ 尽可能使用 const
class AppTheme {
  static const primaryColor = Color(0xFF2196F3);
  static const secondaryColor = Color(0xFF03DAC6);
  
  static const textTheme = TextTheme(
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.bold,
    ),
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.normal,
    ),
  );
}

// Widget 中使用 const
class StationCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Card(
      margin: EdgeInsets.all(8), // const
      child: Padding(
        padding: EdgeInsets.all(16), // const
        child: Text('Station Name'),
      ),
    );
  }
}
```

#### ListView 优化
```dart
// ✅ 大列表使用 ListView.builder
class StationList extends StatelessWidget {
  final List<RadioStation> stations;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: stations.length,
      itemBuilder: (context, index) {
        return StationListItem(
          key: ValueKey(stations[index].id), // 使用稳定的 key
          station: stations[index],
        );
      },
    );
  }
}

// ✅ 分页加载
class InfiniteStationList extends StatefulWidget {
  @override
  _InfiniteStationListState createState() => _InfiniteStationListState();
}

class _InfiniteStationListState extends State<InfiniteStationList> {
  final ScrollController _scrollController = ScrollController();
  final List<RadioStation> _stations = [];
  bool _isLoading = false;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadStations();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent * 0.8) {
      _loadMoreStations();
    }
  }

  Future<void> _loadMoreStations() async {
    if (_isLoading) return;
    
    setState(() => _isLoading = true);
    
    try {
      final newStations = await StationRepository.fetchStations(
        page: _currentPage + 1,
      );
      
      setState(() {
        _stations.addAll(newStations);
        _currentPage++;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }
}
```

#### 图片优化
```dart
// ✅ 图片缓存和优化
class OptimizedNetworkImage extends StatelessWidget {
  const OptimizedNetworkImage({
    super.key,
    required this.imageUrl,
    required this.width,
    required this.height,
  });

  final String imageUrl;
  final double width;
  final double height;

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: const Icon(Icons.error),
      ),
      memCacheWidth: (width * MediaQuery.of(context).devicePixelRatio).round(),
      memCacheHeight: (height * MediaQuery.of(context).devicePixelRatio).round(),
    );
  }
}
```

### 内存管理
```dart
// ✅ 正确的内存管理
class AudioPlayerPage extends StatefulWidget {
  @override
  _AudioPlayerPageState createState() => _AudioPlayerPageState();
}

class _AudioPlayerPageState extends State<AudioPlayerPage> {
  late AudioPlayer _player;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<PlayerState>? _stateSubscription;
  Timer? _progressTimer;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    _player = AudioPlayer();
    
    // 监听播放位置
    _positionSubscription = _player.positionStream.listen((position) {
      if (mounted) {
        setState(() {
          // 更新进度
        });
      }
    });

    // 监听播放状态
    _stateSubscription = _player.playerStateStream.listen((state) {
      if (mounted) {
        setState(() {
          // 更新状态
        });
      }
    });
  }

  @override
  void dispose() {
    // ✅ 释放所有资源
    _positionSubscription?.cancel();
    _stateSubscription?.cancel();
    _progressTimer?.cancel();
    _player.dispose();
    super.dispose();
  }
}
```

## 🎨 主题和样式规范

### 主题定义
```dart
// ✅ 统一的主题定义
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF2196F3),
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: false,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.black87,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF2196F3),
        brightness: Brightness.dark,
      ),
      // ... 暗色主题配置
    );
  }
}
```

### 样式常量
```dart
// ✅ 统一的样式常量
class AppStyles {
  // 间距
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;

  // 圆角
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;

  // 阴影
  static const List<BoxShadow> shadowLight = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 8,
      offset: Offset(0, 2),
    ),
  ];

  static const List<BoxShadow> shadowMedium = [
    BoxShadow(
      color: Color(0x26000000),
      blurRadius: 16,
      offset: Offset(0, 4),
    ),
  ];

  // 文本样式
  static const TextStyle headingLarge = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    height: 1.2,
  );

  static const TextStyle headingMedium = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    height: 1.4,
  );
}
```

### 响应式设计
```dart
// ✅ 响应式设计工具
class ResponsiveHelper {
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 600 && width < 1200;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1200;
  }

  static double getResponsiveValue(
    BuildContext context, {
    required double mobile,
    required double tablet,
    required double desktop,
  }) {
    if (isDesktop(context)) return desktop;
    if (isTablet(context)) return tablet;
    return mobile;
  }
}

// 使用示例
class ResponsiveStationGrid extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final crossAxisCount = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 2,
      tablet: 3,
      desktop: 4,
    ).round();

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: AppStyles.spacingM,
        mainAxisSpacing: AppStyles.spacingM,
      ),
      itemBuilder: (context, index) {
        return StationCard(station: stations[index]);
      },
    );
  }
}
```

## 🌍 国际化规范

### 文本资源管理
```dart
// ✅ 正确的国际化使用
class LocalizedText extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text('app_name'.tr()), // 简单文本
        Text('welcome_message'.tr(args: ['用户名'])), // 带参数
        Text('item_count'.plural(itemCount)), // 复数形式
      ],
    );
  }
}

// assets/lang/en.json
{
  "app_name": "World Tune",
  "welcome_message": "Welcome, {}!",
  "item_count": {
    "zero": "No items",
    "one": "1 item",
    "other": "{} items"
  },
  "player": {
    "play": "Play",
    "pause": "Pause",
    "stop": "Stop"
  }
}

// assets/lang/zh.json
{
  "app_name": "世界调频",
  "welcome_message": "欢迎，{}！",
  "item_count": {
    "zero": "没有项目",
    "one": "1个项目",
    "other": "{}个项目"
  },
  "player": {
    "play": "播放",
    "pause": "暂停",
    "stop": "停止"
  }
}
```

### 日期和数字格式化
```dart
// ✅ 本地化格式化
class LocalizedFormatters {
  static String formatDate(DateTime date, BuildContext context) {
    final locale = Localizations.localeOf(context);
    return DateFormat.yMMMd(locale.toString()).format(date);
  }

  static String formatTime(DateTime time, BuildContext context) {
    final locale = Localizations.localeOf(context);
    return DateFormat.Hm(locale.toString()).format(time);
  }

  static String formatNumber(num number, BuildContext context) {
    final locale = Localizations.localeOf(context);
    return NumberFormat.decimalPattern(locale.toString()).format(number);
  }

  static String formatCurrency(double amount, BuildContext context) {
    final locale = Localizations.localeOf(context);
    return NumberFormat.currency(locale: locale.toString()).format(amount);
  }
}
```

## 🧪 测试规范

### Widget 测试
```dart
// ✅ 完整的 Widget 测试
void main() {
  group('StationCard Widget Tests', () {
    testWidgets('should display station information correctly', (tester) async {
      // Arrange
      const station = RadioStation(
        id: '1',
        name: 'Test Station',
        country: 'Test Country',
        imageUrl: 'https://example.com/image.jpg',
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StationCard(station: station),
          ),
        ),
      );

      // Assert
      expect(find.text('Test Station'), findsOneWidget);
      expect(find.text('Test Country'), findsOneWidget);
      expect(find.byType(CachedNetworkImage), findsOneWidget);
    });

    testWidgets('should handle tap events', (tester) async {
      // Arrange
      bool tapped = false;
      const station = RadioStation(id: '1', name: 'Test');

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: StationCard(
            station: station,
            onTap: () => tapped = true,
          ),
        ),
      );

      await tester.tap(find.byType(StationCard));
      await tester.pump();

      // Assert
      expect(tapped, isTrue);
    });
  });
}
```

### Provider 测试
```dart
// ✅ Provider 测试
void main() {
  group('AudioPlayerNotifier Tests', () {
    late ProviderContainer container;
    late MockAudioPlayer mockAudioPlayer;

    setUp(() {
      mockAudioPlayer = MockAudioPlayer();
      container = ProviderContainer(
        overrides: [
          audioPlayerProvider.overrideWith(
            (ref) => AudioPlayerNotifier(ref, mockAudioPlayer),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('should play station successfully', () async {
      // Arrange
      const station = RadioStation(id: '1', name: 'Test');
      when(mockAudioPlayer.setUrl(any)).thenAnswer((_) async {});
      when(mockAudioPlayer.play()).thenAnswer((_) async {});

      // Act
      await container.read(audioPlayerProvider.notifier).playStation(station);

      // Assert
      final state = container.read(audioPlayerProvider);
      expect(state, isA<AudioPlayerStatePlaying>());
      verify(mockAudioPlayer.setUrl(station.streamUrl)).called(1);
      verify(mockAudioPlayer.play()).called(1);
    });
  });
}
```

## 🔧 调试和开发工具

### Flutter Inspector 使用
```dart
// ✅ 为调试添加有用的信息
class StationCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      // 添加调试信息
      child: Builder(
        builder: (context) {
          // 在调试模式下添加额外信息
          if (kDebugMode) {
            return Tooltip(
              message: 'Station: ${station.name}\nID: ${station.id}',
              child: _buildCardContent(),
            );
          }
          return _buildCardContent();
        },
      ),
    );
  }
}
```

### 性能监控
```dart
// ✅ 性能监控工具
class PerformanceMonitor {
  static void trackWidgetBuild(String widgetName) {
    if (kDebugMode) {
      final stopwatch = Stopwatch()..start();

      // 在 build 完成后记录时间
      WidgetsBinding.instance.addPostFrameCallback((_) {
        stopwatch.stop();
        if (stopwatch.elapsedMilliseconds > 16) { // 超过一帧时间
          debugPrint('⚠️ Slow build: $widgetName took ${stopwatch.elapsedMilliseconds}ms');
        }
      });
    }
  }
}

// 使用示例
class SlowWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    PerformanceMonitor.trackWidgetBuild('SlowWidget');

    return Container(
      // widget 内容
    );
  }
}
```

### 错误边界
```dart
// ✅ 错误边界实现
class ErrorBoundary extends StatefulWidget {
  const ErrorBoundary({
    super.key,
    required this.child,
    this.onError,
  });

  final Widget child;
  final void Function(FlutterErrorDetails)? onError;

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  FlutterErrorDetails? _errorDetails;

  @override
  void initState() {
    super.initState();

    // 捕获 Flutter 错误
    FlutterError.onError = (details) {
      setState(() {
        _errorDetails = details;
      });
      widget.onError?.call(details);
    };
  }

  @override
  Widget build(BuildContext context) {
    if (_errorDetails != null) {
      return ErrorWidget.withDetails(
        message: 'Something went wrong',
        error: _errorDetails!.exception,
      );
    }

    return widget.child;
  }
}
```

---

**注意**: 这些规范应该与团队一起制定和维护，确保所有开发者都能理解和遵循。定期审查和更新规范以适应项目发展需要。
