# World Tune 数据库设计文档

## 📊 MySQL 表结构设计

### 1. 电台基础信息表 (world_tune_radio_stations)

```sql
CREATE TABLE `world_tune_radio_stations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `station_uuid` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '电台UUID（来自radio-browser）',
  `server_uuid` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务器UUID',
  `change_uuid` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更UUID',
  `name` varchar(1255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '电台名称',
  `url` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '播放URL',
  `url_resolved` text COLLATE utf8mb4_unicode_ci COMMENT '解析后的播放URL',
  `homepage` text COLLATE utf8mb4_unicode_ci,
  `favicon` text COLLATE utf8mb4_unicode_ci,
  `country` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家名称',
  `countrycode` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家代码',
  `iso_3166_2` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ISO 3166-2代码',
  `state` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '州/省',
  `language` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言名称',
  `languagecodes` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言代码（逗号分隔）',
  `tags` text COLLATE utf8mb4_unicode_ci COMMENT '标签（逗号分隔）',
  `codec` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '音频编码格式',
  `bitrate` int DEFAULT '0' COMMENT '比特率',
  `is_hls` tinyint(1) DEFAULT '0' COMMENT '是否为HLS流',
  `votes` int DEFAULT '0' COMMENT '投票数',
  `click_count` int DEFAULT '0' COMMENT '点击次数',
  `click_trend` int DEFAULT '0' COMMENT '点击趋势',
  `ssl_error` tinyint(1) DEFAULT '0' COMMENT '是否有SSL错误',
  `geo_lat` decimal(10,8) DEFAULT NULL COMMENT '纬度',
  `geo_long` decimal(11,8) DEFAULT NULL COMMENT '经度',
  `geo_distance` decimal(10,2) DEFAULT NULL COMMENT '地理距离',
  `has_extended_info` tinyint(1) DEFAULT '0' COMMENT '是否有扩展信息',
  `last_check_ok` tinyint(1) DEFAULT '0' COMMENT '最后检查是否正常',
  `last_change_time` datetime DEFAULT NULL COMMENT '最后变更时间',
  `last_check_time` datetime DEFAULT NULL COMMENT '最后检查时间',
  `last_check_ok_time` datetime DEFAULT NULL COMMENT '最后检查正常时间',
  `last_local_check_time` datetime DEFAULT NULL COMMENT '最后本地检查时间',
  `click_timestamp` datetime DEFAULT NULL COMMENT '最后点击时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_station_uuid` (`station_uuid`),
  KEY `idx_countrycode` (`countrycode`),
  KEY `idx_languagecodes` (`languagecodes`),
  KEY `idx_status` (`status`),
  KEY `idx_votes` (`votes`),
  KEY `idx_click_count` (`click_count`),
  KEY `idx_last_check_ok` (`last_check_ok`)
) ENGINE=InnoDB AUTO_INCREMENT=54129 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电台基础信息表';
```

### 2. 国家表 (world_tune_countries)

```sql
CREATE TABLE `world_tune_countries` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '国家名称',
  `code` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '国家代码（ISO 3166-1 alpha-2）',
  `iso_3166_2` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ISO 3166-2代码',
  `sort_order` int DEFAULT '0' COMMENT '排序权重',
  `is_show` tinyint(1) DEFAULT '1' COMMENT '状态：1-正常，0-不展示',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_code` (`name`,`code`),
  KEY `idx_name` (`name`),
  KEY `idx_code` (`code`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_show` (`is_show`)
) ENGINE=InnoDB AUTO_INCREMENT=238 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国家表';
```

### 3. 语言表 (world_tune_languages)

```sql
CREATE TABLE `world_tune_languages` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '语言名称',
  `code` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '语言代码（ISO 639）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='语言表';
```

### 4. 标签表 (world_tune_tags)

```sql
CREATE TABLE `world_tune_tags` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
  `sort_order` int DEFAULT '0' COMMENT '排序权重',
  `is_show` tinyint(1) DEFAULT '1' COMMENT '状态：1-正常，0-不展示',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_show` (`is_show`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';
```

### 5. 电台标签关联表 (world_tune_station_tags)

```sql
CREATE TABLE `world_tune_station_tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `station_id` bigint unsigned NOT NULL COMMENT '电台ID（关联world_tune_radio_stations.id）',
  `tag_id` int NOT NULL COMMENT '标签ID（关联world_tune_tags.id）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_station_tag` (`station_id`,`tag_id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB AUTO_INCREMENT=66254 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电台标签关联表';
```

### 6. 电台国家关联表 (world_tune_station_countries)

```sql
CREATE TABLE `world_tune_station_countries` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `station_id` bigint unsigned NOT NULL COMMENT '电台ID',
  `country_id` int NOT NULL COMMENT '国家ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_station_country` (`station_id`,`country_id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_country_id` (`country_id`),
  CONSTRAINT `fk_station_countries_station` FOREIGN KEY (`station_id`) REFERENCES `world_tune_radio_stations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_station_countries_country` FOREIGN KEY (`country_id`) REFERENCES `world_tune_countries` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电台国家关联表';
```

### 7. 用户表 (world_tune_users)

```sql
CREATE TABLE `world_tune_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `firebase_uid` varchar(128) COMMENT 'Firebase用户ID',
  `email` varchar(255) COMMENT '邮箱',
  `display_name` varchar(100) COMMENT '显示名称',
  `avatar_url` varchar(500) COMMENT '头像URL',
  `language` varchar(10) DEFAULT 'en' COMMENT '用户语言偏好',
  `subscription_status` tinyint(1) DEFAULT 0 COMMENT '订阅状态：0-免费，1-高级会员',
  `subscription_expires_at` datetime COMMENT '订阅到期时间',
  `last_login_at` datetime COMMENT '最后登录时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_firebase_uid` (`firebase_uid`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_subscription_status` (`subscription_status`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 8. 用户收藏表 (world_tune_user_favorites)

```sql
CREATE TABLE `world_tune_user_favorites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `station_id` bigint unsigned NOT NULL COMMENT '电台ID',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_station` (`user_id`, `station_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_favorites_user` FOREIGN KEY (`user_id`) REFERENCES `world_tune_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_favorites_station` FOREIGN KEY (`station_id`) REFERENCES `world_tune_radio_stations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';
```

### 9. 播放历史表 (world_tune_play_history)

```sql
CREATE TABLE `world_tune_play_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint unsigned COMMENT '用户ID（可为空，支持匿名用户）',
  `station_id` bigint unsigned NOT NULL COMMENT '电台ID',
  `device_id` varchar(128) COMMENT '设备ID（用于匿名用户）',
  `play_duration` int DEFAULT 0 COMMENT '播放时长（秒）',
  `ip_address` varchar(45) COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '播放时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_station_id` (`station_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_history_user` FOREIGN KEY (`user_id`) REFERENCES `world_tune_users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_history_station` FOREIGN KEY (`station_id`) REFERENCES `world_tune_radio_stations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='播放历史表';
```

### 10. 电台统计表 (world_tune_station_stats)

```sql
CREATE TABLE `world_tune_station_stats` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `station_id` bigint unsigned NOT NULL COMMENT '电台ID',
  `date` date NOT NULL COMMENT '统计日期',
  `play_count` int DEFAULT 0 COMMENT '播放次数',
  `unique_listeners` int DEFAULT 0 COMMENT '独立听众数',
  `total_duration` bigint DEFAULT 0 COMMENT '总播放时长（秒）',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_station_date` (`station_id`, `date`),
  KEY `idx_date` (`date`),
  KEY `idx_play_count` (`play_count`),
  CONSTRAINT `fk_stats_station` FOREIGN KEY (`station_id`) REFERENCES `world_tune_radio_stations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='电台统计表';
```

## 🔗 表关系说明

### 主要关系

1. **world_tune_radio_stations** ↔ **world_tune_countries** (多对多，通过world_tune_station_countries)
2. **world_tune_radio_stations** ↔ **world_tune_tags** (多对多，通过world_tune_station_tags)
3. **world_tune_users** ↔ **world_tune_radio_stations** (多对多，通过world_tune_user_favorites)
4. **world_tune_users** → **world_tune_play_history** ← **world_tune_radio_stations** (一对多)
5. **world_tune_radio_stations** → **world_tune_station_stats** (一对多)

### 数据结构特点
- **电台表**：包含完整的radio-browser.info API数据结构
- **国家关联**：支持一个电台属于多个国家（通过关联表）
- **标签系统**：灵活的多对多标签关联
- **语言支持**：直接存储语言名称和代码，支持多语言（逗号分隔）

## 📝 常用查询示例

```sql
-- 按国家获取电台
SELECT rs.*, c.name as country_name, c.code as country_code
FROM world_tune_radio_stations rs
JOIN world_tune_station_countries sc ON rs.id = sc.station_id
JOIN world_tune_countries c ON sc.country_id = c.id
WHERE c.code = 'CN' AND rs.status = 1
ORDER BY rs.votes DESC, rs.click_count DESC
LIMIT 20;

-- 按标签获取电台
SELECT rs.*, GROUP_CONCAT(t.name) as tag_names
FROM world_tune_radio_stations rs
JOIN world_tune_station_tags st ON rs.id = st.station_id
JOIN world_tune_tags t ON st.tag_id = t.id
WHERE t.name = 'music' AND rs.status = 1
GROUP BY rs.id
ORDER BY rs.votes DESC
LIMIT 20;

-- 获取用户收藏的电台
SELECT rs.*, GROUP_CONCAT(c.name) as countries
FROM world_tune_radio_stations rs
JOIN world_tune_user_favorites uf ON rs.id = uf.station_id
LEFT JOIN world_tune_station_countries sc ON rs.id = sc.station_id
LEFT JOIN world_tune_countries c ON sc.country_id = c.id
WHERE uf.user_id = ? AND rs.status = 1
GROUP BY rs.id
ORDER BY uf.created_at DESC;

-- 搜索电台（支持名称、国家、语言搜索）
SELECT rs.*, GROUP_CONCAT(DISTINCT c.name) as countries,
       GROUP_CONCAT(DISTINCT t.name) as tags
FROM world_tune_radio_stations rs
LEFT JOIN world_tune_station_countries sc ON rs.id = sc.station_id
LEFT JOIN world_tune_countries c ON sc.country_id = c.id
LEFT JOIN world_tune_station_tags st ON rs.id = st.station_id
LEFT JOIN world_tune_tags t ON st.tag_id = t.id
WHERE (rs.name LIKE '%关键词%'
       OR rs.country LIKE '%关键词%'
       OR rs.language LIKE '%关键词%')
  AND rs.status = 1
GROUP BY rs.id
ORDER BY rs.votes DESC, rs.click_count DESC
LIMIT 20;

-- 按国家统计电台数量
SELECT c.name as country_name, c.code, COUNT(sc.station_id) as station_count
FROM world_tune_countries c
LEFT JOIN world_tune_station_countries sc ON c.id = sc.country_id
LEFT JOIN world_tune_radio_stations rs ON sc.station_id = rs.id AND rs.status = 1
GROUP BY c.id, c.name, c.code
ORDER BY station_count DESC;
```

## 🔄 数据迁移说明

### 从旧结构迁移到新结构
1. **电台表字段变更**：
   - 移除 `country_id` 和 `language_id` 外键
   - 新增 `country`、`countrycode`、`language`、`languagecodes` 等字段
   - 新增 `geo_distance`、`has_extended_info`、`last_local_check_time` 等字段

2. **关联表调整**：
   - 新增 `world_tune_station_countries` 关联表
   - 保留 `world_tune_languages` 表用于语言管理

3. **索引优化**：
   - 针对新的查询模式优化索引结构
   - 支持按国家代码、语言代码快速查询
