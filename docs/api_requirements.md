# World Tune API 需求文档

## 📡 API 概述

### 基础信息
- **Base URL**: `https://api.worldtune.com/v1`
- **认证方式**: <PERSON><PERSON> (Firebase JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-01-08T10:30:00Z"
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "Invalid parameters",
  "error": "详细错误信息",
  "timestamp": "2025-01-08T10:30:00Z"
}
```

## 🎵 电台相关API

### 1. 获取电台列表
```
GET /stations
```

**查询参数**:
- `page` (int): 页码，默认1
- `limit` (int): 每页数量，默认20，最大100
- `country` (string): 国家代码过滤（支持多个，逗号分隔）
- `countrycode` (string): 国家代码过滤（ISO 3166-1 alpha-2）
- `language` (string): 语言名称过滤
- `languagecodes` (string): 语言代码过滤（支持多个，逗号分隔）
- `tag` (string): 标签过滤（支持多个，逗号分隔）
- `search` (string): 搜索关键词（搜索电台名称、国家、语言）
- `sort` (string): 排序方式 (votes, click_count, name, created_at, bitrate)
- `order` (string): 排序顺序 (asc, desc)
- `codec` (string): 音频编码格式过滤
- `bitrate_min` (int): 最小比特率
- `bitrate_max` (int): 最大比特率
- `is_hls` (boolean): 是否为HLS流
- `last_check_ok` (boolean): 是否最后检查正常

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "stations": [
      {
        "id": 1,
        "station_uuid": "63151721-3f42-4515-89e1-51b4ff35aad5",
        "server_uuid": "server-uuid-here",
        "change_uuid": "change-uuid-here",
        "name": "黑龙江都市·女性广播",
        "url": "https://lhttp-hw.qtfm.cn/live/4968/64k.mp3",
        "url_resolved": "https://lhttp-hw.qtfm.cn/live/4968/64k.mp3",
        "homepage": "https://www.hljtv.com/",
        "favicon": "https://pic.qtfm.cn/2014/0330/20140330112742795.jpg",
        "country": "China",
        "countrycode": "CN",
        "iso_3166_2": "CN-HL",
        "state": "Heilongjiang",
        "language": "chinese",
        "languagecodes": "zh",
        "tags": ["lifestyle", "entertainment"],
        "countries": [
          {
            "id": 1,
            "name": "China",
            "code": "CN"
          }
        ],
        "votes": 29,
        "click_count": 29,
        "click_trend": -2,
        "codec": "MP3",
        "bitrate": 64,
        "is_hls": false,
        "ssl_error": false,
        "geo_lat": 45.7536,
        "geo_long": 126.6480,
        "geo_distance": null,
        "has_extended_info": false,
        "last_check_ok": true,
        "last_change_time": "2025-06-13T00:54:54Z",
        "last_check_time": "2025-07-07T03:50:14Z",
        "last_check_ok_time": "2025-07-07T03:50:14Z",
        "last_local_check_time": null,
        "click_timestamp": "2025-07-07T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 1500,
      "total_pages": 75
    }
  }
}
```

### 2. 获取电台详情
```
GET /stations/{station_id}
```

**路径参数**:
- `station_id` (int): 电台ID

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "station_uuid": "63151721-3f42-4515-89e1-51b4ff35aad5",
    "server_uuid": "server-uuid-here",
    "change_uuid": "change-uuid-here",
    "name": "黑龙江都市·女性广播",
    "url": "https://lhttp-hw.qtfm.cn/live/4968/64k.mp3",
    "url_resolved": "https://lhttp-hw.qtfm.cn/live/4968/64k.mp3",
    "homepage": "https://www.hljtv.com/",
    "favicon": "https://pic.qtfm.cn/2014/0330/20140330112742795.jpg",
    "country": "China",
    "countrycode": "CN",
    "iso_3166_2": "CN-HL",
    "state": "Heilongjiang",
    "language": "chinese",
    "languagecodes": "zh",
    "tags": ["lifestyle", "entertainment"],
    "countries": [
      {
        "id": 1,
        "name": "China",
        "code": "CN",
        "iso_3166_2": "CN-HL"
      }
    ],
    "tag_details": [
      {
        "id": 1,
        "name": "lifestyle",
        "sort_order": 10
      },
      {
        "id": 2,
        "name": "entertainment",
        "sort_order": 20
      }
    ],
    "votes": 29,
    "click_count": 29,
    "click_trend": -2,
    "codec": "MP3",
    "bitrate": 64,
    "is_hls": false,
    "ssl_error": false,
    "geo_lat": 45.7536,
    "geo_long": 126.6480,
    "geo_distance": null,
    "has_extended_info": false,
    "last_check_ok": true,
    "last_change_time": "2025-06-13T00:54:54Z",
    "last_check_time": "2025-07-07T03:50:14Z",
    "last_check_ok_time": "2025-07-07T03:50:14Z",
    "last_local_check_time": null,
    "click_timestamp": "2025-07-07T10:30:00Z",
    "status": 1,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-08T10:30:00Z"
  }
}
```

### 3. 获取分类电台
```
GET /stations/categories
```

**查询参数**:
- `limit` (int): 每个分类返回的电台数量，默认10

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "music",
        "sort_order": 10,
        "station_count": 500,
        "stations": [
          {
            "id": 1,
            "station_uuid": "uuid-here",
            "name": "Music Station 1",
            "favicon": "https://example.com/favicon.jpg",
            "country": "China",
            "countrycode": "CN",
            "votes": 100,
            "click_count": 50,
            "codec": "MP3",
            "bitrate": 128,
            "is_hls": false
          }
        ]
      }
    ]
  }
}
```

### 4. 按国家获取电台
```
GET /stations/by-country/{country_code}
```

**路径参数**:
- `country_code` (string): 国家代码（ISO 3166-1 alpha-2）

**查询参数**:
- `page` (int): 页码，默认1
- `limit` (int): 每页数量，默认20
- `sort` (string): 排序方式
- `order` (string): 排序顺序

### 5. 按标签获取电台
```
GET /stations/by-tag/{tag_name}
```

**路径参数**:
- `tag_name` (string): 标签名称

**查询参数**:
- `page` (int): 页码，默认1
- `limit` (int): 每页数量，默认20
- `sort` (string): 排序方式
- `order` (string): 排序顺序

## 👤 用户相关API

### 1. 用户注册/登录
```
POST /auth/login
```

**请求体**:
```json
{
  "firebase_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "device_info": {
    "device_id": "unique_device_id",
    "platform": "ios",
    "app_version": "1.0.0"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "user": {
      "id": 1,
      "firebase_uid": "firebase_user_id",
      "email": "<EMAIL>",
      "display_name": "User Name",
      "avatar_url": "https://example.com/avatar.jpg",
      "language": "zh",
      "subscription_status": 0,
      "subscription_expires_at": null
    },
    "access_token": "jwt_access_token",
    "refresh_token": "jwt_refresh_token"
  }
}
```

### 2. 获取用户信息
```
GET /user/profile
```

**Headers**: `Authorization: Bearer {access_token}`

### 3. 更新用户信息
```
PUT /user/profile
```

**请求体**:
```json
{
  "display_name": "New Name",
  "language": "en"
}
```

## ⭐ 收藏相关API

### 1. 获取用户收藏
```
GET /user/favorites
```

**查询参数**:
- `page` (int): 页码
- `limit` (int): 每页数量

### 2. 添加收藏
```
POST /user/favorites
```

**请求体**:
```json
{
  "station_id": 1
}
```

### 3. 取消收藏
```
DELETE /user/favorites/{station_id}
```

## 📊 播放历史API

### 1. 记录播放历史
```
POST /user/play-history
```

**请求体**:
```json
{
  "station_id": 1,
  "play_duration": 300,
  "device_id": "unique_device_id"
}
```

### 2. 获取播放历史
```
GET /user/play-history
```

**查询参数**:
- `page` (int): 页码
- `limit` (int): 每页数量，默认20

## 🏷️ 基础数据API

### 1. 获取国家列表
```
GET /countries
```

**查询参数**:
- `is_show` (boolean): 是否只返回显示的国家，默认true
- `sort` (string): 排序方式 (name, code, sort_order)
- `order` (string): 排序顺序 (asc, desc)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "countries": [
      {
        "id": 1,
        "name": "China",
        "code": "CN",
        "iso_3166_2": "CN",
        "sort_order": 10,
        "station_count": 1500
      }
    ]
  }
}
```

### 2. 获取标签列表
```
GET /tags
```

**查询参数**:
- `is_show` (boolean): 是否只返回显示的标签，默认true
- `sort` (string): 排序方式 (name, sort_order)
- `order` (string): 排序顺序 (asc, desc)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tags": [
      {
        "id": 1,
        "name": "music",
        "sort_order": 10,
        "is_show": true,
        "station_count": 500
      }
    ]
  }
}
```

### 3. 获取语言统计
```
GET /languages
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "languages": [
      {
        "language": "chinese",
        "languagecodes": "zh",
        "station_count": 800
      },
      {
        "language": "english",
        "languagecodes": "en",
        "station_count": 1200
      }
    ]
  }
}
```

## 📈 统计相关API

### 1. 获取热门电台
```
GET /stations/trending
```

**查询参数**:
- `period` (string): 时间周期 (day, week, month)，默认week
- `limit` (int): 数量限制，默认20
- `country` (string): 国家代码过滤
- `tag` (string): 标签过滤

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "period": "week",
    "stations": [
      {
        "id": 1,
        "station_uuid": "uuid-here",
        "name": "热门电台",
        "favicon": "https://example.com/favicon.jpg",
        "country": "China",
        "countrycode": "CN",
        "votes": 1000,
        "click_count": 5000,
        "click_trend": 150,
        "codec": "MP3",
        "bitrate": 128
      }
    ]
  }
}
```

### 2. 获取推荐电台
```
GET /stations/recommended
```

**Headers**: `Authorization: Bearer {access_token}` (可选)

**查询参数**:
- `limit` (int): 数量限制，默认20
- `user_location` (string): 用户位置（国家代码）
- `user_language` (string): 用户语言偏好

### 3. 电台点击统计
```
POST /stations/{station_id}/click
```

**请求体**:
```json
{
  "device_id": "unique_device_id",
  "user_agent": "Mozilla/5.0...",
  "ip_address": "***********"
}
```

### 4. 获取电台统计信息
```
GET /stations/{station_id}/stats
```

**查询参数**:
- `period` (string): 统计周期 (day, week, month)
- `start_date` (string): 开始日期 (YYYY-MM-DD)
- `end_date` (string): 结束日期 (YYYY-MM-DD)

### 5. 搜索电台
```
GET /stations/search
```

**查询参数**:
- `q` (string): 搜索关键词（必填）
- `page` (int): 页码，默认1
- `limit` (int): 每页数量，默认20
- `country` (string): 国家代码过滤
- `language` (string): 语言过滤
- `tag` (string): 标签过滤
- `sort` (string): 排序方式
- `order` (string): 排序顺序

### 6. 获取相似电台
```
GET /stations/{station_id}/similar
```

**查询参数**:
- `limit` (int): 数量限制，默认10
- `algorithm` (string): 推荐算法 (tag_based, country_based, mixed)，默认mixed

## 🔧 系统API

### 1. 健康检查
```
GET /health
```

### 2. 获取应用配置
```
GET /config
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "app_version": "1.0.0",
    "min_supported_version": "1.0.0",
    "features": {
      "subscription_enabled": true,
      "social_login_enabled": true
    },
    "cdn_base_url": "https://cdn.worldtune.com"
  }
}
```

## 🔐 认证和权限

### 认证方式
1. **Firebase JWT**: 用于用户身份验证
2. **设备ID**: 用于匿名用户追踪
3. **API Key**: 用于客户端应用识别

### 权限级别
- **公开**: 无需认证即可访问
- **用户**: 需要用户登录
- **高级用户**: 需要订阅会员
- **管理员**: 需要管理员权限

## 📝 API使用说明

### 分页参数
- 所有列表API都支持分页
- 默认每页20条记录
- 最大每页100条记录

### 错误码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率限制
- `500`: 服务器内部错误

### 请求频率限制
- 匿名用户: 100次/小时
- 注册用户: 1000次/小时
- 高级用户: 5000次/小时
