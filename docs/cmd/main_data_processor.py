#!/usr/bin/env python3
"""
World Tune 数据处理主控脚本
支持串行执行所有数据处理脚本，也可以单独执行某个脚本
支持参数传递、错误处理、进度追踪等功能

使用示例:
    # 执行所有脚本
    python main_data_processor.py --all
    
    # 执行单个脚本
    python main_data_processor.py --script data_import
    
    # 传递特定参数
    python main_data_processor.py --all --data-import-args "--method all --batch-size 500"
    
    # 试运行模式
    python main_data_processor.py --all --countries-args "--dry-run" --station-countries-args "--dry-run"
"""

import subprocess
import argparse
import sys
import os
from datetime import datetime
from config import (setup_logging, BusinessType, log_script_start, log_script_end,
                   init_environment, get_environment_info)

# 定义脚本执行顺序和配置
SCRIPTS = [
    {
        'name': 'data_import',
        'script': 'data_import.py',
        'description': '导入电台数据（含质量检查）',
        'required_args': [],
        'optional_args': ['--method', '--page-size', '--batch-size', '--skip-quality-check', '--check-existing-only', '--historical-cutoff'],
        'default_args': ['--method', 'all']  # 默认导入新数据+检查历史数据
    },
    {
        'name': 'process_countries',
        'script': 'process_countries.py',
        'description': '处理国家数据（含关联关系）',
        'required_args': [],
        'optional_args': ['--batch-size', '--page-size', '--dry-run'],
        'default_args': []
    },
    {
        'name': 'process_tags',
        'script': 'process_tags.py',
        'description': '处理标签数据',
        'required_args': [],
        'optional_args': ['--use'],
        'default_args': ['--use', '900']  # 默认阈值900
    },
    {
        'name': 'rebuild_station_tags',
        'script': 'rebuild_station_tags.py',
        'description': '重建电台标签关联',
        'required_args': [],
        'optional_args': ['--batch-size', '--skip-clear'],
        'default_args': []
    }
]

# 独立维护脚本（不在正常流程中，但可以单独调用）
MAINTENANCE_SCRIPTS = [
    {
        'name': 'rebuild_station_countries',
        'script': 'rebuild_station_countries.py',
        'description': '重建电台国家关联（维护用）',
        'required_args': [],
        'optional_args': ['--batch-size', '--page-size', '--use-truncate', '--dry-run'],
        'default_args': ['--use-truncate']  # 维护模式默认使用TRUNCATE重建
    }
]

class DataProcessingController:
    def __init__(self, env='production', verbose=False, continue_on_error=False):
        self.env = env
        self.verbose = verbose
        self.continue_on_error = continue_on_error
        self.results = {}
        
    def execute_script(self, script_config, extra_args=None):
        """执行单个脚本"""
        script_name = script_config['name']
        script_file = script_config['script']
        
        # 构建命令
        cmd = ['python3', script_file, '--env', self.env]
        
        # 添加默认参数
        if script_config.get('default_args'):
            cmd.extend(script_config['default_args'])
        
        # 添加用户指定的额外参数
        if extra_args:
            cmd.extend(extra_args)
        
        logger.info(f"🚀 开始执行: {script_config['description']} ({script_file})")
        logger.info(f"📝 执行命令: {' '.join(cmd)}")
        
        try:
            start_time = datetime.now()
            
            # 执行脚本
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 记录结果
            self.results[script_name] = {
                'success': result.returncode == 0,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'command': ' '.join(cmd),
                'return_code': result.returncode
            }
            
            if result.returncode == 0:
                logger.info(f"✅ {script_config['description']} 执行成功 (耗时: {duration:.2f}s)")
                if self.verbose and result.stdout:
                    logger.info(f"📤 标准输出:\n{result.stdout}")
            else:
                logger.error(f"❌ {script_config['description']} 执行失败 (返回码: {result.returncode}, 耗时: {duration:.2f}s)")
                logger.error(f"💢 错误输出:\n{result.stderr}")
                if result.stdout:
                    logger.info(f"📤 标准输出:\n{result.stdout}")
                return False
                
            return True
            
        except FileNotFoundError:
            error_msg = f"未找到脚本文件: {script_file}"
            logger.error(f"❌ {error_msg}")
            self.results[script_name] = {
                'success': False,
                'duration': 0,
                'error': error_msg,
                'command': ' '.join(cmd)
            }
            return False
        except Exception as e:
            error_msg = f"执行 {script_config['description']} 时发生异常: {e}"
            logger.error(f"❌ {error_msg}")
            self.results[script_name] = {
                'success': False,
                'duration': 0,
                'error': error_msg,
                'command': ' '.join(cmd)
            }
            return False
    
    def execute_all(self, script_args=None):
        """串行执行所有脚本"""
        logger.info("=" * 80)
        logger.info("🎯 开始执行完整的数据处理流程")
        logger.info(f"📋 处理顺序: {' -> '.join([s['description'] for s in SCRIPTS])}")
        logger.info(f"🌍 运行环境: {self.env}")
        logger.info(f"⚙️  错误处理策略: {'继续执行' if self.continue_on_error else '遇错停止'}")
        logger.info("=" * 80)
        
        success_count = 0
        total_start_time = datetime.now()
        
        for i, script_config in enumerate(SCRIPTS, 1):
            logger.info(f"\n📍 步骤 {i}/{len(SCRIPTS)}: {script_config['description']}")
            
            # 获取该脚本的特定参数
            extra_args = script_args.get(script_config['name'], []) if script_args else []
            
            success = self.execute_script(script_config, extra_args)
            
            if success:
                success_count += 1
                logger.info(f"   ✅ 步骤 {i} 完成")
            else:
                logger.error(f"   ❌ 步骤 {i} 失败")
                if not self.continue_on_error:
                    logger.error(f"🛑 步骤 {i} 失败，停止后续执行")
                    break
                else:
                    logger.warning(f"⚠️  步骤 {i} 失败，但继续执行后续步骤")
        
        total_end_time = datetime.now()
        total_duration = (total_end_time - total_start_time).total_seconds()
        
        # 输出最终摘要
        self.print_summary(success_count, len(SCRIPTS), total_duration)
        
        return success_count == len(SCRIPTS)
    
    def execute_single(self, script_name, extra_args=None):
        """执行单个脚本"""
        script_config = None
        # 在正常脚本中查找
        for script in SCRIPTS:
            if script['name'] == script_name:
                script_config = script
                break
        
        # 如果没找到，在维护脚本中查找
        if not script_config:
            for script in MAINTENANCE_SCRIPTS:
                if script['name'] == script_name:
                    script_config = script
                    logger.warning(f"⚠️  注意：{script_name} 是维护脚本，不在正常流程中")
                    break
        
        if not script_config:
            all_scripts = [s['name'] for s in SCRIPTS] + [s['name'] for s in MAINTENANCE_SCRIPTS]
            logger.error(f"❌ 未找到脚本: {script_name}")
            logger.info(f"📚 可用的脚本: {', '.join(all_scripts)}")
            return False
        
        logger.info(f"🎯 执行单个脚本: {script_config['description']}")
        success = self.execute_script(script_config, extra_args)
        
        # 输出单个脚本的摘要
        if success:
            logger.info(f"✅ 脚本 {script_name} 执行成功")
        else:
            logger.error(f"❌ 脚本 {script_name} 执行失败")
        
        return success
    
    def print_summary(self, success_count, total_count, total_duration):
        """打印执行摘要"""
        logger.info("\n" + "=" * 80)
        logger.info("📊 执行摘要")
        logger.info("=" * 80)
        logger.info(f"⏱️  总执行时间: {total_duration:.2f} 秒 ({total_duration/60:.2f} 分钟)")
        logger.info(f"📈 成功脚本数: {success_count}/{total_count}")
        logger.info(f"📉 失败脚本数: {total_count - success_count}/{total_count}")
        
        logger.info("\n📋 详细结果:")
        for script_name, result in self.results.items():
            status = "✅ 成功" if result['success'] else "❌ 失败"
            duration = result.get('duration', 0)
            logger.info(f"  📌 {script_name}: {status} (耗时: {duration:.2f}s)")
            
            if not result['success'] and result.get('error'):
                logger.info(f"     💢 错误: {result['error']}")
        
        # 总结状态
        if success_count == total_count:
            logger.info("\n🎉 所有脚本执行成功！数据处理流程完成。")
        elif success_count == 0:
            logger.error("\n💥 所有脚本执行失败！请检查配置和环境。")
        else:
            logger.warning(f"\n⚠️  部分脚本执行失败 ({total_count - success_count} 个失败)")
            logger.info("建议：")
            for script_name, result in self.results.items():
                if not result['success']:
                    logger.info(f"  - 检查 {script_name} 的错误日志和配置")
        
        logger.info("=" * 80)
    
    def list_scripts(self):
        """列出所有可用的脚本"""
        logger.info("📚 正常流程脚本:")
        for i, script in enumerate(SCRIPTS, 1):
            logger.info(f"  {i}. {script['name']}: {script['description']}")
            logger.info(f"     文件: {script['script']}")
            if script.get('default_args'):
                logger.info(f"     默认参数: {' '.join(script['default_args'])}")
            if script.get('optional_args'):
                logger.info(f"     可选参数: {', '.join(script['optional_args'])}")
            logger.info("")
        
        if MAINTENANCE_SCRIPTS:
            logger.info("🔧 维护脚本（用于问题修复，不在正常流程中）:")
            for i, script in enumerate(MAINTENANCE_SCRIPTS, 1):
                logger.info(f"  M{i}. {script['name']}: {script['description']}")
                logger.info(f"      文件: {script['script']}")
                if script.get('default_args'):
                    logger.info(f"      默认参数: {' '.join(script['default_args'])}")
                if script.get('optional_args'):
                    logger.info(f"      可选参数: {', '.join(script['optional_args'])}")
                logger.info("")

def parse_script_args(args_string):
    """解析脚本参数字符串"""
    if not args_string:
        return []
    
    # 简单的参数解析，支持引号
    import shlex
    try:
        return shlex.split(args_string)
    except ValueError as e:
        logger.warning(f"参数解析警告: {e}，使用简单分割")
        return args_string.split()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='World Tune 数据处理主控脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  执行所有脚本:
    python main_data_processor.py --all

  执行单个脚本:
    python main_data_processor.py --script data_import

  传递特定参数:
    python main_data_processor.py --all --data-import-args "--method all --batch-size 500"

  试运行模式:
    python main_data_processor.py --all --countries-args "--dry-run" --station-countries-args "--dry-run"

  继续执行模式:
    python main_data_processor.py --all --continue-on-error

  列出所有脚本:
    python main_data_processor.py --list
        """
    )
    
    parser.add_argument("--env", choices=['development', 'production'], 
                       default='production', help="运行环境 (默认: production)")
    parser.add_argument("--verbose", action='store_true', help="详细输出模式")
    parser.add_argument("--continue-on-error", action='store_true', 
                       help="遇到错误时继续执行后续脚本")
    
    # 执行模式选择
    all_script_names = [s['name'] for s in SCRIPTS] + [s['name'] for s in MAINTENANCE_SCRIPTS]
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument("--all", action='store_true', help="执行所有正常流程脚本")
    mode_group.add_argument("--script", choices=all_script_names, 
                           help="执行单个脚本（包括维护脚本）")
    mode_group.add_argument("--list", action='store_true', help="列出所有可用的脚本")
    
    # 各脚本的特定参数
    parser.add_argument("--data-import-args", type=str,
                       help='传递给data_import.py的额外参数 (用引号包围)')
    parser.add_argument("--countries-args", type=str,
                       help='传递给process_countries.py的额外参数 (用引号包围)')
    parser.add_argument("--tags-args", type=str,
                       help='传递给process_tags.py的额外参数 (用引号包围)')
    parser.add_argument("--station-countries-args", type=str,
                       help='传递给rebuild_station_countries.py的额外参数 (用引号包围)')
    parser.add_argument("--station-tags-args", type=str,
                       help='传递给rebuild_station_tags.py的额外参数 (用引号包围)')
    
    args = parser.parse_args()
    
    # 检查必需的执行模式
    if not any([args.all, args.script, args.list]):
        parser.error("必须指定执行模式: --all, --script <name>, 或 --list")
    
    # 初始化环境和日志
    try:
        current_env = init_environment()
        env_info = get_environment_info()
        global logger
        logger = setup_logging(BusinessType.SYSTEM, advanced=False, logger_name="DataProcessingController")
    except Exception as e:
        print(f"❌ 环境配置初始化失败: {e}")
        return 1
    
    # 如果只是列出脚本，直接返回
    if args.list:
        controller = DataProcessingController(env=args.env, verbose=args.verbose)
        controller.list_scripts()
        return 0
    
    # 解析脚本特定参数
    script_args = {}
    if args.data_import_args:
        script_args['data_import'] = parse_script_args(args.data_import_args)
    if args.countries_args:
        script_args['process_countries'] = parse_script_args(args.countries_args)
    if args.tags_args:
        script_args['process_tags'] = parse_script_args(args.tags_args)
    if args.station_countries_args:
        script_args['rebuild_station_countries'] = parse_script_args(args.station_countries_args)
    if args.station_tags_args:
        script_args['rebuild_station_tags'] = parse_script_args(args.station_tags_args)
    
    # 创建控制器
    controller = DataProcessingController(
        env=args.env, 
        verbose=args.verbose,
        continue_on_error=args.continue_on_error
    )
    
    # 记录脚本开始
    script_name = "数据处理主控脚本"
    mode = "全部脚本" if args.all else f"单个脚本: {args.script}"
    description = f"模式: {mode}, 环境: {args.env}, 错误处理: {'继续' if args.continue_on_error else '停止'}"
    log_script_start(logger, script_name, description)
    
    success = False
    summary = ""
    
    try:
        if args.all:
            success = controller.execute_all(script_args)
            successful_scripts = len([r for r in controller.results.values() if r['success']])
            summary = f"执行所有脚本，成功: {successful_scripts}/{len(SCRIPTS)}"
        else:
            # 获取单个脚本的参数
            extra_args = script_args.get(args.script, [])
            success = controller.execute_single(args.script, extra_args)
            summary = f"执行单个脚本: {args.script}，结果: {'成功' if success else '失败'}"
    
    except KeyboardInterrupt:
        logger.warning("⚠️  用户中断执行")
        summary = "用户中断"
    except Exception as e:
        logger.error(f"💥 执行过程中发生异常: {e}")
        summary = f"执行异常: {str(e)}"
    finally:
        log_script_end(logger, script_name, success, summary)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main()) 