# World Tune 数据处理工具集

本目录包含用于处理 World Tune 电台数据的完整工具集，支持从导入到关联关系重建的全流程自动化处理。

## 📦 核心组件

### 🔄 主控脚本（推荐使用）

**main_data_processor.py** - 统一的数据处理主控脚本
- ✅ 支持串行执行所有脚本
- ✅ 支持单独执行某个脚本  
- ✅ 智能参数传递
- ✅ 完整错误处理和进度追踪
- ✅ 试运行模式支持
- ✅ 继续执行模式（遇错不停）

### 📥 数据导入模块

**data_import.py** - 电台数据导入和质量检查
- 从 Radio Browser API 获取电台数据
- 实时质量检查和可用性验证
- 历史数据质量验证和状态更新
- 支持增量导入和全量更新
- ⚠️ **重要**：只处理 `status=1` 的可用电台

### 🌍 国家数据处理模块

**process_countries.py** - 国家数据标准化和关联
- 分析电台中的国家数据分布
- 标准化国家名称和代码
- 创建和维护国家主表
- ⚠️ **重要**：只处理 `status=1` 的可用电台

### 🏷️ 标签数据处理模块

**process_tags.py** - 电台标签数据处理
- 统计和分析电台标签使用频率
- 智能标签过滤和归类
- 保留高频标签和特定格式标签
- ⚠️ **重要**：只处理 `status=1` 的可用电台

### 🔗 关联关系重建模块

**rebuild_station_countries.py** - 电台国家关联重建
- 重建电台与国家的多对多关系
- 支持 TRUNCATE 和增量更新两种策略
- 详细的数据覆盖率分析
- ⚠️ **重要**：只处理 `status=1` 的可用电台

**rebuild_station_tags.py** - 电台标签关联重建  
- 重建电台与标签的多对多关系
- 智能标签匹配和兜底处理
- 批量处理优化性能
- ⚠️ **重要**：只处理 `status=1` 的可用电台

### ⚙️ 配置管理

**config.py** - 统一配置管理
- 多环境数据库配置（开发/生产）
- 高级日志系统（分级别、分文件）
- 环境自动识别和初始化

**test.py** - Radio Browser API 客户端
- 完整的 API 封装和测试工具
- 自动服务器发现和故障转移

## 主控脚本 (main_data_processor.py)

集中管理所有数据处理脚本的执行，支持单独执行和批量执行。

### 脚本分类

**正常流程脚本（按顺序执行）：**
1. `data_import.py` - 导入电台数据（含质量检查）
2. `process_countries.py` - 处理国家数据（含关联关系）
3. `process_tags.py` - 处理标签数据
4. `rebuild_station_tags.py` - 重建电台标签关联

**维护脚本（独立使用，不在正常流程中）：**
- `rebuild_station_countries.py` - 重建电台国家关联（维护用）

> **注意：** `rebuild_station_countries.py` 被移出正常流程，因为 `process_countries.py` 已经处理了国家关联关系。维护脚本仅在出现数据问题时单独使用。

### 使用方法

```bash
# 查看所有可用脚本
python3 main_data_processor.py --list

# 执行完整的数据处理流程（推荐）
python3 main_data_processor.py --all --env development

# 执行单个脚本
python3 main_data_processor.py --script process_countries --env development

# 使用维护脚本修复问题
python3 main_data_processor.py --script rebuild_station_countries --env development --dry-run

# 传递特定参数
python3 main_data_processor.py --all --countries-args "--dry-run" --tags-args "--use 1000"
```

### 执行流程优化

通过移除重复的关联处理步骤，优化后的流程：
- **避免重复处理：** 国家关联关系只在 `process_countries.py` 中处理一次
- **明确职责：** 正常流程脚本用于日常维护，维护脚本用于问题修复
- **提高效率：** 减少不必要的数据库操作，缩短总执行时间

## 🚀 快速开始

### 使用主控脚本（推荐）

```bash
# 查看所有可用脚本
python main_data_processor.py --list

# 执行完整的数据处理流程
python main_data_processor.py --all

# 执行单个脚本
python main_data_processor.py --script data_import

# 传递特定参数给脚本
python main_data_processor.py --all \
  --data-import-args "--method all --batch-size 500" \
  --tags-args "--use 1000"

# 试运行模式（不执行实际操作）
python main_data_processor.py --all \
  --countries-args "--dry-run" \
  --station-countries-args "--dry-run"

# 继续执行模式（遇错不停）
python main_data_processor.py --all --continue-on-error

# 详细输出模式
python main_data_processor.py --all --verbose
```

### 手动执行单个脚本

```bash
# 1. 数据导入（推荐先执行）
python data_import.py --method all --page-size 1000

# 2. 国家数据处理
python process_countries.py --batch-size 1000

# 3. 标签数据处理  
python process_tags.py --use 900

# 4. 重建国家关联
python rebuild_station_countries.py --batch-size 1000

# 5. 重建标签关联
python rebuild_station_tags.py --batch-size 1000
```

## ⚠️ 重要改进

### Status 字段一致性修复

**问题**：之前其他脚本没有使用 `status` 字段过滤，可能处理了不可用的电台数据。

**解决方案**：所有脚本现在都添加了 `AND status = 1` 过滤条件：
- ✅ `process_countries.py` - 只处理可用电台的国家数据
- ✅ `process_tags.py` - 只统计可用电台的标签
- ✅ `rebuild_station_countries.py` - 只为可用电台建立国家关联
- ✅ `rebuild_station_tags.py` - 只为可用电台建立标签关联

**影响**：
- 提高数据质量和一致性
- 减少无效关联关系
- 前端查询 `WHERE status=1` 获得准确结果

## 📊 脚本执行顺序

推荐的执行顺序（主控脚本会自动按此顺序执行）：

```
1. data_import.py       → 导入电台数据并检查质量
2. process_countries.py → 处理国家数据
3. process_tags.py      → 处理标签数据  
4. rebuild_station_countries.py → 重建国家关联
5. rebuild_station_tags.py → 重建标签关联
```

## 🛡️ 安全特性

### 多环境隔离
- 开发环境：`--env development`
- 生产环境：`--env production`（默认）

### 试运行模式
- 支持 `--dry-run` 参数预览操作结果
- 不执行实际的数据库修改

### TRUNCATE 安全保护
- 涉及表：`world_tune_countries`, `world_tune_tags`, `world_tune_station_countries`, `world_tune_station_tags`
- 部分脚本支持 `--no-truncate` 增量模式
- 环境隔离避免误操作

### 错误处理策略
- 默认：遇到错误立即停止
- 可选：`--continue-on-error` 继续执行后续步骤

## 📝 日志系统

### 日志文件位置
```
../logs/
├── data_import/           # 数据导入日志
├── country_management/    # 国家处理日志  
├── tag/                   # 标签处理日志
├── station_management/    # 关联关系日志
└── system/               # 主控脚本日志
```

### 日志级别
- `info.log` - 正常操作记录
- `warnings.log` - 警告信息
- `errors.log` - 错误信息  
- `all.log` - 完整日志
- `warnings_details.json` - 详细警告（JSON格式）

## 🔧 高级配置

### 性能优化参数
```bash
# 大批量处理
--batch-size 2000 --page-size 10000

# 内存受限环境
--batch-size 500 --page-size 1000

# 快速处理少量数据
--batch-size 100 --page-size 500
```

### 质量检查配置
```bash
# 跳过质量检查（快速导入）
--skip-quality-check

# 只检查历史数据
--check-existing-only

# 指定历史数据检查时间点
--historical-cutoff "2023-12-01 00:00:00"
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查环境配置
   python -c "from config import get_db_config; print(get_db_config())"
   ```

2. **脚本执行失败**
   ```bash
   # 查看详细日志
   python main_data_processor.py --script data_import --verbose
   ```

3. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x *.py
   ```

4. **依赖缺失**
   ```bash
   # 安装依赖
   pip install -r requirements.txt
   ```

### 数据一致性检查

```sql
-- 检查电台状态分布
SELECT status, COUNT(*) as count 
FROM world_tune_radio_stations 
GROUP BY status;

-- 检查关联关系覆盖率
SELECT 
  (SELECT COUNT(*) FROM world_tune_radio_stations WHERE status=1) as total_active_stations,
  (SELECT COUNT(DISTINCT station_id) FROM world_tune_station_countries) as stations_with_countries,
  (SELECT COUNT(DISTINCT station_id) FROM world_tune_station_tags) as stations_with_tags;
```

## 📈 性能监控

主控脚本会自动记录：
- 每个脚本的执行时间
- 总体处理时间和成功率
- 详细的错误信息和建议

## 🔄 定期维护

建议的维护计划：
- **每日**：执行增量数据导入
- **每周**：完整数据质量检查
- **每月**：重建所有关联关系

```bash
# 每日增量导入
python main_data_processor.py --script data_import

# 每周完整检查
python main_data_processor.py --all

# 每月完整重建
python main_data_processor.py --all \
  --station-countries-args "--batch-size 2000" \
  --station-tags-args "--batch-size 2000"
``` 