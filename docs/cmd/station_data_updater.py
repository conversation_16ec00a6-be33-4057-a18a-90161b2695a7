#!/usr/bin/env python3
"""
电台数据更新脚本 - 专门用于更新现有数据库中的电台状态
针对5万+数据量进行高效优化，基于Radio Browser API官方文档设计

🔍 四重验证机制 (全部必须通过):
- API状态验证: 检查 lastcheckok 字段
- 流媒体验证: 深度检测音频格式和可播放性 (包含HLS片段验证)
- Favicon验证: 图标可访问性检查
- 格式一致性: API codec字段与实际检测格式的交叉验证

⚡ 性能优化:
- 批量处理避免内存溢出 (1000条/批)
- 单数据库连接避免影响线上服务
- 40并发验证提升处理速度
- 智能API调用减少重复请求

📊 支持格式 (基于Radio Browser API文档):
- 流媒体: MP3, AAC/AAC+, OGG/Vorbis, WMA, MPEG-TS
- 播放列表: HLS(.m3u8), M3U, PLS, ASX
- 静态文件: FLAC, WAV

🛡️ 可靠性保证:
- 遵循Radio Browser API最佳实践
- 详细日志和进度监控
- 信号处理支持安全退出
- 全面错误处理和重试机制

参考文档: https://api.radio-browser.info/
"""

import mysql.connector
import asyncio
import aiohttp
import argparse
import json
import sys
import time
import signal
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from urllib.parse import urlparse

# 导入现有组件
from test import create_api_client
from config import (get_db_config, setup_logging, BusinessType, log_script_start, log_script_end,
                   init_environment, get_environment_info)

# 初始化日志系统
logger, warning_details_logger = setup_logging(BusinessType.DATA_IMPORT, advanced=True, logger_name="StationUpdater")

@dataclass
class UpdateStats:
    """更新统计信息"""
    total_processed: int = 0
    total_checked: int = 0
    status_changed: int = 0
    became_available: int = 0
    became_unavailable: int = 0
    api_not_found: int = 0
    api_errors: int = 0
    validation_errors: int = 0
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None

    def get_duration(self) -> str:
        """获取执行时长"""
        end = self.end_time or datetime.now()
        duration = end - self.start_time
        return str(duration).split('.')[0]  # 去掉微秒

class HighPerformanceStationValidator:
    """高性能电台验证器 - 针对大数据量优化"""
    
    def __init__(self, concurrent_limit=40, timeout=5):
        self.concurrent_limit = concurrent_limit
        self.timeout = timeout
        self.session = None
        
        # 基于Radio Browser API官方文档的音频格式验证配置
        # 参考: https://api.radio-browser.info/ 和 https://docs.radio-browser.info/
        self.audio_format_config = {
            # MP3格式 - 最常见的流媒体格式
            'mp3': {
                'content_types': ['audio/mpeg', 'audio/mp3', 'audio/mpeg3', 'audio/x-mpeg'],
                'extensions': ['.mp3'],
                'magic_bytes': [b'\xff\xfb', b'\xff\xf3', b'\xff\xf2', b'ID3'],  # MP3 frame sync + ID3
                'min_size': 1024,
                'stream_check': True,
                'rb_codec': 'MP3'  # Radio Browser使用的codec名称
            },
            # AAC/AAC+格式 - 高质量流媒体
            'aac': {
                'content_types': ['audio/aac', 'audio/aacp', 'audio/mp4', 'audio/x-aac'],
                'extensions': ['.aac', '.m4a'],
                'magic_bytes': [b'\xff\xf1', b'\xff\xf9', b'ftyp'],  # ADTS AAC + MP4
                'min_size': 1024,
                'stream_check': True,
                'rb_codec': ['AAC', 'AAC+', 'AACP']  # Radio Browser支持的变体
            },
            # OGG/Vorbis格式 - 开源流媒体
            'ogg': {
                'content_types': ['audio/ogg', 'application/ogg', 'audio/vorbis'],
                'extensions': ['.ogg', '.oga'],
                'magic_bytes': [b'OggS'],  # OGG页面头
                'min_size': 1024,
                'stream_check': True,
                'rb_codec': 'OGG'
            },
            # FLAC格式 - 无损音频
            'flac': {
                'content_types': ['audio/flac', 'audio/x-flac'],
                'extensions': ['.flac'],
                'magic_bytes': [b'fLaC'],  # FLAC signature
                'min_size': 2048,
                'stream_check': False,  # 通常是文件，不是流
                'rb_codec': 'FLAC'
            },
            # WAV格式 - 无压缩音频
            'wav': {
                'content_types': ['audio/wav', 'audio/wave', 'audio/x-wav'],
                'extensions': ['.wav'],
                'magic_bytes': [b'RIFF'],  # RIFF header
                'min_size': 2048,
                'stream_check': False,
                'rb_codec': 'WAV'
            },
            # HLS格式 - HTTP Live Streaming
            'hls': {
                'content_types': ['application/vnd.apple.mpegurl', 'application/x-mpegurl', 
                                'audio/x-mpegurl', 'audio/mpegurl', 'text/plain'],
                'extensions': ['.m3u8'],
                'magic_bytes': [b'#EXTM3U'],
                'min_size': 100,
                'stream_check': True,
                'is_playlist': True,
                'rb_codec': 'HLS'
            },
            # M3U播放列表
            'm3u': {
                'content_types': ['audio/x-mpegurl', 'audio/mpegurl', 'text/plain'],
                'extensions': ['.m3u'],
                'magic_bytes': [b'#EXTM3U', b'http'],
                'min_size': 50,
                'stream_check': False,
                'is_playlist': True,
                'rb_codec': 'M3U'
            },
            # PLS播放列表
            'pls': {
                'content_types': ['audio/x-scpls', 'text/plain'],
                'extensions': ['.pls'],
                'magic_bytes': [b'[playlist]', b'file1=', b'File1='],
                'min_size': 50,
                'stream_check': False,
                'is_playlist': True,
                'rb_codec': 'PLS'
            },
            # ASX播放列表 (Windows Media)
            'asx': {
                'content_types': ['video/x-ms-asf', 'audio/x-ms-wax', 'text/plain'],
                'extensions': ['.asx', '.wax'],
                'magic_bytes': [b'<asx', b'<ASX'],
                'min_size': 50,
                'stream_check': False,
                'is_playlist': True,
                'rb_codec': 'ASX'
            },
            # MPEG-TS (直播流，常用于电视台)
            'ts': {
                'content_types': ['video/mp2t', 'application/octet-stream'],
                'extensions': ['.ts'],
                'magic_bytes': [b'\x47'],  # TS同步字节
                'min_size': 2048,
                'stream_check': True,
                'rb_codec': 'TS'
            },
            # WMA格式 (Windows Media Audio)
            'wma': {
                'content_types': ['audio/x-ms-wma', 'audio/wma'],
                'extensions': ['.wma'],
                'magic_bytes': [b'\x30\x26\xb2\x75'],  # ASF GUID
                'min_size': 1024,
                'stream_check': True,
                'rb_codec': 'WMA'
            }
        }
        
        # 播放列表格式
        self.playlist_formats = ['hls', 'm3u', 'pls']
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        timeout = aiohttp.ClientTimeout(total=self.timeout, connect=3)
        connector = aiohttp.TCPConnector(
            limit=self.concurrent_limit + 10,
            limit_per_host=20,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers={
                # 遵循Radio Browser API文档建议: 发送描述性的User-Agent
                'User-Agent': 'WorldTuneUpdater/1.0.0 (Station Verification Tool; https://worldtune.app)',
                'Accept': 'audio/*,application/*,image/*,text/*,*/*;q=0.8'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def validate_station_comprehensive(self, api_data: Dict, db_record: Dict) -> Dict:
        """综合验证电台 - 三重验证 + 格式交叉验证"""
        try:
            # 1. API状态检查
            api_check = self._check_api_status(api_data)
            
            # 2. 流媒体验证 (必须) - 返回检测到的格式信息
            stream_result = await self._check_stream_availability_enhanced(api_data)
            stream_check = stream_result.get('available', False)
            detected_format = stream_result.get('detected_format')
            
            # 3. Favicon验证 (必须通过)
            favicon_check = await self._check_favicon_availability(api_data)
            
            # 4. 格式交叉验证 (基于Radio Browser API文档)
            format_consistency = self._verify_format_consistency(api_data, detected_format)
            
            # 综合判断 - 所有验证都必须通过
            is_available = (
                api_check and 
                stream_check and 
                favicon_check and
                format_consistency  # 格式一致性也必须通过
            )
            
            return {
                'status': 1 if is_available else 0,
                'api_check': api_check,
                'stream_check': stream_check,
                'favicon_check': favicon_check,
                'format_consistency': format_consistency,
                'detected_format': detected_format,
                'api_codec': api_data.get('codec', ''),
                'reason': self._get_failure_reason_enhanced(api_check, stream_check, favicon_check, 
                                                         format_consistency, api_data, detected_format)
            }
            
        except Exception as e:
            logger.warning(f"验证异常: {api_data.get('name', 'Unknown')}, 错误: {e}")
            return {
                'status': 0,
                'api_check': False,
                'stream_check': False,
                'favicon_check': False,
                'format_consistency': False,
                'reason': f"验证异常: {str(e)}"
            }
    
    def _check_api_status(self, api_data: Dict) -> bool:
        """检查API状态"""
        return api_data.get('lastcheckok', 0) == 1
    
    async def _check_stream_availability_enhanced(self, api_data: Dict) -> Dict:
        """增强版流媒体验证 - 返回详细信息"""
        return await self._check_stream_availability(api_data)
    
    async def _check_stream_availability(self, api_data: Dict) -> Dict:
        """严格检查流媒体可用性 - 支持多种音频格式深度验证，返回详细信息"""
        url = api_data.get('url_resolved') or api_data.get('url', '')
        if not url:
            return {'available': False, 'detected_format': None, 'reason': '无URL'}
        
        url = url.strip()
        logger.debug(f"开始验证音频流: {url}")
        
        try:
            # 第一步：尝试HEAD请求获取基本信息
            head_info = await self._get_stream_head_info(url)
            if head_info:
                content_type = head_info.get('content_type', '').lower()
                content_length = head_info.get('content_length', 0)
                
                # 根据Content-Type和URL识别格式
                detected_format = self._detect_audio_format(url, content_type)
                if detected_format:
                    logger.debug(f"检测到格式: {detected_format} ({content_type})")
                    is_available = await self._verify_audio_format(url, detected_format, content_length)
                    return {
                        'available': is_available,
                        'detected_format': detected_format,
                        'content_type': content_type,
                        'content_length': content_length,
                        'reason': '验证通过' if is_available else '格式验证失败'
                    }
            
            # 第二步：HEAD失败或无法识别，尝试GET请求深度检测
            deep_result = await self._deep_audio_verification_enhanced(url)
            return deep_result
            
        except Exception as e:
            logger.debug(f"流媒体验证异常: {url}, 错误: {e}")
            return {'available': False, 'detected_format': None, 'reason': f'验证异常: {e}'}
    
    async def _get_stream_head_info(self, url: str) -> Optional[Dict]:
        """获取流媒体HEAD信息"""
        try:
            async with self.session.head(url, allow_redirects=True, timeout=self.timeout) as response:
                if response.status == 200:
                    return {
                        'content_type': response.headers.get('content-type', ''),
                        'content_length': int(response.headers.get('content-length', 0) or 0),
                        'server': response.headers.get('server', ''),
                        'icy_name': response.headers.get('icy-name', ''),  # 网络电台信息
                        'icy_genre': response.headers.get('icy-genre', '')
                    }
        except:
            pass
        return None
    
    def _detect_audio_format(self, url: str, content_type: str) -> Optional[str]:
        """根据URL和Content-Type检测音频格式"""
        url_lower = url.lower()
        content_type_lower = content_type.lower()
        
        # 遍历所有支持的格式
        for format_name, config in self.audio_format_config.items():
            # 检查Content-Type匹配
            if any(ct in content_type_lower for ct in config['content_types']):
                return format_name
            
            # 检查文件扩展名匹配
            if any(ext in url_lower for ext in config['extensions']):
                return format_name
        
        return None
    
    async def _verify_audio_format(self, url: str, format_name: str, content_length: int) -> bool:
        """验证特定音频格式"""
        config = self.audio_format_config[format_name]
        
        # 检查最小大小（对于有Content-Length的）
        if content_length > 0 and content_length < config['min_size']:
            logger.debug(f"文件大小不足: {url} ({content_length} < {config['min_size']})")
            return False
        
        # 播放列表格式需要特殊处理
        if config.get('is_playlist', False):
            return await self._verify_playlist_format(url, format_name)
        
        # 流媒体格式需要数据持续性检查
        if config.get('stream_check', False):
            return await self._verify_streaming_audio(url, format_name)
        
        # 静态文件格式检查魔术字节
        return await self._verify_static_audio(url, format_name)
    
    async def _verify_playlist_format(self, url: str, format_name: str) -> bool:
        """验证播放列表格式"""
        try:
            async with self.session.get(url, allow_redirects=True, timeout=self.timeout) as response:
                if response.status != 200:
                    return False
                
                # 读取播放列表内容
                data = await asyncio.wait_for(response.content.read(4096), timeout=3)
                content = data.decode('utf-8', errors='ignore')
                
                if format_name == 'hls':
                    return await self._verify_hls_stream(url, content)
                elif format_name == 'm3u':
                    return self._verify_m3u_playlist(content)
                elif format_name == 'pls':
                    return self._verify_pls_playlist(content)
                
        except Exception as e:
            logger.debug(f"播放列表验证失败: {url}, 错误: {e}")
        
        return False
    
    async def _verify_streaming_audio(self, url: str, format_name: str) -> bool:
        """验证流媒体音频格式"""
        config = self.audio_format_config[format_name]
        
        try:
            async with self.session.get(url, allow_redirects=True, timeout=self.timeout) as response:
                if response.status != 200:
                    return False
                
                # 分批读取数据验证
                chunk_size = 4096
                total_read = 0
                valid_chunks = 0
                
                # 读取前几个chunk进行验证
                for i in range(3):  # 最多读取3个chunk
                    try:
                        chunk = await asyncio.wait_for(response.content.read(chunk_size), timeout=2)
                        if not chunk:
                            break
                        
                        total_read += len(chunk)
                        
                        # 验证魔术字节（只在第一个chunk）
                        if i == 0:
                            if not self._check_magic_bytes(chunk, config['magic_bytes']):
                                logger.debug(f"魔术字节验证失败: {url}")
                                return False
                        
                        # 验证数据连续性（对于流媒体很重要）
                        if self._is_valid_audio_chunk(chunk, format_name):
                            valid_chunks += 1
                        
                    except asyncio.TimeoutError:
                        logger.debug(f"流媒体读取超时: {url}")
                        break
                    except Exception as e:
                        logger.debug(f"流媒体读取异常: {url}, {e}")
                        break
                
                # 判断流媒体质量
                success_rate = valid_chunks / max(1, i + 1)
                logger.debug(f"流媒体验证: {url}, 有效率: {success_rate:.1%}, 读取: {total_read} bytes")
                
                # 至少60%的chunk有效，且总读取量足够
                return success_rate >= 0.6 and total_read >= config['min_size']
                
        except Exception as e:
            logger.debug(f"流媒体验证异常: {url}, 错误: {e}")
        
        return False
    
    async def _verify_static_audio(self, url: str, format_name: str) -> bool:
        """验证静态音频文件"""
        config = self.audio_format_config[format_name]
        
        try:
            async with self.session.get(url, allow_redirects=True, timeout=self.timeout) as response:
                if response.status != 200:
                    return False
                
                # 读取文件头部
                header_data = await asyncio.wait_for(response.content.read(8192), timeout=3)
                
                if len(header_data) < config['min_size']:
                    logger.debug(f"文件头部不足: {url} ({len(header_data)} < {config['min_size']})")
                    return False
                
                # 验证魔术字节
                if not self._check_magic_bytes(header_data, config['magic_bytes']):
                    logger.debug(f"魔术字节验证失败: {url}")
                    return False
                
                return True
                
        except Exception as e:
            logger.debug(f"静态文件验证异常: {url}, 错误: {e}")
        
        return False
    
    async def _deep_audio_verification_enhanced(self, url: str) -> Dict:
        """增强版深度音频验证 - 返回详细信息"""
        result = await self._deep_audio_verification(url)
        if isinstance(result, bool):
            # 尝试检测格式
            detected_format = None
            try:
                async with self.session.get(url, allow_redirects=True, timeout=3) as response:
                    if response.status == 200:
                        data = await asyncio.wait_for(response.content.read(1024), timeout=2)
                        for format_name, config in self.audio_format_config.items():
                            if self._check_magic_bytes(data, config['magic_bytes']):
                                detected_format = format_name
                                break
            except:
                pass
            
            return {
                'available': result,
                'detected_format': detected_format,
                'reason': '深度验证' + ('通过' if result else '失败')
            }
        return result

    async def _deep_audio_verification(self, url: str) -> bool:
        """深度音频验证 - 无法通过HEAD识别时使用"""
        try:
            async with self.session.get(url, allow_redirects=True, timeout=self.timeout) as response:
                if response.status != 200:
                    return False
                
                # 读取更多数据进行格式检测
                data = await asyncio.wait_for(response.content.read(8192), timeout=4)
                
                if len(data) < 100:
                    return False
                
                # 尝试通过魔术字节检测格式
                for format_name, config in self.audio_format_config.items():
                    if self._check_magic_bytes(data, config['magic_bytes']):
                        logger.debug(f"深度检测识别格式: {format_name}")
                        
                        # 对于播放列表，进行内容验证
                        if config.get('is_playlist', False):
                            content = data.decode('utf-8', errors='ignore')
                            if format_name == 'hls' and '#EXTM3U' in content:
                                return await self._verify_hls_stream(url, content)
                            elif format_name == 'm3u':
                                return self._verify_m3u_playlist(content)
                            elif format_name == 'pls':
                                return self._verify_pls_playlist(content)
                        
                        # 对于音频格式，验证数据有效性
                        return len(data) >= config['min_size']
                
                return False
                
        except Exception as e:
            logger.debug(f"深度验证异常: {url}, 错误: {e}")
        
        return False
    
    def _check_magic_bytes(self, data: bytes, magic_patterns: List[bytes]) -> bool:
        """检查魔术字节"""
        if not data or not magic_patterns:
            return False
        
        for pattern in magic_patterns:
            if data.startswith(pattern) or pattern in data[:512]:
                return True
        
        return False
    
    def _is_valid_audio_chunk(self, chunk: bytes, format_name: str) -> bool:
        """验证音频数据块的有效性"""
        if not chunk:
            return False
        
        # 检查是否是静音或损坏数据
        if len(set(chunk)) < 5:  # 数据变化太少可能是静音
            return False
        
        # 根据格式进行特定检查
        if format_name == 'mp3':
            # MP3帧同步检查
            for i in range(len(chunk) - 1):
                if chunk[i] == 0xff and (chunk[i + 1] & 0xe0) == 0xe0:
                    return True
        
        elif format_name == 'aac':
            # AAC ADTS帧头检查
            for i in range(len(chunk) - 1):
                if chunk[i] == 0xff and (chunk[i + 1] & 0xf6) == 0xf0:
                    return True
        
        # 通用检查：数据有一定的熵值
        return True
    
    def _verify_m3u_playlist(self, content: str) -> bool:
        """验证M3U播放列表"""
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        # 计算有效URL数量
        valid_urls = 0
        for line in lines:
            if line.startswith('http') or line.endswith(('.mp3', '.aac', '.ogg')):
                valid_urls += 1
        
        return valid_urls > 0
    
    def _verify_pls_playlist(self, content: str) -> bool:
        """验证PLS播放列表"""
        content_lower = content.lower()
        
        # 必须包含[playlist]段和至少一个File条目
        has_playlist_section = '[playlist]' in content_lower
        has_file_entries = 'file1=' in content_lower or 'file=' in content_lower
        
        return has_playlist_section and has_file_entries
    
    async def _verify_hls_stream(self, playlist_url: str, playlist_content: str) -> bool:
        """验证HLS流的实际可用性"""
        try:
            # 解析播放列表，提取片段URL
            lines = playlist_content.strip().split('\n')
            base_url = '/'.join(playlist_url.split('/')[:-1]) + '/'
            
            segment_urls = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 构建完整的片段URL
                    if line.startswith('http'):
                        segment_urls.append(line)
                    else:
                        segment_urls.append(base_url + line)
                    
                    # 只检查前3个片段，避免过度检查
                    if len(segment_urls) >= 3:
                        break
            
            if not segment_urls:
                logger.debug(f"HLS播放列表无有效片段: {playlist_url}")
                return False
            
            # 检查片段可用性（并发检查前3个）
            segment_tasks = []
            for segment_url in segment_urls[:3]:
                task = self._check_hls_segment(segment_url)
                segment_tasks.append(task)
            
            # 至少需要1个片段可用
            results = await asyncio.gather(*segment_tasks, return_exceptions=True)
            available_count = sum(1 for result in results if result is True)
            
            success_rate = available_count / len(results)
            logger.debug(f"HLS片段检查: {playlist_url}, 可用率: {success_rate:.1%} ({available_count}/{len(results)})")
            
            # 至少50%的片段可用才认为流有效
            return success_rate >= 0.5
            
        except Exception as e:
            logger.debug(f"HLS验证异常: {playlist_url}, 错误: {e}")
            return False
    
    async def _check_hls_segment(self, segment_url: str) -> bool:
        """检查单个HLS片段是否可用"""
        try:
            async with self.session.head(segment_url, allow_redirects=True, timeout=3) as response:
                if response.status == 200:
                    # 检查Content-Length，.ts文件通常有一定大小
                    content_length = response.headers.get('content-length')
                    if content_length:
                        size = int(content_length)
                        # .ts片段通常至少几KB
                        return size > 1024
                    return True
        except:
            # HEAD失败，尝试GET请求前几个字节
            try:
                async with self.session.get(segment_url, allow_redirects=True, timeout=3) as response:
                    if response.status == 200:
                        # 读取前1KB验证是否为有效的TS文件
                        data = await asyncio.wait_for(response.content.read(1024), timeout=2)
                        # TS文件通常以0x47开头（同步字节）
                        return len(data) > 100 and (data[0] == 0x47 or b'ts' in data[:100].lower())
            except:
                pass
        
        return False
    
    async def _check_favicon_availability(self, api_data: Dict) -> bool:
        """检查Favicon可用性 - 必须存在且可访问"""
        favicon_url = api_data.get('favicon', '')
        if not favicon_url or not favicon_url.strip():
            return False  # 没有favicon则验证失败
        
        favicon_url = favicon_url.strip()
        
        # 检查URL合理性
        if not favicon_url.startswith(('http://', 'https://')):
            return False
        
        try:
            async with self.session.head(favicon_url, allow_redirects=True) as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '').lower()
                    if 'image/' in content_type or 'icon' in content_type:
                        return True
        except:
            pass
        
        # 宽松检查：如果URL看起来像图片
        favicon_lower = favicon_url.lower()
        if any(ext in favicon_lower for ext in ['.png', '.jpg', '.jpeg', '.gif', '.ico', '.webp']):
            return True
        
        return False
    
    def _verify_format_consistency(self, api_data: Dict, detected_format: Optional[str]) -> bool:
        """验证API返回的codec与检测到的格式是否一致"""
        api_codec = api_data.get('codec', '').upper()
        api_hls = api_data.get('hls', 0)
        
        if not detected_format:
            # 如果没有检测到格式，但API说有codec，可能是网络问题
            return not api_codec  # 只有当API也没有codec时才算一致
        
        config = self.audio_format_config.get(detected_format, {})
        rb_codec = config.get('rb_codec', '')
        
        # 处理Radio Browser的codec变体
        if isinstance(rb_codec, list):
            expected_codecs = [codec.upper() for codec in rb_codec]
        else:
            expected_codecs = [rb_codec.upper()] if rb_codec else []
        
        # 特殊处理HLS
        if detected_format == 'hls':
            return api_hls == 1 or api_codec in ['HLS', 'M3U8']
        
        # 检查codec是否匹配
        if api_codec in expected_codecs:
            return True
        
        # 宽松匹配 - 一些常见的变体
        codec_aliases = {
            'MP3': ['MPEG', 'MPEG-1', 'MPEG-2'],
            'AAC': ['AACP', 'AAC+', 'HE-AAC'],
            'OGG': ['VORBIS', 'OGG VORBIS'],
            'FLAC': ['FREE LOSSLESS AUDIO CODEC'],
        }
        
        for expected in expected_codecs:
            aliases = codec_aliases.get(expected, [])
            if api_codec in aliases or expected in aliases and api_codec == expected:
                return True
        
        # 如果都不匹配，记录但不完全失败（可能是新格式或API数据陈旧）
        logger.debug(f"格式不一致: API={api_codec}, 检测={detected_format}, 期望={expected_codecs}")
        return len(expected_codecs) == 0  # 如果没有明确期望，则通过
    
    def _get_failure_reason_enhanced(self, api_check: bool, stream_check: bool, 
                                   favicon_check: bool, format_consistency: bool,
                                   api_data: Dict, detected_format: Optional[str]) -> str:
        """获取增强版失败原因"""
        reasons = []
        
        if not api_check:
            reasons.append(f"API标记为不可用 (lastcheckok={api_data.get('lastcheckok', 'N/A')})")
        
        if not stream_check:
            reasons.append("流媒体URL不可访问")
        
        if not favicon_check:
            if not api_data.get('favicon'):
                reasons.append("没有图标")
            else:
                reasons.append("图标不可用")
        
        if not format_consistency:
            api_codec = api_data.get('codec', 'N/A')
            api_hls = api_data.get('hls', 0)
            reasons.append(f"格式不一致 (API: {api_codec}/HLS:{api_hls}, 检测: {detected_format or 'None'})")
        
        return "; ".join(reasons) if reasons else "验证通过"
    
    def _get_failure_reason(self, api_check: bool, stream_check: bool, 
                          favicon_check: bool, api_data: Dict) -> str:
        """获取失败原因 - 兼容性方法"""
        return self._get_failure_reason_enhanced(api_check, stream_check, favicon_check, 
                                               True, api_data, None)

class RadioStationUpdater:
    """电台数据更新器 - 主控制器"""
    
    def __init__(self, db_config: Dict, batch_size=1000, 
                 concurrent_limit=40, timeout=5):
        self.db_config = db_config
        self.batch_size = batch_size
        self.concurrent_limit = concurrent_limit
        self.timeout = timeout
        
        # 数据库连接 (单连接)
        self.conn = None
        self.cursor = None
        
        # API客户端 - 遵循Radio Browser API文档建议
        self.api_client = create_api_client("WorldTuneUpdater/1.0.0 (Station Verification Tool; https://worldtune.app)")
        
        # 验证器
        self.validator = HighPerformanceStationValidator(concurrent_limit, timeout)
        
        # 统计信息
        self.stats = UpdateStats()
        
        # 停止标志
        self.should_stop = False
        
        # 字段映射
        self.field_mapping = {
            'name': 'name',
            'url': 'url',
            'url_resolved': 'url_resolved',
            'homepage': 'homepage',
            'favicon': 'favicon',
            'country': 'country',
            'countrycode': 'countrycode',
            'iso_3166_2': 'iso_3166_2',
            'state': 'state',
            'language': 'language',
            'languagecodes': 'languagecodes',
            'tags': 'tags',
            'codec': 'codec',
            'bitrate': 'bitrate',
            'hls': 'is_hls',
            'votes': 'votes',
            'clickcount': 'click_count',
            'clicktrend': 'click_trend',
            'ssl_error': 'ssl_error',
            'geo_lat': 'geo_lat',
            'geo_long': 'geo_long',
            'has_extended_info': 'has_extended_info',
            'lastcheckok': 'last_check_ok',
            'lastchangetime_iso8601': 'last_change_time',
            'lastchecktime_iso8601': 'last_check_time',
            'lastcheckoktime_iso8601': 'last_check_ok_time',
            'lastlocalchecktime_iso8601': 'last_local_check_time',
            'clicktimestamp_iso8601': 'click_timestamp'
        }
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，准备安全退出...")
            self.should_stop = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run_update(self, max_records: Optional[int] = None):
        """运行更新流程"""
        try:
            self.setup_signal_handlers()
            logger.info("🚀 开始电台数据更新流程")
            
            # 初始化连接
            await self._initialize_connections()
            
            # 获取总数量
            total_count = self._get_total_station_count()
            if max_records:
                total_count = min(total_count, max_records)
            
            logger.info(f"📊 总共需要更新 {total_count} 个电台")
            
            # 分批处理
            processed = 0
            for batch_data in self._get_stations_in_batches(max_records):
                if self.should_stop:
                    logger.info("收到停止信号，安全退出")
                    break
                
                await self._process_batch(batch_data)
                processed += len(batch_data)
                
                # 显示进度
                progress = (processed / total_count) * 100 if total_count > 0 else 0
                logger.info(f"📈 进度: {processed}/{total_count} ({progress:.1f}%)")
                
                # 小休息避免过载
                await asyncio.sleep(0.1)
            
            self.stats.end_time = datetime.now()
            self._print_final_report()
            
        except Exception as e:
            logger.error(f"更新流程异常: {e}")
            raise
        finally:
            await self._cleanup_connections()
    
    async def _initialize_connections(self):
        """初始化连接"""
        # 数据库连接
        self.conn = mysql.connector.connect(**self.db_config)
        self.cursor = self.conn.cursor(dictionary=True)
        
        # 验证器
        await self.validator.__aenter__()
        
        logger.info("✅ 数据库和验证器连接初始化完成")
    
    async def _cleanup_connections(self):
        """清理连接"""
        try:
            if self.validator:
                await self.validator.__aexit__(None, None, None)
            
            if self.cursor:
                self.cursor.close()
            
            if self.conn:
                self.conn.close()
            
            logger.info("✅ 连接清理完成")
        except Exception as e:
            logger.error(f"清理连接时出错: {e}")
    
    def _get_total_station_count(self) -> int:
        """获取总电台数量"""
        try:
            self.cursor.execute("SELECT COUNT(*) as count FROM world_tune_radio_stations")
            result = self.cursor.fetchone()
            return result['count'] if result else 0
        except Exception as e:
            logger.error(f"获取电台总数失败: {e}")
            return 0
    
    def _get_stations_in_batches(self, max_records: Optional[int] = None):
        """分批获取电台数据"""
        offset = 0
        total_fetched = 0
        
        while True:
            if self.should_stop:
                break
            
            # 计算当前批次大小
            current_batch_size = self.batch_size
            if max_records:
                remaining = max_records - total_fetched
                if remaining <= 0:
                    break
                current_batch_size = min(self.batch_size, remaining)
            
            try:
                query = """
                    SELECT id, station_uuid, name, url, url_resolved, homepage, favicon,
                           country, countrycode, iso_3166_2, state, language, languagecodes,
                           tags, codec, bitrate, is_hls, votes, click_count, click_trend,
                           ssl_error, geo_lat, geo_long, has_extended_info, last_check_ok,
                           last_change_time, last_check_time, last_check_ok_time,
                           last_local_check_time, click_timestamp, status, updated_at
                    FROM world_tune_radio_stations
                    ORDER BY id
                    LIMIT %s OFFSET %s
                """
                
                self.cursor.execute(query, (current_batch_size, offset))
                batch_data = self.cursor.fetchall()
                
                if not batch_data:
                    logger.info("所有数据处理完成")
                    break
                
                logger.info(f"📥 获取第 {offset // self.batch_size + 1} 批数据: {len(batch_data)} 个电台")
                yield batch_data
                
                offset += len(batch_data)
                total_fetched += len(batch_data)
                
                if len(batch_data) < current_batch_size:
                    logger.info("已获取所有数据")
                    break
                    
            except Exception as e:
                logger.error(f"获取第 {offset // self.batch_size + 1} 批数据失败: {e}")
                break
    
    async def _process_batch(self, batch_data: List[Dict]):
        """处理单个批次"""
        try:
            # 1. 提取station_uuid列表
            station_uuids = [record['station_uuid'] for record in batch_data]
            
            # 2. 批量获取API数据
            api_data_map = await self._fetch_api_data_batch(station_uuids)
            
            # 3. 并发验证和更新
            await self._validate_and_update_batch(batch_data, api_data_map)
            
            self.stats.total_processed += len(batch_data)
            
        except Exception as e:
            logger.error(f"批次处理失败: {e}")
            raise
    
    async def _fetch_api_data_batch(self, station_uuids: List[str]) -> Dict[str, Dict]:
        """批量获取API数据"""
        try:
            # 使用API的批量UUID查询
            api_stations = self.api_client.get_stations_by_uuid(station_uuids)
            
            # 建立UUID映射
            api_data_map = {}
            for station in api_stations:
                uuid = station.get('stationuuid')
                if uuid:
                    api_data_map[uuid] = station
            
            logger.info(f"🔗 API返回 {len(api_data_map)} / {len(station_uuids)} 个电台数据")
            
            # 记录不存在的电台
            missing_uuids = set(station_uuids) - set(api_data_map.keys())
            if missing_uuids:
                logger.info(f"❌ API中不存在的电台: {len(missing_uuids)} 个")
                self.stats.api_not_found += len(missing_uuids)
            
            return api_data_map
            
        except Exception as e:
            logger.error(f"批量获取API数据失败: {e}")
            self.stats.api_errors += 1
            return {}
    
    async def _validate_and_update_batch(self, batch_data: List[Dict], 
                                       api_data_map: Dict[str, Dict]):
        """验证并更新批次数据"""
        # 准备并发任务
        tasks = []
        semaphore = asyncio.Semaphore(self.concurrent_limit)
        
        async def process_single_station(db_record):
            async with semaphore:
                return await self._process_single_station(db_record, api_data_map)
        
        # 创建并发任务
        for db_record in batch_data:
            tasks.append(process_single_station(db_record))
        
        # 执行并发验证
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 批量更新数据库
        update_data = []
        for result in results:
            if isinstance(result, dict) and result.get('needs_update'):
                update_data.append(result)
        
        if update_data:
            self._batch_update_database(update_data)
    
    async def _process_single_station(self, db_record: Dict, 
                                    api_data_map: Dict[str, Dict]) -> Dict:
        """处理单个电台"""
        try:
            station_uuid = db_record['station_uuid']
            api_data = api_data_map.get(station_uuid)
            
            # 电台在API中不存在
            if not api_data:
                if db_record['status'] != 0:
                    logger.info(f"❌ 电台不存在于API，设为不可用: {db_record['name']}")
                    return {
                        'needs_update': True,
                        'id': db_record['id'],
                        'updates': {'status': 0, 'last_check_time': datetime.now()},
                        'status_changed': True,
                        'became_unavailable': True
                    }
                return {'needs_update': False}
            
            # 验证电台
            validation_result = await self.validator.validate_station_comprehensive(
                api_data, db_record
            )
            
            # 准备更新数据
            updates = self._prepare_field_updates(api_data, db_record)
            updates['last_check_time'] = datetime.now()
            
            # 检查状态变化
            new_status = validation_result['status']
            old_status = db_record['status']
            status_changed = old_status != new_status
            
            if status_changed:
                updates['status'] = new_status
                if new_status == 1:
                    logger.info(f"✅ 电台变为可用: {db_record['name']} ({old_status} -> {new_status})")
                else:
                    logger.info(f"❌ 电台变为不可用: {db_record['name']} ({old_status} -> {new_status}) - {validation_result['reason']}")
            
            self.stats.total_checked += 1
            
            return {
                'needs_update': True,
                'id': db_record['id'],
                'updates': updates,
                'status_changed': status_changed,
                'became_available': status_changed and new_status == 1,
                'became_unavailable': status_changed and new_status == 0
            }
            
        except Exception as e:
            logger.error(f"处理电台异常: {db_record.get('name', 'Unknown')}, 错误: {e}")
            self.stats.validation_errors += 1
            return {'needs_update': False}
    
    def _prepare_field_updates(self, api_data: Dict, db_record: Dict) -> Dict:
        """准备字段更新数据"""
        updates = {}
        
        for api_field, db_field in self.field_mapping.items():
            if api_field in api_data:
                api_value = api_data[api_field]
                
                # 数据类型转换和处理
                if api_field.endswith('_iso8601') and api_value:
                    # 时间字段转换
                    try:
                        if api_value.endswith('Z'):
                            api_value = api_value[:-1] + '+00:00'
                        api_value = datetime.fromisoformat(api_value)
                    except:
                        api_value = None
                
                elif db_field in ['is_hls', 'ssl_error', 'has_extended_info', 'last_check_ok']:
                    # 布尔字段转换
                    api_value = 1 if api_value else 0
                
                elif db_field in ['bitrate', 'votes', 'click_count', 'click_trend']:
                    # 数字字段转换
                    api_value = api_value or 0
                
                elif isinstance(api_value, str):
                    # 字符串字段截断
                    max_lengths = {
                        'name': 1255, 'country': 100, 'countrycode': 2, 
                        'iso_3166_2': 10, 'state': 100, 'language': 100,
                        'languagecodes': 50, 'codec': 20
                    }
                    if db_field in max_lengths:
                        api_value = api_value[:max_lengths[db_field]]
                
                # 检查是否需要更新
                db_value = db_record.get(db_field)
                if api_value != db_value:
                    updates[db_field] = api_value
        
        return updates
    
    def _batch_update_database(self, update_data: List[Dict]):
        """批量更新数据库"""
        if not update_data:
            return
        
        try:
            # 统计信息
            status_changed_count = sum(1 for item in update_data if item.get('status_changed'))
            became_available_count = sum(1 for item in update_data if item.get('became_available'))
            became_unavailable_count = sum(1 for item in update_data if item.get('became_unavailable'))
            
            self.stats.status_changed += status_changed_count
            self.stats.became_available += became_available_count
            self.stats.became_unavailable += became_unavailable_count
            
            # 批量更新
            for item in update_data:
                updates = item['updates']
                station_id = item['id']
                
                # 构建UPDATE语句
                set_clauses = []
                values = []
                
                for field, value in updates.items():
                    set_clauses.append(f"{field} = %s")
                    values.append(value)
                
                if set_clauses:
                    query = f"""
                        UPDATE world_tune_radio_stations 
                        SET {', '.join(set_clauses)}
                        WHERE id = %s
                    """
                    values.append(station_id)
                    
                    self.cursor.execute(query, values)
            
            # 提交事务
            self.conn.commit()
            
            logger.info(f"✅ 批量更新完成: {len(update_data)} 个电台, 状态变化: {status_changed_count} 个")
            
        except Exception as e:
            logger.error(f"批量更新失败: {e}")
            self.conn.rollback()
            raise
    
    def _print_final_report(self):
        """打印最终报告"""
        stats = self.stats
        
        report_lines = [
            "=" * 80,
            "📊 电台数据更新完成报告",
            "=" * 80,
            f"🕒 执行时长: {stats.get_duration()}",
            f"📈 总处理数量: {stats.total_processed} 个电台",
            f"🔍 验证检查: {stats.total_checked} 个电台",
            "",
            "📊 状态变化统计:",
            f"  🔄 总状态变化: {stats.status_changed} 个",
            f"  ✅ 变为可用: {stats.became_available} 个",
            f"  ❌ 变为不可用: {stats.became_unavailable} 个",
            "",
            "⚠️  异常统计:",
            f"  🚫 API中不存在: {stats.api_not_found} 个",
            f"  🌐 API错误: {stats.api_errors} 次",
            f"  🔧 验证错误: {stats.validation_errors} 次",
            "",
            "💡 建议:",
            "  - 状态为0的电台已标记为不可用",
            "  - 建议定期运行此脚本保持数据同步",
            "  - 前端查询时使用 WHERE status=1 筛选可用电台",
            "=" * 80
        ]
        
        for line in report_lines:
            logger.info(line)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="电台数据更新工具 - 高效验证5万+电台状态")
    
    # 基本参数
    parser.add_argument("--env", choices=['development', 'production'], 
                       default='production', help="运行环境")
    parser.add_argument("--batch-size", type=int, default=1000, 
                       help="批处理大小 (默认: 1000)")
    parser.add_argument("--concurrent-limit", type=int, default=40, 
                       help="并发验证数量 (默认: 40)")
    parser.add_argument("--timeout", type=int, default=5, 
                       help="验证超时时间秒 (默认: 5)")
    parser.add_argument("--max-records", type=int, 
                       help="最大处理记录数 (用于测试)")
    
    args = parser.parse_args()
    
    # 初始化环境
    try:
        current_env = init_environment()
        env_info = get_environment_info()
        
        # 记录脚本开始
        log_script_start(logger, "电台数据更新工具", 
                        f"环境: {current_env}, 批量: {args.batch_size}, 并发: {args.concurrent_limit}")
        
        # 获取数据库配置
        db_config = get_db_config(current_env)
        
        # 创建更新器
        updater = RadioStationUpdater(
            db_config=db_config,
            batch_size=args.batch_size,
            concurrent_limit=args.concurrent_limit,
            timeout=args.timeout
        )
        
        # 运行更新
        await updater.run_update(max_records=args.max_records)
        
        # 记录成功
        log_script_end(logger, "电台数据更新工具", True, "更新完成")
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"更新失败: {e}")
        log_script_end(logger, "电台数据更新工具", False, f"更新失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 