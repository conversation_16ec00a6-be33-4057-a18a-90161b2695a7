#!/usr/bin/env python3
"""
电台国家数据处理工具
用于处理world_tune_countries表数据和world_tune_station_countries关联关系
支持多环境配置（开发/生产环境）
"""

import argparse
import json
from collections import defaultdict, Counter
from datetime import datetime
from config import (setup_logging, BusinessType, log_script_start, log_script_end,
                   init_environment, get_environment_info)

# 初始化日志系统
logger, warning_details_logger = setup_logging(BusinessType.COUNTRY_MANAGEMENT, advanced=True, logger_name="CountryProcessor")

class CountryProcessor:
    def __init__(self, batch_size=1000, page_size=5000, dry_run=False):
        """初始化国家数据处理器
        
        Args:
            batch_size: 批量插入大小，默认1000
            page_size: 分页大小，默认5000
            dry_run: 试运行模式，默认False
        """
        self.batch_size = batch_size
        self.page_size = page_size
        self.dry_run = dry_run
        
        # 统计信息
        self.stats = {
            'total_stations': 0,
            'processed_stations': 0,
            'countries_created': 0,
            'countries_updated': 0,
            'relations_created': 0,
            'stations_without_country': 0,
            'invalid_country_codes': 0,
            'warnings': []
        }
        
        # 缓存数据
        self.country_cache = {}  # (name, code) -> country_id
        self.invalid_codes = set()  # 记录无效的国家代码
        
        logger.info("CountryProcessor 初始化完成")
        logger.info(f"配置参数: batch_size={batch_size}, page_size={page_size}, dry_run={dry_run}")
    
    def _execute_query(self, query, params=None, fetch_result=True):
        """执行数据库查询"""
        import mysql.connector
        from config import get_db_config
        
        conn = None
        cursor = None
        try:
            conn = mysql.connector.connect(**get_db_config())
            cursor = conn.cursor(dictionary=True, buffered=True)
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch_result:
                result = cursor.fetchall()
            else:
                conn.commit()
                result = cursor.rowcount
            
            return result
            
        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            if fetch_result:
                return []
            else:
                return 0
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def _fetch_all(self, query, params=None):
        """执行查询并返回所有结果"""
        return self._execute_query(query, params)
    
    def _fetch_one(self, query, params=None):
        """执行查询并返回单个结果"""
        results = self._execute_query(query, params, fetch_result=True)
        return results[0] if results and len(results) > 0 else None
    
    def analyze_country_data(self):
        """分析电台表中的国家数据"""
        logger.info("开始分析电台表中的国家数据...")
        
        # 统计总电台数
        query = "SELECT COUNT(*) as total FROM world_tune_radio_stations"
        result = self._execute_query(query)
        if result and len(result) > 0:
            self.stats['total_stations'] = result[0]['total']
        
        logger.info(f"总电台数: {self.stats['total_stations']}")
        
        # 分析国家数据分布
        query = """
        SELECT 
            country, 
            countrycode, 
            iso_3166_2, 
            COUNT(*) as count
        FROM world_tune_radio_stations 
        WHERE country IS NOT NULL AND country != ''
        GROUP BY country, countrycode, iso_3166_2
        ORDER BY count DESC
        LIMIT 50
        """
        
        country_data = self._execute_query(query)
        
        if country_data:
            logger.info(f"发现 {len(country_data)} 种不同的国家数据组合")
            logger.info("前10个最常见的国家数据:")
            for i, item in enumerate(country_data[:10]):
                logger.info(f"  {i+1}. {item['country']} ({item['countrycode']}) - {item['count']} 个电台")
        
        # 分析无效数据
        invalid_query = """
        SELECT 
            COUNT(*) as count,
            'empty_country' as type
        FROM world_tune_radio_stations 
        WHERE country IS NULL OR country = ''
        
        UNION ALL
        
        SELECT 
            COUNT(*) as count,
            'empty_code' as type
        FROM world_tune_radio_stations 
        WHERE countrycode IS NULL OR countrycode = ''
        
        UNION ALL
        
        SELECT 
            COUNT(*) as count,
            'invalid_code_length' as type
        FROM world_tune_radio_stations 
        WHERE countrycode IS NOT NULL 
        AND countrycode != '' 
        AND LENGTH(countrycode) != 2
        """
        
        invalid_data = self._execute_query(invalid_query)
        if invalid_data:
            for item in invalid_data:
                logger.warning(f"数据质量问题 - {item['type']}: {item['count']} 条记录")
        
        return country_data
    
    def process_countries(self):
        """处理国家数据，使用增量更新方式填充world_tune_countries表"""
        logger.info("开始处理国家数据...")
        
        # 获取所有唯一的国家数据 - 只查询可用电台（status=1）
        query = """
        SELECT DISTINCT
            TRIM(country) as country,
            TRIM(UPPER(countrycode)) as countrycode,
            TRIM(iso_3166_2) as iso_3166_2
        FROM world_tune_radio_stations 
        WHERE country IS NOT NULL 
        AND country != '' 
        AND countrycode IS NOT NULL 
        AND countrycode != ''
        AND LENGTH(countrycode) = 2
        AND status = 1
        ORDER BY country, countrycode
        """
        
        countries = self._fetch_all(query)
        logger.info(f"发现 {len(countries)} 个唯一国家数据")
        
        # 使用UPSERT方式更新国家表，保持ID稳定
        insert_count = 0
        update_count = 0
        
        for country in countries:
            if not self.dry_run:
                # 使用INSERT ... ON DUPLICATE KEY UPDATE 保持ID稳定
                upsert_query = """
                INSERT INTO world_tune_countries (
                    name, 
                    code, 
                    iso_3166_2, 
                    updated_at
                ) VALUES (%s, %s, %s, NOW())
                ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    iso_3166_2 = VALUES(iso_3166_2),
                    updated_at = NOW()
                """
                
                # 检查是否为新记录
                check_query = "SELECT id FROM world_tune_countries WHERE code = %s"
                existing = self._fetch_one(check_query, (country['countrycode'],))
                
                self._execute_query(upsert_query, (
                    country['country'],
                    country['countrycode'], 
                    country['iso_3166_2']
                ))
                
                if existing:
                    update_count += 1
                else:
                    insert_count += 1
            else:
                logger.info(f"试运行：将处理国家 {country['country']} ({country['countrycode']})")
        
        logger.info(f"国家数据处理完成 - 新增: {insert_count}, 更新: {update_count}")
        
    def _update_country_sort_order(self):
        """根据电台数量更新国家的sort_order"""
        logger.info("更新国家sort_order...")
        
        if self.dry_run:
            logger.info("试运行模式，跳过sort_order更新")
            return
        
        # 获取每个国家的电台数量并更新sort_order
        query = """
        UPDATE world_tune_countries c
        SET sort_order = (
            SELECT COUNT(*)
            FROM world_tune_radio_stations s
            WHERE TRIM(s.country) = c.name 
            AND TRIM(UPPER(s.countrycode)) = c.code
            AND s.country IS NOT NULL 
            AND s.country != '' 
            AND s.countrycode IS NOT NULL 
            AND s.countrycode != ''
            AND LENGTH(s.countrycode) = 2
            AND s.status = 1
        )
        """
        
        try:
            affected_rows = self._execute_query(query)
            logger.info(f"成功更新了 {affected_rows} 个国家的sort_order")
        except Exception as e:
            logger.error(f"更新国家sort_order失败: {e}")
    
    def process_station_countries(self):
        """处理电台国家关联关系，使用分批增量更新"""
        logger.info("开始处理电台-国家关联关系...")
        
        # 获取需要处理的电台总数 - 只统计可用电台（status=1）
        count_query = """
        SELECT COUNT(*) as total 
        FROM world_tune_radio_stations 
        WHERE country IS NOT NULL 
        AND country != '' 
        AND countrycode IS NOT NULL 
        AND countrycode != ''
        AND LENGTH(countrycode) = 2
        AND status = 1
        """
        
        total_count = self._fetch_one(count_query)['total']
        logger.info(f"需要处理 {total_count} 个电台的国家关联")
        
        processed = 0
        batch_size = self.page_size
        
        # 记录处理开始时间，用于标记本次更新的数据
        update_timestamp = datetime.now()
        
        # 分批处理电台关联关系
        for offset in range(0, total_count, batch_size):
            # 获取一页电台数据 - 只查询可用电台（status=1）
            query = f"""
            SELECT 
                id,
                TRIM(country) as country,
                TRIM(UPPER(countrycode)) as countrycode
            FROM world_tune_radio_stations 
            WHERE country IS NOT NULL 
            AND country != '' 
            AND countrycode IS NOT NULL 
            AND countrycode != ''
            AND LENGTH(countrycode) = 2
            AND status = 1
            ORDER BY id
            LIMIT {self.page_size} OFFSET {offset}
            """
            
            stations = self._fetch_all(query)
            
            for station in stations:
                try:
                    if not self.dry_run:
                        # 1. 获取国家ID
                        country_query = "SELECT id FROM world_tune_countries WHERE code = %s"
                        country_result = self._fetch_one(country_query, (station['countrycode'],))
                        
                        if country_result:
                            country_id = country_result['id']
                            station_id = station['id']
                            
                            # 2. 检查关联是否已存在
                            check_query = """
                            SELECT 1 FROM world_tune_station_countries 
                            WHERE station_id = %s AND country_id = %s
                            """
                            existing = self._fetch_one(check_query, (station_id, country_id))
                            
                            if not existing:
                                # 3. 插入新的关联关系
                                insert_query = """
                                INSERT INTO world_tune_station_countries 
                                (station_id, country_id, created_at, updated_at) 
                                VALUES (%s, %s, NOW(), %s)
                                """
                                self._execute_query(insert_query, (station_id, country_id, update_timestamp))
                            else:
                                # 4. 更新现有关联的时间戳
                                update_query = """
                                UPDATE world_tune_station_countries 
                                SET updated_at = %s 
                                WHERE station_id = %s AND country_id = %s
                                """
                                self._execute_query(update_query, (update_timestamp, station_id, country_id))
                        else:
                            logger.warning(f"找不到国家代码 {station['countrycode']} 对应的国家记录")
                    else:
                        logger.info(f"试运行：电台 {station['id']} -> 国家 {station['countrycode']}")
                        
                except Exception as e:
                    logger.error(f"处理电台 {station['id']} 的国家关联时出错: {e}")
                    continue
            
            processed += len(stations)
            logger.info(f"已处理 {processed}/{total_count} 个电台关联")
        
        # 5. 清理过期的关联关系（可选，基于时间戳）
        if not self.dry_run:
            cleanup_query = """
            DELETE FROM world_tune_station_countries 
            WHERE updated_at < %s
            """
            # 删除本次更新前的旧关联（说明电台数据已变更或不再可用）
            rows_deleted = self._execute_query(cleanup_query, (update_timestamp,), fetch_result=False)
            if rows_deleted > 0:
                logger.info(f"清理了 {rows_deleted} 条过期的电台-国家关联记录")
        
        logger.info("电台-国家关联关系处理完成")
    
    def _log_warning(self, warning_type, details):
        """记录警告信息到JSON日志"""
        warning_data = {
            'timestamp': datetime.now().isoformat(),
            'type': warning_type,
            'details': details
        }
        
        warning_details_logger.warning(json.dumps(warning_data, ensure_ascii=False))
        self.stats['warnings'].append(warning_data)
    
    def print_summary(self):
        """打印处理摘要"""
        logger.info("=" * 60)
        logger.info("处理摘要:")
        logger.info(f"  总电台数: {self.stats['total_stations']}")
        logger.info(f"  已处理电台数: {self.stats['processed_stations']}")
        logger.info(f"  创建国家记录: {self.stats['countries_created']}")
        logger.info(f"  更新国家记录: {self.stats['countries_updated']}")
        logger.info(f"  创建关联记录: {self.stats['relations_created']}")
        logger.info(f"  无国家信息电台: {self.stats['stations_without_country']}")
        logger.info(f"  无效国家代码: {self.stats['invalid_country_codes']}")
        logger.info(f"  警告数量: {len(self.stats['warnings'])}")
        
        if self.stats['processed_stations'] > 0:
            success_rate = (self.stats['relations_created'] / self.stats['processed_stations']) * 100
            logger.info(f"  关联成功率: {success_rate:.2f}%")
        
        logger.info("=" * 60)
    
    def run(self):
        """运行完整的处理流程"""
        try:
            # 1. 分析数据
            self.analyze_country_data()
            
            # 2. 处理国家数据
            self.process_countries()
            
            # 3. 处理电台国家关联
            self.process_station_countries()
            
            # 4. 更新国家sort_order
            self._update_country_sort_order()
            
            # 5. 打印摘要
            self.print_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"处理过程中发生错误: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='电台国家数据处理工具')
    parser.add_argument("--env", choices=['development', 'production'], 
                       default='production', help="运行环境 (development/production)")
    parser.add_argument('--batch-size', type=int, default=1000,
                       help='批量插入大小（默认: 1000）')
    parser.add_argument('--page-size', type=int, default=5000,
                       help='分页大小（默认: 5000）')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，不执行实际的数据库修改操作')
    
    args = parser.parse_args()
    
    # 初始化环境配置
    try:
        current_env = init_environment()
        env_info = get_environment_info()
    except Exception as e:
        logger.error(f"环境配置初始化失败: {e}")
        return 1
    
    # 记录脚本开始
    script_name = "电台国家数据处理工具"
    description = "处理world_tune_countries表数据和world_tune_station_countries关联关系"
    log_script_start(logger, script_name, description)
    
    # 创建处理器并运行
    processor = CountryProcessor(
        batch_size=args.batch_size,
        page_size=args.page_size,
        dry_run=args.dry_run
    )
    
    success = processor.run()
    
    # 记录脚本结束
    summary = f"批量大小: {args.batch_size}, 分页大小: {args.page_size}"
    if args.dry_run:
        summary += ", 试运行模式"
    
    log_script_end(logger, script_name, success, summary)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main()) 