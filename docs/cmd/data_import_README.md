# data_import.py 模块功能说明

## 📋 概述

`data_import.py` 是 World Tune 项目的核心数据导入模块，负责从 Radio Browser API 获取电台数据并导入到数据库中。该模块具备完整的数据质量检查功能和智能历史数据管理能力。

## ✨ 核心功能

### 🚀 一步完成导入
- **默认行为**：同时完成新数据导入和历史数据检查
- **智能排除**：自动避免重复检查刚导入的数据
- **内存友好**：使用时间戳策略，适合大数据量处理

### 🔍 数据质量检查
- **多维度检验**：API指标、基础数据、外部资源可用性
- **并发处理**：支持异步并发检查，提升效率
- **智能过滤**：支持多种音频格式和流媒体协议
- **状态管理**：自动更新电台可用状态

### 🕒 灵活的历史数据处理
- **时间节点控制**：支持指定历史数据截止时间
- **状态过滤**：可按电台当前状态筛选检查范围
- **分页处理**：支持大数据量的分页遍历
- **批量更新**：高效的批量状态更新机制

## 🎯 使用场景

### 日常维护
```bash
# 默认推荐：一步完成所有工作
python data_import.py --method all
```

### 历史数据清理
```bash
# 检查所有历史数据
python data_import.py --check-existing-only

# 检查特定时间前的数据
python data_import.py --check-existing-only --historical-cutoff "2023-12-01 00:00:00"

# 只检查当前可用但可能失效的电台
python data_import.py --check-existing-only --existing-status-filter 1
```

### 快速导入
```bash
# 仅导入新数据，跳过历史检查
python data_import.py --method all --skip-existing-check
```

## 🧠 智能逻辑原理

### 时间戳策略
1. **导入开始时**：记录 `import_start_time`
2. **新数据处理**：更新 `last_check_time` 为当前时间
3. **历史数据筛选**：查询 `last_check_time < cutoff_time` 的记录
4. **避免重复**：自动排除已处理的数据

### 质量检查维度
1. **API质量指标**：`last_check_ok`, `votes`, `clicks`
2. **基础数据完整性**：电台名称、URL有效性
3. **外部资源**：流媒体URL和图标可用性
4. **综合判断**：多维度评分确定最终状态

## 📊 数据处理流程

```mermaid
graph TB
    A[开始] --> B[初始化导入器]
    B --> C{导入新数据?}
    C -->|是| D[从API获取数据]
    D --> E[质量检查]
    E --> F[批量导入/更新]
    F --> G{检查历史数据?}
    C -->|否| G
    G -->|是| H[分页获取历史数据]
    H --> I[过滤截止时间前数据]
    I --> J[并发质量检查]
    J --> K[批量状态更新]
    K --> L[生成报告]
    G -->|否| L
    L --> M[结束]
```

## 🔧 技术特性

### 性能优化
- **批量处理**：支持自定义批量大小
- **异步并发**：可配置并发检查数量
- **分页遍历**：支持大数据集处理
- **连接池**：高效的数据库连接管理

### 错误处理
- **异常恢复**：单个电台失败不影响整体流程
- **详细日志**：分级别日志记录和错误追踪
- **批次保护**：批量操作失败时自动回滚

### 配置灵活性
- **多环境支持**：开发/生产环境自动切换
- **参数可调**：超时时间、并发数、批量大小
- **过滤选项**：支持多种数据筛选条件

## 📈 统计报告

运行完成后自动生成详细的统计报告：

```
📊 数据同步结果摘要
==============================
📥 新数据导入:
  📈 总处理数量: 50000 个电台
  ✅ 可用电台: 45000 个
  ❌ 不可用电台: 5000 个
  🔍 质量检查通过: 45000 个
  🚫 质量检查失败: 5000 个
  📊 质量通过率: 90.00%

📋 历史数据检查:
  🔍 检查数量: 120000 个历史电台
  🔄 状态更新: 15000 个电台
  💡 智能排除: 本次导入的数据已自动排除
```

## 🛡️ 数据安全

### 备份策略
- **UPSERT机制**：新电台自动添加，现有电台状态更新
- **原数据保护**：历史数据检查只更新状态字段
- **事务保护**：批量操作支持事务回滚

### 质量保证
- **多重验证**：API指标 + 实际连通性测试
- **格式检查**：支持主流音频格式和流媒体协议
- **错误记录**：详细记录检查失败原因

这个模块为 World Tune 项目提供了完整、高效、智能的电台数据管理解决方案。 