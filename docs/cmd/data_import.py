#!/usr/bin/env python3
"""
Radio Browser API 数据导入工具
用于从Radio Browser API获取电台数据并导入到World Tune数据库
支持分页遍历和批量插入以提升性能
支持数据质量检查，确保导入的电台数据可用
支持检查和更新历史数据的可用性状态
支持多环境配置（开发/生产环境）
"""

import mysql.connector
import argparse
import json
import asyncio
import aiohttp
import sys
from datetime import datetime
from test import create_api_client
from config import (get_db_config, setup_logging, BusinessType, log_script_start, log_script_end,
                   init_environment, get_environment_info)

# 初始化日志系统
logger, warning_details_logger = setup_logging(BusinessType.DATA_IMPORT, advanced=True, logger_name="RadioDataImporter")

class StationQualityChecker:
    """电台数据质量检查器"""
    
    def __init__(self, timeout=10, concurrent_limit=20):
        self.timeout = timeout
        self.concurrent_limit = concurrent_limit
        
        # 支持的流媒体格式
        self.supported_audio_formats = [
            'audio/mpeg',           # MP3
            'audio/mp3',
            'audio/aac',            # AAC
            'audio/aacp',
            'audio/ogg',            # OGG
            'application/ogg',
            'video/mp2t',           # HLS/M3U8
            'application/vnd.apple.mpegurl',  # M3U8
            'application/x-mpegurl',
            'audio/x-mpegurl',
            'audio/mpegurl',
            'audio/flac',           # FLAC
            'audio/x-flac',
            'audio/wav',            # WAV
            'audio/x-wav'
        ]
        
        # 支持的文件扩展名
        self.supported_extensions = [
            '.mp3', '.m3u8', '.aac', '.ogg', '.flac', '.wav', '.pls', '.m3u'
        ]
    
    async def check_station_quality(self, station_data):
        """检查单个电台的数据质量"""
        try:
            # 1. 检查 API 提供的基础质量指标
            api_check = self._check_api_quality(station_data)
            
            # 2. 检查基础数据完整性
            basic_check = self._check_basic_data(station_data)
            
            # 3. 检查外部资源可用性
            external_check = await self._check_external_resources(station_data)
            
            # 4. 综合判断
            is_available = self._calculate_availability(api_check, basic_check, external_check)
            
            return {
                'status': 1 if is_available else 0,
                'api_check': api_check,
                'basic_check': basic_check,
                'external_check': external_check,
                'reason': self._get_failure_reason(api_check, basic_check, external_check)
            }
            
        except Exception as e:
            logger.warning(f"质量检查异常: {station_data.get('name', 'Unknown')}, 错误: {e}")
            return {
                'status': 0,
                'reason': f"检查异常: {str(e)}"
            }
    
    def _check_api_quality(self, station_data):
        """检查 API 提供的质量指标"""
        # last_check_ok 是关键指标
        last_check_ok = station_data.get('lastcheckok', 0) == 1
        
        # 其他质量指标
        click_count = station_data.get('clickcount', 0)
        vote_count = station_data.get('votes', 0)
        
        return {
            'last_check_ok': last_check_ok,
            'has_clicks': click_count > 0,
            'has_votes': vote_count > 0,
            'click_count': click_count,
            'vote_count': vote_count
        }
    
    def _check_basic_data(self, station_data):
        """检查基础数据完整性"""
        name = station_data.get('name', '').strip()
        url = station_data.get('url_resolved') or station_data.get('url', '')
        
        return {
            'name_valid': self._is_name_valid(name),
            'url_present': bool(url),
            'url_format_valid': self._is_url_format_valid(url) if url else False
        }
    
    def _is_name_valid(self, name):
        """检查电台名称有效性"""
        if not name or len(name) < 2:
            return False
        
        # 排除无意义的名称
        invalid_names = ['unknown', 'untitled', 'no name', 'test', 'demo', '...', 'n/a', 'null']
        if name.lower() in invalid_names:
            return False
        
        # 检查是否全是特殊字符
        if not any(c.isalnum() for c in name):
            return False
        
        return True
    
    def _is_url_format_valid(self, url):
        """检查URL格式有效性"""
        if not url.startswith(('http://', 'https://')):
            return False
        
        # 检查是否包含支持的格式
        url_lower = url.lower()
        return any(ext in url_lower for ext in self.supported_extensions)
    
    async def _check_external_resources(self, station_data):
        """检查外部资源可用性"""
        url = station_data.get('url_resolved') or station_data.get('url', '')
        favicon = station_data.get('favicon', '')
        
        # 并发检查
        tasks = []
        if url:
            tasks.append(self._check_stream_availability(url))
        if favicon:
            tasks.append(self._check_favicon_availability(favicon))
        
        if not tasks:
            return {'stream_available': False, 'favicon_available': True}
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            'stream_available': results[0] if len(results) > 0 and not isinstance(results[0], Exception) else False,
            'favicon_available': results[1] if len(results) > 1 and not isinstance(results[1], Exception) else True
        }
    
    async def _check_stream_availability(self, stream_url):
        """检查流媒体URL可用性 - 支持多种格式"""
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                
                # 1. 先尝试 HEAD 请求
                try:
                    async with session.head(stream_url, allow_redirects=True) as response:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '').lower()
                            if any(fmt in content_type for fmt in self.supported_audio_formats):
                                return True
                            # 检查重定向后的URL
                            if str(response.url).lower() != stream_url.lower():
                                final_url = str(response.url)
                                if any(ext in final_url.lower() for ext in self.supported_extensions):
                                    return True
                except:
                    pass
                
                # 2. HEAD 失败则尝试 GET 请求
                try:
                    async with session.get(stream_url, allow_redirects=True) as response:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '').lower()
                            
                            # 检查 Content-Type
                            if any(fmt in content_type for fmt in self.supported_audio_formats):
                                return True
                            
                            # 对于 M3U8 和播放列表，需要特殊处理
                            if 'mpegurl' in content_type or 'text/plain' in content_type:
                                # 读取少量内容检查是否是播放列表
                                try:
                                    data = await asyncio.wait_for(response.content.read(512), timeout=3)
                                    content = data.decode('utf-8', errors='ignore')
                                    if content.startswith('#EXTM3U') or '.m3u8' in content or '.mp3' in content:
                                        return True
                                except:
                                    pass
                            
                            # 检查是否有音频数据
                            try:
                                data = await asyncio.wait_for(response.content.read(1024), timeout=3)
                                if len(data) > 0:
                                    # 简单的音频格式检查
                                    if data.startswith(b'ID3') or data.startswith(b'\xff\xfb'):  # MP3
                                        return True
                                    if b'ftypM4A' in data[:100]:  # AAC
                                        return True
                                    if b'OggS' in data[:100]:  # OGG
                                        return True
                            except:
                                pass
                except:
                    pass
                
        except Exception as e:
            logger.warning(f"Stream availability check failed for {stream_url}: {e}")
            return False
        
        return False
    
    async def _check_favicon_availability(self, favicon_url):
        """检查favicon图片可用性 - 增强版本
        
        使用多层检查策略：
        1. 先尝试HEAD请求检查基础信息
        2. 如果HEAD失败，尝试GET请求并验证图片内容
        3. 验证图片文件大小和格式
        4. 支持重试机制处理网络不稳定
        """
        if not favicon_url or not favicon_url.strip():
            return False
        
        favicon_url = favicon_url.strip()
        
        # 检查URL格式是否合理
        if not self._is_valid_favicon_url(favicon_url):
            logger.debug(f"不合理的favicon URL格式: {favicon_url}")
            return False
        
        # 支持的图片格式签名（魔术数字）
        image_signatures = {
            b'\x89PNG\r\n\x1a\n': 'PNG',
            b'\xff\xd8\xff': 'JPEG', 
            b'GIF87a': 'GIF87a',
            b'GIF89a': 'GIF89a',
            b'RIFF': 'WEBP',  # WebP格式以RIFF开头
            b'\x00\x00\x01\x00': 'ICO',  # ICO格式
            b'\x00\x00\x02\x00': 'CUR',  # CUR格式（光标，有时用作图标）
        }
        
        # 允许的Content-Type
        valid_content_types = {
            'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 
            'image/webp', 'image/x-icon', 'image/vnd.microsoft.icon',
            'image/ico', 'image/icon', 'application/octet-stream'
        }
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        # 重试机制
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 第一步：尝试HEAD请求（快速检查）
                    try:
                        async with session.head(favicon_url, allow_redirects=True) as response:
                            if response.status == 200:
                                content_type = response.headers.get('content-type', '').lower().split(';')[0]
                                content_length = response.headers.get('content-length')
                                
                                # 检查Content-Type
                                if content_type in valid_content_types:
                                    # 检查文件大小（如果有Content-Length）
                                    if content_length:
                                        try:
                                            size = int(content_length)
                                            if self._is_reasonable_favicon_size(size):
                                                logger.debug(f"✅ HEAD检查通过: {favicon_url} ({content_type}, {size} bytes)")
                                                return True
                                            else:
                                                logger.debug(f"文件大小不合理: {favicon_url} ({size} bytes)")
                                        except ValueError:
                                            pass
                                    else:
                                        # 没有Content-Length，但Content-Type正确，继续进行内容检查
                                        logger.debug(f"HEAD检查部分通过，需要内容验证: {favicon_url} ({content_type})")
                                
                                # 对于某些特殊的Content-Type（如text/html重定向页面），直接失败
                                if content_type in ['text/html', 'text/plain', 'application/json']:
                                    logger.debug(f"❌ 错误的Content-Type: {favicon_url} ({content_type})")
                                    return False
                                    
                    except asyncio.TimeoutError:
                        logger.debug(f"HEAD请求超时: {favicon_url}")
                    except Exception as e:
                        logger.debug(f"HEAD请求失败: {favicon_url}, 错误: {e}")
                    
                    # 第二步：GET请求并验证内容（更彻底的检查）
                    try:
                        async with session.get(favicon_url, allow_redirects=True) as response:
                            if response.status == 200:
                                content_type = response.headers.get('content-type', '').lower().split(';')[0]
                                
                                # 读取前几个字节进行魔术数字检查
                                try:
                                    # 读取前32字节就足够检查大部分图片格式
                                    initial_data = await asyncio.wait_for(response.content.read(32), timeout=3)
                                    
                                    if len(initial_data) < 4:
                                        logger.debug(f"❌ 内容太短: {favicon_url} ({len(initial_data)} bytes)")
                                        return False
                                    
                                    # 检查图片格式签名
                                    detected_format = None
                                    for signature, format_name in image_signatures.items():
                                        if initial_data.startswith(signature):
                                            detected_format = format_name
                                            break
                                        # 特殊处理WEBP格式
                                        if signature == b'RIFF' and len(initial_data) >= 12:
                                            if initial_data.startswith(b'RIFF') and initial_data[8:12] == b'WEBP':
                                                detected_format = 'WEBP'
                                                break
                                    
                                    if detected_format:
                                        # 继续读取更多内容来检查文件大小
                                        try:
                                            remaining_data = await asyncio.wait_for(response.content.read(512 * 1024), timeout=5)  # 最多读取512KB
                                            total_size = len(initial_data) + len(remaining_data)
                                            
                                            if self._is_reasonable_favicon_size(total_size):
                                                logger.debug(f"✅ 内容验证通过: {favicon_url} (格式: {detected_format}, 大小: {total_size} bytes)")
                                                return True
                                            else:
                                                logger.debug(f"❌ 文件大小不合理: {favicon_url} (格式: {detected_format}, 大小: {total_size} bytes)")
                                                return False
                                                
                                        except asyncio.TimeoutError:
                                            # 如果读取超时，但前面的检查通过了，可能是大文件，根据格式判断
                                            if detected_format in ['PNG', 'JPEG', 'GIF87a', 'GIF89a', 'WEBP', 'ICO']:
                                                logger.debug(f"✅ 格式验证通过（读取超时但格式正确）: {favicon_url} (格式: {detected_format})")
                                                return True
                                            else:
                                                logger.debug(f"❌ 读取超时且格式可疑: {favicon_url}")
                                                return False
                                                
                                    else:
                                        # 没有识别到有效的图片格式
                                        logger.debug(f"❌ 无法识别图片格式: {favicon_url}, 前32字节: {initial_data.hex()}")
                                        return False
                                        
                                except asyncio.TimeoutError:
                                    logger.debug(f"❌ 读取内容超时: {favicon_url}")
                                    return False
                                except Exception as e:
                                    logger.debug(f"❌ 读取内容失败: {favicon_url}, 错误: {e}")
                                    return False
                            else:
                                logger.debug(f"❌ HTTP状态错误: {favicon_url} (状态: {response.status})")
                                return False
                                
                    except asyncio.TimeoutError:
                        logger.debug(f"GET请求超时: {favicon_url}")
                    except Exception as e:
                        logger.debug(f"GET请求失败: {favicon_url}, 错误: {e}")
                        
            except Exception as e:
                logger.debug(f"会话创建失败: {favicon_url}, 错误: {e}")
            
            # 如果不是最后一次尝试，等待一下再重试
            if attempt < max_retries:
                logger.debug(f"Favicon检查失败，等待重试 {attempt + 1}/{max_retries}: {favicon_url}")
                await asyncio.sleep(1)
        
        logger.debug(f"❌ Favicon最终检查失败: {favicon_url}")
        return False
    
    def _is_valid_favicon_url(self, favicon_url):
        """检查favicon URL格式是否合理"""
        if not favicon_url.startswith(('http://', 'https://')):
            return False
        
        # 检查是否包含常见的favicon文件扩展名或路径
        favicon_url_lower = favicon_url.lower()
        valid_patterns = [
            '.png', '.jpg', '.jpeg', '.gif', '.ico', '.webp', '.svg',
            'favicon', 'apple-touch-icon', 'icon'
        ]
        
        return any(pattern in favicon_url_lower for pattern in valid_patterns)
    
    def _is_reasonable_favicon_size(self, size_bytes):
        """检查favicon文件大小是否合理
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            bool: 文件大小是否在合理范围内
        """
        # favicon文件通常在1KB到1MB之间
        min_size = 100  # 100字节（非常小的图标）
        max_size = 1024 * 1024  # 1MB（非常大的高清图标）
        
        return min_size <= size_bytes <= max_size
    
    def _calculate_availability(self, api_check, basic_check, external_check):
        """计算综合可用性"""
        # 1. API 的 last_check_ok 是重要指标
        if not api_check['last_check_ok']:
            return False
        
        # 2. 基础数据必须完整
        if not basic_check['name_valid'] or not basic_check['url_present']:
            return False
        
        # 3. 流媒体必须可用
        if not external_check['stream_available']:
            return False
        
        # 4. favicon必须可用（用户要求严格检查）
        if not external_check['favicon_available']:
            return False
        
        return True
    
    def _get_failure_reason(self, api_check, basic_check, external_check):
        """获取失败原因"""
        reasons = []
        
        if not api_check['last_check_ok']:
            reasons.append("API标记为不可用")
        
        if not basic_check['name_valid']:
            reasons.append("电台名称无效")
        
        if not basic_check['url_present']:
            reasons.append("缺少流媒体URL")
        
        if not external_check['stream_available']:
            reasons.append("流媒体不可用")
        
        if not external_check['favicon_available']:
            reasons.append("图标不可用")
        
        return "; ".join(reasons) if reasons else "未知原因"


class RadioDataImporter:
    def __init__(self, db_config, batch_size=1000, enable_quality_check=True, historical_cutoff_time=None):
        """初始化数据导入器
        
        Args:
            db_config: 数据库配置
            batch_size: 批量插入大小，默认1000
            enable_quality_check: 是否启用质量检查，默认True
            historical_cutoff_time: 历史数据截止时间，None则使用导入开始时间
        """
        self.conn = mysql.connector.connect(**db_config)
        self.cursor = self.conn.cursor(dictionary=True)
        self.batch_size = batch_size
        self.insert_batch = []  # 插入批次缓存
        self.update_batch = []  # 更新批次缓存
        self.status_update_batch = []  # 状态更新批次缓存
        self.enable_quality_check = enable_quality_check
        self.quality_checker = StationQualityChecker() if enable_quality_check else None
        
        # 记录导入开始时间，用于区分历史数据和新导入数据
        self.import_start_time = datetime.now()
        
        # 历史数据截止时间：如果指定则使用指定时间，否则使用导入开始时间
        self.historical_cutoff_time = historical_cutoff_time or self.import_start_time
        
        # 同步统计信息
        self.sync_stats = {
            'total_processed': 0,
            'available_stations': 0,
            'unavailable_stations': 0,
            'quality_check_passed': 0,
            'quality_check_failed': 0,
            'existing_checked': 0,
            'existing_updated': 0
        }
        
        logger.info(f"数据导入器初始化完成，质量检查: {'启用' if enable_quality_check else '禁用'}")
        logger.info(f"📅 导入开始时间: {self.import_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if historical_cutoff_time:
            logger.info(f"🕒 历史数据截止时间: {self.historical_cutoff_time.strftime('%Y-%m-%d %H:%M:%S')} (用户指定)")
        else:
            logger.info(f"🕒 历史数据截止时间: {self.historical_cutoff_time.strftime('%Y-%m-%d %H:%M:%S')} (自动设为导入开始时间)")
        if enable_quality_check:
            logger.info("🔍 启用质量检查 - 将自动更新电台可用状态（status字段）")

    def get_existing_stations_count(self, status_filter=None):
        """获取现有电台数量（基于updated_at字段过滤）
        
        Args:
            status_filter: 状态过滤器 (None=全部, 1=可用, 0=不可用, -1=未检查)
            
        Returns:
            int: 电台数量
        """
        try:
            # 准备时间条件：只查询在截止时间之前的历史数据
            time_condition = "(updated_at IS NULL OR updated_at < %s)"
            
            if status_filter is None:
                query = f"SELECT COUNT(*) as count FROM world_tune_radio_stations WHERE {time_condition}"
                self.cursor.execute(query, (self.historical_cutoff_time,))
            else:
                query = f"SELECT COUNT(*) as count FROM world_tune_radio_stations WHERE status = %s AND {time_condition}"
                self.cursor.execute(query, (status_filter, self.historical_cutoff_time))
            
            result = self.cursor.fetchone()
            return result['count'] if result else 0
            
        except Exception as e:
            logger.error(f"获取现有电台数量失败: {e}")
            return 0

    def get_existing_stations_paginated(self, page_size=1000, status_filter=None, max_pages=None):
        """分页获取现有电台数据（基于updated_at字段过滤）
        
        Args:
            page_size: 每页数量
            status_filter: 状态过滤器 (None=全部, 1=可用, 0=不可用, -1=未检查)
            max_pages: 最大页数限制
            
        Yields:
            List[Dict]: 每页的电台数据
        """
        page = 0
        total_processed = 0
        
        # 准备时间条件：只查询在截止时间之前的历史数据
        # 使用updated_at字段，如果为NULL或在截止时间之前，则认为是历史数据
        time_condition = "(updated_at IS NULL OR updated_at < %s)"
        
        while True:
            if max_pages and page >= max_pages:
                logger.info(f"达到最大页数限制 {max_pages}，停止获取")
                break
            
            try:
                offset = page * page_size
                
                if status_filter is None:
                    query = f"""
                        SELECT id, station_uuid, name, url, url_resolved, homepage, favicon,
                               country, countrycode, iso_3166_2, state, language, languagecodes,
                               tags, codec, bitrate, is_hls, votes, click_count, click_trend,
                               ssl_error, geo_lat, geo_long, has_extended_info, last_check_ok,
                               status, last_check_time, last_check_ok_time, updated_at
                        FROM world_tune_radio_stations 
                        WHERE {time_condition}
                        ORDER BY id 
                        LIMIT %s OFFSET %s
                    """
                    self.cursor.execute(query, (self.historical_cutoff_time, page_size, offset))
                else:
                    query = f"""
                        SELECT id, station_uuid, name, url, url_resolved, homepage, favicon,
                               country, countrycode, iso_3166_2, state, language, languagecodes,
                               tags, codec, bitrate, is_hls, votes, click_count, click_trend,
                               ssl_error, geo_lat, geo_long, has_extended_info, last_check_ok,
                               status, last_check_time, last_check_ok_time, updated_at
                        FROM world_tune_radio_stations 
                        WHERE status = %s AND {time_condition}
                        ORDER BY id 
                        LIMIT %s OFFSET %s
                    """
                    self.cursor.execute(query, (status_filter, self.historical_cutoff_time, page_size, offset))
                
                stations = self.cursor.fetchall()
                
                if not stations:
                    logger.info(f"第 {page + 1} 页无数据，遍历完成")
                    break
                
                logger.info(f"第 {page + 1} 页获取到 {len(stations)} 个历史电台（基于updated_at字段过滤）")
                total_processed += len(stations)
                
                yield stations
                
                if len(stations) < page_size:
                    logger.info(f"数据少于页面大小，遍历完成")
                    break
                
                page += 1
                
            except Exception as e:
                logger.error(f"获取第 {page + 1} 页历史数据失败: {e}")
                break
        
        logger.info(f"总共获取到 {total_processed} 个历史电台（基于updated_at字段过滤）")

    async def check_existing_station_quality(self, station_record):
        """检查单个历史电台的质量
        
        Args:
            station_record: 数据库中的电台记录
            
        Returns:
            dict: 质量检查结果
        """
        try:
            # 将数据库记录转换为API格式，以便复用质量检查逻辑
            station_data = {
                'stationuuid': station_record.get('station_uuid', ''),
                'name': station_record.get('name', ''),
                'url': station_record.get('url', ''),
                'url_resolved': station_record.get('url_resolved', ''),
                'homepage': station_record.get('homepage', ''),
                'favicon': station_record.get('favicon', ''),
                'country': station_record.get('country', ''),
                'countrycode': station_record.get('countrycode', ''),
                'iso_3166_2': station_record.get('iso_3166_2', ''),
                'state': station_record.get('state', ''),
                'language': station_record.get('language', ''),
                'languagecodes': station_record.get('languagecodes', ''),
                'tags': station_record.get('tags', ''),
                'codec': station_record.get('codec', ''),
                'bitrate': station_record.get('bitrate', 0),
                'hls': station_record.get('is_hls', 0),
                'votes': station_record.get('votes', 0),
                'clickcount': station_record.get('click_count', 0),
                'clicktrend': station_record.get('click_trend', 0),
                'ssl_error': station_record.get('ssl_error', 0),
                'geo_lat': station_record.get('geo_lat'),
                'geo_long': station_record.get('geo_long'),
                'has_extended_info': station_record.get('has_extended_info', 0),
                'lastcheckok': station_record.get('last_check_ok', 0)
            }
            
            # 使用现有的质量检查器
            quality_result = await self.quality_checker.check_station_quality(station_data)
            
            return {
                'id': station_record['id'],
                'station_uuid': station_record.get('station_uuid', ''),
                'name': station_record.get('name', ''),
                'old_status': station_record.get('status', -1),
                'new_status': quality_result['status'],
                'reason': quality_result.get('reason', ''),
                'quality_result': quality_result
            }
            
        except Exception as e:
            logger.warning(f"检查历史电台质量异常: {station_record.get('name', 'Unknown')}, 错误: {e}")
            return {
                'id': station_record['id'],
                'station_uuid': station_record.get('station_uuid', ''),
                'name': station_record.get('name', ''),
                'old_status': station_record.get('status', -1),
                'new_status': 0,
                'reason': f"检查异常: {str(e)}",
                'quality_result': None
            }

    async def process_existing_stations_batch(self, stations_batch):
        """批量处理历史电台数据的质量检查
        
        Args:
            stations_batch: 历史电台数据批次
        """
        if not self.enable_quality_check or not self.quality_checker:
            logger.warning("质量检查未启用，跳过历史数据检查")
            return
        
        # 并发处理
        concurrent_limit = self.quality_checker.concurrent_limit
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def process_single_existing_station(station):
            async with semaphore:
                try:
                    return await self.check_existing_station_quality(station)
                except Exception as e:
                    logger.error(f"处理历史电台异常: {station.get('name', 'Unknown')}, 错误: {e}")
                    return None
        
        # 并发处理批次中的所有电台
        tasks = [process_single_existing_station(station) for station in stations_batch]
        check_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果并加入更新批次
        for result in check_results:
            if result and not isinstance(result, Exception):
                self.add_to_status_update_batch(result)
                
                # 更新统计
                self.sync_stats['existing_checked'] += 1
                if result['old_status'] != result['new_status']:
                    self.sync_stats['existing_updated'] += 1
                    
                    status_change = f"{result['old_status']} -> {result['new_status']}"
                    if result['new_status'] == 1:
                        logger.info(f"✅ 历史电台状态变更为可用: {result['name']} ({status_change})")
                    else:
                        logger.info(f"❌ 历史电台状态变更为不可用: {result['name']} ({status_change}) - {result['reason']}")
                else:
                    logger.debug(f"🔄 历史电台状态无变化: {result['name']} (状态: {result['old_status']})")

    def add_to_status_update_batch(self, check_result):
        """将状态更新结果添加到批次中
        
        Args:
            check_result: 质量检查结果
        """
        self.status_update_batch.append({
            'id': check_result['id'],
            'status': check_result['new_status'],
            'last_check_time': datetime.now()
        })
        
        # 检查是否需要执行批量更新
        if len(self.status_update_batch) >= self.batch_size:
            self._execute_batch_status_update()

    def _execute_batch_status_update(self):
        """执行批量状态更新"""
        if not self.status_update_batch:
            return
        
        try:
            # 准备批量更新SQL
            update_sql = """
                UPDATE world_tune_radio_stations 
                SET status = %s, last_check_time = %s
                WHERE id = %s
            """
            
            # 准备数据
            values = [(item['status'], item['last_check_time'], item['id']) 
                     for item in self.status_update_batch]
            
            # 执行批量更新
            self.cursor.executemany(update_sql, values)
            self.conn.commit()
            
            logger.info(f"批量更新 {len(self.status_update_batch)} 个历史电台状态")
            
            # 清空批次
            self.status_update_batch = []
            
        except Exception as e:
            logger.error(f"批量状态更新失败: {str(e)}")
            self.conn.rollback()
            raise

    async def check_all_existing_stations(self, page_size=1000, status_filter=None, max_pages=None):
        """检查所有历史电台的可用性（智能排除指定时间后的数据）
        
        Args:
            page_size: 每页处理数量
            status_filter: 状态过滤器 (None=全部, 1=可用, 0=不可用, -1=未检查)
            max_pages: 最大页数限制
        """
        if not self.enable_quality_check:
            logger.error("质量检查未启用，无法检查历史数据")
            return
        
        logger.info("=" * 60)
        logger.info("🔍 开始检查历史电台数据可用性")
        logger.info(f"📅 导入开始时间: {self.import_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🕒 历史数据截止时间: {self.historical_cutoff_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("💡 智能策略: 基于updated_at字段过滤，只检查截止时间前的历史数据")
        logger.info("=" * 60)
        
        # 获取总数量
        total_count = self.get_existing_stations_count(status_filter)
        logger.info(f"📊 需要检查的历史电台数量: {total_count} 个")
        
        if total_count == 0:
            logger.info("✅ 没有需要检查的历史电台（基于updated_at字段过滤，无符合条件数据）")
            return
        
        # 分页处理
        processed_count = 0
        for stations_batch in self.get_existing_stations_paginated(page_size, status_filter, max_pages):
            try:
                await self.process_existing_stations_batch(stations_batch)
                processed_count += len(stations_batch)
                
                progress = (processed_count / total_count) * 100 if total_count > 0 else 0
                logger.info(f"📈 历史数据检查进度: {processed_count}/{total_count} ({progress:.1f}%)")
                
            except Exception as e:
                logger.error(f"处理历史数据批次失败: {e}")
        
        # 处理剩余的状态更新批次
        if self.status_update_batch:
            self._execute_batch_status_update()
        
        logger.info("=" * 60)
        logger.info("✅ 历史电台数据可用性检查完成")
        logger.info(f"📊 检查完成: {self.sync_stats['existing_checked']} 个历史电台")
        logger.info(f"🔄 状态更新: {self.sync_stats['existing_updated']} 个电台")
        logger.info("💡 说明: 基于updated_at字段智能过滤，已排除截止时间后的数据")
        logger.info("=" * 60)
    
    def _parse_datetime(self, datetime_str):
        """解析ISO 8601格式的日期时间字符串为MySQL兼容格式
        
        Args:
            datetime_str: ISO 8601格式的日期时间字符串
            
        Returns:
            datetime对象或None
        """
        if not datetime_str:
            return None
        try:
            # 处理ISO 8601格式 (如: 2025-06-12T17:54:18Z)
            if datetime_str.endswith('Z'):
                datetime_str = datetime_str[:-1] + '+00:00'
            return datetime.fromisoformat(datetime_str)
        except ValueError:
            logger.warning(f"无法解析日期时间: {datetime_str}")
            return None
    
    def _truncate_field(self, value, max_length=255, field_name="unknown", station_data=None):
        """截断字段内容到指定长度
        
        Args:
            value: 字段值
            max_length: 最大长度
            field_name: 字段名称
            station_data: 原始电台数据（用于记录详细信息）
            
        Returns:
            str: 截断后的字符串
        """
        if not value:
            return ''
        value = str(value).strip()
        if len(value) > max_length:
            truncated = value[:max_length-3] + '...'
            
            # 记录详细的警告信息
            warning_detail = {
                "timestamp": datetime.now().isoformat(),
                "stationuuid": station_data.get('stationuuid', '') if station_data else '',
                "serveruuid": station_data.get('serveruuid', '') if station_data else '',
                "changeuuid": station_data.get('changeuuid', '') if station_data else '',
                "msg": f"字段长度过长：{field_name}字段从{len(value)}字符截断到{max_length}字符",
                "field_name": field_name,
                "original_length": len(value),
                "truncated_length": max_length,
                "original_value": value[:100] + "..." if len(value) > 100 else value
            }
            warning_details_logger.warning(json.dumps(warning_detail, ensure_ascii=False))
            
            logger.warning(f"字段过长已截断: {field_name} - {value[:50]}... -> {len(value)} chars -> {max_length} chars")
            return truncated
        return value
    
    def _clean_station_data(self, station_data):
        """清理电台数据，处理异常情况
        
        Args:
            station_data: 原始电台数据
            
        Returns:
            dict: 清理后的电台数据
        """
        # 复制原始数据
        cleaned_data = station_data.copy()
        
        # 清理name字段：如果name只是数字或过短，尝试从其他字段获取
        name = str(cleaned_data.get('name', '')).strip()
        if not name or name.isdigit() or len(name) <= 2:
            # 尝试从URL中提取有意义的名称
            url = cleaned_data.get('url', '')
            if url:
                # 从URL中提取可能的电台名称
                import re
                # 提取域名或路径中的有意义部分
                match = re.search(r'//([^/]+)', url)
                if match:
                    domain = match.group(1)
                    # 移除常见的前缀和后缀
                    domain = re.sub(r'^(www\.|stream\.|radio\.)', '', domain)
                    domain = re.sub(r'\.(com|fm|org|net)$', '', domain)
                    if len(domain) > 3:
                        new_name = domain.replace('.', ' ').title()
                        
                        # 记录详细的警告信息
                        warning_detail = {
                            "timestamp": datetime.now().isoformat(),
                            "stationuuid": station_data.get('stationuuid', ''),
                            "serveruuid": station_data.get('serveruuid', ''),
                            "changeuuid": station_data.get('changeuuid', ''),
                            "msg": f"电台名称异常修复：原名称'{name}' -> 从URL提取: '{new_name}'",
                            "field_name": "name",
                            "original_name": name,
                            "extracted_name": new_name,
                            "source_url": url
                        }
                        warning_details_logger.warning(json.dumps(warning_detail, ensure_ascii=False))
                        
                        cleaned_data['name'] = new_name
                        logger.warning(f"电台名称异常 '{name}' -> 从URL提取: '{new_name}'")
        
        # 清理tags字段：如果包含URL，则清空
        tags = str(cleaned_data.get('tags', '')).strip()
        if tags and ('http://' in tags or 'https://' in tags):
            # 记录详细的警告信息
            warning_detail = {
                "timestamp": datetime.now().isoformat(),
                "stationuuid": station_data.get('stationuuid', ''),
                "serveruuid": station_data.get('serveruuid', ''),
                "changeuuid": station_data.get('changeuuid', ''),
                "msg": f"Tags字段包含URL已清理：{tags}",
                "field_name": "tags",
                "original_tags": tags,
                "cleaned_tags": ""
            }
            warning_details_logger.warning(json.dumps(warning_detail, ensure_ascii=False))
            
            logger.warning(f"Tags字段包含URL，已清理: {tags}")
            cleaned_data['tags'] = ''
        
        return cleaned_data
    
    async def _prepare_station_data(self, station_data):
        """准备电台数据，处理字段映射和数据类型转换（支持质量检查）
        
        Args:
            station_data: 原始电台数据
            
        Returns:
            dict: 处理后的电台数据
        """
        # 首先清理数据
        cleaned_data = self._clean_station_data(station_data)
        
        # 默认状态为可用
        status = 1
        
        # 如果启用质量检查，则进行检查
        if self.enable_quality_check and self.quality_checker:
            try:
                quality_result = await self.quality_checker.check_station_quality(cleaned_data)
                status = quality_result['status']
                
                # 更新统计信息
                self.sync_stats['total_processed'] += 1
                if status == 1:
                    self.sync_stats['available_stations'] += 1
                    self.sync_stats['quality_check_passed'] += 1
                    logger.debug(f"✅ 电台可用: {cleaned_data.get('name', 'Unknown')}")
                else:
                    self.sync_stats['unavailable_stations'] += 1
                    self.sync_stats['quality_check_failed'] += 1
                    logger.info(f"❌ 电台不可用: {cleaned_data.get('name', 'Unknown')} - {quality_result.get('reason', '未知原因')}")
                    
            except Exception as e:
                logger.warning(f"质量检查异常，设为不可用: {cleaned_data.get('name', 'Unknown')}, 错误: {e}")
                status = 0
                self.sync_stats['total_processed'] += 1
                self.sync_stats['unavailable_stations'] += 1
                self.sync_stats['quality_check_failed'] += 1
        else:
            # 未启用质量检查时的统计
            self.sync_stats['total_processed'] += 1
            self.sync_stats['available_stations'] += 1
        
        return {
            'station_uuid': self._truncate_field(cleaned_data.get('stationuuid', ''), 36, 'station_uuid', cleaned_data),  # varchar(36)
            'server_uuid': self._truncate_field(cleaned_data.get('serveruuid', ''), 36, 'server_uuid', cleaned_data),   # varchar(36)
            'change_uuid': self._truncate_field(cleaned_data.get('changeuuid', ''), 36, 'change_uuid', cleaned_data),   # varchar(36)
            'name': self._truncate_field(cleaned_data.get('name', ''), 1255, 'name', cleaned_data),              # varchar(1255) - 用户修改过
            'url': cleaned_data.get('url', ''),                                            # text - 不需要截断
            'url_resolved': cleaned_data.get('url_resolved', ''),                          # text - 不需要截断
            'homepage': cleaned_data.get('homepage', ''),                                  # text - 不需要截断
            'favicon': cleaned_data.get('favicon', ''),                                    # text - 不需要截断
            'country': self._truncate_field(cleaned_data.get('country', ''), 100, 'country', cleaned_data),         # varchar(100)
            'countrycode': self._truncate_field(cleaned_data.get('countrycode', ''), 2, 'countrycode', cleaned_data),   # varchar(2)
            'iso_3166_2': self._truncate_field(cleaned_data.get('iso_3166_2', ''), 10, 'iso_3166_2', cleaned_data),    # varchar(10)
            'state': self._truncate_field(cleaned_data.get('state', ''), 100, 'state', cleaned_data),             # varchar(100)
            'language': self._truncate_field(cleaned_data.get('language', ''), 100, 'language', cleaned_data),       # varchar(100)
            'languagecodes': self._truncate_field(cleaned_data.get('languagecodes', ''), 50, 'languagecodes', cleaned_data), # varchar(50)
            'tags': cleaned_data.get('tags', ''),                                          # text - 不需要截断
            'codec': self._truncate_field(cleaned_data.get('codec', ''), 20, 'codec', cleaned_data),              # varchar(20)
            'bitrate': cleaned_data.get('bitrate', 0) or 0,
            'is_hls': cleaned_data.get('hls', 0) or 0,
            'votes': cleaned_data.get('votes', 0) or 0,
            'click_count': cleaned_data.get('clickcount', 0) or 0,
            'click_trend': cleaned_data.get('clicktrend', 0) or 0,
            'ssl_error': cleaned_data.get('ssl_error', 0) or 0,
            'geo_lat': cleaned_data.get('geo_lat'),
            'geo_long': cleaned_data.get('geo_long'),
            'geo_distance': cleaned_data.get('geo_distance'),
            'has_extended_info': 1 if cleaned_data.get('has_extended_info') else 0,
            'last_check_ok': cleaned_data.get('lastcheckok', 0) or 0,
            'last_change_time': self._parse_datetime(cleaned_data.get('lastchangetime_iso8601', '')),
            'last_check_time': self._parse_datetime(cleaned_data.get('lastchecktime_iso8601', '')),
            'last_check_ok_time': self._parse_datetime(cleaned_data.get('lastcheckoktime_iso8601', '')),
            'last_local_check_time': self._parse_datetime(cleaned_data.get('lastlocalchecktime_iso8601', '')),
            'click_timestamp': self._parse_datetime(cleaned_data.get('clicktimestamp_iso8601', '')),
            'status': status
        }
    
    def add_to_batch(self, prepared_data):
        """将已准备好的电台数据添加到批次中
        
        Args:
            prepared_data: 已处理的电台数据
        """
        try:
            # 添加到插入批次（使用ON DUPLICATE KEY UPDATE统一处理）
            self.insert_batch.append(prepared_data)
            
            # 检查是否需要执行批量操作
            if len(self.insert_batch) >= self.batch_size:
                self._execute_batch_insert()
                
        except Exception as e:
            logger.error(f"添加电台到批次失败: {str(e)}")
            logger.error(f"问题数据: station_uuid={prepared_data.get('station_uuid', '')}, name={prepared_data.get('name', '')}")
            raise
    
    async def process_stations_batch(self, stations_batch):
        """批量处理电台数据（支持异步质量检查）
        
        Args:
            stations_batch: 电台数据批次
        """
        if not self.enable_quality_check:
            # 不启用质量检查时的快速处理
            for station in stations_batch:
                data = await self._prepare_station_data(station)
                self.add_to_batch(data)
            return
        
        # 启用质量检查时的并发处理
        concurrent_limit = self.quality_checker.concurrent_limit if self.quality_checker else 20
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def process_single_station(station):
            async with semaphore:
                try:
                    return await self._prepare_station_data(station)
                except Exception as e:
                    logger.error(f"处理电台异常: {station.get('name', 'Unknown')}, 错误: {e}")
                    return None
        
        # 并发处理批次中的所有电台
        tasks = [process_single_station(station) for station in stations_batch]
        processed_stations = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        success_count = 0
        failed_count = 0
        
        for station_data in processed_stations:
            if station_data and not isinstance(station_data, Exception):
                self.add_to_batch(station_data)
                success_count += 1
            else:
                failed_count += 1
        
        if failed_count > 0:
            logger.warning(f"批次处理完成: 成功 {success_count}, 失败 {failed_count}")
        else:
            logger.info(f"批次处理完成: 成功 {success_count}")
    
    def add_station_legacy(self, station_data):
        """兼容旧版本的同步方法（不推荐使用）
        
        Args:
            station_data: 电台数据
        """
        try:
            # 创建一个简单的事件循环来处理异步调用
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                data = loop.run_until_complete(self._prepare_station_data(station_data))
                self.add_to_batch(data)
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"添加电台到批次失败: {str(e)}")
            logger.error(f"问题数据: station_uuid={station_data.get('stationuuid', '')}, name={station_data.get('name', '')}")
            raise
    
    def _execute_batch_insert(self):
        """执行批量插入，使用ON DUPLICATE KEY UPDATE处理重复记录"""
        if not self.insert_batch:
            return
        
        try:
            # 准备批量插入SQL，使用ON DUPLICATE KEY UPDATE
            fields = list(self.insert_batch[0].keys())
            placeholders = ['%s'] * len(fields)
            
            # 构建UPDATE子句，排除主键和唯一键
            update_fields = [f"{key} = VALUES({key})" for key in fields if key not in ['id', 'station_uuid']]
            
            insert_sql = f"""
                INSERT INTO world_tune_radio_stations 
                ({', '.join(fields)})
                VALUES ({', '.join(placeholders)})
                ON DUPLICATE KEY UPDATE {', '.join(update_fields)}
            """
            
            # 准备数据
            values = [list(data.values()) for data in self.insert_batch]
            
            # 执行批量插入/更新
            self.cursor.executemany(insert_sql, values)
            self.conn.commit()
            
            logger.info(f"批量插入/更新 {len(self.insert_batch)} 个电台")
            
            # 清空批次
            self.insert_batch = []
            
        except Exception as e:
            logger.error(f"批量插入/更新失败: {str(e)}")
            self.conn.rollback()
            raise
    
    def _execute_batch_update(self):
        """执行批量更新（已不需要，统一使用insert方法）"""
        # 保留此方法以防调用，但实际不执行任何操作
        if self.update_batch:
            logger.info(f"跳过批量更新 {len(self.update_batch)} 个电台（已统一处理）")
            self.update_batch = []
    
    def flush_batches(self):
        """刷新所有批次，处理剩余数据"""
        try:
            if self.insert_batch:
                self._execute_batch_insert()
            if self.update_batch:
                self._execute_batch_update()
            if self.status_update_batch:
                self._execute_batch_status_update()
        except Exception as e:
            logger.error(f"刷新批次失败: {str(e)}")
            raise
    
    def get_sync_summary(self):
        """获取同步结果摘要"""
        stats = self.sync_stats
        summary_lines = [
            "=" * 60,
            "📊 数据同步结果摘要",
            "=" * 60
        ]
        
        # 新数据导入统计
        if stats['total_processed'] > 0:
            summary_lines.extend([
                f"📥 新数据导入:",
                f"  📈 总处理数量: {stats['total_processed']} 个电台",
                f"  ✅ 可用电台: {stats['available_stations']} 个",
                f"  ❌ 不可用电台: {stats['unavailable_stations']} 个"
            ])
            
            if self.enable_quality_check:
                success_rate = (stats['quality_check_passed'] / stats['total_processed']) * 100 if stats['total_processed'] > 0 else 0
                summary_lines.extend([
                    f"  🔍 质量检查通过: {stats['quality_check_passed']} 个",
                    f"  🚫 质量检查失败: {stats['quality_check_failed']} 个",
                    f"  📊 质量通过率: {success_rate:.2f}%"
                ])
        
        # 历史数据检查统计
        if stats['existing_checked'] > 0:
            summary_lines.extend([
                f"📋 历史数据检查:",
                f"  🔍 检查数量: {stats['existing_checked']} 个历史电台",
                f"  🔄 状态更新: {stats['existing_updated']} 个电台",
                f"  💡 智能排除: 本次导入的数据已自动排除"
            ])
        
        if stats['total_processed'] == 0 and stats['existing_checked'] == 0:
            summary_lines.append("📝 未处理任何数据")
        
        summary_lines.extend([
            "=" * 60,
            "💡 说明:",
            "   - 新电台: 使用UPSERT机制，自动处理新增和更新",
            "   - 历史电台: 只更新status和last_check_time字段",
            "   - 前端查询: WHERE status=1 获取可用电台"
        ])
        
        return "\n".join(summary_lines)
    
    def close(self):
        """关闭数据库连接"""
        try:
            # 处理剩余的批次数据
            self.flush_batches()
            
            # 输出同步摘要
            if hasattr(self, 'sync_stats'):
                logger.info("\n" + self.get_sync_summary())
                
        except Exception as e:
            logger.error(f"关闭前刷新批次失败: {str(e)}")
        finally:
            if hasattr(self, 'cursor') and self.cursor:
                self.cursor.close()
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()

def get_all_stations_paginated(api, method, page_size=1000, max_pages=None, **kwargs):
    """分页获取所有电台数据
    
    Args:
        api: API客户端实例
        method: 方法名
        page_size: 每页大小
        max_pages: 最大页数限制（用于测试）
        **kwargs: 方法参数
        
    Yields:
        List[Dict]: 每页的电台列表
    """
    page = 0
    total_stations = 0
    
    # 所有可用的获取电台方法
    methods = {
        'all': lambda **params: api.get_all_stations(**params),
        'top_vote': lambda **params: api.get_top_vote_stations(**params),
        'top_click': lambda **params: api.get_top_click_stations(**params),
        'last_click': lambda **params: api.get_last_click_stations(**params),
        'last_change': lambda **params: api.get_last_change_stations(**params),
        'broken': lambda **params: api.get_broken_stations(**params),
        'by_name': lambda **params: api.get_stations_by_name(params.get('name', ''), **{k: v for k, v in params.items() if k != 'name'}),
        'by_country': lambda **params: api.get_stations_by_country(params.get('country', ''), **{k: v for k, v in params.items() if k != 'country'}),
        'by_countrycode': lambda **params: api.get_stations_by_countrycode(params.get('countrycode', ''), **{k: v for k, v in params.items() if k != 'countrycode'}),
        'by_state': lambda **params: api.get_stations_by_state(params.get('state', ''), **{k: v for k, v in params.items() if k != 'state'}),
        'by_language': lambda **params: api.get_stations_by_language(params.get('language', ''), **{k: v for k, v in params.items() if k != 'language'}),
        'by_tag': lambda **params: api.get_stations_by_tag(params.get('tag', ''), **{k: v for k, v in params.items() if k != 'tag'}),
        'by_codec': lambda **params: api.get_stations_by_codec(params.get('codec', ''), **{k: v for k, v in params.items() if k != 'codec'}),
        'search': lambda **params: api.search_stations(**params),
    }
    
    if method not in methods:
        raise ValueError(f"不支持的方法: {method}. 支持的方法: {list(methods.keys())}")
    
    method_func = methods[method]
    
    while True:
        # 如果设置了最大页数限制，检查是否超过
        if max_pages and page >= max_pages:
            logger.info(f"达到最大页数限制 {max_pages}，停止获取")
            break
        
        # 准备当前页的参数
        current_params = kwargs.copy()
        current_params['limit'] = page_size
        current_params['offset'] = page * page_size
        
        try:
            # 获取当前页数据
            stations = method_func(**current_params)
            
            if not stations:
                logger.info(f"第 {page + 1} 页无数据，遍历完成")
                break
            
            logger.info(f"第 {page + 1} 页获取到 {len(stations)} 个电台")
            total_stations += len(stations)
            
            yield stations
            
            # 如果返回的数据少于page_size，说明已经是最后一页
            if len(stations) < page_size:
                logger.info(f"数据少于页面大小，遍历完成")
                break
            
            page += 1
            
        except Exception as e:
            logger.error(f"获取第 {page + 1} 页数据失败: {str(e)}")
            break
    
    logger.info(f"总共获取到 {total_stations} 个电台")

async def main_async():
    """异步主函数"""
    parser = argparse.ArgumentParser(description="Radio Browser API 数据导入工具 (支持质量检查和多环境)")
    
    # 环境配置参数
    parser.add_argument("--env", choices=['development', 'production'], 
                       default='production', help="运行环境 (development/production)")
    
    # 数据导入参数
    parser.add_argument("--method", default="all", 
                        choices=['all', 'top_vote', 'top_click', 'last_click', 'last_change', 'broken', 
                                'by_name', 'by_country', 'by_countrycode', 'by_state', 'by_language', 'by_tag', 'by_codec', 'search'],
                        help="获取电台数据的方法（默认：all - 全量同步）")
    parser.add_argument("--page-size", type=int, default=1000, help="每页获取的电台数量")
    parser.add_argument("--batch-size", type=int, default=1000, help="批量插入的大小")
    parser.add_argument("--max-pages", type=int, help="最大页数限制（用于测试）")
    
    # 历史数据检查参数
    parser.add_argument("--skip-existing-check", action="store_true", help="跳过历史数据检查（默认会自动检查历史数据）")
    parser.add_argument("--check-existing-only", action="store_true", help="仅检查历史数据，不导入新数据")
    parser.add_argument("--existing-status-filter", type=int, choices=[-1, 0, 1], 
                       help="历史数据状态过滤器 (-1=未检查, 0=不可用, 1=可用, 不指定=全部)")
    parser.add_argument("--existing-max-pages", type=int, help="历史数据检查最大页数限制")
    parser.add_argument("--historical-cutoff", type=str, 
                       help="历史数据截止时间 (格式: YYYY-MM-DD HH:MM:SS)，不指定则使用当前时间")
    
    # 质量检查相关参数  
    parser.add_argument("--skip-quality-check", action="store_true", help="跳过数据质量检查（不推荐，影响数据同步质量）")
    parser.add_argument("--quality-timeout", type=int, default=10, help="质量检查超时时间（秒）")
    parser.add_argument("--concurrent-checks", type=int, default=20, help="并发检查数量")
    
    # 各种搜索参数
    parser.add_argument("--name", help="电台名称")
    parser.add_argument("--country", help="国家名称")
    parser.add_argument("--countrycode", help="国家代码 (如: CN, US)")
    parser.add_argument("--state", help="州/省名称")
    parser.add_argument("--language", help="语言")
    parser.add_argument("--tag", help="标签")
    parser.add_argument("--codec", help="编码格式")
    parser.add_argument("--exact", action="store_true", help="精确匹配")
    
    args = parser.parse_args()
    
    # 初始化环境配置
    try:
        current_env = init_environment()
        env_info = get_environment_info()
    except Exception as e:
        logger.error(f"环境配置初始化失败: {e}")
        sys.exit(1)
    
    # 检查参数兼容性
    if args.check_existing_only and args.skip_quality_check:
        logger.error("❌ --check-existing-only 和 --skip-quality-check 不能同时使用")
        sys.exit(1)
    
    # 解析历史数据截止时间
    historical_cutoff_time = None
    if args.historical_cutoff:
        try:
            historical_cutoff_time = datetime.strptime(args.historical_cutoff, "%Y-%m-%d %H:%M:%S")
            logger.info(f"🕒 用户指定历史数据截止时间: {historical_cutoff_time.strftime('%Y-%m-%d %H:%M:%S')}")
        except ValueError:
            logger.error(f"❌ 历史数据截止时间格式错误，应为: YYYY-MM-DD HH:MM:SS")
            sys.exit(1)
    
    # 确定操作模式
    will_import_new = not args.check_existing_only
    will_check_existing = not args.skip_existing_check or args.check_existing_only
    
    if args.check_existing_only:
        operation_mode = "仅检查历史数据"
    elif will_import_new and will_check_existing:
        operation_mode = "一步完成：导入新数据 + 检查历史数据"
    elif will_import_new:
        operation_mode = "仅导入新数据"
    else:
        operation_mode = "无操作"
    
    # 记录脚本开始
    quality_status = "禁用" if args.skip_quality_check else "启用"
    
    log_script_start(logger, "Radio Browser API 数据导入工具", 
                    f"操作模式: {operation_mode}, 方法: {args.method}, 页面大小: {args.page_size}, 批量大小: {args.batch_size}, 质量检查: {quality_status}")
    
    # 第一步：加载数据库配置
    db_config = get_db_config(current_env)
    
    # 创建数据导入器（支持质量检查和历史数据截止时间）
    enable_quality_check = not args.skip_quality_check
    importer = RadioDataImporter(
        db_config, 
        batch_size=args.batch_size,
        enable_quality_check=enable_quality_check,
        historical_cutoff_time=historical_cutoff_time
    )
    
    # 如果启用质量检查，设置相关参数
    if enable_quality_check and importer.quality_checker:
        importer.quality_checker.timeout = args.quality_timeout
        importer.quality_checker.concurrent_limit = args.concurrent_checks
        logger.info(f"质量检查参数: 超时{args.quality_timeout}秒, 并发{args.concurrent_checks}")
    
    success = False
    summary = ""
    
    try:
        # 第一步：导入新数据（除非只检查历史数据）
        if will_import_new:
            # 第二步：请求test.py中的方法
            api = create_api_client("WorldTuneImporter/1.0.0")
            
            # 准备参数
            params = {}
            
            # 根据方法添加特定参数
            if args.name:
                params['name'] = args.name
            if args.country:
                params['country'] = args.country
            if args.countrycode:
                params['countrycode'] = args.countrycode
            if args.state:
                params['state'] = args.state
            if args.language:
                params['language'] = args.language
            if args.tag:
                params['tag'] = args.tag
            if args.codec:
                params['codec'] = args.codec
            if args.exact:
                params['exact'] = args.exact
            
            success_count = 0
            failed_count = 0
            page_count = 0
            
            logger.info("📥 开始导入新数据...")
            
            # 第三步：分页获取数据并批量处理
            for stations_page in get_all_stations_paginated(api, args.method, 
                                                           page_size=args.page_size, 
                                                           max_pages=args.max_pages, 
                                                           **params):
                page_count += 1
                
                try:
                    # 使用异步批量处理
                    await importer.process_stations_batch(stations_page)
                    
                    page_success = len(stations_page)
                    success_count += page_success
                    
                    logger.info(f"第 {page_count} 页处理完成: 处理 {page_success} 个电台")
                    
                except Exception as e:
                    page_failed = len(stations_page)
                    failed_count += page_failed
                    logger.error(f"第 {page_count} 页处理失败: {str(e)}")
            
            logger.info(f"✅ 新数据导入完成: 成功 {success_count} 个，失败 {failed_count} 个")
        
        # 第二步：检查历史数据可用性
        if will_check_existing:
            logger.info("🔍 开始检查历史数据可用性...")
            await importer.check_all_existing_stations(
                page_size=args.page_size,
                status_filter=args.existing_status_filter,
                max_pages=args.existing_max_pages
            )
        
        # 处理剩余的批次数据
        importer.flush_batches()
        
        success = True
        summary = "数据处理完成"
        
        logger.info(f"🎉 全部操作完成")
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}")
        summary = f"处理失败: {str(e)}"
    finally:
        importer.close()
        log_script_end(logger, "Radio Browser API 数据导入工具", success, summary)

def main():
    """主函数包装器"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
