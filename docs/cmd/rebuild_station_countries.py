#!/usr/bin/env python3
"""
电台国家关联关系重建工具
用于重建world_tune_station_countries表的关联关系
支持TRUNCATE清空和增量更新两种策略
支持多环境配置（开发/生产环境）
"""

import argparse
import json
import mysql.connector
from collections import defaultdict, Counter
from datetime import datetime
from config import (get_db_config, setup_logging, BusinessType, log_script_start, log_script_end,
                   init_environment, get_environment_info)

# 初始化日志系统
logger, warning_details_logger = setup_logging(BusinessType.COUNTRY_MANAGEMENT, advanced=True, logger_name="StationCountryRebuilder")

class StationCountryRebuilder:
    def __init__(self, batch_size=1000, page_size=5000, use_truncate=False, dry_run=False):
        """初始化电台国家关联关系重建器
        
        Args:
            batch_size: 批量处理大小，默认1000
            page_size: 分页大小，默认5000
            use_truncate: 是否使用TRUNCATE清空策略，默认False（建议使用增量更新）
            dry_run: 试运行模式，默认False
        """
        self.batch_size = batch_size
        self.page_size = page_size
        self.use_truncate = use_truncate
        self.dry_run = dry_run
        
        # 统计信息
        self.stats = {
            'total_stations': 0,
            'processed_stations': 0,
            'existing_relations': 0,
            'new_relations': 0,
            'updated_relations': 0,
            'deleted_relations': 0,
            'orphaned_stations': 0,
            'invalid_relations': 0,
            'warnings': []
        }
        
        # 缓存数据
        self.country_cache = {}  # (name, code) -> country_id
        self.existing_relations = set()  # (station_id, country_id)
        self.new_relations = set()  # (station_id, country_id)
        
        logger.info("StationCountryRebuilder 初始化完成")
        logger.info(f"配置参数: batch_size={batch_size}, page_size={page_size}, use_truncate={use_truncate}, dry_run={dry_run}")
    
    def _execute_query(self, query):
        """执行数据库查询"""
        import mysql.connector
        from config import get_db_config
        
        try:
            conn = mysql.connector.connect(**get_db_config())
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query)
            
            # 根据查询类型返回结果
            if query.strip().upper().startswith(('SELECT', 'SHOW', 'DESCRIBE')):
                result = cursor.fetchall()
                cursor.close()
                conn.close()
                return result
            else:
                # 对于INSERT、UPDATE、DELETE等操作
                conn.commit()
                affected_rows = cursor.rowcount
                cursor.close()
                conn.close()
                return affected_rows
                
        except Exception as e:
            logger.error(f"数据库查询失败: {e}")
            return []
    
    def _load_country_cache(self):
        """加载国家缓存"""
        logger.info("加载国家缓存...")
        
        query = "SELECT id, name, code FROM world_tune_countries"
        countries = self._execute_query(query)
        
        if countries:
            for country in countries:
                key = (country['name'], country['code'])
                self.country_cache[key] = country['id']
            
            logger.info(f"加载了 {len(countries)} 个国家到缓存")
        else:
            logger.warning("没有找到国家数据")
    
    def _load_existing_relations(self):
        """加载现有关联关系"""
        logger.info("加载现有关联关系...")
        
        query = "SELECT station_id, country_id FROM world_tune_station_countries"
        relations = self._execute_query(query)
        
        if relations:
            for relation in relations:
                key = (relation['station_id'], relation['country_id'])
                self.existing_relations.add(key)
            
            self.stats['existing_relations'] = len(relations)
            logger.info(f"加载了 {len(relations)} 个现有关联关系")
        else:
            logger.info("没有找到现有关联关系")
    
    def analyze_current_state(self):
        """分析当前数据状态"""
        logger.info("分析当前数据状态...")
        
        # 统计电台总数
        query = "SELECT COUNT(*) as total FROM world_tune_radio_stations"
        result = self._execute_query(query)
        if result:
            self.stats['total_stations'] = result[0]['total']
        
        # 统计有效电台数（有国家信息的）
        query = """
        SELECT COUNT(*) as valid_stations
        FROM world_tune_radio_stations 
        WHERE country IS NOT NULL 
        AND country != '' 
        AND countrycode IS NOT NULL 
        AND countrycode != ''
        AND LENGTH(countrycode) = 2
        """
        result = self._execute_query(query)
        valid_stations = result[0]['valid_stations'] if result else 0
        
        # 统计孤立电台数（没有关联关系的）
        query = """
        SELECT COUNT(*) as orphaned
        FROM world_tune_radio_stations rs
        LEFT JOIN world_tune_station_countries sc ON rs.id = sc.station_id
        WHERE sc.station_id IS NULL
        AND rs.country IS NOT NULL 
        AND rs.country != '' 
        AND rs.countrycode IS NOT NULL 
        AND rs.countrycode != ''
        AND LENGTH(rs.countrycode) = 2
        """
        result = self._execute_query(query)
        orphaned_stations = result[0]['orphaned'] if result else 0
        
        logger.info("=" * 60)
        logger.info("当前数据状态分析:")
        logger.info(f"  总电台数: {self.stats['total_stations']}")
        logger.info(f"  有效电台数: {valid_stations}")
        logger.info(f"  现有关联数: {self.stats['existing_relations']}")
        logger.info(f"  孤立电台数: {orphaned_stations}")
        logger.info(f"  关联覆盖率: {((valid_stations - orphaned_stations) / valid_stations * 100):.2f}%")
        logger.info("=" * 60)
        
        return {
            'total_stations': self.stats['total_stations'],
            'valid_stations': valid_stations,
            'existing_relations': self.stats['existing_relations'],
            'orphaned_stations': orphaned_stations,
            'coverage_rate': (valid_stations - orphaned_stations) / valid_stations * 100 if valid_stations > 0 else 0
        }
    
    def clear_existing_relations(self):
        """清空现有关联关系"""
        if self.use_truncate:
            logger.info("使用TRUNCATE清空现有关联关系...")
            if not self.dry_run:
                query = "TRUNCATE TABLE world_tune_station_countries"
                self._execute_query(query)
                logger.info("已使用TRUNCATE清空world_tune_station_countries表")
            else:
                logger.info("试运行模式：跳过TRUNCATE操作")
        else:
            logger.info("使用DELETE清空现有关联关系...")
            if not self.dry_run:
                query = "DELETE FROM world_tune_station_countries"
                deleted_count = self._execute_query(query)
                logger.info(f"已删除 {deleted_count} 个现有关联关系")
            else:
                logger.info("试运行模式：跳过DELETE操作")
        
        # 清空缓存
        self.existing_relations.clear()
        self.stats['existing_relations'] = 0
    
    def build_new_relations(self):
        """构建新的关联关系"""
        logger.info("开始构建新的关联关系...")
        
        # 获取需要处理的电台总数 - 只统计可用电台（status=1）
        count_query = """
        SELECT COUNT(*) as total 
        FROM world_tune_radio_stations 
        WHERE country IS NOT NULL 
        AND country != '' 
        AND countrycode IS NOT NULL 
        AND countrycode != ''
        AND LENGTH(countrycode) = 2
        AND status = 1
        """
        count_result = self._execute_query(count_query)
        total_valid_stations = count_result[0]['total'] if count_result else 0
        logger.info(f"需要处理的有效电台总数: {total_valid_stations}")
        
        # 分页处理电台数据
        offset = 0
        processed_count = 0
        
        while offset < total_valid_stations:
            # 获取一页电台数据 - 只查询可用电台（status=1）
            query = f"""
            SELECT 
                id,
                TRIM(country) as country,
                TRIM(UPPER(countrycode)) as countrycode
            FROM world_tune_radio_stations 
            WHERE country IS NOT NULL 
            AND country != '' 
            AND countrycode IS NOT NULL 
            AND countrycode != ''
            AND LENGTH(countrycode) = 2
            AND status = 1
            ORDER BY id
            LIMIT {self.page_size} OFFSET {offset}
            """
            
            stations = self._execute_query(query)
            
            if not stations:
                break
            
            current_batch_size = len(stations)
            progress = (offset + current_batch_size) / total_valid_stations * 100
            logger.info(f"构建关联关系: {offset + 1} - {offset + current_batch_size} / {total_valid_stations} ({progress:.1f}%)")
            
            # 处理这一页的电台数据
            relations_to_create = []
            
            for station in stations:
                station_id = station['id']
                country = station['country']
                code = station['countrycode']
                
                # 查找对应的国家ID
                country_id = self._find_country_id(country, code)
                
                if country_id:
                    relation_key = (station_id, country_id)
                    
                    # 检查是否已存在（如果不是TRUNCATE模式）
                    if not self.use_truncate and relation_key in self.existing_relations:
                        continue  # 跳过已存在的关联
                    
                    relations_to_create.append({
                        'station_id': station_id,
                        'country_id': country_id
                    })
                    self.new_relations.add(relation_key)
                else:
                    self.stats['orphaned_stations'] += 1
                    logger.warning(f"电台 {station_id} 找不到对应的国家: {country} ({code})")
                
                processed_count += 1
                self.stats['processed_stations'] = processed_count
            
            # 批量创建关联关系
            if relations_to_create and not self.dry_run:
                self._batch_create_relations(relations_to_create)
            elif relations_to_create and self.dry_run:
                logger.info(f"试运行模式：跳过创建 {len(relations_to_create)} 个关联关系")
                self.stats['new_relations'] += len(relations_to_create)
            
            offset += current_batch_size
            
            # 如果返回的数据少于page_size，说明已经处理完了
            if current_batch_size < self.page_size:
                break
        
        logger.info(f"关联关系构建完成，总共处理了 {processed_count} 个电台")
    
    def _find_country_id(self, country, code):
        """查找国家ID"""
        # 首先尝试精确匹配
        country_key = (country, code)
        country_id = self.country_cache.get(country_key)
        
        if country_id:
            return country_id
        
        # 如果精确匹配失败，尝试只根据代码匹配
        for (cached_name, cached_code), cached_id in self.country_cache.items():
            if cached_code == code:
                return cached_id
        
        return None
    
    def _batch_create_relations(self, relations):
        """批量创建关联关系"""
        logger.info(f"批量创建 {len(relations)} 个关联关系")
        
        for i in range(0, len(relations), self.batch_size):
            batch = relations[i:i + self.batch_size]
            
            # 构建批量插入语句
            values = []
            for relation in batch:
                values.append(f"({relation['station_id']}, {relation['country_id']})")
            
            values_str = ', '.join(values)
            
            query = f"""
            INSERT IGNORE INTO world_tune_station_countries (station_id, country_id)
            VALUES {values_str}
            """
            
            try:
                affected_rows = self._execute_query(query)
                self.stats['new_relations'] += len(batch)
                logger.info(f"批量创建关联关系: {i + len(batch)}/{len(relations)}")
                
            except Exception as e:
                logger.error(f"批量创建关联关系失败: {e}")
                break
    
    def run(self):
        """运行重建过程"""
        try:
            logger.info("开始重建电台国家关联关系...")
            
            # 1. 加载缓存数据
            self._load_country_cache()
            
            # 2. 分析当前状态
            if not self.use_truncate:
                self._load_existing_relations()
            
            state = self.analyze_current_state()
            
            # 3. 根据策略决定是否清空
            if self.use_truncate:
                self.clear_existing_relations()
            elif state['coverage_rate'] < 50:  # 如果覆盖率太低，建议清空重建
                logger.warning(f"当前覆盖率仅 {state['coverage_rate']:.2f}%，建议使用 --truncate 选项清空重建")
            
            # 4. 构建新的关联关系
            self.build_new_relations()
            
            # 5. 输出最终摘要
            self._print_summary()
            
            logger.info("电台国家关联关系重建完成")
            return True
            
        except Exception as e:
            logger.error(f"重建过程出错: {e}")
            return False
    
    def _print_summary(self):
        """输出摘要信息"""
        logger.info("=" * 60)
        logger.info("重建摘要:")
        logger.info(f"  总电台数: {self.stats['total_stations']}")
        logger.info(f"  已处理电台数: {self.stats['processed_stations']}")
        logger.info(f"  原有关联数: {self.stats['existing_relations']}")
        logger.info(f"  新建关联数: {self.stats['new_relations']}")
        logger.info(f"  孤立电台数: {self.stats['orphaned_stations']}")
        logger.info(f"  策略: {'TRUNCATE清空重建' if self.use_truncate else '增量更新'}")
        logger.info(f"  模式: {'试运行' if self.dry_run else '实际执行'}")
        logger.info("=" * 60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='电台国家关联关系重建工具')
    parser.add_argument("--env", choices=['development', 'production'], 
                       default='production', help="运行环境 (development/production)")
    parser.add_argument('--batch-size', type=int, default=1000,
                       help='批量处理大小（默认: 1000）')
    parser.add_argument('--page-size', type=int, default=5000,
                       help='分页大小（默认: 5000）')
    parser.add_argument('--use-truncate', action='store_true',
                       help='使用TRUNCATE清空重建模式（默认: 增量更新）')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，不执行实际的数据库修改操作')
    
    args = parser.parse_args()
    
    # 初始化环境配置
    try:
        current_env = init_environment()
        env_info = get_environment_info()
    except Exception as e:
        logger.error(f"环境配置初始化失败: {e}")
        return 1
    
    # 记录脚本开始
    script_name = "电台国家关联关系重建工具"
    description = "重建world_tune_station_countries表的关联关系"
    log_script_start(logger, script_name, description)
    
    # 创建重建器并运行
    rebuilder = StationCountryRebuilder(
        batch_size=args.batch_size,
        page_size=args.page_size,
        use_truncate=args.use_truncate,  # 默认使用增量更新，除非指定--use-truncate
        dry_run=args.dry_run
    )
    
    success = rebuilder.run()
    
    # 记录脚本结束
    strategy = "TRUNCATE清空重建" if args.use_truncate else "增量更新"
    summary = f"批量大小: {args.batch_size}, 分页大小: {args.page_size}, 策略: {strategy}"
    if args.dry_run:
        summary += ", 试运行模式"
    
    log_script_end(logger, script_name, success, summary)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main()) 