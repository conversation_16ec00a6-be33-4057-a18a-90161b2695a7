#!/usr/bin/env python3
"""
电台标签关联重建工具
用于重建world_tune_station_tags表，建立电台与标签的多对多关系
支持多环境配置（开发/生产环境）
"""

import mysql.connector
import argparse
from collections import defaultdict
from config import (get_db_config, setup_logging, BusinessType, log_script_start, log_script_end,
                   init_environment, get_environment_info)
from datetime import datetime

# 初始化日志系统
logger, warning_details_logger = setup_logging(BusinessType.STATION_MANAGEMENT, advanced=True, logger_name="StationTagsBuilder")

class StationTagsBuilder:
    def __init__(self, batch_size=1000, dry_run=False):
        """初始化电台标签关联构建器
        
        Args:
            batch_size: 批量插入大小，默认1000
            dry_run: 试运行模式，默认False
        """
        self.batch_size = batch_size
        self.dry_run = dry_run
        self.update_timestamp = None  # 增量更新时间戳
        self.conn = mysql.connector.connect(**get_db_config())
        self.cursor = self.conn.cursor(dictionary=True)
        
        # 缓存数据
        self.tag_name_to_id = {}  # 标签名称到ID的映射
        self.other_tag_id = None  # other标签的ID
        
        # 统计数据
        self.total_stations = 0
        self.processed_stations = 0
        self.stations_with_matches = 0
        self.stations_with_other = 0
        self.total_relations = 0
        self.batch_buffer = []
        
    def load_tags_mapping(self):
        """加载标签名称到ID的映射"""
        try:
            self.cursor.execute("SELECT id, name FROM world_tune_tags")
            tags = self.cursor.fetchall()
            
            for tag in tags:
                self.tag_name_to_id[tag['name']] = tag['id']
                if tag['name'] == 'other':
                    self.other_tag_id = tag['id']
            
            logger.info(f"加载了 {len(self.tag_name_to_id)} 个标签映射")
            logger.info(f"other标签ID: {self.other_tag_id}")
            
            if not self.other_tag_id:
                raise ValueError("未找到'other'标签，请先运行标签处理脚本")
                
        except Exception as e:
            logger.error(f"加载标签映射失败: {e}")
            raise
    
    def get_station_count(self):
        """获取电台总数 - 只统计可用电台（status=1）"""
        try:
            self.cursor.execute("SELECT COUNT(*) as count FROM world_tune_radio_stations WHERE status = 1")
            result = self.cursor.fetchone()
            self.total_stations = result['count']
            logger.info(f"总共需要处理 {self.total_stations} 个可用电台")
        except Exception as e:
            logger.error(f"获取电台总数失败: {e}")
            raise
    
    def mark_update_start(self):
        """标记更新开始时间，用于增量更新"""
        self.update_timestamp = datetime.now()
        logger.info(f"开始增量更新，时间戳: {self.update_timestamp}")
        logger.info("使用增量更新模式，保持现有关联记录的ID稳定性")
    
    def parse_station_tags(self, tags_string):
        """解析电台标签字符串
        
        Args:
            tags_string: 标签字符串，如 "pop,music,rock"
            
        Returns:
            list: 清理后的标签列表
        """
        if not tags_string:
            return []
        
        # 按逗号分割并清理
        tags = [tag.strip() for tag in tags_string.split(',') if tag.strip()]
        return tags
    
    def match_tags_to_ids(self, tags_list):
        """将标签列表匹配到标签ID
        
        Args:
            tags_list: 标签名称列表
            
        Returns:
            list: 匹配到的标签ID列表
        """
        matched_ids = []
        
        for tag in tags_list:
            if tag in self.tag_name_to_id:
                matched_ids.append(self.tag_name_to_id[tag])
        
        return matched_ids
    
    def add_to_batch(self, station_id, tag_ids):
        """添加关联记录到批次缓存（增量更新模式）
        
        Args:
            station_id: 电台ID
            tag_ids: 标签ID列表
        """
        for tag_id in tag_ids:
            # 检查关联是否已存在
            if not self.association_exists(station_id, tag_id):
                # 不存在，添加到批量插入缓存
                self.batch_buffer.append({
                    'station_id': station_id,
                    'tag_id': tag_id
                })
            else:
                # 已存在，跳过（无需更新时间戳）
                continue
            
        # 检查是否需要执行批量插入
        if len(self.batch_buffer) >= self.batch_size:
            self.execute_batch_insert()
    
    def execute_batch_insert(self):
        """执行批量插入"""
        if not self.batch_buffer:
            return
            
        if self.dry_run:
            logger.info(f"试运行模式：将插入 {len(self.batch_buffer)} 条关联记录")
            self.total_relations += len(self.batch_buffer)
            self.batch_buffer = []
            return
            
        try:
            # 准备批量插入SQL
            insert_sql = """
                INSERT INTO world_tune_station_tags (station_id, tag_id, created_at)
                VALUES (%(station_id)s, %(tag_id)s, NOW())
            """
            
            # 执行批量插入
            self.cursor.executemany(insert_sql, self.batch_buffer)
            self.conn.commit()
            
            self.total_relations += len(self.batch_buffer)
            logger.info(f"批量插入 {len(self.batch_buffer)} 条关联记录，总计: {self.total_relations}")
            
            # 清空缓存
            self.batch_buffer = []
            
        except Exception as e:
            logger.error(f"批量插入失败: {e}")
            self.conn.rollback()
            raise
    
    def association_exists(self, station_id, tag_id):
        """检查电台-标签关联是否已存在"""
        if self.dry_run:
            return False  # 试运行模式下假设不存在
            
        try:
            query = """
            SELECT 1 FROM world_tune_station_tags 
            WHERE station_id = %s AND tag_id = %s
            """
            self.cursor.execute(query, (station_id, tag_id))
            return self.cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"检查关联关系存在性时出错: {e}")
            return False
    
    def update_association_timestamp(self, station_id, tag_id):
        """更新关联关系的时间戳（world_tune_station_tags表无时间戳字段，跳过）"""
        if self.dry_run:
            return  # 试运行模式下不执行更新
            
        # world_tune_station_tags表没有updated_at字段，跳过时间戳更新
        logger.debug(f"跳过时间戳更新 station_id={station_id}, tag_id={tag_id}")
        return
    
    def cleanup_old_associations(self):
        """清理过期的关联关系（world_tune_station_tags表无时间戳字段，跳过清理）"""
        if self.dry_run:
            logger.info("试运行模式：跳过清理过期关联关系")
            return 0
        
        # world_tune_station_tags表没有时间戳字段，无法基于时间清理
        # 如果需要清理，可以考虑基于电台状态清理
        logger.info("world_tune_station_tags表无时间戳字段，跳过清理过期关联关系")
        return 0
    
    def process_stations(self):
        """处理所有电台的标签关联"""
        try:
            # 分批获取电台数据
            offset = 0
            page_size = 5000  # 每次处理5000个电台
            
            while offset < self.total_stations:
                # 获取当前批次的电台数据 - 只查询可用电台（status=1）
                query = """
                    SELECT id, name, tags 
                    FROM world_tune_radio_stations 
                    WHERE status = 1
                    ORDER BY id 
                    LIMIT %s OFFSET %s
                """
                
                self.cursor.execute(query, (page_size, offset))
                stations = self.cursor.fetchall()
                
                if not stations:
                    break
                
                logger.info(f"处理第 {offset//page_size + 1} 批电台 ({len(stations)} 个)")
                
                # 处理当前批次的电台
                for station in stations:
                    self.process_single_station(station)
                
                offset += page_size
            
            # 处理剩余的批次数据
            self.execute_batch_insert()
            
        except Exception as e:
            logger.error(f"处理电台失败: {e}")
            raise
    
    def process_single_station(self, station):
        """处理单个电台的标签关联
        
        Args:
            station: 电台数据字典
        """
        station_id = station['id']
        station_name = station['name']
        tags_string = station['tags']
        
        # 解析标签
        tags_list = self.parse_station_tags(tags_string)
        
        # 匹配标签到ID
        matched_tag_ids = self.match_tags_to_ids(tags_list)
        
        # 根据匹配结果处理
        if matched_tag_ids:
            # 有匹配的标签，插入所有匹配的标签
            self.add_to_batch(station_id, matched_tag_ids)
            self.stations_with_matches += 1
            
            # 记录详细信息（仅在debug模式或前100个电台）
            if self.processed_stations < 100:
                logger.info(f"电台 '{station_name}' 匹配到 {len(matched_tag_ids)} 个标签: {tags_list}")
        else:
            # 没有匹配的标签，插入other标签
            self.add_to_batch(station_id, [self.other_tag_id])
            self.stations_with_other += 1
            
            # 记录详细信息（仅在debug模式或前100个电台）
            if self.processed_stations < 100:
                logger.info(f"电台 '{station_name}' 无匹配标签，归类为other: {tags_list}")
        
        self.processed_stations += 1
        
        # 每处理1000个电台输出一次进度
        if self.processed_stations % 1000 == 0:
            progress = (self.processed_stations / self.total_stations) * 100
            logger.info(f"处理进度: {self.processed_stations}/{self.total_stations} ({progress:.1f}%)")
    
    def print_summary(self):
        """打印处理摘要"""
        logger.info("=" * 80)
        logger.info("电台标签关联重建完成")
        logger.info(f"总电台数: {self.total_stations}")
        logger.info(f"已处理电台数: {self.processed_stations}")
        logger.info(f"有匹配标签的电台: {self.stations_with_matches}")
        logger.info(f"归类为other的电台: {self.stations_with_other}")
        logger.info(f"总关联记录数: {self.total_relations}")
        logger.info(f"平均每个电台关联标签数: {self.total_relations / self.processed_stations:.2f}")
        logger.info("=" * 80)
    
    def close(self):
        """关闭数据库连接"""
        try:
            # 处理剩余的批次数据
            self.execute_batch_insert()
        except Exception as e:
            logger.error(f"关闭前处理批次失败: {e}")
        finally:
            if hasattr(self, 'cursor') and self.cursor:
                self.cursor.close()
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="电台标签关联重建工具")
    parser.add_argument("--env", choices=['development', 'production'], 
                       default='production', help="运行环境 (development/production)")
    parser.add_argument("--batch-size", type=int, default=1000, help="批量插入大小")
    parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际修改数据库")
    
    args = parser.parse_args()
    
    # 初始化环境配置
    try:
        current_env = init_environment()
        env_info = get_environment_info()
    except Exception as e:
        logger.error(f"环境配置初始化失败: {e}")
        return 1
    
    # 记录脚本开始
    log_script_start(logger, "电台标签关联重建工具", 
                    f"批量大小: {args.batch_size}, 试运行: {args.dry_run}")
    
    # 创建构建器
    builder = StationTagsBuilder(batch_size=args.batch_size, dry_run=args.dry_run)
    
    success = False
    summary = ""
    
    try:
        # 1. 加载标签映射
        builder.load_tags_mapping()
        
        # 2. 获取电台总数
        builder.get_station_count()
        
        # 3. 标记更新开始时间（增量更新模式）
        builder.mark_update_start()
        
        # 4. 处理所有电台
        builder.process_stations()
        
        # 5. 清理过期的关联关系
        cleanup_count = builder.cleanup_old_associations()
        if cleanup_count > 0:
            logger.info(f"清理了 {cleanup_count} 条过期关联记录")
        
        # 6. 打印摘要
        builder.print_summary()
        
        success = True
        summary = f"成功处理 {builder.processed_stations} 个电台，创建 {builder.total_relations} 条关联记录"
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}")
        summary = f"处理失败: {str(e)}"
    finally:
        builder.close()
        log_script_end(logger, "电台标签关联重建工具", success, summary)

if __name__ == "__main__":
    main() 