#!/usr/bin/env python3
"""
Favicon检查逻辑测试脚本
用于验证增强后的favicon检查是否能正确处理各种情况
"""

import asyncio
import aiohttp
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FaviconChecker:
    """独立的Favicon检查器，用于测试"""
    
    def __init__(self, timeout=10):
        self.timeout = timeout
    
    async def _check_favicon_availability(self, favicon_url):
        """检查favicon图片可用性 - 增强版本
        
        使用多层检查策略：
        1. 先尝试HEAD请求检查基础信息
        2. 如果HEAD失败，尝试GET请求并验证图片内容
        3. 验证图片文件大小和格式
        4. 支持重试机制处理网络不稳定
        """
        if not favicon_url or not favicon_url.strip():
            return False
        
        favicon_url = favicon_url.strip()
        
        # 检查URL格式是否合理
        if not self._is_valid_favicon_url(favicon_url):
            logger.debug(f"不合理的favicon URL格式: {favicon_url}")
            return False
        
        # 支持的图片格式签名（魔术数字）
        image_signatures = {
            b'\x89PNG\r\n\x1a\n': 'PNG',
            b'\xff\xd8\xff': 'JPEG', 
            b'GIF87a': 'GIF87a',
            b'GIF89a': 'GIF89a',
            b'RIFF': 'WEBP',  # WebP格式以RIFF开头
            b'\x00\x00\x01\x00': 'ICO',  # ICO格式
            b'\x00\x00\x02\x00': 'CUR',  # CUR格式（光标，有时用作图标）
        }
        
        # 允许的Content-Type
        valid_content_types = {
            'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 
            'image/webp', 'image/x-icon', 'image/vnd.microsoft.icon',
            'image/ico', 'image/icon', 'application/octet-stream'
        }
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        # 重试机制
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 第一步：尝试HEAD请求（快速检查）
                    try:
                        async with session.head(favicon_url, allow_redirects=True) as response:
                            if response.status == 200:
                                content_type = response.headers.get('content-type', '').lower().split(';')[0]
                                content_length = response.headers.get('content-length')
                                
                                # 检查Content-Type
                                if content_type in valid_content_types:
                                    # 检查文件大小（如果有Content-Length）
                                    if content_length:
                                        try:
                                            size = int(content_length)
                                            if self._is_reasonable_favicon_size(size):
                                                logger.debug(f"✅ HEAD检查通过: {favicon_url} ({content_type}, {size} bytes)")
                                                return True
                                            else:
                                                logger.debug(f"文件大小不合理: {favicon_url} ({size} bytes)")
                                        except ValueError:
                                            pass
                                    else:
                                        # 没有Content-Length，但Content-Type正确，继续进行内容检查
                                        logger.debug(f"HEAD检查部分通过，需要内容验证: {favicon_url} ({content_type})")
                                
                                # 对于某些特殊的Content-Type（如text/html重定向页面），直接失败
                                if content_type in ['text/html', 'text/plain', 'application/json']:
                                    logger.debug(f"❌ 错误的Content-Type: {favicon_url} ({content_type})")
                                    return False
                                    
                    except asyncio.TimeoutError:
                        logger.debug(f"HEAD请求超时: {favicon_url}")
                    except Exception as e:
                        logger.debug(f"HEAD请求失败: {favicon_url}, 错误: {e}")
                    
                    # 第二步：GET请求并验证内容（更彻底的检查）
                    try:
                        async with session.get(favicon_url, allow_redirects=True) as response:
                            if response.status == 200:
                                content_type = response.headers.get('content-type', '').lower().split(';')[0]
                                
                                # 读取前几个字节进行魔术数字检查
                                try:
                                    # 读取前32字节就足够检查大部分图片格式
                                    initial_data = await asyncio.wait_for(response.content.read(32), timeout=3)
                                    
                                    if len(initial_data) < 4:
                                        logger.debug(f"❌ 内容太短: {favicon_url} ({len(initial_data)} bytes)")
                                        return False
                                    
                                    # 检查图片格式签名
                                    detected_format = None
                                    for signature, format_name in image_signatures.items():
                                        if initial_data.startswith(signature):
                                            detected_format = format_name
                                            break
                                        # 特殊处理WEBP格式
                                        if signature == b'RIFF' and len(initial_data) >= 12:
                                            if initial_data.startswith(b'RIFF') and initial_data[8:12] == b'WEBP':
                                                detected_format = 'WEBP'
                                                break
                                    
                                    if detected_format:
                                        # 继续读取更多内容来检查文件大小
                                        try:
                                            remaining_data = await asyncio.wait_for(response.content.read(512 * 1024), timeout=5)  # 最多读取512KB
                                            total_size = len(initial_data) + len(remaining_data)
                                            
                                            if self._is_reasonable_favicon_size(total_size):
                                                logger.debug(f"✅ 内容验证通过: {favicon_url} (格式: {detected_format}, 大小: {total_size} bytes)")
                                                return True
                                            else:
                                                logger.debug(f"❌ 文件大小不合理: {favicon_url} (格式: {detected_format}, 大小: {total_size} bytes)")
                                                return False
                                                
                                        except asyncio.TimeoutError:
                                            # 如果读取超时，但前面的检查通过了，可能是大文件，根据格式判断
                                            if detected_format in ['PNG', 'JPEG', 'GIF87a', 'GIF89a', 'WEBP', 'ICO']:
                                                logger.debug(f"✅ 格式验证通过（读取超时但格式正确）: {favicon_url} (格式: {detected_format})")
                                                return True
                                            else:
                                                logger.debug(f"❌ 读取超时且格式可疑: {favicon_url}")
                                                return False
                                                
                                    else:
                                        # 没有识别到有效的图片格式
                                        logger.debug(f"❌ 无法识别图片格式: {favicon_url}, 前32字节: {initial_data.hex()}")
                                        return False
                                        
                                except asyncio.TimeoutError:
                                    logger.debug(f"❌ 读取内容超时: {favicon_url}")
                                    return False
                                except Exception as e:
                                    logger.debug(f"❌ 读取内容失败: {favicon_url}, 错误: {e}")
                                    return False
                            else:
                                logger.debug(f"❌ HTTP状态错误: {favicon_url} (状态: {response.status})")
                                return False
                                
                    except asyncio.TimeoutError:
                        logger.debug(f"GET请求超时: {favicon_url}")
                    except Exception as e:
                        logger.debug(f"GET请求失败: {favicon_url}, 错误: {e}")
                        
            except Exception as e:
                logger.debug(f"会话创建失败: {favicon_url}, 错误: {e}")
            
            # 如果不是最后一次尝试，等待一下再重试
            if attempt < max_retries:
                logger.debug(f"Favicon检查失败，等待重试 {attempt + 1}/{max_retries}: {favicon_url}")
                await asyncio.sleep(1)
        
        logger.debug(f"❌ Favicon最终检查失败: {favicon_url}")
        return False
    
    def _is_valid_favicon_url(self, favicon_url):
        """检查favicon URL格式是否合理"""
        if not favicon_url.startswith(('http://', 'https://')):
            return False
        
        # 检查是否包含常见的favicon文件扩展名或路径
        favicon_url_lower = favicon_url.lower()
        valid_patterns = [
            '.png', '.jpg', '.jpeg', '.gif', '.ico', '.webp', '.svg',
            'favicon', 'apple-touch-icon', 'icon'
        ]
        
        return any(pattern in favicon_url_lower for pattern in valid_patterns)
    
    def _is_reasonable_favicon_size(self, size_bytes):
        """检查favicon文件大小是否合理
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            bool: 文件大小是否在合理范围内
        """
        # favicon文件通常在1KB到1MB之间
        min_size = 100  # 100字节（非常小的图标）
        max_size = 1024 * 1024  # 1MB（非常大的高清图标）
        
        return min_size <= size_bytes <= max_size

async def test_favicon_urls():
    """测试多个favicon URL"""
    
    # 测试用例：包括用户报告的问题URL和其他各种情况
    test_urls = [
        # 用户报告的问题URL
        "https://www.boisestatepublicradio.org/apple-touch-icon.png",
        
        # 一些其他测试URL
        "https://www.google.com/favicon.ico",
        "https://github.com/favicon.ico",
        "https://www.wikipedia.org/static/favicon/wikipedia.ico",
        
        # 一些可能有问题的URL
        "https://example.com/nonexistent.png",
        "https://httpstat.us/404/favicon.ico",
        
        # 无效格式的URL
        "not-a-url",
        "ftp://example.com/favicon.ico",
        ""
    ]
    
    checker = FaviconChecker(timeout=15)
    
    logger.info("=" * 80)
    logger.info("开始测试Favicon检查逻辑")
    logger.info("=" * 80)
    
    for i, url in enumerate(test_urls, 1):
        logger.info(f"\n{i}. 测试URL: {url}")
        logger.info("-" * 50)
        
        start_time = datetime.now()
        try:
            result = await checker._check_favicon_availability(url)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            status = "✅ 可用" if result else "❌ 不可用"
            logger.info(f"结果: {status} (耗时: {duration:.2f}秒)")
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.error(f"测试异常: {e} (耗时: {duration:.2f}秒)")
    
    logger.info("\n" + "=" * 80)
    logger.info("Favicon检查测试完成")
    logger.info("=" * 80)

def main():
    """主函数"""
    try:
        asyncio.run(test_favicon_urls())
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main() 