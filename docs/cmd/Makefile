# World Tune 数据处理 Makefile
# 使用方法: make <命令>

# 虚拟环境配置
VENV_DIR = .venv
VENV_BIN = $(VENV_DIR)/bin
PYTHON = $(VENV_BIN)/python3
PIP = $(VENV_BIN)/pip

# 环境配置
ENV ?= production
ENV_FLAG = --env $(ENV)

# 检查虚拟环境是否存在
VENV_EXISTS = $(shell [ -d $(VENV_DIR) ] && echo "true" || echo "false")

.PHONY: help install import tags countries link-tags link-countries all clean logs setup-venv

# 默认显示帮助信息
help:
	@echo "World Tune 数据处理命令:"
	@echo ""
	@echo "  make setup-venv    - 创建并激活Python3虚拟环境"
	@echo "  make install       - 安装Python依赖包"
	@echo "  make import        - 全量同步电台数据（含质量检查，推荐用于定期同步）"
	@echo "  make import-fast   - 快速全量同步电台数据（跳过质量检查）"
	@echo "  make tags          - 处理标签数据 (同步重建关联关系)"
	@echo "  make countries     - 处理国家数据 (同步重建关联关系)"
	@echo "  make link-tags     - 单独重建电台-标签关联"
	@echo "  make link-countries - 单独重建电台-国家关联"
	@echo ""
	@echo "  make all           - 执行完整数据处理流程 (按顺序执行所有步骤)"
	@echo "  make rebuild       - 快速重建关联关系 (仅重建关联表)"
	@echo "  make stats         - 显示数据统计"
	@echo "  make logs          - 查看最新日志"
	@echo "  make clean         - 清理日志文件"
	@echo ""
	@echo "环境配置:"
	@echo "  ENV=development make import    # 使用开发环境配置"
	@echo "  ENV=production make import     # 使用生产环境配置（默认）"
	@echo ""
	@echo "单独执行示例:"
	@echo "  make setup-venv                # 创建虚拟环境"
	@echo "  make install                   # 安装依赖包"
	@echo "  ENV=development make import    # 使用开发环境导入电台数据"
	@echo "  make tags                      # 处理标签数据并重建关联"
	@echo "  make countries                 # 处理国家数据并重建关联"

# 创建虚拟环境
setup-venv:
ifeq ($(VENV_EXISTS), false)
	@echo "🔧 创建Python3虚拟环境..."
	python3 -m venv $(VENV_DIR)
	@echo "✅ 虚拟环境创建完成"
else
	@echo "✅ 虚拟环境已存在"
endif

# 0. 安装Python依赖包
install: setup-venv
	@echo "📦 安装Python依赖包..."
	$(PIP) install -r requirements.txt
	@echo "✅ 依赖包安装完成"

# 1. 导入电台基础数据（全量同步，含质量检查）
import: install
	@echo "🚀 开始全量同步电台数据（含质量检查）- 环境: $(ENV)..."
	$(PYTHON) data_import.py --method all $(ENV_FLAG)
	@echo "✅ 电台数据全量同步完成"

# 1a. 快速全量同步电台数据（跳过质量检查）
import-fast: install
	@echo "⚡ 开始快速全量同步电台数据（跳过质量检查）- 环境: $(ENV)..."
	$(PYTHON) data_import.py --method all --skip-quality-check $(ENV_FLAG)
	@echo "✅ 电台数据快速全量同步完成"

# 2. 处理标签数据 (同步重建关联关系)
tags: install
	@echo "🏷️  开始处理标签数据 - 环境: $(ENV)..."
	$(PYTHON) process_tags.py $(ENV_FLAG)
	@echo "🔗 开始重建电台-标签关联..."
	$(PYTHON) rebuild_station_tags.py $(ENV_FLAG)
	@echo "✅ 标签数据处理和关联重建完成"

# 3. 处理国家数据 (已包含关联关系重建)
countries: install
	@echo "🌍 开始处理国家数据 - 环境: $(ENV)..."
	$(PYTHON) process_countries.py $(ENV_FLAG)
	@echo "✅ 国家数据处理和关联重建完成"

# 4. 建立电台-标签关联
link-tags: install
	@echo "🔗 开始建立电台-标签关联 - 环境: $(ENV)..."
	$(PYTHON) rebuild_station_tags.py $(ENV_FLAG)
	@echo "✅ 电台-标签关联建立完成"

# 5. 建立电台-国家关联
link-countries: install
	@echo "🔗 开始建立电台-国家关联 - 环境: $(ENV)..."
	$(PYTHON) rebuild_station_countries.py $(ENV_FLAG)
	@echo "✅ 电台-国家关联建立完成"

# 完整流程 - 按顺序执行所有步骤
all: install
	@echo "🎯 开始执行完整数据处理流程..."
	@echo "=================================================="
	@$(MAKE) import
	@echo ""
	@$(MAKE) tags
	@echo ""
	@$(MAKE) countries
	@echo ""
	@echo "🎉 完整数据处理流程执行完成！"
	@echo "=================================================="

# 查看最新日志
logs:
	@echo "📋 最新日志文件:"
	@find ../logs -name "*.log" -type f -exec ls -la {} \; | tail -10

# 清理日志文件
clean:
	@echo "🧹 清理日志文件..."
	@read -p "确定要删除所有日志文件吗? [y/N] " confirm && \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		rm -rf ../logs/*; \
		echo "✅ 日志文件清理完成"; \
	else \
		echo "❌ 取消清理操作"; \
	fi

# 快速重建关联关系 (不重新导入基础数据)
rebuild: install
	@echo "🔄 快速重建关联关系..."
	@echo "=================================================="
	@$(MAKE) link-tags
	@echo ""
	@$(MAKE) link-countries
	@echo ""
	@echo "✅ 关联关系重建完成！"

# 测试数据库连接
test: install
	@echo "🔍 测试数据库连接 - 环境: $(ENV)..."
	$(PYTHON) test.py $(ENV_FLAG)

# 显示数据统计
stats: install
	@echo "📊 数据统计 - 环境: $(ENV):"
	@$(PYTHON) -c "\
import sys; sys.argv = ['stats', '--env', '$(ENV)'];\
import mysql.connector;\
from config import init_environment, get_db_config;\
env = init_environment();\
conn = mysql.connector.connect(**get_db_config());\
cursor = conn.cursor();\
cursor.execute('SELECT COUNT(*) FROM world_tune_radio_stations');\
print(f'  电台总数: {cursor.fetchone()[0]:,}');\
cursor.execute('SELECT COUNT(*) FROM world_tune_countries');\
print(f'  国家总数: {cursor.fetchone()[0]:,}');\
cursor.execute('SELECT COUNT(*) FROM world_tune_tags');\
print(f'  标签总数: {cursor.fetchone()[0]:,}');\
cursor.execute('SELECT COUNT(*) FROM world_tune_station_countries');\
print(f'  电台-国家关联: {cursor.fetchone()[0]:,}');\
cursor.execute('SELECT COUNT(*) FROM world_tune_station_tags');\
print(f'  电台-标签关联: {cursor.fetchone()[0]:,}');\
cursor.close();\
conn.close()\
"

# 清理虚拟环境
clean-venv:
	@echo "🧹 清理虚拟环境..."
	@read -p "确定要删除虚拟环境吗? [y/N] " confirm && \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		rm -rf $(VENV_DIR); \
		echo "✅ 虚拟环境清理完成"; \
	else \
		echo "❌ 取消清理操作"; \
	fi 