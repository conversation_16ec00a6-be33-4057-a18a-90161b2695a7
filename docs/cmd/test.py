#!/usr/bin/env python3
"""
Radio Browser API Python 封装库
基于官方文档: https://api.radio-browser.info/
"""
import socket
import random
import urllib
import urllib.request
import urllib.parse
import json
import time
from typing import Dict, List, Optional, Union


class RadioBrowserAPI:
    """Radio Browser API 客户端"""
    
    def __init__(self, user_agent: str = "RadioBrowserPython/1.0.0"):
        """初始化API客户端
        
        Args:
            user_agent (str): 用户代理字符串
        """
        self.user_agent = user_agent
        self._base_urls = None
    
    def get_base_urls(self) -> List[str]:
        """获取所有当前可用的广播浏览器服务器的基本URL
        
        Returns:
            List[str]: 服务器URL列表
        """
        if self._base_urls is None:
            hosts = []
            try:
                # 从DNS获取所有主机
                ips = socket.getaddrinfo('all.api.radio-browser.info', 80, 0, 0, socket.IPPROTO_TCP)
                for ip_tuple in ips:
                    ip = ip_tuple[4][0]
                    try:
                        # 对每个IP进行反向查找，以获得一个友好的名称
                        host_addr = socket.gethostbyaddr(ip)
                        if host_addr[0] not in hosts:
                            hosts.append(host_addr[0])
                    except socket.herror:
                        # 如果反向DNS查找失败，跳过
                        continue
                
                # 对名称列表进行排序
                hosts.sort()
                # 在前面加上"https://"，使其成为一个URL
                self._base_urls = list(map(lambda x: "https://" + x, hosts))
            except Exception as e:
                print(f"获取服务器列表失败: {e}")
                # 备用服务器列表
                self._base_urls = [
                    "https://de1.api.radio-browser.info",
                    "https://de2.api.radio-browser.info", 
                    "https://fi1.api.radio-browser.info"
                ]
        
        return self._base_urls
    
    def _download_uri(self, uri: str, params: Optional[Dict] = None, method: str = "GET") -> bytes:
        """使用正确的标头下载数据
        
        Args:
            uri (str): 请求URI
            params (Optional[Dict]): 请求参数
            method (str): HTTP方法
            
        Returns:
            bytes: 响应数据
        """
        data = None
        
        if params is not None:
            if method.upper() == "GET":
                # GET请求将参数添加到URL
                query_string = urllib.parse.urlencode(params)
                if '?' in uri:
                    uri += '&' + query_string
                else:
                    uri += '?' + query_string
                print(f'请求 {uri}')
            else:
                # POST请求将参数作为JSON数据发送
                data = json.dumps(params).encode('utf-8')
                print(f'请求 {uri} 参数: {params}')
        else:
            print(f'请求 {uri}')
        
        req = urllib.request.Request(uri, data)
        req.add_header('User-Agent', self.user_agent)
        req.add_header('Content-Type', 'application/json')
        
        response = urllib.request.urlopen(req)
        result = response.read()
        response.close()
        return result
    
    def _download_from_radiobrowser(self, path: str, params: Optional[Dict] = None, method: str = "GET") -> Dict:
        """从随机的API服务器下载数据
        
        Args:
            path (str): API路径
            params (Optional[Dict]): 请求参数
            method (str): HTTP方法
            
        Returns:
            Dict: 解析后的JSON数据
        """
        servers = self.get_base_urls()
        random.shuffle(servers)
        
        for i, server_base in enumerate(servers):
            print(f'随机服务器: {server_base} 尝试: {i}')
            uri = server_base + path
            try:
                data = self._download_uri(uri, params, method)
                return json.loads(data.decode('utf-8'))
            except Exception as e:
                print(f"无法从API URL下载: {uri} {e}")
                continue
        
        raise Exception("所有服务器都无法访问")
    
    # ==================== 基础信息接口 ====================
    
    def get_stats(self) -> Dict:
        """获取服务器统计信息
        
        Returns:
            Dict: 统计信息
        """
        return self._download_from_radiobrowser("/json/stats")
    
    def get_config(self) -> Dict:
        """获取服务器配置信息
        
        Returns:
            Dict: 配置信息
        """
        return self._download_from_radiobrowser("/json/config")
    
    def get_servers(self) -> List[Dict]:
        """获取服务器镜像列表
        
        Returns:
            List[Dict]: 服务器列表
        """
        return self._download_from_radiobrowser("/json/servers")
    
    # ==================== 电台查询接口 ====================
    
    def get_all_stations(self, **kwargs) -> List[Dict]:
        """获取所有电台
        
        Args:
            **kwargs: 查询参数 (order, reverse, offset, limit, hidebroken)
            
        Returns:
            List[Dict]: 电台列表
        """
        return self._download_from_radiobrowser("/json/stations", kwargs)
    
    def get_stations_by_uuid(self, uuids: Union[str, List[str]]) -> List[Dict]:
        """通过UUID获取电台信息
        
        Args:
            uuids: 电台UUID或UUID列表
            
        Returns:
            List[Dict]: 电台信息列表
        """
        if isinstance(uuids, list):
            uuid_str = ','.join(uuids)
        else:
            uuid_str = uuids
        
        return self._download_from_radiobrowser("/json/stations/byuuid", {"uuids": uuid_str}, "POST")
    
    def get_stations_by_name(self, name: str, exact: bool = False, **kwargs) -> List[Dict]:
        """通过名称搜索电台
        
        Args:
            name (str): 电台名称
            exact (bool): 是否精确匹配
            **kwargs: 其他查询参数
            
        Returns:
            List[Dict]: 电台列表
        """
        if exact:
            path = f"/json/stations/bynameexact/{urllib.parse.quote(name)}"
        else:
            path = f"/json/stations/byname/{urllib.parse.quote(name)}"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_stations_by_country(self, country: str, exact: bool = False, **kwargs) -> List[Dict]:
        """通过国家搜索电台
        
        Args:
            country (str): 国家名称
            exact (bool): 是否精确匹配
            **kwargs: 其他查询参数
            
        Returns:
            List[Dict]: 电台列表
        """
        if exact:
            path = f"/json/stations/bycountryexact/{urllib.parse.quote(country)}"
        else:
            path = f"/json/stations/bycountry/{urllib.parse.quote(country)}"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_stations_by_countrycode(self, countrycode: str, **kwargs) -> List[Dict]:
        """通过国家代码搜索电台
        
        Args:
            countrycode (str): 国家代码 (如: CN, US, DE)
            **kwargs: 其他查询参数
            
        Returns:
            List[Dict]: 电台列表
        """
        path = f"/json/stations/bycountrycodeexact/{countrycode.upper()}"
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_stations_by_state(self, state: str, exact: bool = False, **kwargs) -> List[Dict]:
        """通过州/省搜索电台
        
        Args:
            state (str): 州/省名称
            exact (bool): 是否精确匹配
            **kwargs: 其他查询参数
            
        Returns:
            List[Dict]: 电台列表
        """
        if exact:
            path = f"/json/stations/bystateexact/{urllib.parse.quote(state)}"
        else:
            path = f"/json/stations/bystate/{urllib.parse.quote(state)}"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_stations_by_language(self, language: str, exact: bool = False, **kwargs) -> List[Dict]:
        """通过语言搜索电台
        
        Args:
            language (str): 语言名称
            exact (bool): 是否精确匹配
            **kwargs: 其他查询参数
            
        Returns:
            List[Dict]: 电台列表
        """
        if exact:
            path = f"/json/stations/bylanguageexact/{urllib.parse.quote(language)}"
        else:
            path = f"/json/stations/bylanguage/{urllib.parse.quote(language)}"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_stations_by_tag(self, tag: str, exact: bool = False, **kwargs) -> List[Dict]:
        """通过标签搜索电台
        
        Args:
            tag (str): 标签名称
            exact (bool): 是否精确匹配
            **kwargs: 其他查询参数
            
        Returns:
            List[Dict]: 电台列表
        """
        if exact:
            path = f"/json/stations/bytagexact/{urllib.parse.quote(tag)}"
        else:
            path = f"/json/stations/bytag/{urllib.parse.quote(tag)}"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_stations_by_codec(self, codec: str, exact: bool = False, **kwargs) -> List[Dict]:
        """通过编码格式搜索电台
        
        Args:
            codec (str): 编码格式 (如: MP3, AAC)
            exact (bool): 是否精确匹配
            **kwargs: 其他查询参数
            
        Returns:
            List[Dict]: 电台列表
        """
        if exact:
            path = f"/json/stations/bycodecexact/{urllib.parse.quote(codec)}"
        else:
            path = f"/json/stations/bycodec/{urllib.parse.quote(codec)}"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def search_stations(self, **params) -> List[Dict]:
        """高级电台搜索
        
        Args:
            **params: 搜索参数，包括:
                name: 电台名称
                nameExact: 是否精确匹配名称
                country: 国家
                countryExact: 是否精确匹配国家
                countrycode: 国家代码
                state: 州/省
                stateExact: 是否精确匹配州/省
                language: 语言
                languageExact: 是否精确匹配语言
                tag: 标签
                tagExact: 是否精确匹配标签
                tagList: 标签列表
                codec: 编码格式
                bitrateMin: 最小比特率
                bitrateMax: 最大比特率
                has_geo_info: 是否有地理信息
                has_extended_info: 是否有扩展信息
                is_https: 是否使用HTTPS
                order: 排序字段
                reverse: 是否倒序
                offset: 偏移量
                limit: 限制数量
                hidebroken: 是否隐藏损坏的电台
                
        Returns:
            List[Dict]: 电台列表
        """
        return self._download_from_radiobrowser("/json/stations/search", params, "POST")
    
    def get_stations_by_url(self, url: str) -> List[Dict]:
        """通过URL搜索电台
        
        Args:
            url (str): 电台URL
            
        Returns:
            List[Dict]: 电台列表
        """
        return self._download_from_radiobrowser("/json/stations/byurl", {"url": url}, "POST")
    
    # ==================== 特殊电台列表 ====================
    
    def get_top_vote_stations(self, limit: int = 100, **kwargs) -> List[Dict]:
        """获取最高投票电台
        
        Args:
            limit (int): 限制数量
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 电台列表
        """
        kwargs['limit'] = limit
        return self._download_from_radiobrowser("/json/stations/topvote", kwargs)
    
    def get_top_click_stations(self, limit: int = 100, **kwargs) -> List[Dict]:
        """获取最高点击电台
        
        Args:
            limit (int): 限制数量
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 电台列表
        """
        kwargs['limit'] = limit
        return self._download_from_radiobrowser("/json/stations/topclick", kwargs)
    
    def get_last_click_stations(self, limit: int = 100, **kwargs) -> List[Dict]:
        """获取最近点击电台
        
        Args:
            limit (int): 限制数量
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 电台列表
        """
        kwargs['limit'] = limit
        return self._download_from_radiobrowser("/json/stations/lastclick", kwargs)
    
    def get_last_change_stations(self, limit: int = 100, **kwargs) -> List[Dict]:
        """获取最近更改电台
        
        Args:
            limit (int): 限制数量
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 电台列表
        """
        kwargs['limit'] = limit
        return self._download_from_radiobrowser("/json/stations/lastchange", kwargs)
    
    def get_broken_stations(self, limit: int = 100, **kwargs) -> List[Dict]:
        """获取损坏的电台
        
        Args:
            limit (int): 限制数量
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 电台列表
        """
        kwargs['limit'] = limit
        return self._download_from_radiobrowser("/json/stations/broken", kwargs)
    
    # ==================== 分类信息接口 ====================
    
    def get_countries(self, filter_name: str = None, **kwargs) -> List[Dict]:
        """获取国家列表
        
        Args:
            filter_name (str): 过滤名称
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 国家列表
        """
        if filter_name:
            path = f"/json/countries/{urllib.parse.quote(filter_name)}"
        else:
            path = "/json/countries"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_countrycodes(self, filter_code: str = None, **kwargs) -> List[Dict]:
        """获取国家代码列表
        
        Args:
            filter_code (str): 过滤代码
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 国家代码列表
        """
        if filter_code:
            path = f"/json/countrycodes/{filter_code.upper()}"
        else:
            path = "/json/countrycodes"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_states(self, country: str = None, filter_name: str = None, **kwargs) -> List[Dict]:
        """获取州/省列表
        
        Args:
            country (str): 国家名称
            filter_name (str): 过滤名称
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 州/省列表
        """
        if country and filter_name:
            path = f"/json/states/{urllib.parse.quote(country)}/{urllib.parse.quote(filter_name)}"
        elif filter_name:
            path = f"/json/states/{urllib.parse.quote(filter_name)}"
        else:
            path = "/json/states"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_languages(self, filter_name: str = None, **kwargs) -> List[Dict]:
        """获取语言列表
        
        Args:
            filter_name (str): 过滤名称
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 语言列表
        """
        if filter_name:
            path = f"/json/languages/{urllib.parse.quote(filter_name)}"
        else:
            path = "/json/languages"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_tags(self, filter_name: str = None, **kwargs) -> List[Dict]:
        """获取标签列表
        
        Args:
            filter_name (str): 过滤名称
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 标签列表
        """
        if filter_name:
            path = f"/json/tags/{urllib.parse.quote(filter_name)}"
        else:
            path = "/json/tags"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_codecs(self, filter_name: str = None, **kwargs) -> List[Dict]:
        """获取编码格式列表
        
        Args:
            filter_name (str): 过滤名称
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 编码格式列表
        """
        if filter_name:
            path = f"/json/codecs/{urllib.parse.quote(filter_name)}"
        else:
            path = "/json/codecs"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    # ==================== 交互接口 ====================
    
    def click_station(self, station_uuid: str) -> Dict:
        """点击电台计数器
        
        Args:
            station_uuid (str): 电台UUID
            
        Returns:
            Dict: 响应信息
        """
        return self._download_from_radiobrowser(f"/json/url/{station_uuid}")
    
    def vote_for_station(self, station_uuid: str) -> Dict:
        """为电台投票
        
        Args:
            station_uuid (str): 电台UUID
            
        Returns:
            Dict: 响应信息
        """
        return self._download_from_radiobrowser(f"/json/vote/{station_uuid}")
    
    def add_station(self, **station_data) -> Dict:
        """添加新电台
        
        Args:
            **station_data: 电台数据，包括:
                name: 电台名称 (必需)
                url: 电台URL (必需)
                homepage: 主页URL
                favicon: 图标URL
                countrycode: 国家代码
                state: 州/省
                language: 语言
                tags: 标签 (逗号分隔)
                geo_lat: 纬度
                geo_long: 经度
                
        Returns:
            Dict: 响应信息
        """
        return self._download_from_radiobrowser("/json/add", station_data, "POST")
    
    # ==================== 历史和检查接口 ====================
    
    def get_clicks(self, station_uuid: str = None, **kwargs) -> List[Dict]:
        """获取点击历史
        
        Args:
            station_uuid (str): 电台UUID (可选)
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 点击历史列表
        """
        if station_uuid:
            path = f"/json/clicks/{station_uuid}"
        else:
            path = "/json/clicks"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_checks(self, station_uuid: str = None, **kwargs) -> List[Dict]:
        """获取检查历史
        
        Args:
            station_uuid (str): 电台UUID (可选)
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 检查历史列表
        """
        if station_uuid:
            path = f"/json/checks/{station_uuid}"
        else:
            path = "/json/checks"
        
        return self._download_from_radiobrowser(path, kwargs)
    
    def get_station_changes(self, station_uuid: str = None, **kwargs) -> List[Dict]:
        """获取电台变更历史
        
        Args:
            station_uuid (str): 电台UUID (可选)
            **kwargs: 其他参数
            
        Returns:
            List[Dict]: 变更历史列表
        """
        if station_uuid:
            path = f"/json/stations/changed/{station_uuid}"
        else:
            path = "/json/stations/changed"
        
        return self._download_from_radiobrowser(path, kwargs)


# ==================== 便利函数 ====================

def create_api_client(user_agent: str = "RadioBrowserPython/1.0.0") -> RadioBrowserAPI:
    """创建API客户端实例
    
    Args:
        user_agent (str): 用户代理字符串
        
    Returns:
        RadioBrowserAPI: API客户端实例
    """
    return RadioBrowserAPI(user_agent)


# ==================== 示例代码 ====================

if __name__ == "__main__":
    # 创建API客户端
    api = create_api_client("MyRadioApp/1.0.0")
    
    try:
        # 1. 获取服务器信息
        print("所有可用的URL")
        print("------------------")
        for host in api.get_base_urls():
            print(host)
        
        print("\n统计信息")
        print("------------")
        stats = api.get_stats()
        print(json.dumps(stats, indent=4))
        
        # 2. 获取中国的电台列表
        print("\n中国电台列表 (前10个)")
        print("------------------")
        cn_stations = api.get_stations_by_countrycode("CN", limit=10)
        for station in cn_stations:
            print(f"名称: {station['name']}")
            print(f"URL: {station['url_resolved']}")
            print(f"标签: {station['tags']}")
            print(f"编码: {station['codec']}")
            print(f"HLS: {'是' if station['hls'] else '否'}")
            print("---")
        
        # 3. 搜索特定电台
        print("\n搜索电台: '黑龙江'")
        print("------------------")
        search_results = api.search_stations(name="黑龙江", limit=5)
        for station in search_results:
            print(f"名称: {station['name']}")
            print(f"URL: {station['url_resolved']}")
            print("---")
        
        # 4. 获取热门电台
        print("\n热门电台 (前5个)")
        print("------------------")
        top_stations = api.get_top_click_stations(limit=5)
        for station in top_stations:
            print(f"名称: {station['name']}")
            print(f"国家: {station['country']}")
            print(f"点击数: {station['clickcount']}")
            print("---")
        
        # 5. 获取国家列表
        print("\n国家列表 (前10个)")
        print("------------------")
        countries = api.get_countries(limit=10)
        for country in countries:
            print(f"{country['name']}: {country['stationcount']} 个电台")
        
        # 6. 获取标签列表
        print("\n流行标签 (前10个)")
        print("------------------")
        tags = api.get_tags(limit=10, order="stationcount", reverse=True)
        for tag in tags:
            print(f"{tag['name']}: {tag['stationcount']} 个电台")
            
    except Exception as e:
        print(f"发生错误: {e}")