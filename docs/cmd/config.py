#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含日志配置、数据库配置等公共配置
"""

import os
import logging
import argparse
from datetime import datetime

# 多环境数据库配置
DB_CONFIGS = {
    'development': {
        'host': '************',
        'port': 13306,
        'user': 'root',
        'password': '123456',
        'database': 'toolbiz_overseas',
        'charset': 'utf8mb4',
        'autocommit': True,
        'description': '开发环境数据库'
    },
    'production': {
        'host': 'toolbiz-overseas.cxoi2ljqdaed.us-west-2.rds.amazonaws.com',
        'port': 3306,
        'user': 'toolbiz_overseas_pro',
        'password': '64176ug9yw7pmj9rgiz2xz8ql5ywlih',
        'database': 'toolbiz_overseas',
        'charset': 'utf8mb4',
        'autocommit': True,
        'description': '生产环境数据库'
    }
}

# 当前环境配置
CURRENT_ENV = 'production'  # 默认使用生产环境

class LogConfig:
    """日志配置类"""
    
    def __init__(self, business_name):
        """
        初始化日志配置
        
        Args:
            business_name: 业务名称，用于创建对应的日志文件夹
        """
        self.business_name = business_name
        self.logs_base_dir = "../logs"
        self.logs_dir = os.path.join(self.logs_base_dir, business_name)
        
        # 确保日志目录存在
        if not os.path.exists(self.logs_dir):
            os.makedirs(self.logs_dir)
    
    def setup_basic_logging(self, log_filename=None):
        """
        设置基础日志配置（单文件）
        
        Args:
            log_filename: 日志文件名，如果不提供则使用业务名称
        """
        if not log_filename:
            log_filename = f"{self.business_name}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(self.logs_dir, log_filename), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def setup_advanced_logging(self, logger_name="MainLogger"):
        """
        设置高级日志配置（分级别多文件）
        
        Args:
            logger_name: 主日志记录器名称
        
        Returns:
            tuple: (主logger, 详细警告logger)
        """
        # 创建格式化器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # 创建主logger
        main_logger = logging.getLogger(logger_name)
        main_logger.setLevel(logging.DEBUG)
        
        # 清除现有的处理器
        for handler in main_logger.handlers[:]:
            main_logger.removeHandler(handler)
        
        # 创建自定义过滤器类
        class LevelFilter(logging.Filter):
            def __init__(self, level):
                self.level = level
                
            def filter(self, record):
                return record.levelno == self.level
        
        # 1. INFO级别日志文件 (只记录INFO级别)
        info_handler = logging.FileHandler(os.path.join(self.logs_dir, f"{self.business_name}_info.log"))
        info_handler.setLevel(logging.INFO)
        info_handler.setFormatter(formatter)
        info_handler.addFilter(LevelFilter(logging.INFO))
        
        # 2. WARNING级别日志文件 (只记录WARNING级别)
        warning_handler = logging.FileHandler(os.path.join(self.logs_dir, f"{self.business_name}_warnings.log"))
        warning_handler.setLevel(logging.WARNING)
        warning_handler.setFormatter(formatter)
        warning_handler.addFilter(LevelFilter(logging.WARNING))
        
        # 3. ERROR级别日志文件 (只记录ERROR及以上级别)
        error_handler = logging.FileHandler(os.path.join(self.logs_dir, f"{self.business_name}_errors.log"))
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        
        # 4. 完整日志文件 (记录所有级别)
        all_handler = logging.FileHandler(os.path.join(self.logs_dir, f"{self.business_name}_all.log"))
        all_handler.setLevel(logging.DEBUG)
        all_handler.setFormatter(formatter)
        
        # 5. 控制台输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # 将所有处理器添加到主logger
        main_logger.addHandler(info_handler)
        main_logger.addHandler(warning_handler)
        main_logger.addHandler(error_handler)
        main_logger.addHandler(all_handler)
        main_logger.addHandler(console_handler)
        
        # 防止传播到根logger（避免重复日志）
        main_logger.propagate = False
        
        # 创建专门的JSON格式详细警告日志记录器
        warning_details_logger = logging.getLogger("WarningDetails")
        warning_details_handler = logging.FileHandler(
            os.path.join(self.logs_dir, f"{self.business_name}_warnings_details.json")
        )
        warning_details_handler.setLevel(logging.WARNING)
        warning_details_logger.addHandler(warning_details_handler)
        warning_details_logger.setLevel(logging.WARNING)
        warning_details_logger.propagate = False  # 防止传播到根logger
        
        return main_logger, warning_details_logger
    
    def get_logger(self, logger_name="Logger"):
        """
        获取指定名称的logger
        
        Args:
            logger_name: logger名称
            
        Returns:
            logging.Logger: logger实例
        """
        return logging.getLogger(logger_name)

# 预定义的业务类型
class BusinessType:
    """业务类型常量"""
    DATA_IMPORT = "data_import"
    TAG = "tag"
    STATION_MANAGEMENT = "station_management"
    COUNTRY_MANAGEMENT = "country_management"
    SYSTEM = "system"

# 环境管理函数
def set_environment(env):
    """
    设置当前环境
    
    Args:
        env: 环境名称 ('development' 或 'production')
    """
    global CURRENT_ENV
    if env not in DB_CONFIGS:
        raise ValueError(f"不支持的环境: {env}. 支持的环境: {list(DB_CONFIGS.keys())}")
    CURRENT_ENV = env

def get_current_environment():
    """获取当前环境名称"""
    return CURRENT_ENV

def get_environment_from_args():
    """
    从命令行参数或环境变量获取环境配置
    
    优先级: 命令行参数 > 环境变量 > 默认值
    """
    # 1. 优先从环境变量获取
    env_from_var = os.getenv('WORLD_TUNE_ENV', None)
    
    # 2. 尝试从命令行参数获取
    try:
        # 创建一个简单的参数解析器来获取环境参数
        parser = argparse.ArgumentParser(add_help=False)
        parser.add_argument('--env', choices=['development', 'production'], 
                          default=env_from_var or 'production',
                          help='运行环境 (development/production)')
        
        # 解析已知参数，忽略未知参数
        args, _ = parser.parse_known_args()
        return args.env
    except:
        # 如果解析失败，使用环境变量或默认值
        return env_from_var or 'production'

def init_environment():
    """
    初始化环境配置
    
    从命令行参数或环境变量中获取环境设置，并设置当前环境
    """
    env = get_environment_from_args()
    set_environment(env)
    return env

# 便捷函数
def get_db_config(env=None):
    """
    获取数据库配置
    
    Args:
        env: 指定环境名称，如果不指定则使用当前环境
        
    Returns:
        dict: 数据库配置字典（不包含description字段）
    """
    if env is None:
        env = CURRENT_ENV
    
    if env not in DB_CONFIGS:
        raise ValueError(f"不支持的环境: {env}. 支持的环境: {list(DB_CONFIGS.keys())}")
    
    # 复制配置并移除description字段
    config = DB_CONFIGS[env].copy()
    config.pop('description', None)
    return config

def get_environment_info():
    """
    获取当前环境信息
    
    Returns:
        dict: 包含环境名称和描述的字典
    """
    return {
        'name': CURRENT_ENV,
        'description': DB_CONFIGS[CURRENT_ENV].get('description', ''),
        'host': DB_CONFIGS[CURRENT_ENV]['host'],
        'database': DB_CONFIGS[CURRENT_ENV]['database']
    }

def setup_logging(business_type, advanced=False, logger_name="MainLogger"):
    """
    便捷的日志设置函数
    
    Args:
        business_type: 业务类型（使用BusinessType常量）
        advanced: 是否使用高级日志配置（分级别多文件）
        logger_name: 主logger名称
        
    Returns:
        logger或tuple: 如果advanced=False返回单个logger，否则返回(main_logger, warning_details_logger)
    """
    log_config = LogConfig(business_type)
    
    if advanced:
        return log_config.setup_advanced_logging(logger_name)
    else:
        log_config.setup_basic_logging()
        return log_config.get_logger(logger_name)

def log_script_start(logger, script_name, description=""):
    """
    记录脚本开始运行的日志
    
    Args:
        logger: 日志记录器
        script_name: 脚本名称
        description: 脚本描述
    """
    env_info = get_environment_info()
    logger.info("=" * 80)
    logger.info(f"脚本开始运行: {script_name}")
    if description:
        logger.info(f"描述: {description}")
    logger.info(f"运行环境: {env_info['name']} ({env_info['description']})")
    logger.info(f"数据库: {env_info['host']}/{env_info['database']}")
    logger.info(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 80)

def log_script_end(logger, script_name, success=True, summary=""):
    """
    记录脚本结束运行的日志
    
    Args:
        logger: 日志记录器
        script_name: 脚本名称
        success: 是否成功完成
        summary: 执行摘要
    """
    logger.info("=" * 80)
    status = "成功完成" if success else "执行失败"
    logger.info(f"脚本{status}: {script_name}")
    if summary:
        logger.info(f"执行摘要: {summary}")
    logger.info(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 80) 