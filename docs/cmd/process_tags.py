#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标签处理脚本
处理电台标签数据，将热门标签插入到world_tune_tags表中
"""

import mysql.connector
import argparse
import re
from collections import Counter
from config import (get_db_config, setup_logging, BusinessType, log_script_start, log_script_end,
                   init_environment, get_environment_info)

# 初始化日志系统
logger = setup_logging(BusinessType.TAG, advanced=False, logger_name="TagProcessor")

def get_tag_statistics():
    """获取标签统计信息"""
    try:
        conn = mysql.connector.connect(**get_db_config())
        cursor = conn.cursor(dictionary=True)
        
        # 查询所有电台的tags字段 - 只查询可用电台（status=1）
        query = '''
            SELECT tags 
            FROM world_tune_radio_stations 
            WHERE tags IS NOT NULL AND tags != ""
            AND status = 1
        '''
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        # 统计所有标签
        tag_counter = Counter()
        
        for row in results:
            tags = row['tags']
            if tags:
                # 按逗号分割标签
                tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
                tag_counter.update(tag_list)
        
        cursor.close()
        conn.close()
        
        return tag_counter
        
    except Exception as e:
        logger.error(f"获取标签统计失败: {e}")
        return Counter()

def is_pure_decade_tag(tag):
    """检查是否为纯粹的两位数字+s标签（如10s, 20s, 30s等）"""
    # 精确匹配：开头是两位数字，紧接着s，然后结束
    pattern = r'^\d{2}s$'
    return bool(re.match(pattern, tag))

def filter_tags(tag_counter, use_threshold):
    """筛选要保留的标签
    规则:
    1. 保留所有纯粹的两位数字+s标签（如10s, 20s, 30s等）
    2. 保留使用次数 > use_threshold 的其他标签
    3. 其他所有标签归类到'other'标签
    """
    logger.info("=" * 80)
    logger.info("开始筛选标签数据")
    logger.info("规则:")
    logger.info("  1. 保留所有纯粹的两位数字+s标签（如10s, 20s, 30s等）")
    logger.info(f"  2. 保留使用次数 > {use_threshold} 的其他标签")
    logger.info("  3. 其他所有标签归类到'other'标签")
    logger.info("  4. 按使用次数从高到低排序")
    logger.info("=" * 80)
    
    # 找出纯粹的两位数字+s标签和高使用次数标签
    decade_tags = []
    high_use_tags = []
    other_tags_count = 0
    other_tags_total_usage = 0
    
    for tag, count in tag_counter.items():
        if is_pure_decade_tag(tag):
            decade_tags.append({'tag': tag, 'count': count})
        elif count > use_threshold:
            high_use_tags.append({'tag': tag, 'count': count})
        else:
            # 归类到other
            other_tags_count += 1
            other_tags_total_usage += count
    
    logger.info(f"发现 {len(decade_tags)} 个纯粹的两位数字+s标签，将全部保留")
    logger.info(f"发现 {len(high_use_tags)} 个高使用次数标签（>{use_threshold}），将全部保留")
    logger.info(f"发现 {other_tags_count} 个其他标签，总使用次数 {other_tags_total_usage}，将归类到'other'")
    
    # 显示保留的两位数字+s标签
    for tag_data in sorted(decade_tags, key=lambda x: x['count'], reverse=True):
        logger.info(f"保留两位数字+s标签: {tag_data['tag']} (使用次数: {tag_data['count']})")
    
    # 显示保留的高使用次数标签
    for tag_data in sorted(high_use_tags, key=lambda x: x['count'], reverse=True):
        logger.info(f"保留高使用次数标签: {tag_data['tag']} (使用次数: {tag_data['count']})")
    
    # 合并所有要保留的标签
    all_tags_to_keep = decade_tags + high_use_tags
    
    # 添加other标签
    if other_tags_total_usage > 0:
        other_tag_data = {'tag': 'other', 'count': other_tags_total_usage}
        all_tags_to_keep.append(other_tag_data)
        logger.info(f"创建other标签: other (使用次数: {other_tags_total_usage})")
    
    # 按使用次数从高到低排序
    all_tags_to_keep.sort(key=lambda x: x['count'], reverse=True)
    
    logger.info("保留标签统计:")
    logger.info(f"  - 纯粹两位数字+s标签: {len(decade_tags)} 个")
    logger.info(f"  - 使用次数>{use_threshold}的标签: {len(high_use_tags)} 个")
    if other_tags_total_usage > 0:
        logger.info(f"  - other标签: 1 个 (包含{other_tags_count}个其他标签)")
    logger.info(f"  - 总计保留: {len(all_tags_to_keep)} 个标签")
    
    return all_tags_to_keep

def process_tags(use_threshold, dry_run=False):
    """处理标签数据，使用增量更新方式"""
    logger.info("开始处理标签数据...")
    
    # 获取标签统计信息
    tag_stats = get_tag_statistics()
    
    if not tag_stats:
        logger.warning("没有找到标签数据")
        return
    
    logger.info(f"收集到 {len(tag_stats)} 个唯一标签")
    
    # 筛选要保留的标签
    kept_tags = filter_tags(tag_stats, use_threshold)
    
    logger.info(f"筛选后保留 {len(kept_tags)} 个标签")
    
    # 使用增量更新方式写入数据库
    if not dry_run:
        insert_count, update_count = insert_or_update_tags(kept_tags)
        logger.info(f"标签数据处理完成 - 新增: {insert_count}, 更新: {update_count}")
    else:
        logger.info("试运行模式，跳过数据库写入")

def insert_or_update_tags(kept_tags):
    """使用UPSERT方式插入或更新标签数据，保持ID稳定"""
    logger.info("开始写入标签数据到数据库...")
    
    insert_count = 0
    update_count = 0
    
    try:
        conn = mysql.connector.connect(**get_db_config())
        cursor = conn.cursor(dictionary=True)
        
        for tag_data in kept_tags:
            tag_name = tag_data['tag']
            usage_count = tag_data['count']
            
            try:
                # 检查标签是否已存在
                check_query = "SELECT id, sort_order FROM world_tune_tags WHERE name = %s"
                cursor.execute(check_query, (tag_name,))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有标签（使用sort_order存储使用次数）
                    if existing['sort_order'] != usage_count:
                        update_query = """
                        UPDATE world_tune_tags 
                        SET sort_order = %s, updated_at = NOW() 
                        WHERE name = %s
                        """
                        cursor.execute(update_query, (usage_count, tag_name))
                        update_count += 1
                else:
                    # 插入新标签
                    insert_query = """
                    INSERT INTO world_tune_tags (name, sort_order, updated_at)
                    VALUES (%s, %s, NOW())
                    """
                    cursor.execute(insert_query, (tag_name, usage_count))
                    insert_count += 1
                    
            except Exception as e:
                logger.error(f"处理标签 '{tag_name}' 时出错: {e}")
                continue
        
        # 提交所有更改
        conn.commit()
        
        # 可选：清理不再使用的标签（usage_count = 0 或很久没更新的）
        cleanup_count = cleanup_unused_tags(cursor)
        if cleanup_count > 0:
            logger.info(f"清理了 {cleanup_count} 个不再使用的标签")
            conn.commit()
        
        cursor.close()
        conn.close()
        
        logger.info(f"标签数据写入完成 - 新增: {insert_count}, 更新: {update_count}")
        return insert_count, update_count
        
    except Exception as e:
        logger.error(f"写入标签数据到数据库失败: {e}")
        raise

def cleanup_unused_tags(cursor):
    """清理不再使用的标签（可选功能）"""
    try:
        # 删除sort_order为0的标签，或者最后更新时间超过30天的低频标签
        cleanup_query = """
        DELETE FROM world_tune_tags 
        WHERE sort_order = 0 
        OR (sort_order < 10 AND updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY))
        """
        cursor.execute(cleanup_query)
        rows_deleted = cursor.rowcount
        
        if rows_deleted > 0:
            logger.info(f"清理了 {rows_deleted} 个不再使用的标签")
        
        return rows_deleted
        
    except Exception as e:
        logger.error(f"清理不再使用的标签时出错: {e}")
        return 0

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='处理电台标签数据')
    parser.add_argument("--env", choices=['development', 'production'], 
                       default='production', help="运行环境 (development/production)")
    parser.add_argument('--use', type=int, default=900, 
                       help='使用次数阈值，保留使用次数大于此值的标签（默认: 900）')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，不执行实际的数据库修改操作')
    
    args = parser.parse_args()
    
    # 初始化环境配置
    try:
        current_env = init_environment()
        env_info = get_environment_info()
    except Exception as e:
        logger.error(f"环境配置初始化失败: {e}")
        return
    
    # 记录脚本开始
    log_script_start(logger, "标签处理脚本", f"使用次数阈值: {args.use}")
    
    success = False
    summary = ""
    
    try:
        process_tags(args.use, args.dry_run)
        success = True
        dry_run_info = "（试运行）" if args.dry_run else ""
        summary = f"标签处理完成{dry_run_info}，使用阈值: {args.use}"
    except Exception as e:
        logger.error(f"标签处理失败: {e}")
        summary = f"标签处理失败: {str(e)}"
    finally:
        log_script_end(logger, "标签处理脚本", success, summary)

if __name__ == "__main__":
    main() 