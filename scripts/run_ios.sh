#!/bin/bash

# World Tune iOS 运行脚本
# 使用方法: ./scripts/run_ios.sh

echo "🚀 World Tune iOS 启动脚本"
echo "=========================="

# 检查Flutter环境
echo "📋 检查Flutter环境..."
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter未安装或未添加到PATH"
    exit 1
fi

# 检查Xcode
echo "📋 检查Xcode环境..."
if ! command -v xcode-select &> /dev/null; then
    echo "❌ Xcode未安装"
    exit 1
fi

# 显示Flutter doctor信息
echo "📋 Flutter环境检查..."
flutter doctor

# 清理并获取依赖
echo "📦 获取Flutter依赖..."
flutter clean
flutter pub get

# 安装iOS依赖
echo "📦 安装iOS依赖..."
cd ios
pod install
cd ..

# 检查可用设备
echo "📱 检查可用设备..."
flutter devices

# 询问用户选择运行方式
echo ""
echo "请选择运行方式:"
echo "1) 在iOS模拟器中运行"
echo "2) 在Xcode中打开项目"
echo "3) 运行在连接的iOS设备上"
echo "4) 构建Release版本"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🚀 在iOS模拟器中启动..."
        flutter run --flavor development --target lib/main_development.dart -d ios
        ;;
    2)
        echo "🔧 在Xcode中打开项目..."
        open ios/Runner.xcworkspace
        ;;
    3)
        echo "📱 在真机上运行..."
        echo "请确保设备已连接并信任此电脑"
        flutter devices
        read -p "请输入设备ID: " device_id
        flutter run --flavor development --target lib/main_development.dart -d $device_id
        ;;
    4)
        echo "🏗️ 构建Release版本..."
        flutter build ios --release
        echo "✅ Release版本构建完成"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo "✅ 操作完成!"
