<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorldTune - Radio Share</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📻</text></svg>">
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: 
                linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #000000 100%);
            min-height: 100vh;
            color: #ffffff;
            overflow-x: hidden;
            position: relative;
        }

        /* 暗黑动态背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(108, 123, 149, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 75% 25%, rgba(74, 111, 165, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 25% 75%, rgba(143, 164, 179, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(122, 140, 168, 0.15) 0%, transparent 50%),
                linear-gradient(45deg, transparent 48%, rgba(143, 164, 179, 0.05) 49%, rgba(143, 164, 179, 0.05) 51%, transparent 52%);
            animation: dark-matrix 12s ease-in-out infinite;
            z-index: -2;
        }

        /* 暗色数字雨效果 */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(1px 1px at 20px 30px, #8fa4b3, transparent),
                radial-gradient(1px 1px at 40px 70px, #6c7b95, transparent),
                radial-gradient(1px 1px at 90px 40px, #a4a4a4, transparent),
                radial-gradient(1px 1px at 130px 80px, #7a8ca8, transparent),
                radial-gradient(1px 1px at 160px 30px, #9ba8b7, transparent),
                radial-gradient(2px 2px at 180px 120px, #8fa4b3, transparent),
                linear-gradient(180deg, transparent 95%, rgba(143, 164, 179, 0.05) 96%, rgba(143, 164, 179, 0.05) 97%, transparent 98%);
            background-repeat: repeat;
            background-size: 150px 150px;
            animation: dark-rain 8s linear infinite;
            z-index: -1;
            opacity: 0.3;
        }

        @keyframes dark-matrix {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg) scale(1);
                filter: hue-rotate(0deg) brightness(0.8);
            }
            25% { 
                transform: translateY(-20px) rotate(0.5deg) scale(1.02);
                filter: hue-rotate(30deg) brightness(0.9);
            }
            50% { 
                transform: translateY(-10px) rotate(0deg) scale(1);
                filter: hue-rotate(60deg) brightness(0.7);
            }
            75% { 
                transform: translateY(-30px) rotate(-0.5deg) scale(1.01);
                filter: hue-rotate(90deg) brightness(0.85);
            }
        }

        @keyframes dark-rain {
            0% { transform: translateY(0px) translateX(0px); }
            100% { transform: translateY(-150px) translateX(5px); }
        }

        /* 暗色扫描线网格效果 */
        .container::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(90deg, transparent 98%, rgba(143, 164, 179, 0.03) 100%),
                linear-gradient(180deg, transparent 98%, rgba(108, 123, 149, 0.03) 100%);
            background-size: 30px 30px;
            z-index: -1;
            animation: dark-grid-scan 20s linear infinite;
            pointer-events: none;
        }

        @keyframes dark-grid-scan {
            0% { opacity: 0.2; }
            50% { opacity: 0.4; }
            100% { opacity: 0.2; }
        }

        .container {
            max-width: 420px;
            margin: 0 auto;
            padding: 20px 15px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            padding: 20px 0;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ff0080, #7928ca, #00d4ff, transparent);
            border-radius: 2px;
            animation: header-glow 3s ease-in-out infinite;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #39ff14, #00d4ff, #ff0080, transparent);
            border-radius: 2px;
            animation: header-glow 3s ease-in-out infinite reverse;
        }

        @keyframes header-glow {
            0%, 100% { 
                opacity: 0.6;
                transform: translateX(-50%) scaleX(1);
            }
            50% { 
                opacity: 1;
                transform: translateX(-50%) scaleX(1.2);
            }
        }

        .app-title {
            font-size: 3.8rem;
            font-weight: 900;
            background: linear-gradient(45deg, #6c7b95, #a4a4a4, #8fa4b3, #7a8ca8, #9ba8b7, #6c7b95);
            background-size: 600% 600%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            letter-spacing: -0.5px;
            animation: dark-cyber-text 3s ease-in-out infinite;
            position: relative;
            text-transform: uppercase;
            font-family: 'Arial Black', 'Helvetica Neue', sans-serif;
            filter: 
                drop-shadow(0 0 8px rgba(108, 123, 149, 0.6))
                drop-shadow(0 0 16px rgba(143, 164, 179, 0.4))
                drop-shadow(0 0 32px rgba(122, 140, 168, 0.3))
                drop-shadow(0 0 64px rgba(155, 168, 183, 0.2));
        }

        /* 暗黑光晕背景 */
        .app-title::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: 
                radial-gradient(ellipse at center, rgba(108, 123, 149, 0.2) 0%, transparent 50%),
                radial-gradient(ellipse at 30% 70%, rgba(74, 111, 165, 0.15) 0%, transparent 50%),
                radial-gradient(ellipse at 70% 30%, rgba(143, 164, 179, 0.1) 0%, transparent 50%);
            border-radius: 50%;
            z-index: -1;
            animation: dark-aura 4s ease-in-out infinite;
            filter: blur(15px);
        }

        /* 暗色扫描线效果 */
        .app-title::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(143, 164, 179, 0.4) 45%, 
                rgba(199, 210, 216, 0.6) 50%, 
                rgba(143, 164, 179, 0.4) 55%, 
                transparent 100%);
            z-index: 1;
            animation: dark-scan 6s linear infinite;
            mix-blend-mode: screen;
        }

        @keyframes dark-cyber-text {
            0%, 100% { 
                background-position: 0% 50%;
                transform: scale(1) skew(0deg);
                filter: 
                    drop-shadow(0 0 8px rgba(108, 123, 149, 0.6))
                    drop-shadow(0 0 16px rgba(143, 164, 179, 0.4))
                    drop-shadow(0 0 32px rgba(122, 140, 168, 0.3))
                    drop-shadow(0 0 64px rgba(155, 168, 183, 0.2));
            }
            25% { 
                background-position: 100% 50%;
                transform: scale(1.02) skew(-0.5deg);
                filter: 
                    drop-shadow(0 0 10px rgba(74, 111, 165, 0.7))
                    drop-shadow(0 0 20px rgba(143, 164, 179, 0.5))
                    drop-shadow(0 0 40px rgba(199, 210, 216, 0.3))
                    drop-shadow(0 0 80px rgba(184, 197, 209, 0.2));
            }
            50% { 
                background-position: 0% 100%;
                transform: scale(1) skew(0deg);
                filter: 
                    drop-shadow(0 0 12px rgba(143, 164, 179, 0.6))
                    drop-shadow(0 0 24px rgba(122, 140, 168, 0.4))
                    drop-shadow(0 0 48px rgba(74, 111, 165, 0.3))
                    drop-shadow(0 0 96px rgba(199, 210, 216, 0.2));
            }
            75% { 
                background-position: 100% 0%;
                transform: scale(1.02) skew(0.5deg);
                filter: 
                    drop-shadow(0 0 14px rgba(184, 197, 209, 0.6))
                    drop-shadow(0 0 28px rgba(155, 168, 183, 0.4))
                    drop-shadow(0 0 56px rgba(108, 123, 149, 0.3))
                    drop-shadow(0 0 112px rgba(74, 111, 165, 0.2));
            }
        }

        @keyframes dark-aura {
            0%, 100% { 
                transform: scale(1) rotate(0deg);
                opacity: 0.4;
            }
            33% { 
                transform: scale(1.1) rotate(120deg);
                opacity: 0.6;
            }
            66% { 
                transform: scale(0.9) rotate(240deg);
                opacity: 0.3;
            }
        }

        @keyframes dark-scan {
            0% { 
                left: -100%;
                opacity: 0;
            }
            10% { 
                opacity: 0.7;
            }
            90% { 
                opacity: 0.7;
            }
            100% { 
                left: 100%;
                opacity: 0;
            }
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 500;
            letter-spacing: 1.2px;
            text-transform: uppercase;
            background: linear-gradient(90deg, #ffffff, #00d4ff, #ffffff);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: text-shimmer 4s ease-in-out infinite;
            position: relative;
            padding: 0 30px;
        }

        .subtitle::before,
        .subtitle::after {
            content: '✦';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
            color: #ff0080;
            animation: star-twinkle 2s ease-in-out infinite;
        }

        .subtitle::before {
            left: 0;
            animation-delay: 0s;
        }

        .subtitle::after {
            right: 0;
            animation-delay: 1s;
        }

        @keyframes star-twinkle {
            0%, 100% { 
                opacity: 0.4;
                transform: translateY(-50%) scale(1);
                color: #ff0080;
            }
            50% { 
                opacity: 1;
                transform: translateY(-50%) scale(1.3);
                color: #00d4ff;
            }
        }

        @keyframes text-shimmer {
            0%, 100% { background-position: -200% 0; }
            50% { background-position: 200% 0; }
        }

        .player-card {
            background: 
                linear-gradient(135deg, rgba(5, 5, 20, 0.9) 0%, rgba(10, 10, 30, 0.95) 100%);
            backdrop-filter: blur(30px) saturate(1.5);
            border-radius: 32px;
            padding: 40px 32px;
            width: 100%;
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(143, 164, 179, 0.15);
        }

        /* 暗色边框 */
        .player-card::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, 
                #6c7b95, #8fa4b3, #a4a4a4, #7a8ca8, #9ba8b7, #6c7b95, #8fa4b3);
            background-size: 800% 800%;
            border-radius: 35px;
            z-index: -3;
            animation: dark-border 6s linear infinite;
            filter: blur(1px);
        }

        /* 内部暗色发光层 */
        .player-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 50% 50%, rgba(143, 164, 179, 0.06) 0%, transparent 70%),
                radial-gradient(circle at 20% 80%, rgba(108, 123, 149, 0.05) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(74, 111, 165, 0.04) 0%, transparent 50%);
            border-radius: 32px;
            z-index: -1;
            animation: dark-inner-glow 4s ease-in-out infinite;
        }

        .player-card:hover {
            transform: translateY(-15px) scale(1.03) rotateX(2deg);
            box-shadow: 
                0 0 40px rgba(108, 123, 149, 0.3),
                0 0 80px rgba(143, 164, 179, 0.2),
                0 0 120px rgba(74, 111, 165, 0.15),
                0 50px 100px rgba(0, 0, 0, 0.5);
            border-color: rgba(143, 164, 179, 0.3);
        }

        /* 暗色扫描线效果 */
        .player-card .scan-line {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent, rgba(143, 164, 179, 0.5), transparent);
            animation: dark-scan-sweep 8s linear infinite;
            z-index: 2;
        }

        @keyframes dark-border {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes dark-inner-glow {
            0%, 100% { opacity: 0.4; }
            50% { opacity: 0.7; }
        }

        @keyframes dark-scan-sweep {
            0% { left: -100%; opacity: 0; }
            10% { opacity: 0.6; }
            90% { opacity: 0.6; }
            100% { left: 100%; opacity: 0; }
        }

        .station-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .station-favicon {
            width: 100px;
            height: 100px;
            border-radius: 25px;
            margin: 0 auto 20px;
            background: rgba(10, 10, 30, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .station-favicon::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(45deg, #ff0080, #7928ca, #00d4ff, #39ff14);
            background-size: 400% 400%;
            border-radius: 28px;
            z-index: -1;
            animation: icon-glow 3s linear infinite;
        }

        .station-favicon:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 
                0 0 30px rgba(255, 0, 128, 0.6),
                0 0 60px rgba(121, 40, 202, 0.4);
        }

        .station-favicon img {
            width: 100%;
            height: 100%;
            border-radius: 22px;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .station-favicon:hover img {
            filter: brightness(1.2) saturate(1.3);
        }

        @keyframes icon-glow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .station-name {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1.3;
            letter-spacing: -0.3px;
        }

        .station-location {
            font-size: 0.95rem;
            opacity: 0.8;
            font-weight: 400;
            margin-bottom: 16px;
        }

        .station-details {
            margin-bottom: 28px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 18px;
            margin: 8px 0;
            background: rgba(10, 10, 30, 0.6);
            border-radius: 14px;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .detail-row::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 0, 128, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .detail-row:hover::before {
            left: 100%;
        }

        .detail-label {
            opacity: 0.7;
            font-weight: 500;
        }

        .detail-value {
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .tags-container {
            margin: 12px 0;
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            justify-content: center;
        }

        .tag {
            background: rgba(255, 255, 255, 0.15);
            color: rgba(255, 255, 255, 0.9);
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .player-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .play-button {
            width: 110px;
            height: 110px;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transform-style: preserve-3d;
        }

        /* 主六边形按钮 */
        .play-button::before {
            content: '';
            position: absolute;
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, 
                rgba(30, 35, 50, 0.95) 0%,
                rgba(108, 123, 149, 0.9) 30%,
                rgba(143, 164, 179, 0.95) 60%,
                rgba(74, 111, 165, 0.9) 100%);
            clip-path: polygon(50% 0%, 93.3% 25%, 93.3% 75%, 50% 100%, 6.7% 75%, 6.7% 25%);
            z-index: 1;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4))
                    drop-shadow(0 0 20px rgba(108, 123, 149, 0.3));
            animation: hex-pulse 3s ease-in-out infinite;
        }

        /* 外圈能量环 */
        .play-button::after {
            content: '';
            position: absolute;
            width: 105px;
            height: 105px;
            background: conic-gradient(
                transparent 0deg,
                rgba(143, 164, 179, 0.4) 90deg,
                transparent 180deg,
                rgba(108, 123, 149, 0.4) 270deg,
                transparent 360deg
            );
            border-radius: 50%;
            z-index: -1;
            animation: energy-ring 4s linear infinite;
            filter: blur(1px);
        }

        /* 播放三角形图标 */
        .play-icon {
            position: relative;
            z-index: 10;
            color: #ffffff;
            font-size: 2.2rem;
            text-shadow: 
                0 0 10px rgba(255, 255, 255, 0.5),
                0 2px 4px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            font-family: Arial, sans-serif;
            font-weight: normal;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }

        /* 暂停状态的特殊效果 */
        .play-button.playing .play-icon {
            animation: pause-pulse 2s ease-in-out infinite;
        }

        @keyframes pause-pulse {
            0%, 100% { 
                transform: scale(1);
                opacity: 1;
            }
            50% { 
                transform: scale(0.95);
                opacity: 0.8;
            }
        }

        .play-button:hover {
            transform: scale(1.15) translateY(-4px) rotateX(5deg);
        }

        .play-button:hover::before {
            filter: drop-shadow(0 12px 24px rgba(0, 0, 0, 0.6))
                    drop-shadow(0 0 30px rgba(143, 164, 179, 0.6))
                    drop-shadow(0 0 50px rgba(108, 123, 149, 0.4));
            animation: hex-hover-pulse 0.8s ease-in-out infinite;
        }

        .play-button:hover::after {
            animation-duration: 2s;
            filter: blur(0.5px);
        }

        .play-button:hover .play-icon {
            color: #f0f8ff;
            transform: scale(1.1);
            text-shadow: 
                0 0 15px rgba(255, 255, 255, 0.8),
                0 0 25px rgba(143, 164, 179, 0.6),
                0 2px 4px rgba(0, 0, 0, 0.4);
        }

        .play-button:active {
            transform: scale(1.1) translateY(-2px) rotateX(2deg);
        }

        .play-button:active::before {
            filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.8))
                    drop-shadow(0 0 20px rgba(108, 123, 149, 0.8));
        }

        .play-button:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: none;
            animation: none;
            filter: grayscale(100%);
        }

        .play-button:disabled::before,
        .play-button:disabled::after {
            animation: none;
        }

        @keyframes hex-pulse {
            0%, 100% { 
                transform: scale(1);
                filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4))
                        drop-shadow(0 0 20px rgba(108, 123, 149, 0.3));
            }
            50% { 
                transform: scale(1.02);
                filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.5))
                        drop-shadow(0 0 25px rgba(143, 164, 179, 0.4))
                        drop-shadow(0 0 40px rgba(74, 111, 165, 0.2));
            }
        }

        @keyframes hex-hover-pulse {
            0%, 100% { 
                transform: scale(1);
            }
            50% { 
                transform: scale(1.05);
            }
        }

        @keyframes energy-ring {
            0% { 
                transform: rotate(0deg);
                opacity: 0.4;
            }
            50% { 
                opacity: 0.7;
            }
            100% { 
                transform: rotate(360deg);
                opacity: 0.4;
            }
        }

        /* 超炫酷的播放按钮容器 */
        .play-button-container {
            position: relative;
            display: inline-block;
            perspective: 1000px;
        }

        /* 外层能量波纹 */
        .play-button-container::before,
        .play-button-container::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 1px solid rgba(143, 164, 179, 0.2);
            border-radius: 50%;
            animation: energy-wave 4s ease-out infinite;
            pointer-events: none;
        }

        .play-button-container::before {
            width: 140px;
            height: 140px;
            animation-delay: 0s;
            border-color: rgba(108, 123, 149, 0.3);
        }

        .play-button-container::after {
            width: 170px;
            height: 170px;
            animation-delay: 2s;
            border-color: rgba(74, 111, 165, 0.2);
        }

        @keyframes energy-wave {
            0% {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(0.7);
                border-width: 2px;
            }
            50% {
                opacity: 0.3;
                border-width: 1px;
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(1.3);
                border-width: 0px;
            }
        }

        /* 添加悬浮时的额外粒子效果 */
        .play-button-container:hover::before {
            animation-duration: 2s;
        }

        .play-button-container:hover::after {
            animation-duration: 2s;
        }

        /* 添加内部脉冲光环 */
        .play-button-container:hover .play-button::before {
            box-shadow: 
                inset 0 0 20px rgba(143, 164, 179, 0.3),
                inset 0 0 40px rgba(108, 123, 149, 0.2);
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
            max-width: 160px;
            padding: 12px 16px;
            background: rgba(10, 10, 30, 0.4);
            border-radius: 20px;
            border: 1px solid rgba(143, 164, 179, 0.2);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .volume-control::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(143, 164, 179, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .volume-control:hover::before {
            left: 100%;
        }

        .volume-icon {
            font-size: 1.3rem;
            opacity: 0.9;
            color: #8fa4b3;
            text-shadow: 0 0 8px rgba(143, 164, 179, 0.3);
            transition: all 0.3s ease;
        }

        .volume-control:hover .volume-icon {
            color: #a4a4a4;
            text-shadow: 0 0 12px rgba(164, 164, 164, 0.5);
        }

        .volume-slider {
            flex: 1;
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(90deg, 
                rgba(143, 164, 179, 0.3) 0%, 
                rgba(108, 123, 149, 0.2) 100%);
            outline: none;
            -webkit-appearance: none;
            cursor: pointer;
            position: relative;
            box-shadow: 
                inset 0 2px 4px rgba(0, 0, 0, 0.3),
                0 1px 2px rgba(255, 255, 255, 0.1);
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #8fa4b3, #6c7b95, #a4a4a4);
            cursor: pointer;
            box-shadow: 
                0 4px 12px rgba(0, 0, 0, 0.4),
                0 2px 6px rgba(143, 164, 179, 0.3),
                inset 0 1px 3px rgba(255, 255, 255, 0.2),
                inset 0 -1px 3px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(143, 164, 179, 0.4);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .volume-slider::-webkit-slider-thumb:hover {
            transform: scale(1.15);
            box-shadow: 
                0 6px 16px rgba(0, 0, 0, 0.5),
                0 0 12px rgba(143, 164, 179, 0.5),
                inset 0 1px 4px rgba(255, 255, 255, 0.25),
                inset 0 -1px 4px rgba(0, 0, 0, 0.25);
        }

        .volume-slider::-webkit-slider-thumb:active {
            transform: scale(1.1);
        }

        .status-container {
            text-align: center;
            margin-bottom: 24px;
            padding: 12px 20px;
            background: rgba(10, 10, 30, 0.3);
            border-radius: 16px;
            border: 1px solid rgba(143, 164, 179, 0.15);
            backdrop-filter: blur(8px);
            position: relative;
            overflow: hidden;
        }

        .status-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(143, 164, 179, 0.08), transparent);
            animation: status-shimmer 3s ease-in-out infinite;
        }

        .status-text {
            font-size: 0.95rem;
            opacity: 0.9;
            font-weight: 500;
            min-height: 20px;
            color: #a4a4a4;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        @keyframes status-shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }



        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            text-align: center;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(143, 164, 179, 0.2);
            border-top: 4px solid #8fa4b3;
            border-right: 4px solid #6c7b95;
            border-radius: 50%;
            animation: dark-spin 1.5s linear infinite;
            position: relative;
        }

        .spinner::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border: 2px solid transparent;
            border-top: 2px solid rgba(164, 164, 164, 0.4);
            border-radius: 50%;
            animation: dark-spin-reverse 2s linear infinite;
        }

        @keyframes dark-spin {
            0% { 
                transform: rotate(0deg);
                filter: drop-shadow(0 0 8px rgba(143, 164, 179, 0.4));
            }
            50% { 
                filter: drop-shadow(0 0 12px rgba(108, 123, 149, 0.6));
            }
            100% { 
                transform: rotate(360deg);
                filter: drop-shadow(0 0 8px rgba(143, 164, 179, 0.4));
            }
        }

        @keyframes dark-spin-reverse {
            0% { transform: rotate(360deg); }
            100% { transform: rotate(0deg); }
        }

        .error-card {
            background: rgba(30, 20, 20, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(143, 100, 100, 0.3);
            border-radius: 24px;
            padding: 32px 24px;
            text-align: center;
            max-width: 400px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .error-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(143, 100, 100, 0.1) 0%, transparent 70%);
            z-index: -1;
        }

        .error-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            color: #c7a6a6;
            text-shadow: 0 2px 8px rgba(143, 100, 100, 0.4);
            filter: drop-shadow(0 0 8px rgba(143, 100, 100, 0.3));
        }

        .error-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 12px;
            letter-spacing: -0.3px;
            color: #d4b8b8;
        }

        .error-message {
            opacity: 0.9;
            line-height: 1.6;
            font-size: 1rem;
            color: #b8a0a0;
        }

        .retry-info {
            margin-top: 20px;
            font-size: 0.9rem;
            opacity: 0.7;
            font-style: italic;
            color: #a09090;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                padding: 15px 10px;
            }

            .app-title {
                font-size: 2.8rem;
            }

            .subtitle {
                font-size: 0.9rem;
                padding: 0 25px;
            }

            .subtitle::before,
            .subtitle::after {
                font-size: 1rem;
            }

            .header::before {
                width: 80px;
                height: 2px;
            }

            .header::after {
                width: 60px;
                height: 1.5px;
            }

            .player-card {
                padding: 24px 20px;
                border-radius: 20px;
            }

            .station-favicon {
                width: 80px;
                height: 80px;
                border-radius: 20px;
                font-size: 2.2rem;
            }

            .station-name {
                font-size: 1.2rem;
            }

            .station-location {
                font-size: 0.85rem;
            }

            .detail-row {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .tag {
                font-size: 0.7rem;
                padding: 3px 8px;
            }

            .play-button {
                width: 95px;
                height: 95px;
            }

            .play-button::before {
                width: 80px;
                height: 80px;
            }

            .play-button::after {
                width: 90px;
                height: 90px;
            }

            .play-icon {
                font-size: 1.8rem;
            }

            .play-button-container::before {
                width: 120px;
                height: 120px;
            }

            .play-button-container::after {
                width: 140px;
                height: 140px;
            }

            .volume-control {
                max-width: 130px;
                padding: 10px 14px;
                gap: 12px;
            }

            .volume-icon {
                font-size: 1.1rem;
            }

            .volume-slider {
                height: 6px;
            }

            .volume-slider::-webkit-slider-thumb {
                width: 20px;
                height: 20px;
            }


        }

        @media (max-width: 360px) {
            .player-card {
                padding: 20px 16px;
            }

            .detail-row {
                padding: 5px 10px;
                margin: 4px 0;
            }

            .player-controls {
                gap: 15px;
            }

            .volume-control {
                max-width: 110px;
                padding: 8px 12px;
                gap: 10px;
            }

            .volume-slider::-webkit-slider-thumb {
                width: 18px;
                height: 18px;
            }
        }



        .hidden {
            display: none !important;
        }

        /* 暗黑风格音频可视化 */
        .audio-visualizer {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px;
            height: 50px;
            margin: 25px 0;
            position: relative;
            padding: 10px;
            border-radius: 20px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(143, 164, 179, 0.15);
        }

        .audio-visualizer::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #6c7b95, #8fa4b3, #a4a4a4, #7a8ca8);
            background-size: 400% 400%;
            border-radius: 22px;
            z-index: -1;
            animation: dark-visualizer-border 3s linear infinite;
            filter: blur(1px);
        }

        .audio-visualizer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(ellipse at center, rgba(143, 164, 179, 0.06) 0%, transparent 70%),
                radial-gradient(ellipse at 30% 70%, rgba(108, 123, 149, 0.05) 0%, transparent 50%);
            border-radius: 20px;
            animation: dark-beat-pulse 2s ease-in-out infinite;
        }

        .bar {
            width: 6px;
            background: linear-gradient(180deg, 
                #6c7b95 0%, #8fa4b3 25%, #a4a4a4 50%, #7a8ca8 75%, #9ba8b7 100%);
            border-radius: 3px;
            animation: dark-edm-beat 0.8s ease-in-out infinite;
            box-shadow: 
                0 0 6px rgba(108, 123, 149, 0.6),
                0 0 12px rgba(143, 164, 179, 0.4),
                0 0 18px rgba(164, 164, 164, 0.3);
            position: relative;
            transition: all 0.2s ease;
        }

        .bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 100%;
            background: rgba(199, 210, 216, 0.8);
            border-radius: 1px;
            animation: dark-core-glow 0.8s ease-in-out infinite;
        }

        .bar::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -1px;
            right: -1px;
            bottom: -2px;
            background: inherit;
            filter: blur(3px);
            z-index: -1;
            opacity: 0.4;
        }

        .bar:nth-child(1) { height: 18px; animation-delay: 0s; }
        .bar:nth-child(2) { height: 32px; animation-delay: 0.1s; }
        .bar:nth-child(3) { height: 25px; animation-delay: 0.2s; }
        .bar:nth-child(4) { height: 40px; animation-delay: 0.3s; }
        .bar:nth-child(5) { height: 22px; animation-delay: 0.4s; }
        .bar:nth-child(6) { height: 35px; animation-delay: 0.5s; }
        .bar:nth-child(7) { height: 20px; animation-delay: 0.6s; }

        @keyframes dark-edm-beat {
            0%, 100% { 
                transform: scaleY(0.2) scaleX(1); 
                opacity: 0.5;
                filter: hue-rotate(0deg) brightness(0.8);
            }
            25% { 
                transform: scaleY(0.8) scaleX(1.1); 
                opacity: 0.7;
                filter: hue-rotate(30deg) brightness(1);
            }
            50% { 
                transform: scaleY(1) scaleX(1); 
                opacity: 0.9;
                filter: hue-rotate(60deg) brightness(1.2);
            }
            75% { 
                transform: scaleY(0.6) scaleX(1.05); 
                opacity: 0.6;
                filter: hue-rotate(90deg) brightness(0.9);
            }
        }

        @keyframes dark-core-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 0.8; }
        }

        @keyframes dark-beat-pulse {
            0%, 100% { 
                transform: scale(1);
                opacity: 0.3;
            }
            50% { 
                transform: scale(1.05);
                opacity: 0.5;
            }
        }

        @keyframes dark-visualizer-border {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* 增强的交互效果 */
        .detail-row:hover {
            background: rgba(20, 20, 50, 0.8);
            transform: translateX(4px) scale(1.02);
            border-color: rgba(255, 0, 128, 0.3);
            box-shadow: 0 4px 8px rgba(255, 0, 128, 0.2);
        }

        .tag:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        /* 加载状态优化 */
        .loading p {
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.9;
        }

        /* 浮动粒子效果 */
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #8fa4b3 0%, transparent 70%);
            border-radius: 50%;
            animation: float-particle 15s linear infinite;
        }

        .particle:nth-child(1) {
            left: 10%;
            animation-delay: 0s;
            animation-duration: 12s;
            background: radial-gradient(circle, #6c7b95 0%, transparent 70%);
        }

        .particle:nth-child(2) {
            left: 20%;
            animation-delay: 2s;
            animation-duration: 16s;
            background: radial-gradient(circle, #8fa4b3 0%, transparent 70%);
        }

        .particle:nth-child(3) {
            left: 70%;
            animation-delay: 4s;
            animation-duration: 14s;
            background: radial-gradient(circle, #a4a4a4 0%, transparent 70%);
        }

        .particle:nth-child(4) {
            left: 80%;
            animation-delay: 6s;
            animation-duration: 18s;
            background: radial-gradient(circle, #7a8ca8 0%, transparent 70%);
        }

        .particle:nth-child(5) {
            left: 40%;
            animation-delay: 8s;
            animation-duration: 13s;
            background: radial-gradient(circle, #9ba8b7 0%, transparent 70%);
        }

        .particle:nth-child(6) {
            left: 60%;
            animation-delay: 10s;
            animation-duration: 17s;
            background: radial-gradient(circle, #74a0c4 0%, transparent 70%);
        }

        @keyframes float-particle {
            0% {
                transform: translateY(100vh) translateX(0px) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
                transform: translateY(90vh) translateX(10px) scale(1);
            }
            90% {
                opacity: 1;
                transform: translateY(10vh) translateX(-10px) scale(1);
            }
            100% {
                transform: translateY(-10vh) translateX(0px) scale(0);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 添加浮动粒子效果 -->
        <div class="floating-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <div class="header">
            <h1 class="app-title">WorldTune</h1>
            <p class="subtitle">Discover Radio Stations Worldwide</p>
        </div>

        <!-- 加载状态 -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Loading station information...</p>
        </div>

        <!-- 播放器卡片 -->
        <div id="player-card" class="player-card hidden">
            <!-- 扫描线效果 -->
            <div class="scan-line"></div>
            
            <!-- 电台基本信息 -->
            <div class="station-header">
                <div id="station-favicon" class="station-favicon">
                    📻
                </div>
                <h2 id="station-name" class="station-name">Station Name</h2>
                <p id="station-location" class="station-location">Location</p>
            </div>

            <!-- 电台详细信息 -->
            <div class="station-details">
                <div id="genre-row" class="detail-row">
                    <span class="detail-label">Genre</span>
                    <span id="station-genre" class="detail-value">-</span>
                </div>
                <div id="language-row" class="detail-row">
                    <span class="detail-label">Language</span>
                    <span id="station-language" class="detail-value">-</span>
                </div>
                <div id="codec-row" class="detail-row">
                    <span class="detail-label">Codec</span>
                    <span id="station-codec" class="detail-value">-</span>
                </div>
                <div id="bitrate-row" class="detail-row">
                    <span class="detail-label">Bitrate</span>
                    <span id="station-bitrate" class="detail-value">-</span>
                </div>
                <div id="listeners-row" class="detail-row">
                    <span class="detail-label">Listeners</span>
                    <span id="station-listeners" class="detail-value">-</span>
                </div>
            </div>

            <!-- 标签 -->
            <div id="tags-container" class="tags-container hidden">
                <div id="station-tags" class="tags"></div>
            </div>

            <!-- 播放控制 -->
            <div class="player-controls">
                <div class="play-button-container">
                    <button id="play-button" class="play-button" title="Play/Pause">
                        <span class="play-icon">▶</span>
                    </button>
                </div>
                <div class="volume-control">
                    <span class="volume-icon">🔊</span>
                    <input type="range" id="volume-slider" class="volume-slider" 
                           min="0" max="1" step="0.1" value="0.8" title="Volume">
                </div>
            </div>

            <!-- 音频可视化效果 -->
            <div id="audio-visualizer" class="audio-visualizer hidden">
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
            </div>

            <!-- 状态显示 -->
            <div class="status-container">
                <p id="status-text" class="status-text">Ready to play</p>
            </div>
        </div>

        <!-- 错误状态 -->
        <div id="error-card" class="error-card hidden">
            <div class="error-icon">❌</div>
            <h3 class="error-title">Station Not Available</h3>
            <p class="error-message">The requested radio station could not be found or is currently unavailable.</p>
            <p class="retry-info">Please check the station UUID and try again.</p>
        </div>
    </div>



    <!-- 隐藏的音频元素 -->
    <audio id="audio-player" preload="none"></audio>

    <script>
        class RadioPlayer {
            constructor() {
                this.audio = document.getElementById('audio-player');
                this.hls = null;
                this.isPlaying = false;
                this.retryCount = 0;
                this.maxRetries = 5;
                this.retryDelay = 3000; // 3 seconds
                this.stationData = null;
                this.retryTimeout = null;

                this.initializeElements();
                this.setupEventListeners();
                this.loadStation();
            }

            initializeElements() {
                this.loadingEl = document.getElementById('loading');
                this.playerCardEl = document.getElementById('player-card');
                this.errorCardEl = document.getElementById('error-card');
                this.stationFaviconEl = document.getElementById('station-favicon');
                this.stationNameEl = document.getElementById('station-name');
                this.stationLocationEl = document.getElementById('station-location');
                this.stationGenreEl = document.getElementById('station-genre');
                this.stationLanguageEl = document.getElementById('station-language');
                this.stationCodecEl = document.getElementById('station-codec');
                this.stationBitrateEl = document.getElementById('station-bitrate');
                this.stationListenersEl = document.getElementById('station-listeners');
                this.stationTagsEl = document.getElementById('station-tags');
                this.tagsContainerEl = document.getElementById('tags-container');
                this.playButtonEl = document.getElementById('play-button');
                this.volumeSliderEl = document.getElementById('volume-slider');
                this.statusTextEl = document.getElementById('status-text');
                this.audioVisualizerEl = document.getElementById('audio-visualizer');
            }

            setupEventListeners() {
                this.playButtonEl.addEventListener('click', () => this.togglePlay());
                this.volumeSliderEl.addEventListener('input', (e) => this.setVolume(e.target.value));

                // 音频事件监听
                this.audio.addEventListener('loadstart', () => this.updateStatus('Loading...'));
                this.audio.addEventListener('canplay', () => this.updateStatus('Ready to play'));
                this.audio.addEventListener('playing', () => this.onPlaying());
                this.audio.addEventListener('pause', () => this.onPaused());
                this.audio.addEventListener('error', () => this.onError());
                this.audio.addEventListener('ended', () => this.onEnded());
                this.audio.addEventListener('waiting', () => this.updateStatus('Buffering...'));
                this.audio.addEventListener('timeupdate', () => this.onTimeUpdate());

                // 设置初始音量
                this.audio.volume = this.volumeSliderEl.value;
            }

            async loadStation() {
                const stationUuid = this.getStationUuidFromUrl();
                
                if (!stationUuid) {
                    this.showError('No station UUID provided in URL');
                    return;
                }

                try {
                    this.updateStatus('Fetching station information...');
                    const stationData = await this.fetchStationData(stationUuid);
                    
                    if (!stationData) {
                        this.showError('Station not found');
                        return;
                    }

                    this.stationData = stationData;
                    this.displayStationInfo(stationData);
                    this.setupAudioSource(stationData.url_resolved || stationData.url);
                    
                    this.showPlayer();
                } catch (error) {
                    console.error('Error loading station:', error);
                    this.showError('Failed to load station information');
                }
            }

            getStationUuidFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('s');
            }

            async fetchStationData(stationUuid) {
                const servers = [
                    'https://de1.api.radio-browser.info',
                    'https://de2.api.radio-browser.info',
                    'https://fi1.api.radio-browser.info'
                ];

                // 随机选择服务器
                const server = servers[Math.floor(Math.random() * servers.length)];
                const url = `${server}/json/stations/byuuid/${stationUuid}`;

                try {
                    const response = await fetch(url, {
                        headers: {
                            'User-Agent': 'WorldTune/1.0.0'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    
                    if (Array.isArray(data) && data.length > 0) {
                        // 点击计数
                        this.recordClick(stationUuid, server);
                        return data[0];
                    }
                    
                    return null;
                } catch (error) {
                    console.error('Fetch error:', error);
                    throw error;
                }
            }

            async recordClick(stationUuid, server) {
                try {
                    await fetch(`${server}/json/url/${stationUuid}`, {
                        headers: {
                            'User-Agent': 'WorldTune/1.0.0'
                        }
                    });
                } catch (error) {
                    console.warn('Failed to record click:', error);
                }
            }

            displayStationInfo(station) {
                // 设置电台名称
                this.stationNameEl.textContent = station.name || 'Unknown Station';
                
                // 设置位置信息
                const locationInfo = [];
                if (station.country) locationInfo.push(station.country);
                if (station.state) locationInfo.push(station.state);
                this.stationLocationEl.textContent = locationInfo.join(', ') || 'Unknown Location';

                // 设置图标
                if (station.favicon && station.favicon.trim()) {
                    const img = document.createElement('img');
                    img.src = station.favicon;
                    img.alt = station.name;
                    img.onerror = () => {
                        this.stationFaviconEl.innerHTML = '📻';
                    };
                    this.stationFaviconEl.innerHTML = '';
                    this.stationFaviconEl.appendChild(img);
                } else {
                    this.stationFaviconEl.innerHTML = '📻';
                }

                // 设置详细信息
                this.displayStationDetails(station);

                // 设置标签
                this.displayStationTags(station);

                // 更新页面标题
                document.title = `WorldTune - ${station.name}`;
            }

            displayStationDetails(station) {
                // 流派信息 (从tags中提取或使用现有字段)
                const genre = this.extractGenre(station.tags) || station.tags || '-';
                this.stationGenreEl.textContent = genre;

                // 语言
                this.stationLanguageEl.textContent = station.language || '-';

                // 编码格式
                this.stationCodecEl.textContent = station.codec || '-';

                // 比特率
                const bitrate = station.bitrate ? `${station.bitrate} kbps` : '-';
                this.stationBitrateEl.textContent = bitrate;

                // 听众数 (使用clickcount作为近似值)
                const listeners = station.clickcount ? 
                    this.formatNumber(station.clickcount) : '-';
                this.stationListenersEl.textContent = listeners;
            }

            displayStationTags(station) {
                if (!station.tags || !station.tags.trim()) {
                    this.tagsContainerEl.classList.add('hidden');
                    return;
                }

                const tags = station.tags.split(',')
                    .map(tag => tag.trim())
                    .filter(tag => tag.length > 0)
                    .slice(0, 6); // 限制显示6个标签

                if (tags.length === 0) {
                    this.tagsContainerEl.classList.add('hidden');
                    return;
                }

                this.stationTagsEl.innerHTML = '';
                tags.forEach(tag => {
                    const tagEl = document.createElement('span');
                    tagEl.className = 'tag';
                    tagEl.textContent = tag;
                    this.stationTagsEl.appendChild(tagEl);
                });

                this.tagsContainerEl.classList.remove('hidden');
            }

            extractGenre(tags) {
                if (!tags) return null;
                
                const commonGenres = [
                    'pop', 'rock', 'jazz', 'classical', 'electronic', 'dance', 
                    'hip hop', 'country', 'folk', 'blues', 'reggae', 'latin',
                    'world music', 'news', 'talk', 'sports', 'music'
                ];
                
                const tagsList = tags.toLowerCase().split(',').map(t => t.trim());
                
                for (const genre of commonGenres) {
                    if (tagsList.some(tag => tag.includes(genre))) {
                        return genre.charAt(0).toUpperCase() + genre.slice(1);
                    }
                }
                
                // 返回第一个标签作为流派
                return tagsList[0] ? 
                    tagsList[0].charAt(0).toUpperCase() + tagsList[0].slice(1) : null;
            }

            formatNumber(num) {
                if (num >= 1000000) {
                    return (num / 1000000).toFixed(1) + 'M';
                } else if (num >= 1000) {
                    return (num / 1000).toFixed(1) + 'K';
                } else {
                    return num.toString();
                }
            }

            setupAudioSource(url) {
                if (!url) {
                    this.updateStatus('No audio URL available');
                    return;
                }

                // 清理之前的HLS实例
                if (this.hls) {
                    this.hls.destroy();
                    this.hls = null;
                }

                // 检查是否是HLS流
                if (url.includes('.m3u8') || url.includes('hls')) {
                    this.setupHLSStream(url);
                } else {
                    // 直接设置音频源
                    this.audio.src = url;
                }

                this.updateStatus('Ready to play');
            }

            setupHLSStream(url) {
                if (Hls.isSupported()) {
                    this.hls = new Hls({
                        enableWorker: true,
                        lowLatencyMode: true,
                        backBufferLength: 90
                    });

                    this.hls.loadSource(url);
                    this.hls.attachMedia(this.audio);

                    this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                        console.log('HLS manifest parsed successfully');
                    });

                    this.hls.on(Hls.Events.ERROR, (event, data) => {
                        console.error('HLS error:', data);
                        if (data.fatal) {
                            this.handlePlaybackError();
                        }
                    });
                } else if (this.audio.canPlayType('application/vnd.apple.mpegurl')) {
                    // Safari 原生支持
                    this.audio.src = url;
                } else {
                    this.updateStatus('HLS not supported in this browser');
                }
            }

            async togglePlay() {
                if (this.isPlaying) {
                    this.pause();
                } else {
                    await this.play();
                }
            }

            async play() {
                try {
                    this.playButtonEl.disabled = true;
                    this.updateStatus('Starting playback...');
                    
                    await this.audio.play();
                    this.retryCount = 0; // 重置重试计数
                } catch (error) {
                    console.error('Play error:', error);
                    this.handlePlaybackError();
                } finally {
                    this.playButtonEl.disabled = false;
                }
            }

            pause() {
                this.audio.pause();
            }

            setVolume(value) {
                this.audio.volume = value;
            }

            onPlaying() {
                this.isPlaying = true;
                this.playButtonEl.querySelector('.play-icon').textContent = '⏸';
                this.playButtonEl.classList.add('playing');
                this.updateStatus('Playing');
                this.audioVisualizerEl.classList.remove('hidden');
            }

            onPaused() {
                this.isPlaying = false;
                this.playButtonEl.querySelector('.play-icon').textContent = '▶';
                this.playButtonEl.classList.remove('playing');
                this.updateStatus('Paused');
                this.audioVisualizerEl.classList.add('hidden');
            }

            onError() {
                console.error('Audio error occurred');
                this.handlePlaybackError();
            }

            onEnded() {
                this.isPlaying = false;
                this.playButtonEl.querySelector('.play-icon').textContent = '▶';
                this.playButtonEl.classList.remove('playing');
                this.updateStatus('Stream ended');
                this.audioVisualizerEl.classList.add('hidden');
            }

            onTimeUpdate() {
                // 检查是否有音频数据
                if (this.audio.currentTime > 0) {
                    this.retryCount = 0; // 重置重试计数
                }
            }

            handlePlaybackError() {
                this.isPlaying = false;
                this.playButtonEl.textContent = '▶️';
                this.audioVisualizerEl.classList.add('hidden');

                if (this.retryCount < this.maxRetries) {
                    this.retryCount++;
                    this.updateStatus(`Playback failed, retrying in 3 seconds... (${this.retryCount}/${this.maxRetries})`);
                    
                    this.retryTimeout = setTimeout(() => {
                        this.updateStatus('Retrying...');
                        this.play();
                    }, this.retryDelay);
                } else {
                    this.updateStatus('Playback failed after multiple attempts');
                }
            }



            updateStatus(text) {
                this.statusTextEl.textContent = text;
            }

            showPlayer() {
                this.loadingEl.classList.add('hidden');
                this.errorCardEl.classList.add('hidden');
                this.playerCardEl.classList.remove('hidden');
            }

            showError(message) {
                this.loadingEl.classList.add('hidden');
                this.playerCardEl.classList.add('hidden');
                this.errorCardEl.classList.remove('hidden');
                
                const errorMessageEl = this.errorCardEl.querySelector('.error-message');
                if (errorMessageEl) {
                    errorMessageEl.textContent = message;
                }
            }



            destroy() {
                if (this.retryTimeout) {
                    clearTimeout(this.retryTimeout);
                }
                
                if (this.hls) {
                    this.hls.destroy();
                }
                
                this.audio.pause();
                this.audio.src = '';
            }
        }

        // 初始化应用
        let radioPlayer;

        document.addEventListener('DOMContentLoaded', () => {
            radioPlayer = new RadioPlayer();
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (radioPlayer) {
                radioPlayer.destroy();
            }
        });

        // 处理页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && radioPlayer && radioPlayer.isPlaying) {
                // 页面隐藏时保持播放（后台播放）
                console.log('Page hidden, continuing playback');
            } else if (!document.hidden && radioPlayer) {
                // 页面显示时
                console.log('Page visible');
            }
        });
    </script>
</body>
</html>