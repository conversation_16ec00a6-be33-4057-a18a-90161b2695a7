#!/bin/bash

# World Tune - 快速启动脚本
# 用于解决常见的开发环境问题并快速启动应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[World Tune]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装或不在 PATH 中"
        return 1
    fi
    return 0
}

# 检查环境
check_environment() {
    print_message "检查开发环境..."
    
    # 检查 Flutter
    if ! check_command flutter; then
        print_error "请先安装 Flutter SDK"
        exit 1
    fi
    
    # 检查 Flutter 版本
    flutter_version=$(flutter --version | head -n 1)
    print_info "Flutter 版本: $flutter_version"
    
    # 检查 CocoaPods (仅在 macOS 上)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if ! check_command pod; then
            print_warning "CocoaPods 未安装，正在安装..."
            sudo gem install cocoapods
        fi
    fi
    
    print_message "环境检查完成 ✅"
}

# 清理项目
clean_project() {
    print_message "清理项目..."
    
    # 清理 Flutter
    flutter clean
    rm -f pubspec.lock
    
    # 清理 iOS (仅在 macOS 上)
    if [[ "$OSTYPE" == "darwin"* ]] && [ -d "ios" ]; then
        print_message "清理 iOS 依赖..."
        cd ios
        rm -rf Pods/ Podfile.lock
        cd ..
    fi
    
    print_message "项目清理完成 ✅"
}

# 安装依赖
install_dependencies() {
    print_message "安装依赖..."
    
    # 安装 Flutter 依赖
    flutter pub get
    
    # 安装 iOS 依赖 (仅在 macOS 上)
    if [[ "$OSTYPE" == "darwin"* ]] && [ -d "ios" ]; then
        print_message "安装 iOS 依赖..."
        cd ios
        pod install
        cd ..
    fi
    
    print_message "依赖安装完成 ✅"
}

# 生成代码
generate_code() {
    print_message "生成代码..."
    flutter packages pub run build_runner build --delete-conflicting-outputs
    print_message "代码生成完成 ✅"
}

# 运行应用
run_app() {
    local platform=$1
    local flavor=${2:-development}
    
    print_message "启动应用 (平台: $platform, 环境: $flavor)..."
    
    case $platform in
        ios)
            if [[ "$OSTYPE" != "darwin"* ]]; then
                print_error "iOS 开发需要 macOS 系统"
                exit 1
            fi
            flutter run -d ios --flavor $flavor -t lib/main_$flavor.dart
            ;;
        android)
            flutter run -d android --flavor $flavor -t lib/main_$flavor.dart
            ;;
        web)
            flutter run -d chrome --flavor $flavor -t lib/main_$flavor.dart
            ;;
        *)
            flutter run --flavor $flavor -t lib/main_$flavor.dart
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "World Tune 快速启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -c, --clean         仅清理项目"
    echo "  -s, --setup         仅设置环境"
    echo "  -p, --platform      指定平台 (ios|android|web)"
    echo "  -f, --flavor        指定环境 (development|staging|production)"
    echo "  --fix               修复常见问题"
    echo ""
    echo "示例:"
    echo "  $0                  # 完整设置并运行默认平台"
    echo "  $0 -p ios           # 运行 iOS 版本"
    echo "  $0 -p android -f production  # 运行 Android 生产版本"
    echo "  $0 --fix            # 修复常见问题"
}

# 修复常见问题
fix_issues() {
    print_message "修复常见问题..."
    
    # 检查并修复类型错误
    if grep -q "station.tags.split" lib/src/shared/widgets/station_list_item.dart 2>/dev/null; then
        print_warning "发现类型错误，正在修复..."
        sed -i.bak 's/station.tags.split.*\.take(2)\.map.*\.toList()/station.tags.take(2).toList()/' lib/src/shared/widgets/station_list_item.dart
        rm -f lib/src/shared/widgets/station_list_item.dart.bak
        print_message "类型错误已修复 ✅"
    fi
    
    # 清理和重新安装
    clean_project
    install_dependencies
    generate_code
    
    print_message "问题修复完成 ✅"
}

# 主函数
main() {
    local platform=""
    local flavor="development"
    local clean_only=false
    local setup_only=false
    local fix_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                clean_only=true
                shift
                ;;
            -s|--setup)
                setup_only=true
                shift
                ;;
            -p|--platform)
                platform="$2"
                shift 2
                ;;
            -f|--flavor)
                flavor="$2"
                shift 2
                ;;
            --fix)
                fix_only=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示欢迎信息
    echo ""
    print_message "🎵 World Tune 快速启动脚本"
    echo ""
    
    # 执行相应操作
    if [ "$fix_only" = true ]; then
        fix_issues
        exit 0
    fi
    
    if [ "$clean_only" = true ]; then
        clean_project
        exit 0
    fi
    
    if [ "$setup_only" = true ]; then
        check_environment
        install_dependencies
        generate_code
        exit 0
    fi
    
    # 完整流程
    check_environment
    clean_project
    install_dependencies
    generate_code
    
    print_message "🚀 准备启动应用..."
    run_app "$platform" "$flavor"
}

# 运行主函数
main "$@"
