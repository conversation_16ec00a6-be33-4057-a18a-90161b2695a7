#!/bin/bash

echo "=== World Tune 应用配置验证 ==="
echo ""

echo "1. 检查应用名称配置："
echo "   Android Production: $(grep -A 1 'production {' android/app/build.gradle | grep 'appName' | cut -d'"' -f2)"
echo "   Android Staging: $(grep -A 3 'staging {' android/app/build.gradle | grep 'appName' | cut -d'"' -f2)"
echo "   Android Development: $(grep -A 3 'development {' android/app/build.gradle | grep 'appName' | cut -d'"' -f2)"
echo "   iOS: $(grep -A 1 'CFBundleName' ios/Runner/Info.plist | grep 'string' | sed 's/.*<string>\(.*\)<\/string>.*/\1/')"
echo ""

echo "2. 检查应用图标文件："
echo "   Android 图标文件数量: $(find android/app/src/main/res -name 'launcher_icon.png' | wc -l)"
echo "   iOS 图标文件数量: $(find ios/Runner/Assets.xcassets/AppIcon.appiconset -name '*.png' | wc -l)"
echo ""

echo "3. 检查应用包名："
echo "   Android: $(grep 'applicationId' android/app/build.gradle | head -1 | cut -d'"' -f2)"
echo ""

echo "4. 检查构建产物："
if [ -f "build/app/outputs/flutter-apk/app-production-release.apk" ]; then
    echo "   ✅ Android Production APK 已生成"
    echo "   文件大小: $(du -h build/app/outputs/flutter-apk/app-production-release.apk | cut -f1)"
else
    echo "   ❌ Android Production APK 未找到"
fi

echo ""
echo "5. 验证图标配置："
if [ -f "pubspec.yaml" ] && grep -q "flutter_launcher_icons:" pubspec.yaml; then
    echo "   ✅ flutter_launcher_icons 已配置"
    echo "   图标路径: $(grep 'image_path:' pubspec.yaml | cut -d'"' -f2)"
else
    echo "   ❌ flutter_launcher_icons 未配置"
fi

echo ""
echo "=== 验证完成 ==="
