PODS:
  - Flutter (1.0.0)
  - Google-Mobile-Ads-SDK (12.2.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (6.0.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 12.2.0)
    - webview_flutter_wkwebview
  - GoogleUserMessagingPlatform (3.0.0)
  - integration_test (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Google-Mobile-Ads-SDK
    - GoogleUserMessagingPlatform

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  Google-Mobile-Ads-SDK: 1dfb0c3cb46c7e2b00b0f4de74a1e06d9ea25d67
  google_mobile_ads: 535223588a6791b7a3cc3513a1bc7b89d12f3e62
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
