import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    // 注册原生广告工厂
    let nativeAdFactory = NativeAdFactoryExample()
    FLTGoogleMobileAdsPlugin.registerNativeAdFactory(
      self,
      factoryId: "adFactoryExample",
      nativeAdFactory: nativeAdFactory
    )

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
