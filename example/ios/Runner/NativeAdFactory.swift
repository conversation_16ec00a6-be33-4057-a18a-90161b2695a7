// Copyright 2021 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import Foundation
import UIKit
import GoogleMobileAds

/// Swift版本的原生广告工厂类
/// 用于创建和配置原生广告视图
class NativeAdFactoryExample: NSObject, FLTNativeAdFactory {
    
    /// 创建原生广告视图
    /// - Parameters:
    ///   - nativeAd: Google Mobile Ads的原生广告对象
    ///   - customOptions: 自定义选项字典
    /// - Returns: 配置好的NativeAdView
    func createNativeAd(_ nativeAd: NativeAd, customOptions: [AnyHashable : Any]?) -> NativeAdView {

        // 从XIB文件加载原生广告视图
        guard let adView = Bundle.main.loadNibNamed("NativeAdView", owner: nil, options: nil)?.first as? NativeAdView else {
            fatalError("无法加载NativeAdView.xib文件")
        }
        
        // 将原生广告对象与视图关联，这是使广告可点击的必要步骤
        adView.nativeAd = nativeAd
        
        // 使用原生广告资源填充视图
        // 标题是每个原生广告都保证存在的
        if let headlineView = adView.headlineView as? UILabel {
            headlineView.text = nativeAd.headline
        }
        
        // 这些资源不保证存在，需要检查后再显示或隐藏
        
        // 广告正文
        if let bodyView = adView.bodyView as? UILabel {
            bodyView.text = nativeAd.body
            bodyView.isHidden = nativeAd.body == nil
        }
        
        // 行动号召按钮
        if let callToActionView = adView.callToActionView as? UIButton {
            callToActionView.setTitle(nativeAd.callToAction, for: .normal)
            callToActionView.isHidden = nativeAd.callToAction == nil
        }
        
        // 图标
        if let iconView = adView.iconView as? UIImageView {
            iconView.image = nativeAd.icon?.image
            iconView.isHidden = nativeAd.icon == nil
        }
        
        // 商店信息
        if let storeView = adView.storeView as? UILabel {
            storeView.text = nativeAd.store
            storeView.isHidden = nativeAd.store == nil
        }
        
        // 价格信息
        if let priceView = adView.priceView as? UILabel {
            priceView.text = nativeAd.price
            priceView.isHidden = nativeAd.price == nil
        }
        
        // 广告主信息
        if let advertiserView = adView.advertiserView as? UILabel {
            advertiserView.text = nativeAd.advertiser
            advertiserView.isHidden = nativeAd.advertiser == nil
        }
        
        // 为了让SDK正确处理触摸事件，需要禁用用户交互
        adView.callToActionView?.isUserInteractionEnabled = false
        
        return adView
    }
}
