import 'package:flutter/material.dart';
import 'package:ad_sdk/ad_sdk.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart' as gma;

/// 广告演示页面
/// 
/// 展示单个广告类型的完整功能，包括加载、展示、状态检查等
class AdDemoPage extends StatefulWidget {
  final AdType adType;
  final String title;

  const AdDemoPage({
    super.key,
    required this.adType,
    required this.title,
  });

  @override
  State<AdDemoPage> createState() => _AdDemoPageState();
}

class _AdDemoPageState extends State<AdDemoPage> {

  String _lastAction = '';
  bool _isLoading = false;
  final List<String> _logs = [];
  gma.AdSize _selectedBannerSize = gma.AdSize.banner;
  bool _bannerShown = false; // 标记Banner是否已展示
  bool _isUsingBackup = false; // 标记当前使用的是否为备用广告

  /// 获取广告场景ID
  /// 不同广告类型使用不同的场景ID，用于价格事件上报
  int _getSceneId() {
    switch (widget.adType) {
      case AdType.banner:
        return 6; // 主界面Banner
      case AdType.interstitial:
        return 1; // 开屏插屏
      case AdType.rewarded:
        return 2; // 激励视频
      case AdType.appOpen:
        return 3; // 开屏广告
      case AdType.native:
        return 5; // 新闻列表页Native
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 价格事件提示卡片
            Card(
              color: Colors.green.shade50,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  children: [
                    Icon(Icons.monetization_on, color: Colors.green.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '💰 价格事件上报已启用',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                          Text(
                            '场景ID: ${_getSceneId()} - 展示广告时将自动上报价格事件到控制台',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 操作按钮
            Text(
              widget.adType == AdType.banner ? 'Banner广告操作' : '主广告操作',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _loadAd(false),
                    icon: const Icon(Icons.download),
                    label: Text(widget.adType == AdType.banner ? '加载主Banner' : '加载主广告'),
                  ),
                ),
                const SizedBox(width: 8),
                if (widget.adType == AdType.native) ...[
                  // 原生广告有两个展示按钮
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _showNativeWithTemplate(false),
                      icon: const Icon(Icons.view_agenda),
                      label: const Text('中模板'),
                    ),
                  ),
                ] else ...[
                  // 其他广告类型只有一个展示按钮
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _showAd(false),
                      icon: const Icon(Icons.play_arrow),
                      label: Text(widget.adType == AdType.banner ? '展示主Banner' : '展示主广告'),
                    ),
                  ),
                ],
              ],
            ),

            // 原生广告的第二行按钮
            if (widget.adType == AdType.native) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _showNativeWithFactory(false),
                      icon: const Icon(Icons.widgets),
                      label: const Text('展示原生'),
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 8),

            // Banner广告尺寸选择
            if (widget.adType == AdType.banner) ...[
              Text(
                'Banner广告尺寸',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              DropdownButton<gma.AdSize>(
                value: _selectedBannerSize,
                isExpanded: true,
                items: [
                  DropdownMenuItem(
                    value: gma.AdSize.banner,
                    child: Text('标准Banner (320x50)'),
                  ),
                  DropdownMenuItem(
                    value: gma.AdSize.largeBanner,
                    child: Text('大Banner (320x100)'),
                  ),
                  DropdownMenuItem(
                    value: gma.AdSize.mediumRectangle,
                    child: Text('中等矩形 (300x250)'),
                  ),
                  DropdownMenuItem(
                    value: gma.AdSize.fullBanner,
                    child: Text('全Banner (468x60)'),
                  ),
                  DropdownMenuItem(
                    value: gma.AdSize.leaderboard,
                    child: Text('排行榜 (728x90)'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    // 切换尺寸时，先销毁之前的Banner广告
                    if (widget.adType == AdType.banner) {
                      AdSDK.disposeBanner(pageId: 'demo_page', backup: false);
                      AdSDK.disposeBanner(pageId: 'demo_page', backup: true);
                    }

                    setState(() {
                      _selectedBannerSize = value;
                      // 切换尺寸时，清除当前Banner显示状态
                      // 用户需要重新加载对应尺寸的Banner
                      _bannerShown = false;
                    });
                    _addLog('Banner尺寸已切换，请重新加载广告');
                  }
                },
              ),
              const SizedBox(height: 16),
            ],

            Text(
              '备用广告操作',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            if (widget.adType == AdType.native) ...[
              // 原生广告的备用操作
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _loadAd(true),
                      icon: const Icon(Icons.download_outlined),
                      label: const Text('加载备用广告'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _showNativeWithTemplate(true),
                      icon: const Icon(Icons.view_agenda_outlined),
                      label: const Text('备用中模板'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _showNativeWithFactory(true),
                      icon: const Icon(Icons.widgets_outlined),
                      label: const Text('备用原生'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              // 其他广告类型的备用操作
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _loadAd(true),
                      icon: const Icon(Icons.download_outlined),
                      label: const Text('加载备用广告'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _showAd(true),
                      icon: const Icon(Icons.play_arrow_outlined),
                      label: const Text('展示备用广告'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 16),

            // Banner广告展示区域（只有在show后才显示）
            if (widget.adType == AdType.banner && _bannerShown) ...[
              Card(
                child: Container(
                  height: _getBannerHeight(),
                  alignment: Alignment.center,
                  child: AdSDK.getBannerWidget(
                    pageId: 'demo_page',
                    backup: _isUsingBackup,
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // 原生广告展示区域
            if (widget.adType == AdType.native) ...[
              Card(
                child: SizedBox(
                  height: 300,
                  child: AdSDK.getNativeWidget(
                    backup: _isUsingBackup,
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

           
          ],
        ),
      ),
    );
  }



  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _logs.add('[$timestamp] $message');
      print('[$timestamp] $message');
    });
  }


  Future<void> _loadAd(bool backup) async {
    setState(() {
      _isLoading = true;
      _lastAction = '加载${backup ? '备用' : '主'}广告';
      _isUsingBackup = backup; // 更新备用广告状态
    });

    _addLog('开始$_lastAction');

    try {
      if (widget.adType == AdType.native) {
        // 原生广告使用preLoad
        await AdSDK.preLoad(
          widget.adType,
          backup: backup,
          callback: _createCallback(),
        );
      } else if (backup) {
        await AdSDK.loadBackup(
          widget.adType,
          callback: _createCallback(),
          bannerSize: widget.adType == AdType.banner ? _selectedBannerSize : null,
          pageId: widget.adType == AdType.banner ? 'demo_page' : null,
        );
      } else {
        await AdSDK.load(
          widget.adType,
          callback: _createCallback(),
          bannerSize: widget.adType == AdType.banner ? _selectedBannerSize : null,
          pageId: widget.adType == AdType.banner ? 'demo_page' : null,
        );
      }
    } catch (e) {
      _addLog('$_lastAction异常: $e');

    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showAd(bool backup) async {
    setState(() {
      _isLoading = true;
      _lastAction = '展示${backup ? '备用' : '主'}广告';
      _isUsingBackup = backup; // 更新备用广告状态
    });

    _addLog('开始$_lastAction');

    try {
      if (backup) {
        await AdSDK.showBackup(
          widget.adType,
          callback: _createCallback(),
          bannerSize: widget.adType == AdType.banner ? _selectedBannerSize : null,
          pageId: widget.adType == AdType.banner ? 'demo_page' : null,
          scene: _getSceneId(), // 添加场景参数，用于价格事件上报
        );
      } else {
        await AdSDK.show(
          widget.adType,
          callback: _createCallback(),
          bannerSize: widget.adType == AdType.banner ? _selectedBannerSize : null,
          pageId: widget.adType == AdType.banner ? 'demo_page' : null,
          scene: _getSceneId(), // 添加场景参数，用于价格事件上报
        );
      }
    } catch (e) {
      _addLog('$_lastAction异常: $e');

    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 使用模板展示原生广告
  Future<void> _showNativeWithTemplate(bool backup) async {
    setState(() {
      _isLoading = true;
      _lastAction = '展示${backup ? '备用' : '主'}原生广告(模板)';
      _isUsingBackup = backup; // 更新备用广告状态
    });

    _addLog('开始$_lastAction');

    try {
      await AdSDK.bindViewByTemplateStyle(
        AdType.native,
        templateType: 'medium',
        backup: backup,
        callback: _createCallback(),
        scene: _getSceneId(), // 添加场景参数，用于价格事件上报
      );

    } catch (e) {
      _addLog('$_lastAction异常: $e');

    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 使用Factory展示原生广告
  Future<void> _showNativeWithFactory(bool backup) async {
    setState(() {
      _isLoading = true;
      _lastAction = '展示${backup ? '备用' : '主'}原生广告(Factory)';
      _isUsingBackup = backup; // 更新备用广告状态
    });

    _addLog('开始$_lastAction');

    try {
      await AdSDK.bindViewByFactoryId(
        AdType.native,
        factoryId: 'adFactoryExample',
        backup: backup,
        callback: _createCallback(),
        scene: _getSceneId(), // 添加场景参数，用于价格事件上报
      );

    } catch (e) {
      _addLog('$_lastAction异常: $e');

    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 获取Banner广告高度
  double _getBannerHeight() {
    if (_selectedBannerSize == gma.AdSize.banner) return 60;
    if (_selectedBannerSize == gma.AdSize.largeBanner) return 110;
    if (_selectedBannerSize == gma.AdSize.mediumRectangle) return 260;
    if (_selectedBannerSize == gma.AdSize.fullBanner) return 70;
    if (_selectedBannerSize == gma.AdSize.leaderboard) return 100;
    return 60; // 默认高度
  }

  AdCallback _createCallback() {
    return AdCallback(
      onAdLoaded: (source) {
        _addLog('广告加载成功 (${source.displayName})');

      },
      onAdFailedToLoad: (error) {
        _addLog('广告加载失败: $error');

      },
      onAdShowed: (source) {
        _addLog('广告开始展示 (${source.displayName})');
        setState(() {
          // 如果是Banner广告，标记为已展示
          if (widget.adType == AdType.banner) {
            _bannerShown = true;
          }
        });
      },
      onAdImpression: (source) {
        _addLog('广告曝光 (${source.displayName})');
      },
      onAdClicked: (source) {
        _addLog('广告被点击 (${source.displayName})');
      },
      onAdDismissed: (source) {
        _addLog('广告关闭 (${source.displayName})');
      },
      onAdFailedToShow: (error) {
        _addLog('广告展示失败: $error');

      },
      onUserEarnedReward: (reward, source) {
        _addLog('获得奖励: ${reward.type} x${reward.amount} (${source.displayName})');
      },
      onPaidEvent: (data, source) {
        _addLog('收益事件: ${data.actualValue} ${data.currencyCode} (${source.displayName})');
      },
    );
  }



  @override
  void dispose() {
    // 清理Banner广告资源
    if (widget.adType == AdType.banner) {
      AdSDK.disposeBanner(pageId: 'demo_page');
    }
    super.dispose();
  }
}
