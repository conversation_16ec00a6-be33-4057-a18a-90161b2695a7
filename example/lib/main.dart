import 'dart:io';
import 'package:flutter/material.dart';
import 'package:ad_sdk/ad_sdk.dart';
import 'pages/ad_demo_page.dart';
import 'utils/demo_reporter.dart';

void main() {
  // 设置广告事件上报器
  AdSDK.setEventReporter(DemoReporter.report);

  // 设置广告价格事件上报器
  AdSDK.setEventPriceReporter((scene, posid, platform, price, currency, paytype) {
    print('💰 广告价格事件上报:');
    print('   场景: $scene');
    print('   广告ID: $posid');
    print('   平台: $platform');
    print('   价格: $price $currency');
    print('   精确度: ${_getPrecisionTypeName(paytype)}');
    print('   ----------------------------------------');
  });

  runApp(const MyApp());
}

/// 获取精确度类型名称
String _getPrecisionTypeName(int paytype) {
  switch (paytype) {
    case 1:
      return '预估';
    case 2:
      return '发布者提供';
    case 3:
      return '精准';
    default:
      return '未知';
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '广告SDK演示',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  bool _isInitialized = false;
  String _initStatus = '未初始化';

  @override
  void initState() {
    super.initState();
    _initializeAds();
  }

  Future<void> _initializeAds() async {
    try {
      setState(() {
        _initStatus = '初始化中...';
      });

      // 初始化广告SDK
      await AdSDK.initialize(AdConfig(
        ads: {
          // Banner广告
          AdType.banner: AdIds(
            main: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/2934735716' // iOS Banner广告测试ID
              : 'ca-app-pub-3940256099942544/6300978111', // Android Banner广告测试ID
            backup: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/2934735716' // iOS Banner广告测试ID
              : 'ca-app-pub-3940256099942544/6300978111', // Android Banner广告测试ID
          ),

          // 插屏广告
          AdType.interstitial: AdIds(
            main: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/4411468910' // iOS插屏广告测试ID
              : 'ca-app-pub-3940256099942544/1033173712', // Android插屏广告测试ID
            backup: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/4411468910' // iOS插屏广告测试ID
              : 'ca-app-pub-3940256099942544/1033173712', // Android插屏广告测试ID
          ),

          // 激励广告
          AdType.rewarded: AdIds(
            main: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/1712485313' // iOS激励广告测试ID
              : 'ca-app-pub-3940256099942544/5224354917', // Android激励广告测试ID
            backup: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/1712485313' // iOS激励广告测试ID
              : 'ca-app-pub-3940256099942544/5224354917', // Android激励广告测试ID
          ),

          // 开屏广告
          AdType.appOpen: AdIds(
            main: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/5575463023' // iOS开屏广告官方测试ID
              : 'ca-app-pub-3940256099942544/3419835294', // Android开屏广告测试ID
            backup: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/5575463023' // iOS开屏广告官方测试ID
              : 'ca-app-pub-3940256099942544/3419835294', // Android开屏广告测试ID
          ),

          // 原生广告
          AdType.native: AdIds(
            main: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/3986624511' // iOS原生广告测试ID
              : 'ca-app-pub-3940256099942544/2247696110', // Android原生广告测试ID
            backup: Platform.isIOS
              ? 'ca-app-pub-3940256099942544/3986624511' // iOS原生广告测试ID
              : 'ca-app-pub-3940256099942544/2247696110', // Android原生广告测试ID
          ),
        },
      ));

      setState(() {
        _isInitialized = true;
        _initStatus = '初始化成功';
      });
    } catch (e) {
      setState(() {
        _initStatus = '初始化失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('广告SDK演示'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            // 初始化状态卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '初始化状态',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _isInitialized ? Icons.check_circle : Icons.error,
                          color: _isInitialized ? Colors.green : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(_initStatus)),
                      ],
                    ),
                    if (!_isInitialized) ...[
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _initializeAds,
                        child: const Text('重新初始化'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 功能演示按钮
            if (_isInitialized) ...[
              // 广告检查器按钮
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '调试工具',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () {
                            AdSDK.openAdInspector();
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('广告检查器已打开（仅调试模式有效）'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          },
                          icon: const Icon(Icons.bug_report),
                          label: const Text('打开广告检查器'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              const Text(
                '广告类型演示',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              
              _buildAdTypeButton(
                context,
                'Banner广告',
                '横幅广告演示',
                Icons.view_headline,
                AdType.banner,
                'home_banner',
              ),
              
              _buildAdTypeButton(
                context,
                '插屏广告',
                '全屏插屏广告演示',
                Icons.fullscreen,
                AdType.interstitial,
                'main_interstitial',
              ),
              
              _buildAdTypeButton(
                context,
                '激励广告',
                '观看视频获得奖励',
                Icons.play_circle_filled,
                AdType.rewarded,
                'reward_video',
              ),
              
              _buildAdTypeButton(
                context,
                '开屏广告',
                '应用启动广告演示',
                Icons.launch,
                AdType.appOpen,
                'app_open',
              ),
              const SizedBox(height: 16),
              _buildAdTypeButton(
                context,
                '原生广告',
                '与内容融合的广告演示',
                Icons.integration_instructions,
                AdType.native,
                'native_ad',
              ),
            ] else ...[
              const SizedBox(height: 100), // 添加一些空间
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      '请等待初始化完成',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 100), // 添加一些空间
            ],
          ],
        ),
        ),
      ),
    );
  }

  Widget _buildAdTypeButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    AdType adType,
    String placement,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AdDemoPage(
                adType: adType,
                title: title,
              ),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          children: [
            Icon(icon, size: 32),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    AdSDK.dispose();
    super.dispose();
  }
}
