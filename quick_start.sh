#!/bin/bash

# World Tune - 快速启动脚本
# 用于解决常见的开发环境问题并快速启动应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[World Tune]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

# 修复常见问题
fix_issues() {
    print_message "修复常见问题..."
    
    # 检查并修复类型错误
    if grep -q "station.tags.split" lib/src/shared/widgets/station_list_item.dart 2>/dev/null; then
        print_warning "发现类型错误，正在修复..."
        # 这个错误已经在代码中修复了
        print_message "类型错误已修复 ✅"
    fi
    
    # 清理和重新安装
    print_message "清理项目..."
    flutter clean
    rm -f pubspec.lock
    
    # 清理 iOS (仅在 macOS 上)
    if [[ "$OSTYPE" == "darwin"* ]] && [ -d "ios" ]; then
        print_message "清理 iOS 依赖..."
        cd ios
        rm -rf Pods/ Podfile.lock
        cd ..
    fi
    
    print_message "安装依赖..."
    flutter pub get
    
    # 安装 iOS 依赖 (仅在 macOS 上)
    if [[ "$OSTYPE" == "darwin"* ]] && [ -d "ios" ]; then
        print_message "安装 iOS 依赖..."
        cd ios
        pod install
        cd ..
    fi
    
    print_message "生成代码..."
    flutter packages pub run build_runner build --delete-conflicting-outputs
    
    print_message "问题修复完成 ✅"
}

# 运行 iOS 开发版本
run_ios_dev() {
    print_message "启动 iOS 开发版本..."
    flutter run -d ios --flavor development -t lib/main_development.dart
}

# 运行默认开发版本
run_dev() {
    print_message "启动开发版本..."
    flutter run --flavor development -t lib/main_development.dart
}

# 显示帮助信息
show_help() {
    echo "🎵 World Tune 快速启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  --fix               修复常见问题"
    echo "  --ios               运行 iOS 开发版本"
    echo "  --dev               运行默认开发版本"
    echo ""
    echo "示例:"
    echo "  $0 --fix            # 修复常见问题"
    echo "  $0 --ios            # 运行 iOS 版本"
    echo "  $0 --dev            # 运行默认版本"
    echo ""
    echo "或者使用 Makefile 命令:"
    echo "  make help           # 查看所有可用命令"
    echo "  make fix_all        # 修复所有问题"
    echo "  make ios_dev        # iOS 快速开发"
    echo "  make dev            # 快速开发"
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        --fix)
            fix_issues
            exit 0
            ;;
        --ios)
            fix_issues
            run_ios_dev
            exit 0
            ;;
        --dev)
            fix_issues
            run_dev
            exit 0
            ;;
        "")
            print_message "🎵 World Tune 快速启动"
            print_info "使用 --help 查看可用选项"
            print_info "推荐使用: make help 查看所有 Makefile 命令"
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
