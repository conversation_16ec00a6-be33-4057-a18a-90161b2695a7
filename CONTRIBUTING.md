# 贡献指南

感谢您对World Tune项目的关注！我们欢迎所有形式的贡献，包括但不限于代码、文档、设计、测试和反馈。

## 🤝 如何贡献

### 报告问题
如果您发现了bug或有功能建议，请：

1. 检查 [Issues](https://github.com/your-repo/world_tune/issues) 确保问题未被报告
2. 使用合适的Issue模板创建新Issue
3. 提供详细的描述和复现步骤
4. 如果是bug，请包含：
   - 设备信息（操作系统、版本）
   - Flutter版本
   - 错误日志
   - 复现步骤

### 提交代码
1. **Fork项目** 到您的GitHub账户
2. **创建分支** 从`main`分支创建功能分支
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **编写代码** 遵循项目的代码规范
4. **测试代码** 确保所有测试通过
5. **提交更改** 使用清晰的提交信息
   ```bash
   git commit -m "feat: add new feature description"
   ```
6. **推送分支** 到您的Fork仓库
   ```bash
   git push origin feature/your-feature-name
   ```
7. **创建Pull Request** 详细描述您的更改

## 📝 代码规范

### Dart/Flutter规范
- 遵循 [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- 使用 `dart format` 格式化代码
- 运行 `flutter analyze` 检查代码质量
- 变量和函数使用驼峰命名法
- 类名使用帕斯卡命名法
- 常量使用大写下划线命名法

### 代码示例
```dart
// ✅ 好的示例
class AudioService {
  static const int maxHistoryCount = 50;
  
  Future<void> playStation(StationSimple station) async {
    // 实现代码
  }
}

// ❌ 不好的示例
class audioservice {
  static const int MAX_HISTORY_COUNT = 50;
  
  Future<void> play_station(StationSimple station) async {
    // 实现代码
  }
}
```

### 文件组织
- 每个文件只包含一个主要的类或功能
- 相关的类可以放在同一个文件中
- 使用有意义的文件名
- 按功能模块组织目录结构

### 注释规范
```dart
/// 播放指定的电台
/// 
/// [station] 要播放的电台信息
/// 
/// 如果播放成功，会自动添加到播放历史
/// 如果播放失败，会抛出 [AudioException]
Future<void> playStation(StationSimple station) async {
  // 实现代码
}
```

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/services/audio_service_test.dart

# 生成覆盖率报告
flutter test --coverage
```

### 测试规范
- 为新功能编写单元测试
- 测试覆盖率应保持在80%以上
- 使用描述性的测试名称
- 遵循AAA模式（Arrange, Act, Assert）

### 测试示例
```dart
group('AudioService Tests', () {
  test('should play station successfully', () async {
    // Arrange
    final audioService = AudioService();
    final station = StationSimple(id: '1', name: 'Test Station');
    
    // Act
    await audioService.playStation(station);
    
    // Assert
    expect(audioService.currentStation, equals(station));
    expect(audioService.isPlaying, isTrue);
  });
});
```

## 📚 文档

### 文档类型
- **README**: 项目概述和快速开始
- **API文档**: 接口说明和示例
- **代码注释**: 函数和类的详细说明
- **架构文档**: 系统设计和技术决策

### 文档规范
- 使用Markdown格式
- 包含代码示例
- 保持文档与代码同步
- 使用中英文双语（优先中文）

## 🎨 设计规范

### UI/UX原则
- 遵循Material Design 3规范
- 保持界面简洁直观
- 确保良好的可访问性
- 支持明暗主题

### 颜色规范
- 主色调：绿色系 (#4CAF50)
- 辅助色：蓝色系 (#2196F3)
- 错误色：红色系 (#F44336)
- 警告色：橙色系 (#FF9800)

## 🔄 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

### 提交类型
- `feat`: 新功能
- `fix`: bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 提交格式
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

### 示例
```bash
feat(player): add volume control functionality

- Add volume slider to player interface
- Implement volume persistence
- Add volume change animations

Closes #123
```

## 🚀 发布流程

### 版本号规范
遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范：
- `MAJOR.MINOR.PATCH`
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 发布检查清单
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 文档更新
- [ ] CHANGELOG更新
- [ ] 版本号更新
- [ ] 构建测试通过

## 🏆 贡献者认可

我们重视每一位贡献者的努力：

### 贡献类型
- 💻 代码贡献
- 📖 文档改进
- 🎨 设计优化
- 🐛 bug报告
- 💡 功能建议
- 🧪 测试改进
- 🌍 翻译工作

### 认可方式
- 在README中列出贡献者
- 在发布说明中感谢贡献者
- 颁发贡献者徽章
- 邀请加入核心团队

## 📞 联系我们

如果您有任何问题或建议，可以通过以下方式联系我们：

- **GitHub Issues**: [项目Issues页面](https://github.com/your-repo/world_tune/issues)
- **邮箱**: <EMAIL>
- **讨论区**: [GitHub Discussions](https://github.com/your-repo/world_tune/discussions)

## 📄 许可证

通过贡献代码，您同意您的贡献将在 [MIT许可证](LICENSE) 下发布。

---

再次感谢您的贡献！让我们一起打造更好的World Tune！ 🎵
