import UIKit
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    // TODO: 注册设备国家检测插件
    // let controller = window?.rootViewController as! FlutterViewController
    // DeviceCountryPlugin.register(with: registrar(forPlugin: "DeviceCountryPlugin")!)

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
