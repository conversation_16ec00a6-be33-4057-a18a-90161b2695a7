import Flutter
import UIKit
import CoreTelephony

/**
 * 设备国家检测插件
 * 
 * 用于获取iOS设备的当前国家代码
 */
public class DeviceCountryPlugin: NSObject, FlutterPlugin {
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "device_country", binaryMessenger: registrar.messenger())
        let instance = DeviceCountryPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "getCountryCode":
            do {
                let countryCode = getDeviceCountryCode()
                result(countryCode)
            } catch {
                result(FlutterError(code: "ERROR", message: "Failed to get country code: \(error.localizedDescription)", details: nil))
            }
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    /**
     * 获取设备国家代码
     * 
     * 尝试多种方式获取设备的国家代码：
     * 1. 从运营商信息获取
     * 2. 从系统语言环境获取
     * 3. 从时区获取
     */
    private func getDeviceCountryCode() -> String? {
        // 方法1: 从运营商信息获取国家代码
        if let carrierCountryCode = getCountryFromCarrier() {
            if isValidCountryCode(carrierCountryCode) {
                return carrierCountryCode.uppercased()
            }
        }
        
        // 方法2: 从系统语言环境获取国家代码
        if let localeCountryCode = getCountryFromLocale() {
            if isValidCountryCode(localeCountryCode) {
                return localeCountryCode.uppercased()
            }
        }
        
        // 方法3: 从时区获取国家代码（最后的备用方案）
        if let timezoneCountryCode = getCountryFromTimezone() {
            if isValidCountryCode(timezoneCountryCode) {
                return timezoneCountryCode.uppercased()
            }
        }
        
        return nil
    }
    
    /**
     * 从运营商信息获取国家代码
     */
    private func getCountryFromCarrier() -> String? {
        let networkInfo = CTTelephonyNetworkInfo()
        
        // iOS 12+ 支持多SIM卡
        if #available(iOS 12.0, *) {
            if let carriers = networkInfo.serviceSubscriberCellularProviders {
                for (_, carrier) in carriers {
                    if let countryCode = carrier.isoCountryCode, !countryCode.isEmpty {
                        return countryCode
                    }
                }
            }
        } else {
            // iOS 12以下版本
            if let carrier = networkInfo.subscriberCellularProvider {
                if let countryCode = carrier.isoCountryCode, !countryCode.isEmpty {
                    return countryCode
                }
            }
        }
        
        return nil
    }
    
    /**
     * 从系统语言环境获取国家代码
     */
    private func getCountryFromLocale() -> String? {
        let locale = Locale.current
        if let countryCode = locale.regionCode, !countryCode.isEmpty {
            return countryCode
        }
        
        // 备用方案：从语言标识符中提取
        let localeIdentifier = locale.identifier
        let components = Locale.components(fromIdentifier: localeIdentifier)
        if let regionCode = components[NSLocale.Key.countryCode.rawValue], !regionCode.isEmpty {
            return regionCode
        }
        
        return nil
    }
    
    /**
     * 从时区获取国家代码（备用方案）
     */
    private func getCountryFromTimezone() -> String? {
        let timeZone = TimeZone.current
        let timeZoneIdentifier = timeZone.identifier
        
        // 简单的时区到国家代码映射
        let timezoneCountryMap: [String: String] = [
            "Asia/Shanghai": "CN",
            "Asia/Hong_Kong": "HK",
            "Asia/Taipei": "TW",
            "Asia/Tokyo": "JP",
            "Asia/Seoul": "KR",
            "America/New_York": "US",
            "America/Los_Angeles": "US",
            "America/Chicago": "US",
            "America/Denver": "US",
            "Europe/London": "GB",
            "Europe/Paris": "FR",
            "Europe/Berlin": "DE",
            "Europe/Rome": "IT",
            "Europe/Madrid": "ES",
            "Europe/Amsterdam": "NL",
            "Europe/Stockholm": "SE",
            "Europe/Oslo": "NO",
            "Europe/Copenhagen": "DK",
            "Europe/Helsinki": "FI",
            "Europe/Zurich": "CH",
            "Europe/Vienna": "AT",
            "Europe/Brussels": "BE",
            "Europe/Warsaw": "PL",
            "Europe/Prague": "CZ",
            "Europe/Budapest": "HU",
            "Europe/Athens": "GR",
            "Europe/Lisbon": "PT",
            "Europe/Dublin": "IE",
            "Australia/Sydney": "AU",
            "Australia/Melbourne": "AU",
            "Pacific/Auckland": "NZ",
            "Asia/Singapore": "SG",
            "Asia/Bangkok": "TH",
            "Asia/Kuala_Lumpur": "MY",
            "Asia/Jakarta": "ID",
            "Asia/Manila": "PH",
            "Asia/Ho_Chi_Minh": "VN",
            "Asia/Kolkata": "IN",
            "Asia/Dubai": "AE",
            "Asia/Riyadh": "SA",
            "Asia/Tehran": "IR",
            "Asia/Baghdad": "IQ",
            "Asia/Karachi": "PK",
            "Asia/Dhaka": "BD",
            "Africa/Cairo": "EG",
            "Africa/Johannesburg": "ZA",
            "Africa/Lagos": "NG",
            "Africa/Nairobi": "KE",
            "America/Sao_Paulo": "BR",
            "America/Argentina/Buenos_Aires": "AR",
            "America/Santiago": "CL",
            "America/Bogota": "CO",
            "America/Lima": "PE",
            "America/Caracas": "VE",
            "America/Mexico_City": "MX",
            "America/Toronto": "CA",
            "America/Vancouver": "CA"
        ]
        
        return timezoneCountryMap[timeZoneIdentifier]
    }
    
    /**
     * 验证国家代码格式
     */
    private func isValidCountryCode(_ countryCode: String?) -> Bool {
        guard let code = countryCode else { return false }
        return !code.isEmpty && code.count == 2 && code.range(of: "^[A-Za-z]{2}$", options: .regularExpression) != nil
    }
}
