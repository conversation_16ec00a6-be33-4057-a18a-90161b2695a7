# iOS 项目配置说明

## 概述
本文档记录了 World Tune iOS 项目的配置和常见问题解决方案。

## 项目结构
```
ios/
├── Flutter/                    # Flutter 生成的配置文件
├── Pods/                      # CocoaPods 依赖
├── Runner/                    # 主应用目标
│   ├── AppDelegate.swift      # 应用委托
│   ├── DeviceCountryPlugin.swift  # 设备国家检测插件
│   └── ...
├── Runner.xcodeproj/          # Xcode 项目文件
├── Runner.xcworkspace/        # Xcode 工作空间
└── Podfile                    # CocoaPods 配置
```

## 多环境配置
项目支持三种环境配置：
- **Development**: 开发环境
- **Staging**: 测试环境  
- **Production**: 生产环境

每个环境都有对应的 Debug、Release、Profile 构建配置。

## 构建命令

### 设备构建
```bash
# 开发环境
flutter build ios --no-codesign --flavor development

# 测试环境
flutter build ios --no-codesign --flavor staging

# 生产环境
flutter build ios --no-codesign --flavor production
```

### 模拟器构建
```bash
# 开发环境
flutter build ios --simulator --flavor development

# 测试环境
flutter build ios --simulator --flavor staging

# 生产环境
flutter build ios --simulator --flavor production
```

## 常见问题解决

### 1. "No such module 'Flutter'" 错误
**问题**: iOS 项目无法找到 Flutter 模块

**解决方案**:
1. 清理项目: `flutter clean`
2. 重新获取依赖: `flutter pub get`
3. 重新安装 CocoaPods: `cd ios && pod install`

### 2. 构建配置错误
**问题**: Flutter 期望标准的 Release 配置，但项目使用自定义配置

**解决方案**: 使用 `--flavor` 参数指定环境：
```bash
flutter build ios --flavor development
```

### 3. DeviceCountryPlugin 编译错误
**问题**: 自定义插件未正确集成到项目中

**当前状态**: 已临时注释掉插件注册代码，待后续完善

**TODO**: 
- 将 DeviceCountryPlugin 正确集成到 Xcode 项目中
- 或者将其转换为独立的 Flutter 插件包

## 依赖管理

### CocoaPods 依赖
项目使用 CocoaPods 管理 iOS 原生依赖，主要包括：
- Flutter 框架
- audio_service (音频服务)
- audio_session (音频会话)
- connectivity_plus (网络连接检测)
- flutter_secure_storage (安全存储)
- just_audio (音频播放)
- shared_preferences (偏好设置)
- sqflite_darwin (SQLite 数据库)

### 更新依赖
```bash
cd ios
pod update
```

## 开发注意事项

1. **代码签名**: 构建时使用 `--no-codesign` 跳过代码签名，部署到设备前需要手动签名
2. **多环境**: 确保使用正确的 flavor 参数构建对应环境的应用
3. **插件集成**: 新增原生插件时需要同时更新 Podfile 和 Xcode 项目配置
4. **版本兼容**: 项目最低支持 iOS 12.0

## 故障排除

如果遇到构建问题，请按以下顺序尝试：

1. 清理并重新构建:
   ```bash
   flutter clean
   flutter pub get
   cd ios && pod install
   ```

2. 删除派生数据 (在 Xcode 中):
   - Xcode → Preferences → Locations → Derived Data → Delete

3. 重置 CocoaPods:
   ```bash
   cd ios
   rm -rf Pods Podfile.lock
   pod install
   ```

4. 检查 Xcode 项目配置是否正确

## 更新历史

- **2024-07-14**: 修复 "No such module 'Flutter'" 错误，临时禁用 DeviceCountryPlugin
- **初始版本**: 建立多环境 iOS 项目配置
